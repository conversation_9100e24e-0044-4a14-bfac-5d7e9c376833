# 王府井小程序后台管理系统 Makefile

# 变量定义
APP_NAME = wangfujing-admin
VERSION = v1.0.0
BUILD_TIME = $(shell date +%Y-%m-%d_%H:%M:%S)
GIT_COMMIT = $(shell git rev-parse --short HEAD)

# Go相关变量
GOCMD = go
GOBUILD = $(GOCMD) build
GOCLEAN = $(GOCMD) clean
GOTEST = $(GOCMD) test
GOGET = $(GOCMD) get
GOMOD = $(GOCMD) mod

# 构建标志
LDFLAGS = -ldflags "-X main.Version=$(VERSION) -X main.BuildTime=$(BUILD_TIME) -X main.GitCommit=$(GIT_COMMIT)"

# 目录
BIN_DIR = bin
API_BINARY = $(BIN_DIR)/api
ADMIN_BINARY = $(BIN_DIR)/admin

# Docker相关
DOCKER_IMAGE = $(APP_NAME)
DOCKER_TAG = $(VERSION)

.PHONY: all build clean test deps docker help

# 默认目标
all: clean deps test build

# 构建所有二进制文件
build: build-api build-admin

# 构建API服务
build-api:
	@echo "Building API service..."
	@mkdir -p $(BIN_DIR)
	$(GOBUILD) $(LDFLAGS) -o $(API_BINARY) ./cmd/api

# 构建Admin服务
build-admin:
	@echo "Building Admin service..."
	@mkdir -p $(BIN_DIR)
	$(GOBUILD) $(LDFLAGS) -o $(ADMIN_BINARY) ./cmd/admin

# 构建Windows版本
build-windows:
	@echo "Building for Windows..."
	@mkdir -p $(BIN_DIR)
	GOOS=windows GOARCH=amd64 $(GOBUILD) $(LDFLAGS) -o $(BIN_DIR)/api.exe ./cmd/api
	GOOS=windows GOARCH=amd64 $(GOBUILD) $(LDFLAGS) -o $(BIN_DIR)/admin.exe ./cmd/admin

# 构建Linux版本
build-linux:
	@echo "Building for Linux..."
	@mkdir -p $(BIN_DIR)
	GOOS=linux GOARCH=amd64 $(GOBUILD) $(LDFLAGS) -o $(BIN_DIR)/api-linux ./cmd/api
	GOOS=linux GOARCH=amd64 $(GOBUILD) $(LDFLAGS) -o $(BIN_DIR)/admin-linux ./cmd/admin

# 清理构建文件
clean:
	@echo "Cleaning..."
	$(GOCLEAN)
	rm -rf $(BIN_DIR)
	rm -rf logs/*

# 运行测试
test:
	@echo "Running tests..."
	$(GOTEST) -v ./...

# 运行测试并生成覆盖率报告
test-coverage:
	@echo "Running tests with coverage..."
	$(GOTEST) -v -coverprofile=coverage.out ./...
	$(GOCMD) tool cover -html=coverage.out -o coverage.html

# 下载依赖
deps:
	@echo "Downloading dependencies..."
	$(GOMOD) download
	$(GOMOD) tidy

# 更新依赖
deps-update:
	@echo "Updating dependencies..."
	$(GOGET) -u ./...
	$(GOMOD) tidy

# 代码格式化
fmt:
	@echo "Formatting code..."
	$(GOCMD) fmt ./...

# 代码检查
lint:
	@echo "Running linter..."
	golangci-lint run

# 运行API服务
run-api:
	@echo "Starting API service..."
	./$(API_BINARY)

# 运行Admin服务
run-admin:
	@echo "Starting Admin service..."
	./$(ADMIN_BINARY)

# 运行所有服务
run: build
	@echo "Starting all services..."
	./$(API_BINARY) &
	./$(ADMIN_BINARY) &
	@echo "Services started. Press Ctrl+C to stop."

# Docker相关命令
docker-build:
	@echo "Building Docker image..."
	docker build -t $(DOCKER_IMAGE):$(DOCKER_TAG) .
	docker tag $(DOCKER_IMAGE):$(DOCKER_TAG) $(DOCKER_IMAGE):latest

docker-run:
	@echo "Running Docker container..."
	docker run -d --name $(APP_NAME) -p 8888:8888 -p 8889:8889 $(DOCKER_IMAGE):latest

docker-stop:
	@echo "Stopping Docker container..."
	docker stop $(APP_NAME) || true
	docker rm $(APP_NAME) || true

docker-push:
	@echo "Pushing Docker image..."
	docker push $(DOCKER_IMAGE):$(DOCKER_TAG)
	docker push $(DOCKER_IMAGE):latest

# Docker Compose相关命令
compose-up:
	@echo "Starting services with Docker Compose..."
	docker-compose up -d

compose-down:
	@echo "Stopping services with Docker Compose..."
	docker-compose down

compose-logs:
	@echo "Showing Docker Compose logs..."
	docker-compose logs -f

compose-build:
	@echo "Building services with Docker Compose..."
	docker-compose build

# 数据库相关命令
db-init:
	@echo "Initializing database..."
	mysql -h localhost -u root -p < sql/init.sql

db-migrate:
	@echo "Running database migrations..."
	# TODO: 添加数据库迁移命令

# 部署相关命令
deploy-dev:
	@echo "Deploying to development environment..."
	$(MAKE) build
	# TODO: 添加开发环境部署脚本

deploy-prod:
	@echo "Deploying to production environment..."
	$(MAKE) build-linux
	# TODO: 添加生产环境部署脚本

# 生成API文档
docs:
	@echo "Generating API documentation..."
	# TODO: 添加API文档生成命令

# 安装开发工具
install-tools:
	@echo "Installing development tools..."
	$(GOGET) github.com/golangci/golangci-lint/cmd/golangci-lint@latest
	$(GOGET) github.com/swaggo/swag/cmd/swag@latest

# 检查代码质量
quality: fmt lint test

# 完整构建流程
release: clean deps quality build-linux docker-build

# 帮助信息
help:
	@echo "Available commands:"
	@echo "  build          - Build all binaries"
	@echo "  build-api      - Build API service"
	@echo "  build-admin    - Build Admin service"
	@echo "  build-windows  - Build for Windows"
	@echo "  build-linux    - Build for Linux"
	@echo "  clean          - Clean build files"
	@echo "  test           - Run tests"
	@echo "  test-coverage  - Run tests with coverage"
	@echo "  deps           - Download dependencies"
	@echo "  deps-update    - Update dependencies"
	@echo "  fmt            - Format code"
	@echo "  lint           - Run linter"
	@echo "  run            - Run all services"
	@echo "  run-api        - Run API service"
	@echo "  run-admin      - Run Admin service"
	@echo "  docker-build   - Build Docker image"
	@echo "  docker-run     - Run Docker container"
	@echo "  docker-stop    - Stop Docker container"
	@echo "  compose-up     - Start with Docker Compose"
	@echo "  compose-down   - Stop Docker Compose"
	@echo "  db-init        - Initialize database"
	@echo "  quality        - Check code quality"
	@echo "  release        - Complete release build"
	@echo "  help           - Show this help"
