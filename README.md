# 王府井小程序后台管理系统

## 项目简介

王府井小程序后台管理系统是一个基于Go语言开发的现代化后台管理系统，为王府井小程序提供完整的后台支撑服务。系统采用微服务架构设计，支持多端访问，包括用户端小程序、商家端小程序和后台管理系统。

### 项目特色

- **多端统一**: 所有端（用户端、商家端、管理端）都通过小程序接收请求，统一技术栈
- **双端口架构**: 8888端口服务小程序端，8889端口服务管理端，职责分离
- **完整权限体系**: 基于RBAC的权限管理，支持细粒度权限控制
- **积分商城系统**: 完整的积分获取、消费、兑换体系
- **商家分值管理**: 商家服务质量评分系统，支持等级管理
- **智能审核流程**: 商品、客诉等多种审核流程
- **实时数据统计**: 丰富的数据报表和统计分析

## 技术栈

### 后端技术
- **语言**: Go 1.21
- **框架**: Gin Web Framework
- **数据库**: MySQL 8.0
- **ORM**: Gorm
- **缓存**: Redis
- **认证**: JWT
- **日志**: Zap
- **对象存储**: 阿里云OSS
- **配置管理**: Viper

### 架构特点
- RESTful API设计
- JWT双token认证机制
- 基于角色的权限控制(RBAC)
- 多端口服务架构
- 统一响应格式
- 完善的错误处理
- 结构化日志记录

## 项目结构

```
wangfujing_admin/
├── cmd/                    # 应用程序入口
│   ├── api/               # 小程序端服务
│   └── admin/             # 管理端服务
├── internal/              # 内部包
│   ├── config/           # 配置管理
│   ├── handlers/         # HTTP处理器
│   ├── middleware/       # 中间件
│   ├── models/          # 数据模型
│   ├── services/        # 业务逻辑
│   └── utils/           # 工具函数
├── pkg/                  # 公共包
│   ├── auth/            # 认证相关
│   ├── cache/           # 缓存操作
│   ├── logger/          # 日志管理
│   ├── oss/             # 对象存储
│   └── response/        # 响应处理
├── etc/                 # 配置文件
├── docs/                # 文档
│   ├── api/            # API文档
│   └── postman/        # Postman集合
├── sql/                 # 数据库脚本
├── logs/                # 日志文件
└── bin/                 # 编译输出
```

## 核心功能模块

### 1. 认证管理
- 微信小程序登录
- JWT双token机制
- 多端认证支持
- 权限验证

### 2. 用户管理
- 用户信息管理
- 角色权限分配
- 积分系统
- 用户状态管理

### 3. 商家管理
- 商家信息管理
- 分值系统
- 等级管理
- 权益申请

### 4. 商品管理
- 商品审核
- 积分商城
- 库存管理
- 商品分类

### 5. 订单管理
- 订单处理
- 状态跟踪
- 积分兑换
- 退款处理

### 6. 客诉管理
- 客诉受理
- 审核流程
- 分值扣减
- 处理跟踪

### 7. 营销管理
- 积分活动
- 会员规则
- 节目管理
- 优惠券

### 8. 统计报表
- 销售统计
- 用户分析
- 商家排行
- 数据报表

## 快速开始

### 环境要求

- Go 1.21+
- MySQL 8.0+
- Redis 6.0+
- Git
- Docker & Docker Compose (可选)

### 方式一：Docker Compose 部署（推荐）

1. **克隆项目**
```bash
git clone <repository-url>
cd wangfujing_admin
```

2. **配置环境变量**
```bash
# 复制环境变量文件
cp .env.example .env

# 修改环境变量（数据库密码、JWT密钥、微信配置等）
vim .env
```

3. **启动所有服务**
```bash
# 启动所有服务（MySQL、Redis、应用、Nginx）
docker-compose up -d

# 查看服务状态
docker-compose ps

# 查看日志
docker-compose logs -f
```

4. **访问服务**
- 小程序API: http://localhost:8888
- 管理端API: http://localhost:8889
- 健康检查: http://localhost:8888/health

### 方式二：本地开发部署

#### 环境要求
- Go 1.21+
- MySQL 8.0+
- Redis 6.0+

### 安装步骤

1. **克隆项目**
```bash
git clone <repository-url>
cd wangfujing_admin
```

2. **安装依赖**
```bash
# 使用Makefile（推荐）
make deps

# 或者手动安装
go mod tidy
```

3. **配置环境**
```bash
# 复制配置文件
cp etc/wangfujing-dev.yaml.example etc/wangfujing-dev.yaml

# 修改配置文件中的数据库、Redis、微信等配置
vim etc/wangfujing-dev.yaml
```

4. **初始化数据库**
```bash
# 创建数据库
mysql -u root -p -e "CREATE DATABASE wangfujing_dev CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;"

# 使用Makefile初始化（推荐）
make db-init

# 或者手动运行脚本
mysql -u root -p wangfujing_dev < sql/init.sql
```

5. **编译和启动**
```bash
# 使用Makefile（推荐）
make build
make run

# 或者手动编译和启动
go build -o bin/api ./cmd/api
go build -o bin/admin ./cmd/admin
./bin/api &    # 小程序端服务 (端口8888)
./bin/admin &  # 管理端服务 (端口8889)
```

### 开发工具

```bash
# 安装开发工具
make install-tools

# 代码格式化
make fmt

# 代码检查
make lint

# 运行测试
make test

# 生成测试覆盖率报告
make test-coverage

# 检查代码质量
make quality

# 查看所有可用命令
make help
```

### 配置说明

#### 环境变量
- `APP_ENV`: 运行环境 (dev/test/prod)

#### 配置文件
- `etc/wangfujing-dev.yaml`: 开发环境配置
- `etc/wangfujing-test.yaml`: 测试环境配置
- `etc/wangfujing-prod.yaml`: 生产环境配置

#### 主要配置项
```yaml
server:
  mode: debug          # 运行模式
  api_port: 8888      # 小程序端口
  admin_port: 8889    # 管理端端口

database:
  host: localhost
  port: 3306
  username: root
  password: "123456"
  database: wangfujing_dev

redis:
  addr: localhost:6379
  password: ""
  db: 0

jwt:
  api_secret: "your_api_secret"
  admin_secret: "your_admin_secret"
  api_expire_time: 7200
  admin_expire_time: 28800

wechat:
  user_app_id: "your_user_app_id"
  user_app_secret: "your_user_app_secret"
  merchant_app_id: "your_merchant_app_id"
  merchant_app_secret: "your_merchant_app_secret"

oss:
  endpoint: "oss-cn-beijing.aliyuncs.com"
  access_key_id: "your_access_key_id"
  access_key_secret: "your_access_key_secret"
  bucket_name: "your_bucket_name"
```

## API文档

### 接口文档
- [API概览](docs/api/README.md)
- [认证管理 API](docs/api/auth.md)
- [用户管理 API](docs/api/user.md)
- [商家管理 API](docs/api/merchant.md)
- [楼层管理 API](docs/api/floor.md)
- [商品管理 API](docs/api/product.md)
- [订单管理 API](docs/api/order.md)
- [客诉管理 API](docs/api/complaint.md)
- [活动管理 API](docs/api/activity.md)
- [扫码核销 API](docs/api/scan.md)
- [统计报表 API](docs/api/stats.md)

### Postman集合
- [API测试集合](docs/postman/wangfujing_api.postman_collection.json)

### 接口规范

#### 请求格式
- 内容类型: `application/json`
- 认证方式: `Authorization: Bearer {token}`
- 字符编码: `UTF-8`

#### 响应格式
```json
{
  "code": 0,
  "message": "success",
  "data": {}
}
```

#### 分页响应
```json
{
  "code": 0,
  "message": "success",
  "data": [],
  "total": 100,
  "page": 1,
  "size": 10
}
```

## 部署指南

### Docker部署
```bash
# 构建镜像
docker build -t wangfujing-admin .

# 运行容器
docker run -d -p 8888:8888 -p 8889:8889 wangfujing-admin
```

### 生产环境部署

#### 系统要求
- CPU: 2核心以上
- 内存: 4GB以上
- 磁盘: 50GB以上
- 操作系统: Linux (推荐CentOS 7+/Ubuntu 18+)

#### 部署步骤
1. **环境准备**
```bash
# 设置环境变量
export APP_ENV=prod

# 创建应用目录
mkdir -p /opt/wangfujing
cd /opt/wangfujing
```

2. **配置文件**
```bash
# 复制生产配置
cp etc/wangfujing-prod.yaml.example etc/wangfujing-prod.yaml
# 修改配置文件中的数据库、Redis、微信等配置
```

3. **服务管理**
```bash
# 创建systemd服务文件
sudo tee /etc/systemd/system/wangfujing-api.service > /dev/null <<EOF
[Unit]
Description=Wangfujing API Service
After=network.target

[Service]
Type=simple
User=wangfujing
WorkingDirectory=/opt/wangfujing
ExecStart=/opt/wangfujing/bin/api
Restart=always
RestartSec=5

[Install]
WantedBy=multi-user.target
EOF

# 启动服务
sudo systemctl enable wangfujing-api
sudo systemctl start wangfujing-api
```

4. **Nginx配置**
```nginx
upstream wangfujing_api {
    server 127.0.0.1:8888;
}

upstream wangfujing_admin {
    server 127.0.0.1:8889;
}

server {
    listen 80;
    server_name api.wangfujing.com;

    location / {
        proxy_pass http://wangfujing_api;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
    }
}

server {
    listen 80;
    server_name admin.wangfujing.com;

    location / {
        proxy_pass http://wangfujing_admin;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
    }
}
```

5. **SSL证书配置**
```bash
# 使用Let's Encrypt
sudo certbot --nginx -d api.wangfujing.com -d admin.wangfujing.com
```

6. **日志轮转**
```bash
# 创建logrotate配置
sudo tee /etc/logrotate.d/wangfujing > /dev/null <<EOF
/opt/wangfujing/logs/*.log {
    daily
    rotate 30
    compress
    delaycompress
    missingok
    notifempty
    create 644 wangfujing wangfujing
    postrotate
        systemctl reload wangfujing-api
        systemctl reload wangfujing-admin
    endscript
}
EOF
```

## 开发指南

### 代码规范
- 遵循Go官方代码规范
- 使用gofmt格式化代码
- 添加必要的注释
- 编写单元测试

### 提交规范
- feat: 新功能
- fix: 修复bug
- docs: 文档更新
- style: 代码格式调整
- refactor: 代码重构
- test: 测试相关
- chore: 构建过程或辅助工具的变动

### 分支管理
- main: 主分支
- develop: 开发分支
- feature/*: 功能分支
- hotfix/*: 热修复分支

## 监控运维

### 健康检查
```bash
# API服务健康检查
curl http://localhost:8888/health

# 管理端服务健康检查
curl http://localhost:8889/health
```

### 日志查看
```bash
# 查看API服务日志
sudo journalctl -u wangfujing-api -f

# 查看应用日志
tail -f /opt/wangfujing/logs/app.log
```

### 性能监控
建议使用以下工具进行监控：
- **Prometheus + Grafana**: 系统指标监控
- **ELK Stack**: 日志分析
- **Jaeger**: 链路追踪
- **Alertmanager**: 告警管理

### 备份策略
```bash
# 数据库备份
mysqldump -u root -p wangfujing_prod > backup_$(date +%Y%m%d_%H%M%S).sql

# Redis备份
redis-cli --rdb backup_$(date +%Y%m%d_%H%M%S).rdb

# 配置文件备份
tar -czf config_backup_$(date +%Y%m%d_%H%M%S).tar.gz etc/
```

## 常见问题

### Q: 如何重置管理员密码？
A: 目前系统使用微信登录，如需密码登录请联系系统管理员。

### Q: 如何配置微信小程序？
A: 在配置文件中设置正确的AppID和AppSecret，确保小程序已发布。

### Q: 数据库连接失败怎么办？
A: 检查数据库配置、网络连接和权限设置。

### Q: Redis连接失败怎么办？
A: 确认Redis服务已启动，检查连接地址和端口。

### Q: 如何查看系统运行状态？
A: 访问 `/health` 端点查看服务健康状态，查看日志文件了解详细信息。

### Q: 如何进行数据迁移？
A: 使用提供的SQL脚本进行数据库迁移，注意备份原有数据。


## 联系我们

- 项目维护者: 开发团队
- 邮箱: <EMAIL>
- 文档: [项目文档](docs/)

## 贡献指南

### 如何贡献
1. Fork 本仓库
2. 创建特性分支 (`git checkout -b feature/AmazingFeature`)
3. 提交更改 (`git commit -m 'Add some AmazingFeature'`)
4. 推送到分支 (`git push origin feature/AmazingFeature`)
5. 打开 Pull Request

### 代码规范
- 遵循 Go 官方代码规范
- 使用 `gofmt` 格式化代码
- 添加必要的单元测试
- 更新相关文档

### 问题反馈
- 使用 GitHub Issues 报告 bug
- 提供详细的错误信息和复现步骤
- 建议新功能时请先讨论可行性

## 更新日志

### v1.0.0 (2024-01-01)
- 🎉 初始版本发布
- ✨ 完成基础功能模块
  - 认证管理（微信登录、JWT认证）
  - 用户管理（用户CRUD、角色分配、积分系统）
  - 商家管理（商家信息、分值系统、等级管理）
  - 楼层管理（楼层信息、排序管理）
  - 商品管理（商品审核、积分商城）
  - 订单管理（订单处理、状态跟踪）
  - 客诉管理（客诉受理、审核流程）
  - 活动管理（积分活动、签到系统）
- 🏗️ 支持小程序端和管理端双端口架构
- 🔐 实现JWT双token认证机制
- 📚 完善API文档和Postman集合
- 🐳 支持Docker部署
- 📊 集成日志系统和监控

### 计划中的功能
- [ ] 消息推送系统
- [ ] 数据导出功能
- [ ] 更多统计报表
- [ ] 移动端管理应用
- [ ] 第三方支付集成
