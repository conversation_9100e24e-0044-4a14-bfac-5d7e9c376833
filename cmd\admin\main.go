package main

import (
	"context"
	"flag"
	"fmt"
	"log"
	"net/http"
	"os"
	"os/signal"
	"syscall"
	"time"

	"wangfujing_admin/internal/config"
	"wangfujing_admin/internal/database"
	"wangfujing_admin/internal/handlers"
	"wangfujing_admin/internal/routes"
	"wangfujing_admin/internal/services"
	"wangfujing_admin/pkg/cache"
	"wangfujing_admin/pkg/logger"

	"github.com/gin-contrib/cors"
	"github.com/gin-gonic/gin"
	"gorm.io/driver/mysql"
	"gorm.io/gorm"
)

func main() {
	// 解析命令行参数
	var configFile string
	var processRewards bool
	flag.StringVar(&configFile, "f", "", "指定配置文件路径")
	flag.BoolVar(&processRewards, "process-rewards", false, "手动处理过期活动奖励")
	flag.Parse()

	// 加载配置
	var cfg *config.Config
	var err error
	if configFile != "" {
		cfg, err = config.LoadFromFile(configFile)
		if err != nil {
			log.Fatalf("Failed to load config from file %s: %v", configFile, err)
		}
		log.Printf("Using config file: %s", configFile)
	} else {
		cfg, err = config.Load()
		if err != nil {
			log.Fatalf("Failed to load config: %v", err)
		}
	}

	// 初始化日志
	logger.InitWithConfig(logger.LogConfig{
		Level:       cfg.Log.Level,
		Filename:    cfg.Log.Filename,
		ErrorFile:   cfg.Log.ErrorFile,
		MaxSize:     cfg.Log.MaxSize,
		MaxAge:      cfg.Log.MaxAge,
		Compress:    cfg.Log.Compress,
		LocalTime:   cfg.Log.LocalTime,
		RotateDaily: cfg.Log.RotateDaily,
		Console:     cfg.Log.Console,
	})
	defer logger.Sync()

	// 初始化数据库
	db, err := initDB(cfg)
	if err != nil {
		logger.Fatal("Failed to connect to database", logger.Err(err))
	}

	// 初始化Redis
	rdb := cache.InitRedis(cfg.Redis.Addr, cfg.Redis.Password, cfg.Redis.DB)

	// 如果是手动处理奖励模式，执行后退出
	if processRewards {
		rewardService := services.NewActivityRewardService(db)
		log.Println("开始处理过期活动奖励...")

		ctx := context.Background()
		processedCount, err := rewardService.ManualProcessExpiredActivities(ctx)
		if err != nil {
			log.Fatalf("处理过期活动失败: %v", err)
		}

		if processedCount == 0 {
			log.Println("没有需要处理的过期活动")
		} else {
			log.Printf("成功处理了 %d 个过期活动的奖励发放", processedCount)
		}

		fmt.Printf("奖励处理完成，共处理 %d 个活动\n", processedCount)
		return
	}

	// 初始化Gin引擎
	if cfg.Server.Mode == "release" {
		gin.SetMode(gin.ReleaseMode)
	}

	r := gin.New()

	// 中间件
	r.Use(gin.Logger())
	r.Use(gin.Recovery())
	r.Use(cors.New(cors.Config{
		AllowOrigins:     []string{"*"},
		AllowMethods:     []string{"GET", "POST", "PUT", "DELETE", "OPTIONS"},
		AllowHeaders:     []string{"*"},
		ExposeHeaders:    []string{"Content-Length"},
		AllowCredentials: true,
		MaxAge:           12 * time.Hour,
	}))

	// 初始化处理器
	h := handlers.NewHandler(db, rdb, cfg)

	// 注册路由
	routes.SetupAdminRoutes(r, h)
	routes.SetupMallRoutes(r, h) // 添加商场端路由

	// 启动服务器
	srv := &http.Server{
		Addr:    fmt.Sprintf(":%d", cfg.Server.AdminPort),
		Handler: r,
	}

	// 优雅关闭
	go func() {
		if err := srv.ListenAndServe(); err != nil && err != http.ErrServerClosed {
			logger.Fatal("Failed to start server", logger.Err(err))
		}
	}()

	logger.Info("Admin Server started", logger.Int("port", cfg.Server.AdminPort))

	// 启动活动奖励处理器（定时任务）
	rewardService := services.NewActivityRewardService(db)
	ctx, cancelRewardProcessor := context.WithCancel(context.Background())
	go rewardService.StartRewardProcessor(ctx)
	logger.Info("Activity reward processor started (runs every 10 minutes)")

	// 启动过期商品清理服务（定时任务）
	cleanupService := services.NewExpiredProductCleanupService(db, rdb)
	cleanupCtx, cancelCleanupService := context.WithCancel(context.Background())
	go cleanupService.StartCleanupScheduler(cleanupCtx)
	logger.Info("Expired product cleanup service started (runs every hour)")

	// 启动VIP形象过期处理服务（定时任务）
	vipAvatarExpiryService := services.NewVipAvatarExpiryService(db)
	vipAvatarCtx, cancelVipAvatarService := context.WithCancel(context.Background())
	go vipAvatarExpiryService.StartExpiryProcessor(vipAvatarCtx)
	logger.Info("VIP avatar expiry service started (runs daily at 00:00)")

	// 等待中断信号
	quit := make(chan os.Signal, 1)
	signal.Notify(quit, syscall.SIGINT, syscall.SIGTERM)
	<-quit

	// 停止定时任务
	cancelRewardProcessor()
	cancelCleanupService()
	cancelVipAvatarService()
	logger.Info("Shutting down server...")

	// 30秒超时关闭
	ctx, cancel := context.WithTimeout(context.Background(), 30*time.Second)
	defer cancel()
	if err := srv.Shutdown(ctx); err != nil {
		logger.Fatal("Server forced to shutdown", logger.Err(err))
	}

	logger.Info("Server exiting")
}

// initDB 初始化数据库连接
func initDB(cfg *config.Config) (*gorm.DB, error) {
	// 构建DSN
	dsn := cfg.Database.DSN
	if dsn == "" {
		dsn = fmt.Sprintf("%s:%s@tcp(%s:%d)/%s?charset=%s&parseTime=True&loc=Asia%%2FShanghai",
			cfg.Database.Username,
			cfg.Database.Password,
			cfg.Database.Host,
			cfg.Database.Port,
			cfg.Database.Database,
			cfg.Database.Charset,
		)
	}

	db, err := gorm.Open(mysql.Open(dsn), &gorm.Config{
		DisableForeignKeyConstraintWhenMigrating: true, // 禁用外键约束
	})
	if err != nil {
		return nil, err
	}

	// 获取底层的sql.DB对象来配置连接池
	sqlDB, err := db.DB()
	if err != nil {
		return nil, err
	}

	// 设置连接池参数（如果配置文件中没有设置，使用默认值）
	maxOpenConns := cfg.Database.MaxOpenConns
	if maxOpenConns <= 0 {
		maxOpenConns = 100 // 默认值
	}
	sqlDB.SetMaxOpenConns(maxOpenConns)

	maxIdleConns := cfg.Database.MaxIdleConns
	if maxIdleConns <= 0 {
		maxIdleConns = 20 // 默认值
	}
	sqlDB.SetMaxIdleConns(maxIdleConns)

	connMaxLifetime := cfg.Database.ConnMaxLifetime
	if connMaxLifetime <= 0 {
		connMaxLifetime = 3600 // 默认1小时
	}
	sqlDB.SetConnMaxLifetime(time.Duration(connMaxLifetime) * time.Second)

	connMaxIdleTime := cfg.Database.ConnMaxIdleTime
	if connMaxIdleTime <= 0 {
		connMaxIdleTime = 1800 // 默认30分钟
	}
	sqlDB.SetConnMaxIdleTime(time.Duration(connMaxIdleTime) * time.Second)

	// 记录连接池配置
	logger.Info("Database connection pool configured",
		logger.Int("max_open_conns", maxOpenConns),
		logger.Int("max_idle_conns", maxIdleConns),
		logger.Int("conn_max_lifetime_seconds", connMaxLifetime),
		logger.Int("conn_max_idle_time_seconds", connMaxIdleTime),
	)

	// 执行数据库自动迁移
	if err := database.AutoMigrate(db); err != nil {
		return nil, err
	}

	return db, nil
}
