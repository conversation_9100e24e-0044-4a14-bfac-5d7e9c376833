package main

import (
	"context"
	"flag"
	"fmt"
	"log"
	"net/http"
	"os"
	"os/signal"
	"syscall"
	"time"

	"wangfujing_admin/internal/config"
	"wangfujing_admin/internal/handlers"
	"wangfujing_admin/internal/middleware"
	"wangfujing_admin/internal/routes"
	"wangfujing_admin/internal/services"
	"wangfujing_admin/pkg/cache"
	"wangfujing_admin/pkg/logger"

	"github.com/gin-contrib/cors"
	"github.com/gin-gonic/gin"
	"gorm.io/driver/mysql"
	"gorm.io/gorm"
)

func main() {
	// 解析命令行参数
	var configFile string
	flag.StringVar(&configFile, "f", "", "指定配置文件路径")
	flag.Parse()

	// 加载配置
	var cfg *config.Config
	var err error
	if configFile != "" {
		cfg, err = config.LoadFromFile(configFile)
		if err != nil {
			log.Fatalf("Failed to load config from file %s: %v", configFile, err)
		}
		log.Printf("Using config file: %s", configFile)
	} else {
		cfg, err = config.Load()
		if err != nil {
			log.Fatalf("Failed to load config: %v", err)
		}
	}

	// 初始化日志
	logger.InitWithConfig(logger.LogConfig{
		Level:       cfg.Log.Level,
		Filename:    cfg.Log.Filename,
		ErrorFile:   cfg.Log.ErrorFile,
		MaxSize:     cfg.Log.MaxSize,
		MaxAge:      cfg.Log.MaxAge,
		Compress:    cfg.Log.Compress,
		LocalTime:   cfg.Log.LocalTime,
		RotateDaily: cfg.Log.RotateDaily,
		Console:     cfg.Log.Console,
	})
	defer logger.Sync()

	// 初始化数据库
	db, err := initDB(cfg)
	if err != nil {
		logger.Fatal("Failed to connect to database", logger.Err(err))
	}

	// 初始化Redis（使用配置文件中的连接池设置）
	rdb := cache.InitRedisWithConfig(cfg.Redis)

	// 初始化Gin引擎
	if cfg.Server.Mode == "release" {
		gin.SetMode(gin.ReleaseMode)
	}

	r := gin.New()

	// 中间件
	r.Use(middleware.ErrorLoggerMiddleware()) // 错误日志中间件
	r.Use(gin.Recovery())
	r.Use(cors.New(cors.Config{
		AllowOrigins:     []string{"*"},
		AllowMethods:     []string{"GET", "POST", "PUT", "DELETE", "OPTIONS"},
		AllowHeaders:     []string{"*"},
		ExposeHeaders:    []string{"Content-Length", "X-Request-ID"},
		AllowCredentials: true,
		MaxAge:           12 * time.Hour,
	}))

	// 初始化处理器
	h := handlers.NewHandler(db, rdb, cfg)

	// 启动秒杀队列处理器
	workerManager := services.NewSeckillWorkerManager(db, rdb, &cfg.Seckill)
	// 为worker manager中的秒杀服务设置二维码服务
	if workerManager.GetSeckillService() != nil && h.GetQRCodeService() != nil {
		workerManager.GetSeckillService().SetQRCodeService(h.GetQRCodeService())
	}
	if err := workerManager.Start(); err != nil {
		log.Fatalf("Failed to start seckill workers: %v", err)
	}
	defer workerManager.Stop()

	// 启动每日库存刷新定时任务
	dailyRefreshService := services.NewSeckillDailyRefreshService(db, rdb)
	refreshCtx, refreshCancel := context.WithCancel(context.Background())
	defer refreshCancel()
	dailyRefreshService.StartDailyRefreshScheduler(refreshCtx)

	// 注册路由
	routes.SetupUserRoutes(r, h)

	// 启动服务器
	srv := &http.Server{
		Addr:    fmt.Sprintf(":%d", cfg.Server.APIPort),
		Handler: r,
	}

	// 优雅关闭
	go func() {
		if err := srv.ListenAndServe(); err != nil && err != http.ErrServerClosed {
			logger.Fatal("Failed to start server", logger.Err(err))
		}
	}()

	logger.Info("API Server started", logger.Int("port", cfg.Server.APIPort))

	// 等待中断信号
	quit := make(chan os.Signal, 1)
	signal.Notify(quit, syscall.SIGINT, syscall.SIGTERM)
	<-quit

	logger.Info("Shutting down server...")

	ctx, cancel := context.WithTimeout(context.Background(), 30*time.Second)
	defer cancel()

	if err := srv.Shutdown(ctx); err != nil {
		logger.Fatal("Server forced to shutdown", logger.Err(err))
	}

	logger.Info("Server exited")
}

func initDB(cfg *config.Config) (*gorm.DB, error) {
	// 构建DSN
	dsn := cfg.Database.DSN
	if dsn == "" {
		dsn = fmt.Sprintf("%s:%s@tcp(%s:%d)/%s?charset=%s&parseTime=True&loc=Asia%%2FShanghai",
			cfg.Database.Username,
			cfg.Database.Password,
			cfg.Database.Host,
			cfg.Database.Port,
			cfg.Database.Database,
			cfg.Database.Charset,
		)
	}

	db, err := gorm.Open(mysql.Open(dsn), &gorm.Config{
		DisableForeignKeyConstraintWhenMigrating: true, // 禁用外键约束
		CreateBatchSize:                          1000,
	})
	if err != nil {
		return nil, err
	}

	// 设置数据库字符集和排序规则
	sqlDB, err := db.DB()
	if err != nil {
		return nil, err
	}

	// 设置连接池参数（如果配置文件中没有设置，使用默认值）
	maxOpenConns := cfg.Database.MaxOpenConns
	if maxOpenConns <= 0 {
		maxOpenConns = 100 // 默认值
	}
	sqlDB.SetMaxOpenConns(maxOpenConns)

	maxIdleConns := cfg.Database.MaxIdleConns
	if maxIdleConns <= 0 {
		maxIdleConns = 20 // 默认值
	}
	sqlDB.SetMaxIdleConns(maxIdleConns)

	connMaxLifetime := cfg.Database.ConnMaxLifetime
	if connMaxLifetime <= 0 {
		connMaxLifetime = 3600 // 默认1小时
	}
	sqlDB.SetConnMaxLifetime(time.Duration(connMaxLifetime) * time.Second)

	connMaxIdleTime := cfg.Database.ConnMaxIdleTime
	if connMaxIdleTime <= 0 {
		connMaxIdleTime = 1800 // 默认30分钟
	}
	sqlDB.SetConnMaxIdleTime(time.Duration(connMaxIdleTime) * time.Second)

	// 记录连接池配置
	logger.Info("Database connection pool configured",
		logger.Int("max_open_conns", maxOpenConns),
		logger.Int("max_idle_conns", maxIdleConns),
		logger.Int("conn_max_lifetime_seconds", connMaxLifetime),
		logger.Int("conn_max_idle_time_seconds", connMaxIdleTime),
	)

	// 确保连接使用正确的字符集
	_, err = sqlDB.Exec("SET NAMES utf8mb4 COLLATE utf8mb4_general_ci")
	if err != nil {
		return nil, err
	}

	// 注意：数据库迁移统一在admin服务中处理，api服务不执行迁移

	return db, nil
}
