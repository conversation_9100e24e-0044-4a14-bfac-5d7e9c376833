version: '3.8'

services:
  # MySQL数据库
  mysql:
    image: mysql:8.0
    container_name: wangfujing-mysql
    restart: unless-stopped
    environment:
      MYSQL_ROOT_PASSWORD: ${MYSQL_ROOT_PASSWORD:-123456}
      MYSQL_DATABASE: ${MYSQL_DATABASE:-wangfujing_prod}
      MYSQL_USER: ${MYSQL_USER:-wangfujing}
      MYSQL_PASSWORD: ${MYSQL_PASSWORD:-wangfujing123}
    ports:
      - "3306:3306"
    volumes:
      - mysql_data:/var/lib/mysql
      - ./sql/init.sql:/docker-entrypoint-initdb.d/init.sql
    command: --default-authentication-plugin=mysql_native_password
    networks:
      - wangfujing-network

  # Redis缓存
  redis:
    image: redis:7-alpine
    container_name: wangfujing-redis
    restart: unless-stopped
    ports:
      - "6379:6379"
    volumes:
      - redis_data:/data
    command: redis-server --appendonly yes --requirepass ${REDIS_PASSWORD:-}
    networks:
      - wangfujing-network

  # 应用服务
  app:
    build: .
    container_name: wangfujing-app
    restart: unless-stopped
    environment:
      APP_ENV: ${APP_ENV:-prod}
    ports:
      - "8888:8888"  # API端口
      - "8889:8889"  # Admin端口
    volumes:
      - ./etc:/app/etc
      - ./logs:/app/logs
    depends_on:
      - mysql
      - redis
    networks:
      - wangfujing-network
    healthcheck:
      test: ["CMD", "wget", "--no-verbose", "--tries=1", "--spider", "http://localhost:8888/health"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 40s

  # Nginx反向代理
  nginx:
    image: nginx:alpine
    container_name: wangfujing-nginx
    restart: unless-stopped
    ports:
      - "80:80"
      - "443:443"
    volumes:
      - ./nginx/nginx.conf:/etc/nginx/nginx.conf
      - ./nginx/conf.d:/etc/nginx/conf.d
      - ./nginx/ssl:/etc/nginx/ssl
    depends_on:
      - app
    networks:
      - wangfujing-network

volumes:
  mysql_data:
    driver: local
  redis_data:
    driver: local

networks:
  wangfujing-network:
    driver: bridge
