#!/bin/sh

# 设置默认环境变量
export APP_ENV=${APP_ENV:-prod}

# 检查配置文件是否存在
CONFIG_FILE="./etc/wangfujing-${APP_ENV}.yaml"
if [ ! -f "$CONFIG_FILE" ]; then
    echo "Error: Configuration file $CONFIG_FILE not found"
    exit 1
fi

echo "Starting Wangfujing services with environment: $APP_ENV"

# 启动API服务（后台运行）
echo "Starting API service on port 8888..."
./api &
API_PID=$!

# 启动Admin服务（后台运行）
echo "Starting Admin service on port 8889..."
./admin &
ADMIN_PID=$!

# 等待服务启动
sleep 5

# 检查服务是否正常启动
if ! kill -0 $API_PID 2>/dev/null; then
    echo "Error: API service failed to start"
    exit 1
fi

if ! kill -0 $ADMIN_PID 2>/dev/null; then
    echo "Error: Admin service failed to start"
    exit 1
fi

echo "All services started successfully"
echo "API service PID: $API_PID"
echo "Admin service PID: $ADMIN_PID"

# 信号处理函数
cleanup() {
    echo "Shutting down services..."
    kill $API_PID $ADMIN_PID
    wait $API_PID $ADMIN_PID
    echo "Services stopped"
    exit 0
}

# 捕获信号
trap cleanup SIGTERM SIGINT

# 等待进程结束
wait
