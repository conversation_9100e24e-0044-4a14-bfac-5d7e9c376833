# 王府井小程序后台管理系统 - 开发环境配置

server:
  mode: debug
  api_port: 8888      # 小程序端口
  admin_port: 8889    # 后台管理端口

database:
  host: localhost
  port: 3306
  username: root
  password: "123456"
  database: wang<PERSON><PERSON>ji
  charset: utf8mb4
  # dsn: "root:123456@tcp(localhost:3306)/wangfu<PERSON>ji?charset=utf8mb4&parseTime=True&loc=Asia%2FShanghai"

  # 连接池配置
  max_open_conns: 100       # 最大打开连接数
  max_idle_conns: 20        # 最大空闲连接数
  conn_max_lifetime: 3600   # 连接最大生存时间(秒)
  conn_max_idle_time: 1800  # 连接最大空闲时间(秒)

redis:
  addr: localhost:6379
  password: ""
  db: 0
  pool_size: 200        # 连接池大小
  min_idle_conns: 20    # 最小空闲连接数
  max_retries: 3        # 最大重试次数
  dial_timeout: 5       # 连接超时(秒)
  read_timeout: 3       # 读取超时(秒)
  write_timeout: 3      # 写入超时(秒)

jwt:
  # 小程序端JWT配置
  api_secret: "wfj_api_secret_key_dev_2025"
  api_expire_time: 720000  # 200小时

  # 管理端JWT配置  
  admin_secret: "wfj_admin_secret_key_dev_2025"
  admin_expire_time: 2880000  # 800小时

  # 刷新Token过期时间
  refresh_expire_time: 604800  # 7天

wechat:
  # 用户端小程序配置
  user_app_id: "wx7131ff9883c5e05a"
  user_app_secret: "1c9b7cdb8ce2697e190d1029bfd3e313"

  # 商家端小程序配置
  merchant_app_id: "wx7131ff9883c5e05a"
  merchant_app_secret: "1c9b7cdb8ce2697e190d1029bfd3e313"

oss:
  endpoint: "oss-cn-beijing.aliyuncs.com"
  access_key_id: "LTAI4FuMKQLa6ZAYrvvk894N"
  access_key_secret: "******************************"
  bucket_name: "wangfushiji"
  domain: "https://wangfushiji.oss-cn-beijing.aliyuncs.com"

log:
  level: debug
  filename: "logs/wangfujing-dev.log"
  error_file: "logs/wangfujing-dev-error.log"  # 错误日志文件
  max_size: 0        # 不限制文件大小，完全按天切割
  max_age: 7         # days
  compress: false    # 不压缩
  local_time: true   # 使用本地时间
  rotate_daily: true # 按天切割
  console: true      # 开发环境输出到控制台

# 高并发秒杀配置
seckill:
  # 全局限流配置
  global_rate_limit: 1000     # 全局QPS限制
  global_bucket_size: 2000    # 全局令牌桶大小

  # 商品级别限流配置
  product_rate_limit: 500     # 单商品QPS限制
  product_bucket_size: 1000   # 单商品令牌桶大小

  # Worker配置
  worker_count: 10            # Worker数量
  worker_batch_size: 50       # Worker批处理大小

  # 队列配置
  queue_timeout: 30           # 队列超时时间(秒)
  result_cache_time: 600      # 结果缓存时间(秒)

  # 监控配置
  monitor_interval: 30        # 监控间隔(秒)
  alert_queue_size: 5000      # 队列告警阈值
