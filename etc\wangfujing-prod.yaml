# 王府井小程序后台管理系统 - 生产环境配置

server:
  mode: release
  api_port: 8888      # 小程序端口
  admin_port: 8889    # 后台管理端口

database:
  host: prod-db-host
  port: 3306
  username: wang<PERSON><PERSON><PERSON>
  password: "prod_password_2024_secure"
  database: wangfushiji
  charset: utf8mb4
  # dsn: "wangfushiji:prod_password_2024_secure@tcp(prod-db-host:3306)/wangfushiji?charset=utf8mb4&parseTime=True&loc=Asia%2FShanghai"

  # 连接池配置
  max_open_conns: 200       # 最大打开连接数
  max_idle_conns: 50        # 最大空闲连接数
  conn_max_lifetime: 3600   # 连接最大生存时间(秒)
  conn_max_idle_time: 1800  # 连接最大空闲时间(秒)

redis:
  addr: prod-redis-host:6379
  password: "prod_redis_password_secure"
  db: 0
  pool_size: 500        # 生产环境大连接池
  min_idle_conns: 50    # 最小空闲连接数
  max_retries: 5        # 最大重试次数
  dial_timeout: 10      # 连接超时(秒)
  read_timeout: 5       # 读取超时(秒)
  write_timeout: 5      # 写入超时(秒)

jwt:
  # 小程序端JWT配置
  api_secret: "wfj_api_secret_key_prod_2024_secure"
  api_expire_time: 7200  # 2小时

  # 管理端JWT配置  
  admin_secret: "wfj_admin_secret_key_prod_2024_secure"
  admin_expire_time: 28800  # 8小时

  # 刷新Token过期时间
  refresh_expire_time: 604800  # 7天

wechat:
  # 用户端小程序配置
  user_app_id: "wx1ae74c41b8b1ea82"
  user_app_secret: "503c1c665696139005ac02b5ba466797"

  # 商家端小程序配置
  merchant_app_id: "wxd064bfedb43df87b"
  merchant_app_secret: "d7b6828a777fa3177bf9da5307a59f20"

oss:
  endpoint: "oss-cn-beijing.aliyuncs.com"
  access_key_id: "LTAI4FuMKQLa6ZAYrvvk894N"
  access_key_secret: "******************************"
  bucket_name: "wangfushiji"
  domain: "https://wangfushiji.oss-cn-beijing.aliyuncs.com"

log:
  level: warn
  filename: "logs/wangfujing-prod.log"
  error_file: "logs/wangfujing-prod-error.log"  # 错误日志文件
  max_size: 0        # 不限制文件大小，完全按天切割
  max_age: 180       # days
  compress: false     # 生产环境压缩日志
  local_time: true   # 使用本地时间
  rotate_daily: true # 按天切割
  console: false     # 生产环境不输出到控制台

# 高并发秒杀配置（生产环境）
seckill:
  # 全局限流配置
  global_rate_limit: 2000     # 生产环境高QPS限制
  global_bucket_size: 5000    # 大令牌桶容量

  # 商品级别限流配置
  product_rate_limit: 1000    # 单商品高QPS限制
  product_bucket_size: 2000   # 单商品大令牌桶

  # Worker配置
  worker_count: 20            # 生产环境更多Worker
  worker_batch_size: 100      # 大批处理大小

  # 队列配置
  queue_timeout: 60           # 队列超时时间(秒)
  result_cache_time: 1800     # 结果缓存时间(秒)

  # 监控配置
  monitor_interval: 10        # 频繁监控间隔(秒)
  alert_queue_size: 10000     # 高队列告警阈值
