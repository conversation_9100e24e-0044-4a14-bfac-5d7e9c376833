package config

import (
	"fmt"
	"os"
	"strings"

	"github.com/spf13/viper"
)

// Config 应用配置结构
type Config struct {
	Server   ServerConfig   `mapstructure:"server"`
	Database DatabaseConfig `mapstructure:"database"`
	Redis    RedisConfig    `mapstructure:"redis"`
	JWT      JWTConfig      `mapstructure:"jwt"`
	WeChat   WeChatConfig   `mapstructure:"wechat"`
	OSS      OSSConfig      `mapstructure:"oss"`
	Log      LogConfig      `mapstructure:"log"`
	Seckill  SeckillConfig  `mapstructure:"seckill"`
}

// ServerConfig 服务器配置
type ServerConfig struct {
	Mode      string `mapstructure:"mode"`
	APIPort   int    `mapstructure:"api_port"`
	AdminPort int    `mapstructure:"admin_port"`
}

// DatabaseConfig 数据库配置
type DatabaseConfig struct {
	Host     string `mapstructure:"host"`
	Port     int    `mapstructure:"port"`
	Username string `mapstructure:"username"`
	Password string `mapstructure:"password"`
	Database string `mapstructure:"database"`
	Charset  string `mapstructure:"charset"`
	DSN      string `mapstructure:"dsn"`

	// 连接池配置
	MaxOpenConns    int `mapstructure:"max_open_conns"`     // 最大打开连接数
	MaxIdleConns    int `mapstructure:"max_idle_conns"`     // 最大空闲连接数
	ConnMaxLifetime int `mapstructure:"conn_max_lifetime"`  // 连接最大生存时间(秒)
	ConnMaxIdleTime int `mapstructure:"conn_max_idle_time"` // 连接最大空闲时间(秒)
}

// RedisConfig Redis配置
type RedisConfig struct {
	Addr         string `mapstructure:"addr"`
	Password     string `mapstructure:"password"`
	DB           int    `mapstructure:"db"`
	PoolSize     int    `mapstructure:"pool_size"`
	MinIdleConns int    `mapstructure:"min_idle_conns"`
	MaxRetries   int    `mapstructure:"max_retries"`
	DialTimeout  int    `mapstructure:"dial_timeout"`  // 连接超时(秒)
	ReadTimeout  int    `mapstructure:"read_timeout"`  // 读取超时(秒)
	WriteTimeout int    `mapstructure:"write_timeout"` // 写入超时(秒)
}

// JWTConfig JWT配置
type JWTConfig struct {
	// 小程序端JWT配置
	APISecret     string `mapstructure:"api_secret"`
	APIExpireTime int    `mapstructure:"api_expire_time"`

	// 管理端JWT配置
	AdminSecret     string `mapstructure:"admin_secret"`
	AdminExpireTime int    `mapstructure:"admin_expire_time"`

	// 刷新Token配置
	RefreshExpireTime int `mapstructure:"refresh_expire_time"`
}

// WeChatConfig 微信配置
type WeChatConfig struct {
	// 用户端小程序配置
	UserAppID     string `mapstructure:"user_app_id"`
	UserAppSecret string `mapstructure:"user_app_secret"`

	// 商家端小程序配置
	MerchantAppID     string `mapstructure:"merchant_app_id"`
	MerchantAppSecret string `mapstructure:"merchant_app_secret"`
}

// OSSConfig 阿里云OSS配置
type OSSConfig struct {
	Endpoint        string `mapstructure:"endpoint"`
	AccessKeyID     string `mapstructure:"access_key_id"`
	AccessKeySecret string `mapstructure:"access_key_secret"`
	BucketName      string `mapstructure:"bucket_name"`
	Domain          string `mapstructure:"domain"`
}

// LogConfig 日志配置
type LogConfig struct {
	Level       string `mapstructure:"level"`
	Filename    string `mapstructure:"filename"`
	ErrorFile   string `mapstructure:"error_file"` // 错误日志文件
	MaxSize     int    `mapstructure:"max_size"`
	MaxAge      int    `mapstructure:"max_age"`
	Compress    bool   `mapstructure:"compress"`
	LocalTime   bool   `mapstructure:"local_time"`   // 使用本地时间
	RotateDaily bool   `mapstructure:"rotate_daily"` // 按天切割
	Console     bool   `mapstructure:"console"`      // 是否输出到控制台
}

// SeckillConfig 秒杀配置
type SeckillConfig struct {
	// 全局限流配置
	GlobalRateLimit  int `mapstructure:"global_rate_limit"`  // 全局QPS限制
	GlobalBucketSize int `mapstructure:"global_bucket_size"` // 全局令牌桶大小

	// 商品级别限流配置
	ProductRateLimit  int `mapstructure:"product_rate_limit"`  // 单商品QPS限制
	ProductBucketSize int `mapstructure:"product_bucket_size"` // 单商品令牌桶大小

	// Worker配置
	WorkerCount     int `mapstructure:"worker_count"`      // Worker数量
	WorkerBatchSize int `mapstructure:"worker_batch_size"` // Worker批处理大小

	// 队列配置
	QueueTimeout    int `mapstructure:"queue_timeout"`     // 队列超时时间(秒)
	ResultCacheTime int `mapstructure:"result_cache_time"` // 结果缓存时间(秒)

	// 监控配置
	MonitorInterval int `mapstructure:"monitor_interval"` // 监控间隔(秒)
	AlertQueueSize  int `mapstructure:"alert_queue_size"` // 队列告警阈值
}

// Load 加载配置文件
func Load() (*Config, error) {
	return LoadFromFile("")
}

// LoadFromFile 从指定文件加载配置
func LoadFromFile(configFile string) (*Config, error) {
	if configFile != "" {
		// 如果指定了配置文件，直接使用
		viper.SetConfigFile(configFile)
	} else {
		// 获取环境变量，默认为dev
		env := os.Getenv("APP_ENV")
		if env == "" {
			env = "dev"
		}

		// 设置配置文件名
		configName := fmt.Sprintf("wangfujing-%s", env)

		viper.SetConfigName(configName)
		viper.SetConfigType("yaml")
		viper.AddConfigPath("./etc")
		viper.AddConfigPath("../etc")
		viper.AddConfigPath("../../etc")
	}

	// 设置环境变量前缀
	viper.SetEnvPrefix("WFJ")
	viper.SetEnvKeyReplacer(strings.NewReplacer(".", "_"))
	viper.AutomaticEnv()

	// 读取配置文件
	if err := viper.ReadInConfig(); err != nil {
		return nil, fmt.Errorf("failed to read config file: %w", err)
	}

	var config Config
	if err := viper.Unmarshal(&config); err != nil {
		return nil, fmt.Errorf("failed to unmarshal config: %w", err)
	}

	// 构建数据库DSN
	if config.Database.DSN == "" {
		config.Database.DSN = fmt.Sprintf("%s:%s@tcp(%s:%d)/%s?charset=%s&collation=utf8mb4_general_ci&parseTime=True&loc=Asia%%2FShanghai",
			config.Database.Username,
			config.Database.Password,
			config.Database.Host,
			config.Database.Port,
			config.Database.Database,
			config.Database.Charset,
		)
	}

	return &config, nil
}

// GetEnv 获取当前环境
func GetEnv() string {
	env := os.Getenv("APP_ENV")
	if env == "" {
		return "dev"
	}
	return env
}

// IsDev 是否为开发环境
func IsDev() bool {
	return GetEnv() == "dev"
}

// IsTest 是否为测试环境
func IsTest() bool {
	return GetEnv() == "test"
}

// IsProd 是否为生产环境
func IsProd() bool {
	return GetEnv() == "prod"
}
