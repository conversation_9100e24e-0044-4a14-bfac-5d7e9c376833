package database

import (
	"fmt"

	"wangfujing_admin/internal/models"

	"gorm.io/gorm"
)

// InitAdminPermissions 初始化管理员权限
func InitAdminPermissions(db *gorm.DB) error {
	// 管理员权限列表
	adminPermissions := []models.Permission{
		// 权限管理
		{Name: "admin:permission:read", DisplayName: "查看权限", Module: "权限管理", Action: "read", Resource: "permission"},
		{Name: "admin:permission:create", DisplayName: "创建权限", Module: "权限管理", Action: "create", Resource: "permission"},
		{Name: "admin:permission:update", DisplayName: "更新权限", Module: "权限管理", Action: "update", Resource: "permission"},
		{Name: "admin:permission:delete", DisplayName: "删除权限", Module: "权限管理", Action: "delete", Resource: "permission"},

		// 角色管理
		{Name: "admin:role:read", DisplayName: "查看角色", Module: "角色管理", Action: "read", Resource: "role"},
		{Name: "admin:role:create", DisplayName: "创建角色", Module: "角色管理", Action: "create", Resource: "role"},
		{Name: "admin:role:update", DisplayName: "更新角色", Module: "角色管理", Action: "update", Resource: "role"},
		{Name: "admin:role:delete", DisplayName: "删除角色", Module: "角色管理", Action: "delete", Resource: "role"},
		{Name: "admin:role:assign", DisplayName: "分配角色", Module: "角色管理", Action: "assign", Resource: "role"},

		// 用户管理
		{Name: "admin:user:read", DisplayName: "查看用户", Module: "用户管理", Action: "read", Resource: "user"},
		{Name: "admin:user:create", DisplayName: "创建用户", Module: "用户管理", Action: "create", Resource: "user"},
		{Name: "admin:user:update", DisplayName: "更新用户", Module: "用户管理", Action: "update", Resource: "user"},
		{Name: "admin:user:delete", DisplayName: "删除用户", Module: "用户管理", Action: "delete", Resource: "user"},

		// 楼层管理
		{Name: "admin:floor:read", DisplayName: "查看楼层", Module: "楼层管理", Action: "read", Resource: "floor"},
		{Name: "admin:floor:create", DisplayName: "创建楼层", Module: "楼层管理", Action: "create", Resource: "floor"},
		{Name: "admin:floor:update", DisplayName: "更新楼层", Module: "楼层管理", Action: "update", Resource: "floor"},
		{Name: "admin:floor:delete", DisplayName: "删除楼层", Module: "楼层管理", Action: "delete", Resource: "floor"},

		// 商家管理
		{Name: "admin:merchant:read", DisplayName: "查看商家", Module: "商家管理", Action: "read", Resource: "merchant"},
		{Name: "admin:merchant:create", DisplayName: "创建商家", Module: "商家管理", Action: "create", Resource: "merchant"},
		{Name: "admin:merchant:update", DisplayName: "更新商家", Module: "商家管理", Action: "update", Resource: "merchant"},
		{Name: "admin:merchant:delete", DisplayName: "删除商家", Module: "商家管理", Action: "delete", Resource: "merchant"},

		// 商品管理
		{Name: "admin:product:read", DisplayName: "查看商品", Module: "商品管理", Action: "read", Resource: "product"},
		{Name: "admin:product:create", DisplayName: "创建商品", Module: "商品管理", Action: "create", Resource: "product"},
		{Name: "admin:product:update", DisplayName: "更新商品", Module: "商品管理", Action: "update", Resource: "product"},
		{Name: "admin:product:delete", DisplayName: "删除商品", Module: "商品管理", Action: "delete", Resource: "product"},
		{Name: "admin:product:approve", DisplayName: "审核商品", Module: "商品管理", Action: "approve", Resource: "product"},

		// 等级管理
		{Name: "admin:level:read", DisplayName: "查看等级", Module: "等级管理", Action: "read", Resource: "level"},
		{Name: "admin:level:create", DisplayName: "创建等级", Module: "等级管理", Action: "create", Resource: "level"},
		{Name: "admin:level:update", DisplayName: "更新等级", Module: "等级管理", Action: "update", Resource: "level"},
		{Name: "admin:level:delete", DisplayName: "删除等级", Module: "等级管理", Action: "delete", Resource: "level"},

		// 权益管理
		{Name: "admin:benefit:read", DisplayName: "查看权益", Module: "权益管理", Action: "read", Resource: "benefit"},
		{Name: "admin:benefit:create", DisplayName: "创建权益", Module: "权益管理", Action: "create", Resource: "benefit"},
		{Name: "admin:benefit:update", DisplayName: "更新权益", Module: "权益管理", Action: "update", Resource: "benefit"},
		{Name: "admin:benefit:delete", DisplayName: "删除权益", Module: "权益管理", Action: "delete", Resource: "benefit"},
		{Name: "admin:benefit:manage", DisplayName: "管理权益申请", Module: "权益管理", Action: "manage", Resource: "benefit"},

		// 用户营销活动管理
		{Name: "admin:user-marketing:read", DisplayName: "查看用户营销活动", Module: "用户营销管理", Action: "read", Resource: "user-marketing"},
		{Name: "admin:user-marketing:create", DisplayName: "创建用户营销活动", Module: "用户营销管理", Action: "create", Resource: "user-marketing"},
		{Name: "admin:user-marketing:update", DisplayName: "更新用户营销活动", Module: "用户营销管理", Action: "update", Resource: "user-marketing"},
		{Name: "admin:user-marketing:delete", DisplayName: "删除用户营销活动", Module: "用户营销管理", Action: "delete", Resource: "user-marketing"},

		// 商家活动管理
		{Name: "admin:merchant-activity:read", DisplayName: "查看商家活动", Module: "商家活动管理", Action: "read", Resource: "merchant-activity"},
		{Name: "admin:merchant-activity:create", DisplayName: "创建商家活动", Module: "商家活动管理", Action: "create", Resource: "merchant-activity"},
		{Name: "admin:merchant-activity:update", DisplayName: "更新商家活动", Module: "商家活动管理", Action: "update", Resource: "merchant-activity"},
		{Name: "admin:merchant-activity:delete", DisplayName: "删除商家活动", Module: "商家活动管理", Action: "delete", Resource: "merchant-activity"},

		// 积分商城管理
		{Name: "admin:points-mall:read", DisplayName: "查看积分商城", Module: "积分商城管理", Action: "read", Resource: "points-mall"},
		{Name: "admin:points-mall:create", DisplayName: "创建积分商城商品", Module: "积分商城管理", Action: "create", Resource: "points-mall"},
		{Name: "admin:points-mall:update", DisplayName: "更新积分商城商品", Module: "积分商城管理", Action: "update", Resource: "points-mall"},
		{Name: "admin:points-mall:delete", DisplayName: "删除积分商城商品", Module: "积分商城管理", Action: "delete", Resource: "points-mall"},

		// 会员规则管理
		{Name: "admin:member-rule:read", DisplayName: "查看会员规则", Module: "会员规则管理", Action: "read", Resource: "member-rule"},
		{Name: "admin:member-rule:create", DisplayName: "创建会员规则", Module: "会员规则管理", Action: "create", Resource: "member-rule"},
		{Name: "admin:member-rule:update", DisplayName: "更新会员规则", Module: "会员规则管理", Action: "update", Resource: "member-rule"},
		{Name: "admin:member-rule:delete", DisplayName: "删除会员规则", Module: "会员规则管理", Action: "delete", Resource: "member-rule"},

		// 其他管理员权限...
	}

	// 批量创建权限
	for _, permission := range adminPermissions {
		var existingPermission models.Permission
		result := db.Where("name = ?", permission.Name).First(&existingPermission)
		if result.Error == gorm.ErrRecordNotFound {
			if err := db.Create(&permission).Error; err != nil {
				return fmt.Errorf("创建管理员权限失败: %v", err)
			}
		}
	}

	return nil
}

// InitMerchantPermissions 初始化商家端权限
func InitMerchantPermissions(db *gorm.DB) error {
	// 商家端权限列表
	merchantPermissions := []models.Permission{
		// 首页
		{
			Name:        "merchant:dashboard:read",
			DisplayName: "查看首页",
			Module:      "首页",
			Action:      "read",
			Resource:    "dashboard",
			Description: "商家端首页查看权限",
		},

		// 核销扫码
		{
			Name:        "merchant:verification:scan",
			DisplayName: "核销扫码",
			Module:      "核销扫码",
			Action:      "scan",
			Resource:    "verification",
			Description: "商家端核销扫码权限",
		},
		{
			Name:        "merchant:verification:read",
			DisplayName: "查看核销记录",
			Module:      "核销扫码",
			Action:      "read",
			Resource:    "verification",
			Description: "商家端查看核销记录权限",
		},

		// 商品管理
		{
			Name:        "merchant:product:create",
			DisplayName: "新增商品",
			Module:      "商品管理",
			Action:      "create",
			Resource:    "product",
			Description: "商家端新增商品权限",
		},
		{
			Name:        "merchant:product:read",
			DisplayName: "查看商品列表",
			Module:      "商品管理",
			Action:      "read",
			Resource:    "product",
			Description: "商家端查看商品列表权限",
		},
		{
			Name:        "merchant:product:update",
			DisplayName: "编辑商品",
			Module:      "商品管理",
			Action:      "update",
			Resource:    "product",
			Description: "商家端编辑商品权限",
		},
		{
			Name:        "merchant:product:offline",
			DisplayName: "下架商品",
			Module:      "商品管理",
			Action:      "offline",
			Resource:    "product",
			Description: "商家端下架商品权限",
		},
		{
			Name:        "merchant:product:delete",
			DisplayName: "删除商品",
			Module:      "商品管理",
			Action:      "delete",
			Resource:    "product",
			Description: "商家端删除商品权限",
		},
		{
			Name:        "merchant:product:audit_record",
			DisplayName: "查看审核记录",
			Module:      "商品管理",
			Action:      "read",
			Resource:    "audit_record",
			Description: "商家端查看商品审核记录权限",
		},

		// 我的权益
		{
			Name:        "merchant:benefit:apply",
			DisplayName: "权益申请",
			Module:      "我的权益",
			Action:      "apply",
			Resource:    "benefit",
			Description: "商家端权益申请权限",
		},
		{
			Name:        "merchant:benefit:read",
			DisplayName: "查看申请历史",
			Module:      "我的权益",
			Action:      "read",
			Resource:    "benefit",
			Description: "商家端查看权益申请历史权限",
		},

		// 活动报名
		{
			Name:        "merchant:activity:read",
			DisplayName: "查看活动列表",
			Module:      "活动报名",
			Action:      "read",
			Resource:    "activity",
			Description: "商家端查看活动列表权限",
		},
		{
			Name:        "merchant:activity:register",
			DisplayName: "活动报名",
			Module:      "活动报名",
			Action:      "register",
			Resource:    "activity",
			Description: "商家端活动报名权限",
		},

		// 等级展示
		{
			Name:        "merchant:ranking:sales",
			DisplayName: "查看销售排名",
			Module:      "等级展示",
			Action:      "read",
			Resource:    "sales_ranking",
			Description: "商家端查看销售排名权限",
		},
		{
			Name:        "merchant:ranking:efficiency",
			DisplayName: "查看坪效排名",
			Module:      "等级展示",
			Action:      "read",
			Resource:    "efficiency_ranking",
			Description: "商家端查看坪效排名权限",
		},
		{
			Name:        "merchant:ranking:level_contribution",
			DisplayName: "查看等级贡献度排名",
			Module:      "等级展示",
			Action:      "read",
			Resource:    "level_contribution_ranking",
			Description: "商家端查看等级贡献度排名权限",
		},

		// 个人中心
		{
			Name:        "merchant:profile:read",
			DisplayName: "查看个人信息",
			Module:      "个人中心",
			Action:      "read",
			Resource:    "profile",
			Description: "商家端查看个人信息权限",
		},
		{
			Name:        "merchant:profile:update",
			DisplayName: "修改个人信息",
			Module:      "个人中心",
			Action:      "update",
			Resource:    "profile",
			Description: "商家端修改个人信息权限",
		},
		{
			Name:        "merchant:auth:logout",
			DisplayName: "退出登录",
			Module:      "个人中心",
			Action:      "logout",
			Resource:    "auth",
			Description: "商家端退出登录权限",
		},
	}

	// 创建商家角色
	merchantRole := models.Role{
		Name:        "merchant",
		DisplayName: "商家用户",
		Description: "商家端用户角色，拥有商家端所有权限",
	}

	// 开始事务
	tx := db.Begin()
	if tx.Error != nil {
		return fmt.Errorf("开始事务失败: %v", tx.Error)
	}
	defer func() {
		if r := recover(); r != nil {
			tx.Rollback()
		}
	}()

	// 检查并创建商家角色
	var existingRole models.Role
	err := tx.Where("name = ?", merchantRole.Name).First(&existingRole).Error
	if err != nil {
		if err == gorm.ErrRecordNotFound {
			// 创建商家角色
			if err := tx.Create(&merchantRole).Error; err != nil {
				tx.Rollback()
				return fmt.Errorf("创建商家角色失败: %v", err)
			}
		} else {
			tx.Rollback()
			return fmt.Errorf("查询商家角色失败: %v", err)
		}
	} else {
		merchantRole = existingRole
	}

	// 批量创建权限（如果不存在）
	for _, permission := range merchantPermissions {
		var existingPermission models.Permission
		err := tx.Where("name = ?", permission.Name).First(&existingPermission).Error
		if err != nil {
			if err == gorm.ErrRecordNotFound {
				// 创建权限
				if err := tx.Create(&permission).Error; err != nil {
					tx.Rollback()
					return fmt.Errorf("创建权限 %s 失败: %v", permission.Name, err)
				}
			} else {
				tx.Rollback()
				return fmt.Errorf("查询权限 %s 失败: %v", permission.Name, err)
			}
		} else {
			permission = existingPermission
		}

		// 为商家角色分配权限
		var existingRolePermission models.RolePermission
		err = tx.Where("role_id = ? AND permission_id = ?", merchantRole.ID, permission.ID).First(&existingRolePermission).Error
		if err != nil {
			if err == gorm.ErrRecordNotFound {
				rolePermission := models.RolePermission{
					RoleID:       merchantRole.ID,
					PermissionID: permission.ID,
				}
				if err := tx.Create(&rolePermission).Error; err != nil {
					tx.Rollback()
					return fmt.Errorf("为商家角色分配权限 %s 失败: %v", permission.Name, err)
				}
			} else {
				tx.Rollback()
				return fmt.Errorf("查询角色权限关联失败: %v", err)
			}
		}
	}

	// 提交事务
	if err := tx.Commit().Error; err != nil {
		return fmt.Errorf("提交事务失败: %v", err)
	}

	return nil
}

// AssignMerchantRoleToUser 为商家用户分配商家角色
func AssignMerchantRoleToUser(db *gorm.DB, adminUserID uint64) error {
	// 查找商家角色
	var merchantRole models.Role
	if err := db.Where("name = ?", "merchant").First(&merchantRole).Error; err != nil {
		return fmt.Errorf("查找商家角色失败: %v", err)
	}

	// 检查是否已经分配了角色
	var existingUserRole models.AdminUserRole
	err := db.Where("admin_user_id = ? AND role_id = ?", adminUserID, merchantRole.ID).First(&existingUserRole).Error
	if err != nil {
		if err == gorm.ErrRecordNotFound {
			// 分配角色
			userRole := models.AdminUserRole{
				AdminUserID: adminUserID,
				RoleID:      merchantRole.ID,
			}
			if err := db.Create(&userRole).Error; err != nil {
				return fmt.Errorf("为用户分配商家角色失败: %v", err)
			}
		} else {
			return fmt.Errorf("查询用户角色关联失败: %v", err)
		}
	}

	return nil
}
