package database

import (
	"fmt"

	"wangfujing_admin/internal/models"

	"gorm.io/gorm"
)

// AutoMigrate 执行数据库自动迁移
func AutoMigrate(db *gorm.DB) error {
	// 自动迁移 - 为每个表单独设置表名注释并执行迁移

	// 基础表（无外键依赖）
	if err := db.Set("gorm:table_options", "COMMENT='普通用户表'").AutoMigrate(&models.User{}); err != nil {
		return fmt.Errorf("迁移用户表失败: %v", err)
	}
	if err := db.Set("gorm:table_options", "COMMENT='管理端用户表'").AutoMigrate(&models.AdminUser{}); err != nil {
		return fmt.Errorf("迁移管理端用户表失败: %v", err)
	}
	if err := db.Set("gorm:table_options", "COMMENT='角色表'").AutoMigrate(&models.Role{}); err != nil {
		return fmt.Errorf("迁移角色表失败: %v", err)
	}
	if err := db.Set("gorm:table_options", "COMMENT='权限表'").AutoMigrate(&models.Permission{}); err != nil {
		return fmt.Errorf("迁移权限表失败: %v", err)
	}
	if err := db.Set("gorm:table_options", "COMMENT='楼层表'").AutoMigrate(&models.Floor{}); err != nil {
		return fmt.Errorf("迁移楼层表失败: %v", err)
	}

	// 关联表
	if err := db.Set("gorm:table_options", "COMMENT='角色权限关联表'").AutoMigrate(&models.RolePermission{}); err != nil {
		return fmt.Errorf("迁移角色权限关联表失败: %v", err)
	}
	if err := db.Set("gorm:table_options", "COMMENT='普通用户角色关联表'").AutoMigrate(&models.UserRole{}); err != nil {
		return fmt.Errorf("迁移普通用户角色关联表失败: %v", err)
	}
	if err := db.Set("gorm:table_options", "COMMENT='管理端用户角色关联表'").AutoMigrate(&models.AdminUserRole{}); err != nil {
		return fmt.Errorf("迁移管理端用户角色关联表失败: %v", err)
	}

	// 业务表（有外键依赖）
	if err := db.Set("gorm:table_options", "COMMENT='商家表'").AutoMigrate(&models.Merchant{}); err != nil {
		return fmt.Errorf("迁移商家表失败: %v", err)
	}
	if err := db.Set("gorm:table_options", "COMMENT='商品表'").AutoMigrate(&models.Product{}); err != nil {
		return fmt.Errorf("迁移商品表失败: %v", err)
	}
	if err := db.Set("gorm:table_options", "COMMENT='订单表'").AutoMigrate(&models.Order{}); err != nil {
		return fmt.Errorf("迁移订单表失败: %v", err)
	}
	if err := db.Set("gorm:table_options", "COMMENT='客诉表'").AutoMigrate(&models.Complaint{}); err != nil {
		return fmt.Errorf("迁移客诉表失败: %v", err)
	}
	if err := db.Set("gorm:table_options", "COMMENT='意见表'").AutoMigrate(&models.Opinion{}); err != nil {
		return fmt.Errorf("迁移意见表失败: %v", err)
	}
	if err := db.Set("gorm:table_options", "COMMENT='活动表'").AutoMigrate(&models.Activity{}); err != nil {
		return fmt.Errorf("迁移活动表失败: %v", err)
	}
	if err := db.Set("gorm:table_options", "COMMENT='节目表'").AutoMigrate(&models.Program{}); err != nil {
		return fmt.Errorf("迁移节目表失败: %v", err)
	}
	if err := db.Set("gorm:table_options", "COMMENT='积分记录表'").AutoMigrate(&models.PointsRecord{}); err != nil {
		return fmt.Errorf("迁移积分记录表失败: %v", err)
	}
	if err := db.Set("gorm:table_options", "COMMENT='核销记录表'").AutoMigrate(&models.VerifyRecord{}); err != nil {
		return fmt.Errorf("迁移核销记录表失败: %v", err)
	}
	if err := db.Set("gorm:table_options", "COMMENT='分值记录表'").AutoMigrate(&models.ScoreRecord{}); err != nil {
		return fmt.Errorf("迁移分值记录表失败: %v", err)
	}
	if err := db.Set("gorm:table_options", "COMMENT='权益申请表'").AutoMigrate(&models.BenefitApplication{}); err != nil {
		return fmt.Errorf("迁移权益申请表失败: %v", err)
	}
	if err := db.Set("gorm:table_options", "COMMENT='等级规则表'").AutoMigrate(&models.LevelRule{}); err != nil {
		return fmt.Errorf("迁移等级规则表失败: %v", err)
	}

	if err := db.Set("gorm:table_options", "COMMENT='分值规则表'").AutoMigrate(&models.ScoreRule{}); err != nil {
		return fmt.Errorf("迁移分值规则表失败: %v", err)
	}
	if err := db.Set("gorm:table_options", "COMMENT='增减分项目表'").AutoMigrate(&models.ScoreItem{}); err != nil {
		return fmt.Errorf("迁移增减分项目表失败: %v", err)
	}
	if err := db.Set("gorm:table_options", "COMMENT='等级权益表'").AutoMigrate(&models.LevelBenefit{}); err != nil {
		return fmt.Errorf("迁移等级权益表失败: %v", err)
	}
	if err := db.Set("gorm:table_options", "COMMENT='操作日志表'").AutoMigrate(&models.OperationLog{}); err != nil {
		return fmt.Errorf("迁移操作日志表失败: %v", err)
	}

	// 营销管理相关表
	if err := db.Set("gorm:table_options", "COMMENT='营销活动表'").AutoMigrate(&models.MarketingActivity{}); err != nil {
		return fmt.Errorf("迁移营销活动表失败: %v", err)
	}
	if err := db.Set("gorm:table_options", "COMMENT='营销活动参与记录表'").AutoMigrate(&models.MarketingActivityParticipant{}); err != nil {
		return fmt.Errorf("迁移营销活动参与记录表失败: %v", err)
	}
	if err := db.Set("gorm:table_options", "COMMENT='任务报名记录表'").AutoMigrate(&models.TaskRegistration{}); err != nil {
		return fmt.Errorf("迁移任务报名记录表失败: %v", err)
	}
	if err := db.Set("gorm:table_options", "COMMENT='商家活动表'").AutoMigrate(&models.MerchantActivity{}); err != nil {
		return fmt.Errorf("迁移商家活动表失败: %v", err)
	}
	if err := db.Set("gorm:table_options", "COMMENT='商家活动参与记录表'").AutoMigrate(&models.MerchantActivityParticipant{}); err != nil {
		return fmt.Errorf("迁移商家活动参与记录表失败: %v", err)
	}
	if err := db.Set("gorm:table_options", "COMMENT='积分商城商品表'").AutoMigrate(&models.PointsMallItem{}); err != nil {
		return fmt.Errorf("迁移积分商城商品表失败: %v", err)
	}
	if err := db.Set("gorm:table_options", "COMMENT='积分商城兑换记录表'").AutoMigrate(&models.PointsMallExchange{}); err != nil {
		return fmt.Errorf("迁移积分商城兑换记录表失败: %v", err)
	}
	if err := db.Set("gorm:table_options", "COMMENT='会员规则表'").AutoMigrate(&models.MembershipRule{}); err != nil {
		return fmt.Errorf("迁移会员规则表失败: %v", err)
	}

	// 初始化管理员权限
	if err := InitAdminPermissions(db); err != nil {
		return fmt.Errorf("初始化管理员权限失败: %v", err)
	}

	// 初始化商家端权限
	if err := InitMerchantPermissions(db); err != nil {
		return fmt.Errorf("初始化商家端权限失败: %v", err)
	}

	return nil
}
