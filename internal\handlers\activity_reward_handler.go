package handlers

import (
	"net/http"
	"strconv"

	"github.com/gin-gonic/gin"
	"gorm.io/gorm"

	"wangfujing_admin/internal/services"
	"wangfujing_admin/pkg/response"
)

// ActivityRewardHandler 活动奖励处理器
type ActivityRewardHandler struct {
	db      *gorm.DB
	service *services.ActivityRewardService
}

// NewActivityRewardHandler 创建活动奖励处理器实例
func NewActivityRewardHandler(db *gorm.DB) *ActivityRewardHandler {
	return &ActivityRewardHandler{
		db:      db,
		service: services.NewActivityRewardService(db),
	}
}

// ProcessExpiredActivities 手动处理过期活动奖励
func (h *ActivityRewardHandler) ProcessExpiredActivities(ctx *gin.Context) {
	processedCount, err := h.service.ManualProcessExpiredActivities(ctx.Request.Context())
	if err != nil {
		response.Error(ctx, http.StatusInternalServerError, "处理过期活动失败: "+err.Error())
		return
	}

	result := map[string]interface{}{
		"processed_count": processedCount,
		"message":         "处理完成",
	}

	response.Success(ctx, result)
}

// GetActivityRewardStatus 获取活动奖励发放状态
func (h *ActivityRewardHandler) GetActivityRewardStatus(ctx *gin.Context) {
	// 获取活动ID
	activityIDStr := ctx.Param("id")
	activityID, err := strconv.ParseUint(activityIDStr, 10, 64)
	if err != nil {
		response.Error(ctx, http.StatusBadRequest, "无效的活动ID")
		return
	}

	status, err := h.service.GetActivityRewardStatus(ctx.Request.Context(), activityID)
	if err != nil {
		response.Error(ctx, http.StatusInternalServerError, "获取活动奖励状态失败: "+err.Error())
		return
	}

	response.Success(ctx, status)
}
