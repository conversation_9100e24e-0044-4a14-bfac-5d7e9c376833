package handlers

import (
	"strconv"
	"wangfujing_admin/pkg/response"

	"github.com/gin-gonic/gin"
)

// AdminGenerateOrderQRCode 管理员生成订单二维码
func (h *Handler) AdminGenerateOrderQRCode(c *gin.Context) {
	orderIDStr := c.Param("order_id")
	orderID, err := strconv.ParseUint(orderIDStr, 10, 64)
	if err != nil {
		response.BadRequest(c, "订单ID格式错误")
		return
	}

	qrCodeURL, err := h.qrCodeService.GenerateOrderQRCode(c.Request.Context(), orderID)
	if err != nil {
		response.InternalServerError(c, "生成二维码失败: "+err.Error())
		return
	}

	response.Success(c, gin.H{
		"order_id":    orderID,
		"qr_code_url": qrCodeURL,
		"message":     "二维码生成成功",
	})
}

// AdminRegenerateOrderQRCode 管理员重新生成订单二维码
func (h *Handler) AdminRegenerateOrderQRCode(c *gin.Context) {
	orderIDStr := c.Param("order_id")
	orderID, err := strconv.ParseUint(orderIDStr, 10, 64)
	if err != nil {
		response.BadRequest(c, "订单ID格式错误")
		return
	}

	qrCodeURL, err := h.qrCodeService.RegenerateQRCode(c.Request.Context(), orderID)
	if err != nil {
		response.InternalServerError(c, "重新生成二维码失败: "+err.Error())
		return
	}

	response.Success(c, gin.H{
		"order_id":    orderID,
		"qr_code_url": qrCodeURL,
		"message":     "二维码重新生成成功",
	})
}

// AdminBatchGenerateQRCodes 管理员批量生成二维码
func (h *Handler) AdminBatchGenerateQRCodes(c *gin.Context) {
	type BatchGenerateRequest struct {
		Limit int `json:"limit" binding:"required,min=1,max=1000"`
	}

	var req BatchGenerateRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		response.BadRequest(c, "参数错误: "+err.Error())
		return
	}

	// 异步执行批量生成
	go func() {
		err := h.qrCodeService.BatchGenerateQRCodes(c.Request.Context(), req.Limit)
		if err != nil {
			// 记录错误日志
			// TODO: 可以考虑使用更完善的日志系统
			println("批量生成二维码失败:", err.Error())
		}
	}()

	response.Success(c, gin.H{
		"message": "批量生成二维码任务已启动",
		"limit":   req.Limit,
	})
}

// AdminGetQRCodeStats 管理员获取二维码生成统计
func (h *Handler) AdminGetQRCodeStats(c *gin.Context) {
	stats, err := h.qrCodeService.GetQRCodeStats(c.Request.Context())
	if err != nil {
		response.InternalServerError(c, "获取统计信息失败: "+err.Error())
		return
	}

	response.Success(c, stats)
}

// AdminGetOrdersWithoutQRCode 获取没有二维码的订单列表
func (h *Handler) AdminGetOrdersWithoutQRCode(c *gin.Context) {
	// 获取分页参数
	page, _ := strconv.Atoi(c.DefaultQuery("page", "1"))
	size, _ := strconv.Atoi(c.DefaultQuery("size", "20"))
	if page < 1 {
		page = 1
	}
	if size < 1 || size > 100 {
		size = 20
	}

	offset := (page - 1) * size

	// 查询没有二维码URL的订单
	var orders []struct {
		ID        uint64 `json:"id"`
		OrderNo   string `json:"order_no"`
		UserID    uint64 `json:"user_id"`
		ProductID uint64 `json:"product_id"`
		Status    int    `json:"status"`
		CreatedAt string `json:"created_at"`
	}

	var total int64

	// 统计总数
	if err := h.db.Table("orders").
		Where("qr_code_url = '' OR qr_code_url IS NULL").
		Count(&total).Error; err != nil {
		response.InternalServerError(c, "查询订单总数失败")
		return
	}

	// 查询订单列表
	if err := h.db.Table("orders").
		Select("id, order_no, user_id, product_id, status, created_at").
		Where("qr_code_url = '' OR qr_code_url IS NULL").
		Order("created_at DESC").
		Limit(size).
		Offset(offset).
		Find(&orders).Error; err != nil {
		response.InternalServerError(c, "查询订单列表失败")
		return
	}

	response.Success(c, gin.H{
		"orders": orders,
		"pagination": gin.H{
			"page":  page,
			"size":  size,
			"total": total,
		},
	})
}

// AdminGetOrdersWithQRCode 获取已有二维码的订单列表
func (h *Handler) AdminGetOrdersWithQRCode(c *gin.Context) {
	// 获取分页参数
	page, _ := strconv.Atoi(c.DefaultQuery("page", "1"))
	size, _ := strconv.Atoi(c.DefaultQuery("size", "20"))
	if page < 1 {
		page = 1
	}
	if size < 1 || size > 100 {
		size = 20
	}

	offset := (page - 1) * size

	// 查询有二维码URL的订单
	var orders []struct {
		ID        uint64 `json:"id"`
		OrderNo   string `json:"order_no"`
		UserID    uint64 `json:"user_id"`
		ProductID uint64 `json:"product_id"`
		Status    int    `json:"status"`
		QRCodeURL string `json:"qr_code_url"`
		CreatedAt string `json:"created_at"`
	}

	var total int64

	// 统计总数
	if err := h.db.Table("orders").
		Where("qr_code_url != '' AND qr_code_url IS NOT NULL").
		Count(&total).Error; err != nil {
		response.InternalServerError(c, "查询订单总数失败")
		return
	}

	// 查询订单列表
	if err := h.db.Table("orders").
		Select("id, order_no, user_id, product_id, status, qr_code_url, created_at").
		Where("qr_code_url != '' AND qr_code_url IS NOT NULL").
		Order("created_at DESC").
		Limit(size).
		Offset(offset).
		Find(&orders).Error; err != nil {
		response.InternalServerError(c, "查询订单列表失败")
		return
	}

	response.Success(c, gin.H{
		"orders": orders,
		"pagination": gin.H{
			"page":  page,
			"size":  size,
			"total": total,
		},
	})
}
