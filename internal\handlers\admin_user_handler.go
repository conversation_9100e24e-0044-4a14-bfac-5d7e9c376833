package handlers

import (
	"strconv"

	"wangfujing_admin/internal/database"
	"wangfujing_admin/internal/models"
	"wangfujing_admin/pkg/logger"
	"wangfujing_admin/pkg/response"

	"github.com/gin-gonic/gin"
)

// CreateAdminUserRequest 创建管理员用户请求
type CreateAdminUserRequest struct {
	Phone      string `json:"phone" binding:"required"`
	Nickname   string `json:"nickname"`
	Avatar     string `json:"avatar"`
	UserType   int    `json:"user_type" binding:"required"` // 1:普通管理员 2:商家用户 3:超级管理员
	Status     int    `json:"status"`
	MerchantID *int   `json:"merchant_id,omitempty"`       // 商家用户需要关联商家ID，使用指针类型
	RoleIDs    []int  `json:"role_ids" binding:"required"` // 角色ID列表，必填
}

// UpdateAdminUserRequest 更新管理员用户请求
type UpdateAdminUserRequest struct {
	Nickname   string `json:"nickname"`
	Avatar     string `json:"avatar"`
	Status     int    `json:"status"`
	MerchantID *int   `json:"merchant_id,omitempty"` // 使用指针类型
}

// AssignAdminUserRolesRequest 分配管理员用户角色请求
type AssignAdminUserRolesRequest struct {
	RoleIDs []int `json:"role_ids" binding:"required"`
}

// UpdateAdminUserStatusRequest 更新管理员用户状态请求
type UpdateAdminUserStatusRequest struct {
	Status *int `json:"status" binding:"required"` // 使用指针类型，避免零值验证问题
}

// AdminUserWithRoles 带角色信息的管理员用户
type AdminUserWithRoles struct {
	models.AdminUser
	RoleNames []string `json:"role_names"` // 角色名称列表
}

// UpdateAdminUserRoleRequest 更新管理员用户角色请求
type UpdateAdminUserRoleRequest struct {
	RoleIDs []int `json:"role_ids" binding:"required"` // 新的角色ID列表
}

// GetAdminUsers 获取管理员用户列表
func (h *Handler) GetAdminUsers(c *gin.Context) {
	page, _ := strconv.Atoi(c.DefaultQuery("page", "1"))
	size, _ := strconv.Atoi(c.DefaultQuery("size", "10"))

	var userType *int
	if userTypeStr := c.Query("user_type"); userTypeStr != "" {
		if ut, err := strconv.Atoi(userTypeStr); err == nil {
			userType = &ut
		}
	}

	var status *int
	if statusStr := c.Query("status"); statusStr != "" {
		if s, err := strconv.Atoi(statusStr); err == nil {
			status = &s
		}
	}

	// 构建查询
	query := h.db.Model(&models.AdminUser{})

	if userType != nil {
		query = query.Where("user_type = ?", *userType)
	}
	if status != nil {
		query = query.Where("status = ?", *status)
	}

	// 获取总数
	var total int64
	if err := query.Count(&total).Error; err != nil {
		response.InternalServerError(c, "获取用户总数失败")
		return
	}

	// 获取分页数据
	var adminUsers []models.AdminUser
	offset := (page - 1) * size
	if err := query.Offset(offset).Limit(size).Order("created_at ASC").Find(&adminUsers).Error; err != nil {
		response.InternalServerError(c, "获取管理员用户列表失败")
		return
	}

	// 为每个用户获取角色信息
	var usersWithRoles []AdminUserWithRoles
	for _, user := range adminUsers {
		userWithRoles := AdminUserWithRoles{
			AdminUser: user,
			RoleNames: []string{},
		}

		// 先检查用户是否有角色分配
		var roleCount int64
		h.db.Table("admin_user_roles").Where("admin_user_id = ?", user.ID).Count(&roleCount)

		// 获取用户的角色名称 - 使用子查询避免字符集冲突
		var roleNames []string
		if roleCount > 0 {
			err := h.db.Table("roles").
				Select("roles.display_name").
				Where("roles.id IN (SELECT role_id FROM admin_user_roles WHERE admin_user_id = ?)", user.ID).
				Pluck("display_name", &roleNames).Error

			if err != nil {
				// 记录角色查询错误，但不中断流程
				logger.Error("获取用户角色名称失败",
					logger.String("user_id", strconv.FormatUint(user.ID, 10)),
					logger.String("error", err.Error()),
				)
				roleNames = []string{}
			}
		} else {
			// 用户没有分配角色
			logger.Info("用户未分配角色",
				logger.String("user_id", strconv.FormatUint(user.ID, 10)),
				logger.String("phone", user.Phone),
			)
			roleNames = []string{}
		}

		userWithRoles.RoleNames = roleNames

		usersWithRoles = append(usersWithRoles, userWithRoles)
	}

	response.Success(c, gin.H{
		"users": usersWithRoles,
		"total": total,
		"page":  page,
		"size":  size,
	})
}

// GetAdminUser 获取管理员用户详情
func (h *Handler) GetAdminUser(c *gin.Context) {
	id, err := strconv.Atoi(c.Param("id"))
	if err != nil {
		response.BadRequest(c, "Invalid user ID")
		return
	}

	var adminUser models.AdminUser
	if err := h.db.First(&adminUser, "id = ?", id).Error; err != nil {
		logger.Error("获取管理员用户详情失败：用户不存在",
			logger.String("user_id", strconv.Itoa(id)),
			logger.String("error", err.Error()),
			logger.String("client_ip", c.ClientIP()),
		)
		response.NotFound(c, "管理员用户不存在")
		return
	}

	// 获取用户角色信息 - 使用子查询避免字符集冲突
	var roles []models.Role
	err = h.db.Table("roles").
		Where("id IN (SELECT role_id FROM admin_user_roles WHERE admin_user_id = ?)", id).
		Find(&roles).Error

	if err != nil {
		logger.Error("获取用户角色失败",
			logger.String("user_id", strconv.Itoa(id)),
			logger.String("error", err.Error()),
		)
		// 不中断流程，只是没有角色信息
		roles = []models.Role{}
	}

	// 构建响应数据
	userDetail := AdminUserWithRoles{
		AdminUser: adminUser,
		RoleNames: make([]string, len(roles)),
	}

	// 提取角色名称
	for i, role := range roles {
		userDetail.RoleNames[i] = role.DisplayName
	}

	// 添加完整的角色信息到响应中
	response.Success(c, gin.H{
		"user":  userDetail,
		"roles": roles, // 完整的角色信息，用于编辑时的下拉选择
	})
}

// CreateAdminUser 创建管理员用户
func (h *Handler) CreateAdminUser(c *gin.Context) {
	var req CreateAdminUserRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		response.BadRequest(c, "参数错误: "+err.Error())
		return
	}

	// 验证用户类型
	if req.UserType != int(models.UserTypeAdmin) && req.UserType != int(models.UserTypeMerchant) && req.UserType != int(models.UserTypeSuperAdmin) {
		logger.Error("创建管理员用户失败：用户类型无效",
			logger.String("phone", req.Phone),
			logger.Int("user_type", req.UserType),
			logger.String("client_ip", c.ClientIP()),
		)
		response.BadRequest(c, "用户类型必须是1(普通管理员)、2(商家用户)或3(超级管理员)")
		return
	}

	// 验证商家用户必须提供商家ID
	if req.UserType == int(models.UserTypeMerchant) && (req.MerchantID == nil || *req.MerchantID <= 0) {
		logger.Error("创建管理员用户失败：商家用户必须提供商家ID",
			logger.String("phone", req.Phone),
			logger.String("client_ip", c.ClientIP()),
		)
		response.BadRequest(c, "商家用户必须提供商家ID")
		return
	}

	// 验证角色ID不能为空
	if len(req.RoleIDs) == 0 {
		logger.Error("创建管理员用户失败：必须分配至少一个角色",
			logger.String("phone", req.Phone),
			logger.String("client_ip", c.ClientIP()),
		)
		response.BadRequest(c, "必须分配至少一个角色")
		return
	}

	// 检查手机号是否已存在
	var existingUser models.AdminUser
	if err := h.db.Where("phone = ?", req.Phone).First(&existingUser).Error; err == nil {
		logger.Error("创建管理员用户失败：手机号已存在",
			logger.String("phone", req.Phone),
			logger.String("existing_user_id", strconv.FormatUint(existingUser.ID, 10)),
			logger.String("client_ip", c.ClientIP()),
		)
		response.BadRequest(c, "手机号已存在")
		return
	}

	adminUser := &models.AdminUser{
		Phone:    req.Phone,
		Nickname: req.Nickname,
		Avatar:   req.Avatar,
		UserType: models.UserType(req.UserType),
		Status:   models.Status(req.Status),
	}

	// 设置商家ID（如果是商家用户）
	if req.UserType == int(models.UserTypeMerchant) && req.MerchantID != nil && *req.MerchantID > 0 {
		// 转换int为uint64
		merchantIDUint := uint64(*req.MerchantID)
		adminUser.MerchantID = &merchantIDUint
	}

	if adminUser.Status == 0 {
		adminUser.Status = 1 // 默认启用
	}

	// ID由数据库自动生成，无需手动设置

	if err := h.db.Create(adminUser).Error; err != nil {
		logger.Error("创建管理员用户失败：数据库创建失败",
			logger.String("phone", req.Phone),
			logger.String("user_type", strconv.Itoa(req.UserType)),
			logger.String("error", err.Error()),
			logger.String("client_ip", c.ClientIP()),
		)
		response.InternalServerError(c, "创建管理员用户失败")
		return
	}

	// 分配角色 - 直接操作关联表
	if len(req.RoleIDs) > 0 {
		for _, roleID := range req.RoleIDs {
			// 验证角色ID有效性
			if roleID <= 0 {
				response.BadRequest(c, "Invalid role ID")
				return
			}

			// 转换int为uint64
			roleIDUint := uint64(roleID)

			userRole := &models.AdminUserRole{
				AdminUserID: adminUser.ID,
				RoleID:      roleIDUint,
			}
			h.db.Create(userRole)
		}
	}

	// 如果是商家用户（user_type=2），自动分配商家角色
	if req.UserType == int(models.UserTypeMerchant) {
		// 导入database包的函数
		if err := database.AssignMerchantRoleToUser(h.db, adminUser.ID); err != nil {
			logger.Error("为商家用户分配商家角色失败",
				logger.String("user_id", strconv.FormatUint(adminUser.ID, 10)),
				logger.String("phone", adminUser.Phone),
				logger.String("error", err.Error()),
			)
			// 不中断流程，只记录错误
		} else {
			logger.Info("为商家用户自动分配商家角色成功",
				logger.String("user_id", strconv.FormatUint(adminUser.ID, 10)),
				logger.String("phone", adminUser.Phone),
			)
		}
	}

	// 记录成功日志
	logger.Info("管理员用户创建成功",
		logger.String("user_id", strconv.FormatUint(adminUser.ID, 10)),
		logger.String("phone", adminUser.Phone),
		logger.String("user_type", strconv.Itoa(int(adminUser.UserType))),
		logger.String("operator", c.GetString("user_id")),
		logger.String("client_ip", c.ClientIP()),
	)

	response.Success(c, gin.H{
		"message": "管理员用户创建成功",
		"user":    adminUser,
	})
}

// UpdateAdminUser 更新管理员用户
func (h *Handler) UpdateAdminUser(c *gin.Context) {
	id := c.Param("id")

	var req UpdateAdminUserRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		response.BadRequest(c, "参数错误: "+err.Error())
		return
	}

	// 检查用户是否存在
	var adminUser models.AdminUser
	if err := h.db.First(&adminUser, "id = ?", id).Error; err != nil {
		response.NotFound(c, "管理员用户不存在")
		return
	}

	// 构建更新数据
	updates := make(map[string]interface{})
	if req.Nickname != "" {
		updates["nickname"] = req.Nickname
	}
	if req.Avatar != "" {
		updates["avatar"] = req.Avatar
	}
	if req.Status != 0 {
		updates["status"] = req.Status
	}
	if req.MerchantID != nil && *req.MerchantID > 0 {
		merchantIDUint := uint64(*req.MerchantID)
		updates["merchant_id"] = &merchantIDUint
	}

	if len(updates) > 0 {
		if err := h.db.Model(&adminUser).Updates(updates).Error; err != nil {
			response.InternalServerError(c, "更新管理员用户失败")
			return
		}
	}

	response.Success(c, gin.H{
		"message": "管理员用户更新成功",
	})
}

// DeleteAdminUser 删除管理员用户
func (h *Handler) DeleteAdminUser(c *gin.Context) {
	id := c.Param("id")

	// 检查是否为当前用户
	currentUserID, exists := c.Get("user_id")
	if exists && id == currentUserID.(string) {
		response.BadRequest(c, "不能删除自己")
		return
	}

	// 检查用户是否存在
	var adminUser models.AdminUser
	if err := h.db.First(&adminUser, "id = ?", id).Error; err != nil {
		logger.Error("删除管理员用户失败：用户不存在",
			logger.String("user_id", id),
			logger.String("error", err.Error()),
			logger.String("client_ip", c.ClientIP()),
		)
		response.NotFound(c, "管理员用户不存在")
		return
	}

	// 只能删除停用状态的用户
	if adminUser.Status != 0 {
		logger.Error("删除管理员用户失败：只能删除停用状态的用户",
			logger.String("user_id", id),
			logger.Int("current_status", int(adminUser.Status)),
			logger.String("client_ip", c.ClientIP()),
		)
		response.BadRequest(c, "只能删除停用状态的用户")
		return
	}

	// 软删除用户
	if err := h.db.Delete(&adminUser).Error; err != nil {
		logger.Error("删除管理员用户失败：数据库删除失败",
			logger.String("user_id", id),
			logger.String("error", err.Error()),
			logger.String("client_ip", c.ClientIP()),
		)
		response.InternalServerError(c, "删除管理员用户失败")
		return
	}

	// 删除用户角色关联
	h.db.Where("admin_user_id = ?", id).Delete(&models.AdminUserRole{})

	// 记录成功日志
	logger.Info("管理员用户删除成功",
		logger.String("user_id", id),
		logger.String("phone", adminUser.Phone),
		logger.String("operator", c.GetString("user_id")),
		logger.String("client_ip", c.ClientIP()),
	)

	response.Success(c, gin.H{
		"message": "管理员用户删除成功",
	})
}

// AssignUserRoles 分配用户角色
func (h *Handler) AssignUserRoles(c *gin.Context) {
	id, err := strconv.Atoi(c.Param("id"))
	if err != nil {
		response.BadRequest(c, "Invalid user ID")
		return
	}

	var req AssignAdminUserRolesRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		response.BadRequest(c, "参数错误: "+err.Error())
		return
	}

	// 检查用户是否存在
	var adminUser models.AdminUser
	if err := h.db.First(&adminUser, "id = ?", id).Error; err != nil {
		response.NotFound(c, "管理员用户不存在")
		return
	}

	// 先删除现有角色
	h.db.Where("admin_user_id = ?", id).Delete(&models.AdminUserRole{})

	// 分配新角色 - 直接操作关联表
	for _, roleID := range req.RoleIDs {
		// 验证角色ID有效性
		if roleID <= 0 {
			response.BadRequest(c, "Invalid role ID")
			return
		}

		userRole := &models.AdminUserRole{
			AdminUserID: uint64(id),
			RoleID:      uint64(roleID),
		}
		if err := h.db.Create(userRole).Error; err != nil {
			response.InternalServerError(c, "分配角色失败")
			return
		}
	}

	response.Success(c, gin.H{
		"message": "角色分配成功",
	})
}

// UpdateUserStatus 更新用户状态
func (h *Handler) UpdateUserStatus(c *gin.Context) {
	id := c.Param("id")

	var req UpdateAdminUserStatusRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		logger.Error("更新用户状态参数绑定失败",
			logger.String("user_id", id),
			logger.String("error", err.Error()),
			logger.String("client_ip", c.ClientIP()),
		)
		response.BadRequest(c, "参数错误: "+err.Error())
		return
	}

	// 验证状态值
	if req.Status == nil {
		logger.Error("更新用户状态失败：状态值为空",
			logger.String("user_id", id),
			logger.String("client_ip", c.ClientIP()),
		)
		response.BadRequest(c, "状态值不能为空")
		return
	}

	if *req.Status < 0 || *req.Status > 1 {
		logger.Error("更新用户状态失败：状态值无效",
			logger.String("user_id", id),
			logger.Int("status", *req.Status),
			logger.String("client_ip", c.ClientIP()),
		)
		response.BadRequest(c, "状态值必须是0(禁用)或1(启用)")
		return
	}

	// 检查用户是否存在
	var adminUser models.AdminUser
	if err := h.db.First(&adminUser, "id = ?", id).Error; err != nil {
		logger.Error("更新用户状态失败：用户不存在",
			logger.String("user_id", id),
			logger.String("error", err.Error()),
			logger.String("client_ip", c.ClientIP()),
		)
		response.NotFound(c, "管理员用户不存在")
		return
	}

	// 验证状态变更规则
	currentStatus := int(adminUser.Status)
	newStatus := *req.Status

	// 启用状态(1)下只能停用(0)
	if currentStatus == 1 && newStatus != 0 {
		logger.Error("更新用户状态失败：启用状态下只能停用",
			logger.String("user_id", id),
			logger.Int("current_status", currentStatus),
			logger.Int("new_status", newStatus),
			logger.String("client_ip", c.ClientIP()),
		)
		response.BadRequest(c, "启用状态下只能停用")
		return
	}

	// 停用状态(0)下可以启用(1)，但不能重复设置为停用
	if currentStatus == 0 && newStatus == 0 {
		logger.Error("更新用户状态失败：用户已经是停用状态",
			logger.String("user_id", id),
			logger.String("client_ip", c.ClientIP()),
		)
		response.BadRequest(c, "用户已经是停用状态")
		return
	}

	// 更新状态
	if err := h.db.Model(&adminUser).Update("status", *req.Status).Error; err != nil {
		logger.Error("更新用户状态失败：数据库更新失败",
			logger.String("user_id", id),
			logger.Int("new_status", *req.Status),
			logger.String("error", err.Error()),
			logger.String("client_ip", c.ClientIP()),
		)
		response.InternalServerError(c, "更新用户状态失败")
		return
	}

	// 记录成功日志
	logger.Info("用户状态更新成功",
		logger.String("user_id", id),
		logger.Int("old_status", int(adminUser.Status)),
		logger.Int("new_status", *req.Status),
		logger.String("operator", c.GetString("user_id")),
		logger.String("client_ip", c.ClientIP()),
	)

	response.Success(c, gin.H{
		"message": "用户状态更新成功",
	})
}

// UpdateAdminUserRole 更新管理员用户角色（仅限停用状态）
func (h *Handler) UpdateAdminUserRole(c *gin.Context) {
	id, err := strconv.Atoi(c.Param("id"))
	if err != nil {
		response.BadRequest(c, "Invalid user ID")
		return
	}

	var req UpdateAdminUserRoleRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		logger.Error("更新用户角色参数绑定失败",
			logger.String("user_id", strconv.Itoa(id)),
			logger.String("error", err.Error()),
			logger.String("client_ip", c.ClientIP()),
		)
		response.BadRequest(c, "参数错误: "+err.Error())
		return
	}

	// 检查用户是否存在
	var adminUser models.AdminUser
	if err := h.db.First(&adminUser, "id = ?", id).Error; err != nil {
		logger.Error("更新用户角色失败：用户不存在",
			logger.String("user_id", strconv.Itoa(id)),
			logger.String("error", err.Error()),
			logger.String("client_ip", c.ClientIP()),
		)
		response.NotFound(c, "管理员用户不存在")
		return
	}

	// 检查用户状态：只有停用状态才能更换角色
	if adminUser.Status != 0 {
		logger.Error("更新用户角色失败：用户状态不允许",
			logger.String("user_id", strconv.Itoa(id)),
			logger.Int("current_status", int(adminUser.Status)),
			logger.String("client_ip", c.ClientIP()),
		)
		response.BadRequest(c, "只有停用状态的用户才能更换角色")
		return
	}

	// 验证角色是否存在
	for _, roleID := range req.RoleIDs {
		if roleID <= 0 {
			response.BadRequest(c, "Invalid role ID: "+strconv.Itoa(roleID))
			return
		}

		var role models.Role
		if err := h.db.First(&role, "id = ?", roleID).Error; err != nil {
			logger.Error("更新用户角色失败：角色不存在",
				logger.String("user_id", strconv.Itoa(id)),
				logger.String("role_id", strconv.Itoa(roleID)),
				logger.String("error", err.Error()),
				logger.String("client_ip", c.ClientIP()),
			)
			response.BadRequest(c, "角色不存在: "+strconv.Itoa(roleID))
			return
		}
	}

	// 先删除现有角色
	if err := h.db.Where("admin_user_id = ?", id).Delete(&models.AdminUserRole{}).Error; err != nil {
		logger.Error("更新用户角色失败：删除现有角色失败",
			logger.String("user_id", strconv.Itoa(id)),
			logger.String("error", err.Error()),
			logger.String("client_ip", c.ClientIP()),
		)
		response.InternalServerError(c, "更新角色失败")
		return
	}

	// 分配新角色
	for _, roleID := range req.RoleIDs {
		if roleID <= 0 {
			response.BadRequest(c, "Invalid role ID")
			return
		}

		// 转换int为uint64
		roleIDUint := uint64(roleID)

		userRole := &models.AdminUserRole{
			AdminUserID: uint64(id),
			RoleID:      roleIDUint,
		}
		if err := h.db.Create(userRole).Error; err != nil {
			logger.Error("更新用户角色失败：分配新角色失败",
				logger.String("user_id", strconv.Itoa(id)),
				logger.String("role_id", strconv.Itoa(roleID)),
				logger.String("error", err.Error()),
				logger.String("client_ip", c.ClientIP()),
			)
			response.InternalServerError(c, "分配角色失败")
			return
		}
	}

	// 记录成功日志
	logger.Info("用户角色更新成功",
		logger.String("user_id", strconv.Itoa(id)),
		logger.Any("new_role_ids", req.RoleIDs),
		logger.String("operator", c.GetString("user_id")),
		logger.String("client_ip", c.ClientIP()),
	)

	response.Success(c, gin.H{
		"message": "用户角色更新成功",
	})
}
