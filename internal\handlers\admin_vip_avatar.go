package handlers

import (
	"time"

	"github.com/gin-gonic/gin"

	"wangfujing_admin/internal/models"
	"wangfujing_admin/internal/services"
	"wangfujing_admin/pkg/response"
)

// GetVipAvatarStats 获取VIP形象统计信息
func (h *Handler) GetVipAvatarStats(c *gin.Context) {
	// 统计各状态的VIP形象数量
	var stats struct {
		Redeemed    int64 `json:"redeemed"`    // 已兑换
		InUse       int64 `json:"in_use"`      // 正在使用
		Expired     int64 `json:"expired"`     // 已过期
		Deactivated int64 `json:"deactivated"` // 已停用
		Total       int64 `json:"total"`       // 总数
	}

	// 只统计中奖的记录
	baseQuery := h.db.Model(&models.PointsMallExchange{}).Where("lottery_win = ?", true)

	baseQuery.Where("status = ?", models.ExchangeStatusRedeemed).Count(&stats.Redeemed)
	baseQuery.Where("status = ?", models.ExchangeStatusInUse).Count(&stats.InUse)
	baseQuery.Where("status = ?", models.ExchangeStatusExpired).Count(&stats.Expired)
	baseQuery.Where("status = ?", models.ExchangeStatusDeactivated).Count(&stats.Deactivated)
	baseQuery.Count(&stats.Total)

	// 统计今天的数据
	now := time.Now()
	today := time.Date(now.Year(), now.Month(), now.Day(), 0, 0, 0, 0, now.Location())
	todayStr := today.Format("2006-01-02")

	var todayStats struct {
		TodayRedeemed int64 `json:"today_redeemed"` // 今天兑换的
		TodayInUse    int64 `json:"today_in_use"`   // 今天正在使用的
		TodayExpired  int64 `json:"today_expired"`  // 今天过期的
	}

	baseQuery.Where("status = ? AND DATE(exchange_time) = ?", models.ExchangeStatusRedeemed, todayStr).Count(&todayStats.TodayRedeemed)
	baseQuery.Where("status = ? AND DATE(exchange_time) = ?", models.ExchangeStatusInUse, todayStr).Count(&todayStats.TodayInUse)
	baseQuery.Where("status = ? AND DATE(updated_at) = ?", models.ExchangeStatusExpired, todayStr).Count(&todayStats.TodayExpired)

	// 统计需要过期的数据（0点前兑换或应用的）
	var needExpiry struct {
		NeedExpireRedeemed int64 `json:"need_expire_redeemed"` // 需要过期的已兑换
		NeedExpireInUse    int64 `json:"need_expire_in_use"`   // 需要过期的正在使用
	}

	baseQuery.Where("status = ? AND DATE(exchange_time) < ?", models.ExchangeStatusRedeemed, todayStr).Count(&needExpiry.NeedExpireRedeemed)
	baseQuery.Where("status = ? AND (DATE(exchange_time) < ? OR (applied_at IS NOT NULL AND DATE(applied_at) < ?))",
		models.ExchangeStatusInUse, todayStr, todayStr).Count(&needExpiry.NeedExpireInUse)

	response.Success(c, gin.H{
		"stats":        stats,
		"today_stats":  todayStats,
		"need_expiry":  needExpiry,
		"current_time": now.Format("2006-01-02 15:04:05"),
		"today_date":   todayStr,
	})
}

// ProcessVipAvatarExpiry 手动触发VIP形象过期处理
func (h *Handler) ProcessVipAvatarExpiry(c *gin.Context) {
	// 创建VIP形象过期服务
	expiryService := services.NewVipAvatarExpiryService(h.db)

	// 手动触发过期处理
	expiryService.ProcessExpiredAvatarsManually()

	response.Success(c, gin.H{
		"message":      "VIP形象过期处理已完成",
		"processed_at": time.Now().Format("2006-01-02 15:04:05"),
	})
}

// GetVipAvatarExpirySchedule 获取VIP形象过期处理计划
func (h *Handler) GetVipAvatarExpirySchedule(c *gin.Context) {
	// 计算下次0点的时间
	now := time.Now()
	tomorrow := now.AddDate(0, 0, 1)
	nextMidnight := time.Date(tomorrow.Year(), tomorrow.Month(), tomorrow.Day(), 0, 0, 0, 0, now.Location())

	response.Success(c, gin.H{
		"schedule":            "每天 00:00:00 执行",
		"timezone":            "Asia/Shanghai",
		"current_time":        now.Format("2006-01-02 15:04:05"),
		"next_run_time":       nextMidnight.Format("2006-01-02 15:04:05"),
		"time_until_next_run": nextMidnight.Sub(now).String(),
	})
}

// GetVipAvatarExpiryLogs 获取VIP形象过期处理日志（最近的记录）
func (h *Handler) GetVipAvatarExpiryLogs(c *gin.Context) {
	// 查询最近过期的记录
	var recentExpired []struct {
		ID           uint64     `json:"id"`
		UserID       uint64     `json:"user_id"`
		ItemID       uint64     `json:"item_id"`
		ExchangeTime time.Time  `json:"exchange_time"`
		AppliedAt    *time.Time `json:"applied_at"`
		UpdatedAt    time.Time  `json:"updated_at"`
		Status       int        `json:"status"`
		StatusText   string     `json:"status_text"`
	}

	// 查询今天更新为过期状态的记录
	today := time.Now().Format("2006-01-02")

	err := h.db.Model(&models.PointsMallExchange{}).
		Select("id, user_id, item_id, exchange_time, applied_at, updated_at, status").
		Where("status = ? AND DATE(updated_at) = ? AND lottery_win = ?",
			models.ExchangeStatusExpired, today, true).
		Order("updated_at DESC").
		Limit(50).
		Find(&recentExpired).Error

	if err != nil {
		response.InternalServerError(c, "查询过期日志失败")
		return
	}

	// 添加状态文本
	for i := range recentExpired {
		recentExpired[i].StatusText = "已过期"
	}

	// 统计今天的过期处理情况
	var expiredCount int64
	h.db.Model(&models.PointsMallExchange{}).
		Where("status = ? AND DATE(updated_at) = ? AND lottery_win = ?",
			models.ExchangeStatusExpired, today, true).
		Count(&expiredCount)

	response.Success(c, gin.H{
		"recent_expired":      recentExpired,
		"today_expired_count": expiredCount,
		"query_date":          today,
	})
}

// GetVipAvatarUserStats 获取用户VIP形象使用统计
func (h *Handler) GetVipAvatarUserStats(c *gin.Context) {
	// 统计每个用户的VIP形象使用情况
	var userStats []struct {
		UserID           uint64     `json:"user_id"`
		Nickname         string     `json:"nickname"`
		TotalCount       int64      `json:"total_count"`        // 总兑换次数
		InUseCount       int64      `json:"in_use_count"`       // 正在使用数量
		ExpiredCount     int64      `json:"expired_count"`      // 已过期数量
		LastExchangeTime *time.Time `json:"last_exchange_time"` // 最后兑换时间
	}

	// 查询用户统计（只查询有VIP形象记录的用户）
	err := h.db.Raw(`
		SELECT 
			e.user_id,
			u.nickname,
			COUNT(*) as total_count,
			SUM(CASE WHEN e.status = ? THEN 1 ELSE 0 END) as in_use_count,
			SUM(CASE WHEN e.status = ? THEN 1 ELSE 0 END) as expired_count,
			MAX(e.exchange_time) as last_exchange_time
		FROM points_mall_exchanges e
		LEFT JOIN users u ON e.user_id = u.id
		WHERE e.lottery_win = 1
		GROUP BY e.user_id, u.nickname
		ORDER BY total_count DESC, last_exchange_time DESC
		LIMIT 100
	`, models.ExchangeStatusInUse, models.ExchangeStatusExpired).Scan(&userStats).Error

	if err != nil {
		response.InternalServerError(c, "查询用户统计失败")
		return
	}

	response.Success(c, gin.H{
		"user_stats":  userStats,
		"total_users": len(userStats),
	})
}
