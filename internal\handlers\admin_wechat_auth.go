package handlers

import (
	"context"
	"strconv"
	"time"
	"wangfujing_admin/internal/database"
	"wangfujing_admin/internal/middleware"
	"wangfujing_admin/internal/models"
	"wangfujing_admin/pkg/response"

	"github.com/gin-gonic/gin"
	"gorm.io/gorm"
)

// AdminWechatLoginRequest 管理端微信小程序登录请求
type AdminWechatLoginRequest struct {
	LoginCode string `json:"login_code" binding:"required"` // 微信登录凭证code
	PhoneCode string `json:"phone_code" binding:"required"` // 获取手机号的code（管理端必须）
}

// AdminWechatRefreshTokenRequest 管理端刷新令牌请求
type AdminWechatRefreshTokenRequest struct {
	RefreshToken string `json:"refresh_token" binding:"required"`
}

// AdminUpdateUserInfoRequest 管理端更新用户信息请求
type AdminUpdateUserInfoRequest struct {
	Nickname string `json:"nickname"`
	Avatar   string `json:"avatar"`
	Gender   int    `json:"gender"`
}

// AdminGetPhoneRequest 管理端获取手机号请求
type AdminGetPhoneRequest struct {
	Code string `json:"code" binding:"required"` // 获取手机号的code
}

// AdminWechatLogin 管理端微信小程序登录
func (h *Handler) AdminWechatLogin(c *gin.Context) {
	var req AdminWechatLoginRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		response.ValidateError(c, err)
		return
	}

	// 1. 调用微信接口获取openid和session_key（使用管理端配置）
	sessionResp, err := h.wechatManager.GetMerchantOpenID(req.LoginCode)
	if err != nil {
		// 记录管理端微信API调用失败日志
		c.Header("X-Request-ID", c.GetString("request_id"))
		response.InternalServerError(c, "管理端微信登录失败: "+err.Error())
		return
	}

	if sessionResp.ErrCode != 0 {
		// 记录管理端微信API业务错误日志
		c.Header("X-Request-ID", c.GetString("request_id"))
		response.BadRequest(c, "管理端微信登录失败: "+sessionResp.ErrMsg)
		return
	}

	openID := sessionResp.OpenID
	unionID := sessionResp.UnionID

	// 2. 获取微信手机号
	accessToken, err := h.wechatManager.GetMerchantAccessToken()
	if err != nil {
		middleware.LogWeChatAPIError(c, "GetMerchantAccessToken", err, "", "")
		response.InternalServerError(c, "获取微信访问令牌失败")
		return
	}

	phoneInfo, err := h.wechatManager.GetPhoneNumber(accessToken.AccessToken, req.PhoneCode)
	if err != nil {
		middleware.LogWeChatAPIError(c, "GetPhoneNumber", err, req.PhoneCode[:10]+"...", "")
		response.InternalServerError(c, "获取手机号失败")
		return
	}
	phone := phoneInfo.PhoneNumber

	// 移除调试日志

	// 3. 查询管理端用户（通过手机号）
	var adminUser models.AdminUser

	// 移除调试查询

	result := h.db.Where("phone = ?", phone).First(&adminUser)

	if result.Error != nil {
		if result.Error == gorm.ErrRecordNotFound {
			// 查找不到admin_users记录，检查merchants表的login_phone
			var merchant models.Merchant
			merchantResult := h.db.Where("login_phone = ?", phone).First(&merchant)

			if merchantResult.Error != nil {
				if merchantResult.Error == gorm.ErrRecordNotFound {
					// merchants表中也没有找到，拒绝登录
					middleware.LogBusinessError(c, "AdminLogin", result.Error, map[string]interface{}{
						"phone":   phone,
						"open_id": openID,
						"message": "管理端用户和商家登录手机号都不存在",
					})
					response.Forbidden(c, "非管理员手机号，请联系系统管理员")
					return
				} else {
					// 查询merchants表失败
					middleware.LogDatabaseError(c, "SELECT", merchantResult.Error, "merchants", map[string]interface{}{
						"phone":      phone,
						"query":      "login_phone = ?",
						"table":      "merchants",
						"error_type": merchantResult.Error.Error(),
					})
					response.InternalServerError(c, "查询商家信息失败")
					return
				}
			}

			// 在merchants表中找到了login_phone，自动创建商家用户
			now := time.Now()
			adminUser = models.AdminUser{
				Phone:        phone,
				OpenID:       openID,
				UnionID:      unionID,
				Nickname:     merchant.Contact,        // 使用商家联系人作为昵称
				UserType:     models.UserTypeMerchant, // user_type = 2
				Status:       models.StatusActive,
				MerchantID:   &merchant.ID,
				RegisterDate: now,
				LastLoginAt:  &now,
			}

			// 创建商家用户
			if err := h.db.Create(&adminUser).Error; err != nil {
				middleware.LogDatabaseError(c, "INSERT", err, "admin_users", map[string]interface{}{
					"phone":       phone,
					"merchant_id": merchant.ID,
					"user_type":   models.UserTypeMerchant,
				})
				response.InternalServerError(c, "创建商家用户失败")
				return
			}

			// 为新创建的商家用户分配商家角色
			if err := database.AssignMerchantRoleToUser(h.db, adminUser.ID); err != nil {
				// 记录错误但不中断流程
				middleware.LogBusinessError(c, "AssignMerchantRole", err, map[string]interface{}{
					"user_id":     adminUser.ID,
					"phone":       phone,
					"merchant_id": merchant.ID,
				})
			}

			// 记录成功创建商家用户的日志
			middleware.LogBusinessError(c, "MerchantUserAutoCreated", nil, map[string]interface{}{
				"user_id":       adminUser.ID,
				"phone":         phone,
				"merchant_id":   merchant.ID,
				"merchant_name": merchant.Name,
			})

		} else {
			// 记录数据库查询失败日志，包含更多调试信息
			middleware.LogDatabaseError(c, "SELECT", result.Error, "admin_users", map[string]interface{}{
				"phone":      phone,
				"query":      "phone = ?",
				"table":      "admin_users",
				"error_type": result.Error.Error(),
			})
			response.InternalServerError(c, "查询管理端用户失败")
			return
		}
	} else {
		// 检查用户状态
		if adminUser.Status != models.StatusActive {
			middleware.LogBusinessError(c, "AdminLoginBlocked", nil, map[string]interface{}{
				"user_id": adminUser.ID,
				"phone":   phone,
				"status":  adminUser.Status,
			})
			response.Forbidden(c, "管理员账户已被禁用")
			return
		}

		// 4. 检查绑定状态
		if adminUser.OpenID != "" && adminUser.OpenID != openID {
			middleware.LogBusinessError(c, "AdminBindConflict", nil, map[string]interface{}{
				"user_id":         adminUser.ID,
				"phone":           phone,
				"existing_openid": adminUser.OpenID,
				"new_openid":      openID,
			})
			response.Forbidden(c, "该手机号已被其他微信账号绑定")
			return
		}

		// 5. 更新绑定信息和登录时间（一次数据库操作）
		now := time.Now()
		updates := map[string]interface{}{
			"last_login_at": &now,
		}

		// 如果未绑定或需要更新OpenID
		if adminUser.OpenID == "" || adminUser.OpenID != openID {
			updates["open_id"] = openID
		}

		// 如果有新的UnionID且当前值为空
		if unionID != "" && adminUser.UnionID == "" {
			updates["union_id"] = unionID
			adminUser.UnionID = unionID // 更新内存中的值
		}

		// 一次性更新所有字段
		if err := h.db.Model(&adminUser).Updates(updates).Error; err != nil {
			middleware.LogDatabaseError(c, "UPDATE", err, "admin_users", map[string]interface{}{
				"user_id": adminUser.ID,
				"updates": updates,
			})
			response.InternalServerError(c, "更新管理员信息失败")
			return
		}

		// 更新内存中的值
		adminUser.LastLoginAt = &now
		if adminUser.OpenID == "" {
			adminUser.OpenID = openID
		}
	}

	// 移除会话创建，只使用Token存储

	// 生成JWT Token
	userIDStr := strconv.FormatUint(adminUser.ID, 10)
	token, err := h.JWTService.GenerateToken(userIDStr, adminUser.OpenID, string(adminUser.UserType), "")
	if err != nil {
		response.InternalServerError(c, "生成令牌失败")
		return
	}

	// 存储Token到Redis（24小时过期）
	ctx := context.Background()
	if err := h.TokenStorageService.StoreAdminToken(ctx, userIDStr, token.AccessToken, token.RefreshToken, 24*time.Hour); err != nil {
		middleware.LogBusinessError(c, "StoreAdminToken", err, map[string]interface{}{
			"user_id": userIDStr,
		})
		response.InternalServerError(c, "存储令牌失败")
		return
	}

	response.Success(c, gin.H{
		"token": gin.H{
			"access_token":  token.AccessToken,
			"refresh_token": token.RefreshToken,
			"expires_at":    token.ExpiresAt,
		},
		"user_info": gin.H{
			"user_id":       adminUser.ID,
			"nickname":      adminUser.Nickname,
			"avatar":        adminUser.Avatar,
			"phone":         adminUser.Phone,
			"user_type":     adminUser.UserType,
			"merchant_id":   adminUser.MerchantID,
			"register_date": adminUser.RegisterDate,
			// 移除敏感信息：open_id 和 union_id
		},
	})
}

// AdminWechatRefreshToken 管理端刷新微信令牌
func (h *Handler) AdminWechatRefreshToken(c *gin.Context) {
	var req AdminWechatRefreshTokenRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		response.ValidateError(c, err)
		return
	}

	// 解析refresh token获取用户ID
	refreshClaims, err := h.JWTService.ParseRefreshToken(req.RefreshToken)
	if err != nil {
		response.Unauthorized(c, "无效的刷新令牌")
		return
	}

	// 验证refresh token是否存在于Redis中
	ctx := context.Background()
	valid, err := h.TokenStorageService.ValidateAdminRefreshToken(ctx, refreshClaims.UserID, req.RefreshToken)
	if err != nil || !valid {
		response.Unauthorized(c, "刷新令牌已过期或不存在")
		return
	}

	// 刷新令牌
	token, err := h.JWTService.RefreshToken(req.RefreshToken)
	if err != nil {
		response.Unauthorized(c, "无效的刷新令牌")
		return
	}

	// 更新Redis中的token
	if err := h.TokenStorageService.StoreAdminToken(ctx, refreshClaims.UserID, token.AccessToken, token.RefreshToken, 24*time.Hour); err != nil {
		response.InternalServerError(c, "存储新令牌失败")
		return
	}

	response.Success(c, gin.H{
		"token": gin.H{
			"access_token":  token.AccessToken,
			"refresh_token": token.RefreshToken,
			"expires_at":    token.ExpiresAt,
		},
	})
}

// AdminWechatLogout 管理端微信小程序登出
func (h *Handler) AdminWechatLogout(c *gin.Context) {
	// 获取当前用户ID
	userID, exists := middleware.GetUserIDFromContext(c)
	if !exists {
		response.Unauthorized(c, "用户未认证")
		return
	}

	// 使Token失效
	ctx := context.Background()
	if err := h.TokenStorageService.InvalidateAdminToken(ctx, userID); err != nil {
		middleware.LogBusinessError(c, "InvalidateAdminToken", err, map[string]interface{}{
			"user_id": userID,
		})
		// 即使删除失败也返回成功，避免用户重复尝试
	}

	response.SuccessWithMessage(c, "登出成功", gin.H{
		"user_id": userID,
	})
}

// AdminUpdateUserInfo 管理端更新用户信息
func (h *Handler) AdminUpdateUserInfo(c *gin.Context) {
	var req AdminUpdateUserInfoRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		response.ValidateError(c, err)
		return
	}

	// 获取当前用户ID
	userID, exists := middleware.GetUserIDFromContext(c)
	if !exists {
		response.Unauthorized(c, "用户未认证")
		return
	}

	// 更新用户信息
	updates := map[string]interface{}{}
	if req.Nickname != "" {
		updates["nickname"] = req.Nickname
	}
	if req.Avatar != "" {
		updates["avatar"] = req.Avatar
	}
	if req.Gender > 0 {
		updates["gender"] = req.Gender
	}

	if len(updates) > 0 {
		if err := h.db.Model(&models.AdminUser{}).Where("id = ?", userID).Updates(updates).Error; err != nil {
			response.InternalServerError(c, "更新用户信息失败")
			return
		}
	}

	// 获取更新后的用户信息
	var adminUser models.AdminUser
	if err := h.db.First(&adminUser, "id = ?", userID).Error; err != nil {
		response.InternalServerError(c, "获取用户信息失败")
		return
	}

	response.Success(c, gin.H{
		"user_id":       adminUser.ID,
		"nickname":      adminUser.Nickname,
		"avatar":        adminUser.Avatar,
		"gender":        adminUser.Gender,
		"phone":         adminUser.Phone,
		"user_type":     adminUser.UserType,
		"merchant_id":   adminUser.MerchantID,
		"register_date": adminUser.RegisterDate,
	})
}

// AdminGetPhone 管理端获取手机号
func (h *Handler) AdminGetPhone(c *gin.Context) {
	var req AdminGetPhoneRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		response.ValidateError(c, err)
		return
	}

	// 获取当前用户ID
	userID, exists := middleware.GetUserIDFromContext(c)
	if !exists {
		response.Unauthorized(c, "用户未认证")
		return
	}

	// 调用微信接口获取手机号（使用管理端配置）
	// 获取access_token
	accessToken, err := h.wechatManager.GetMerchantAccessToken()
	if err != nil {
		response.InternalServerError(c, "获取微信访问令牌失败")
		return
	}

	// 获取手机号
	phoneInfo, err := h.wechatManager.GetPhoneNumber(accessToken.AccessToken, req.Code)
	if err != nil {
		response.InternalServerError(c, "获取手机号失败: "+err.Error())
		return
	}

	// 更新用户手机号
	if err := h.db.Model(&models.AdminUser{}).Where("id = ?", userID).Update("phone", phoneInfo.PhoneNumber).Error; err != nil {
		response.InternalServerError(c, "更新手机号失败")
		return
	}

	response.Success(c, gin.H{
		"phone": phoneInfo.PhoneNumber,
	})
}

// AdminGetUserInfo 管理端获取用户信息
func (h *Handler) AdminGetUserInfo(c *gin.Context) {
	// 获取当前用户ID
	userID, exists := middleware.GetUserIDFromContext(c)
	if !exists {
		response.Unauthorized(c, "用户未认证")
		return
	}

	// 获取用户信息
	var adminUser models.AdminUser
	if err := h.db.First(&adminUser, "id = ?", userID).Error; err != nil {
		if err == gorm.ErrRecordNotFound {
			response.NotFound(c, "用户不存在")
			return
		}
		response.InternalServerError(c, "获取用户信息失败")
		return
	}

	response.Success(c, gin.H{
		"user_id":       adminUser.ID,
		"open_id":       adminUser.OpenID,
		"nickname":      adminUser.Nickname,
		"avatar":        adminUser.Avatar,
		"gender":        adminUser.Gender,
		"phone":         adminUser.Phone,
		"user_type":     adminUser.UserType,
		"merchant_id":   adminUser.MerchantID,
		"register_date": adminUser.RegisterDate,
		"last_login_at": adminUser.LastLoginAt,
	})
}
