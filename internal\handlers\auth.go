package handlers

import (
	"context"
	"strconv"
	"time"
	"wangfujing_admin/internal/models"
	"wangfujing_admin/pkg/auth"
	"wangfujing_admin/pkg/logger"
	"wangfujing_admin/pkg/response"

	"github.com/gin-gonic/gin"
)

// min 返回两个整数中的较小值
func min(a, b int) int {
	if a < b {
		return a
	}
	return b
}

// LoginRequest 登录请求
type LoginRequest struct {
	Code     string `json:"code" binding:"required"`
	UserType int    `json:"user_type" binding:"required"` // 1:用户端 2:商家端
	Phone    string `json:"phone"`
	Nickname string `json:"nickname"`
	Avatar   string `json:"avatar"`
}

// AdminLoginRequest 管理端登录请求
type AdminLoginRequest struct {
	Phone    string `json:"phone" binding:"required"`
	Password string `json:"password" binding:"required"`
}

// RefreshTokenRequest 刷新令牌请求
type RefreshTokenRequest struct {
	RefreshToken string `json:"refresh_token" binding:"required"`
}

// LoginResponse 登录响应
type LoginResponse struct {
	User         interface{} `json:"user"`
	AccessToken  string      `json:"access_token"`
	RefreshToken string      `json:"refresh_token"`
	ExpiresIn    int64       `json:"expires_in"`
}

// Login 小程序端登录
func (h *Handler) Login(c *gin.Context) {
	var req LoginRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		response.ValidateError(c, err)
		return
	}

	// 获取微信用户信息
	var wechatResp *auth.Code2SessionResponse
	var err error

	if req.UserType == 1 {
		// 用户端
		wechatResp, err = h.wechatManager.GetUserOpenID(req.Code)
	} else {
		// 商家端
		wechatResp, err = h.wechatManager.GetMerchantOpenID(req.Code)
	}

	if err != nil {
		response.ErrorWithCustomCode(c, response.CodeWeChatError)
		return
	}

	// 查找或创建用户
	user, err := h.userService.GetUserByOpenID(c.Request.Context(), wechatResp.OpenID)
	if err != nil {
		if err.Error() == "user not found" {
			// 创建新用户
			userType := models.UserTypeCustomer
			if req.UserType == 2 {
				userType = models.UserTypeMerchant
			}

			user = &models.User{
				Phone:    req.Phone,
				Nickname: req.Nickname,
				Avatar:   req.Avatar,
				OpenID:   wechatResp.OpenID,
				UnionID:  wechatResp.UnionID,
				UserType: userType,
				Status:   models.StatusActive,
			}

			if err := h.userService.CreateUser(c.Request.Context(), user); err != nil {
				response.InternalServerError(c, "Failed to create user")
				return
			}
		} else {
			response.InternalServerError(c, "Failed to get user")
			return
		}
	}

	// 检查用户状态
	if user.Status != models.StatusActive {
		response.ErrorWithCustomCode(c, response.CodeUserDisabled)
		return
	}

	// 更新最后登录时间
	userIDStr := strconv.FormatUint(user.ID, 10)
	h.userService.UpdateLastLogin(c.Request.Context(), userIDStr)

	// 生成JWT令牌
	tokenInfo, err := h.JWTService.GenerateToken(userIDStr, "", string(user.UserType), "")
	if err != nil {
		response.InternalServerError(c, "Failed to generate token")
		return
	}

	resp := LoginResponse{
		User:         user,
		AccessToken:  tokenInfo.AccessToken,
		RefreshToken: tokenInfo.RefreshToken,
		ExpiresIn:    tokenInfo.ExpiresAt,
	}

	response.Success(c, resp)
}

// AdminLogin 管理端登录
func (h *Handler) AdminLogin(c *gin.Context) {
	var req AdminLoginRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		response.ValidateError(c, err)
		return
	}

	// 查找用户
	user, err := h.userService.GetUserByPhone(c.Request.Context(), req.Phone)
	if err != nil {
		response.ErrorWithCustomCode(c, response.CodeUserNotFound)
		return
	}

	// 检查用户类型
	if user.UserType != models.UserTypeAdmin {
		response.ErrorWithCustomCode(c, response.CodePermissionDenied)
		return
	}

	// 检查用户状态
	if user.Status != models.StatusActive {
		response.ErrorWithCustomCode(c, response.CodeUserDisabled)
		return
	}

	// 这里应该验证密码，但由于我们使用微信登录，暂时跳过密码验证
	// 在实际项目中，管理员可能需要单独的密码验证机制

	// 更新最后登录时间
	userIDStr := strconv.FormatUint(user.ID, 10)
	h.userService.UpdateLastLogin(c.Request.Context(), userIDStr)

	// 生成JWT令牌
	tokenInfo, err := h.JWTService.GenerateToken(userIDStr, "", string(user.UserType), "")
	if err != nil {
		response.InternalServerError(c, "Failed to generate token")
		return
	}

	resp := LoginResponse{
		User:         user,
		AccessToken:  tokenInfo.AccessToken,
		RefreshToken: tokenInfo.RefreshToken,
		ExpiresIn:    tokenInfo.ExpiresAt,
	}

	response.Success(c, resp)
}

// RefreshToken 刷新小程序端令牌
func (h *Handler) RefreshToken(c *gin.Context) {
	var req RefreshTokenRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		response.ValidateError(c, err)
		return
	}

	// 解析refresh token获取用户ID
	refreshClaims, err := h.JWTService.ParseRefreshToken(req.RefreshToken)
	if err != nil {
		response.Unauthorized(c, "无效的刷新令牌")
		return
	}

	// 验证refresh token是否存在于Redis中
	ctx := context.Background()
	valid, err := h.TokenStorageService.ValidateUserRefreshToken(ctx, refreshClaims.UserID, req.RefreshToken)
	if err != nil || !valid {
		response.Unauthorized(c, "刷新令牌已过期或不存在")
		return
	}

	// 刷新令牌
	tokenInfo, err := h.JWTService.RefreshToken(req.RefreshToken)
	if err != nil {
		response.Unauthorized(c, "无效的刷新令牌")
		return
	}

	// 先删除旧token，再存储新token
	if err := h.TokenStorageService.DeleteUserToken(ctx, refreshClaims.UserID); err != nil {
		// 删除失败不影响流程，只记录日志
		logger.Warn("删除旧token失败", logger.Err(err), logger.String("user_id", refreshClaims.UserID))
	}

	// 存储新token到Redis
	if err := h.TokenStorageService.StoreUserToken(ctx, refreshClaims.UserID, tokenInfo.AccessToken, tokenInfo.RefreshToken, time.Duration(h.config.JWT.APIExpireTime)*time.Second); err != nil {
		response.InternalServerError(c, "存储新令牌失败")
		return
	}

	// 添加调试日志
	logger.Info("刷新token成功",
		logger.String("user_id", refreshClaims.UserID),
		logger.String("new_access_token_prefix", tokenInfo.AccessToken[:min(20, len(tokenInfo.AccessToken))]),
		logger.String("new_refresh_token_prefix", tokenInfo.RefreshToken[:min(20, len(tokenInfo.RefreshToken))]))

	response.Success(c, gin.H{
		"access_token":  tokenInfo.AccessToken,
		"refresh_token": tokenInfo.RefreshToken,
		"expires_at":    tokenInfo.ExpiresAt,
		"expires_in":    h.config.JWT.APIExpireTime,
	})
}

// AdminRefreshToken 刷新管理端令牌
func (h *Handler) AdminRefreshToken(c *gin.Context) {
	var req RefreshTokenRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		response.ValidateError(c, err)
		return
	}

	tokenInfo, err := h.JWTService.RefreshToken(req.RefreshToken)
	if err != nil {
		response.Unauthorized(c, "Invalid refresh token")
		return
	}

	response.Success(c, gin.H{
		"access_token":  tokenInfo.AccessToken,
		"refresh_token": tokenInfo.RefreshToken,
		"expires_at":    tokenInfo.ExpiresAt,
		"expires_in":    h.config.JWT.AdminExpireTime,
	})
}

// 删除了未使用的空登出函数
// 实际使用的登出函数在 wechat_auth.go 和 admin_wechat_auth.go 中

// GetProfile 获取当前用户信息
func (h *Handler) GetProfile(c *gin.Context) {
	userID, exists := c.Get("user_id")
	if !exists {
		response.Unauthorized(c, "User not authenticated")
		return
	}

	user, err := h.userService.GetUserByID(c.Request.Context(), userID.(string))
	if err != nil {
		response.InternalServerError(c, "Failed to get user profile")
		return
	}

	response.Success(c, user)
}

// UpdateProfile 更新当前用户信息
func (h *Handler) UpdateProfile(c *gin.Context) {
	userID, exists := c.Get("user_id")
	if !exists {
		response.Unauthorized(c, "User not authenticated")
		return
	}

	var req struct {
		Nickname string `json:"nickname"`
		Avatar   string `json:"avatar"`
	}
	if err := c.ShouldBindJSON(&req); err != nil {
		response.ValidateError(c, err)
		return
	}

	updates := make(map[string]interface{})
	if req.Nickname != "" {
		updates["nickname"] = req.Nickname
	}
	if req.Avatar != "" {
		updates["avatar"] = req.Avatar
	}

	if err := h.userService.UpdateUser(c.Request.Context(), userID.(string), updates); err != nil {
		response.InternalServerError(c, "Failed to update profile")
		return
	}

	response.SuccessWithMessage(c, "Profile updated successfully", nil)
}

// GetUserPermissions 获取当前用户权限
func (h *Handler) GetUserPermissions(c *gin.Context) {
	userID, exists := c.Get("user_id")
	if !exists {
		response.Unauthorized(c, "User not authenticated")
		return
	}

	permissions, err := h.permissionManager.GetUserPermissions(c.Request.Context(), userID.(string))
	if err != nil {
		response.InternalServerError(c, "Failed to get user permissions")
		return
	}

	response.Success(c, permissions)
}

// CheckPermission 检查权限
func (h *Handler) CheckPermission(c *gin.Context) {
	userID, exists := c.Get("user_id")
	if !exists {
		response.Unauthorized(c, "User not authenticated")
		return
	}

	permission := c.Query("permission")
	if permission == "" {
		response.BadRequest(c, "Permission parameter is required")
		return
	}

	hasPermission, err := h.permissionManager.HasPermission(c.Request.Context(), userID.(string), permission)
	if err != nil {
		response.InternalServerError(c, "Failed to check permission")
		return
	}

	response.Success(c, gin.H{
		"permission": permission,
		"allowed":    hasPermission,
	})
}
