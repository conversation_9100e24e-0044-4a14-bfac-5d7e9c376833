package handlers

import (
	"log"
	"net/http"
	"strconv"
	"wangfujing_admin/internal/models"
	"wangfujing_admin/internal/services"
	"wangfujing_admin/pkg/response"

	"github.com/gin-gonic/gin"
)

// BenefitApplicationHandler 权益申请处理器
type BenefitApplicationHandler struct {
	benefitService *services.BenefitApplicationService
}

// NewBenefitApplicationHandler 创建权益申请处理器
func NewBenefitApplicationHandler(benefitService *services.BenefitApplicationService) *BenefitApplicationHandler {
	return &BenefitApplicationHandler{
		benefitService: benefitService,
	}
}

// CreateBenefitApplication 创建权益申请
func (h *BenefitApplicationHandler) CreateBenefitApplication(c *gin.Context) {
	log.Printf("=== CreateBenefitApplication 开始处理 ===")
	log.Printf("请求方法: %s", c.Request.Method)
	log.Printf("请求路径: %s", c.Request.URL.Path)
	log.Printf("请求头: %+v", c.Request.Header)

	var req services.CreateBenefitApplicationRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		log.Printf("参数绑定失败: %v", err)
		response.Error(c, http.StatusBadRequest, "参数错误: "+err.Error())
		return
	}
	log.Printf("请求参数: %+v", req)

	// 从JWT中获取申请人ID
	log.Printf("开始获取用户ID...")
	applicantID, err := getUserIDFromContext(c)
	if err != nil {
		log.Printf("获取用户ID失败: %v", err)
		response.Error(c, http.StatusUnauthorized, err.Error())
		return
	}
	log.Printf("获取到用户ID: %d", applicantID)

	log.Printf("开始调用服务创建权益申请...")
	application, err := h.benefitService.CreateBenefitApplication(c.Request.Context(), &req, applicantID)
	if err != nil {
		log.Printf("创建权益申请失败: %v", err)
		response.Error(c, http.StatusInternalServerError, "创建权益申请失败: "+err.Error())
		return
	}
	log.Printf("权益申请创建成功: %+v", application)

	log.Printf("=== CreateBenefitApplication 处理完成 ===")
	response.SuccessWithMessage(c, "创建权益申请成功", application)
}

// GetBenefitApplications 获取权益申请列表
func (h *BenefitApplicationHandler) GetBenefitApplications(c *gin.Context) {
	statusStr := c.DefaultQuery("status", "")
	page, _ := strconv.Atoi(c.DefaultQuery("page", "1"))
	size, _ := strconv.Atoi(c.DefaultQuery("size", "20"))

	if page < 1 {
		page = 1
	}
	if size < 1 || size > 100 {
		size = 20
	}

	var status *models.ApprovalStatus
	var statusValue models.ApprovalStatus
	if statusStr != "" {
		statusInt, _ := strconv.Atoi(statusStr)
		statusValue = models.ApprovalStatus(statusInt)
		status = &statusValue
	}

	applications, total, err := h.benefitService.GetBenefitApplicationsWithOptionalStatus(c.Request.Context(), status, page, size)
	if err != nil {
		response.Error(c, http.StatusInternalServerError, "获取权益申请失败: "+err.Error())
		return
	}

	filterData := gin.H{}
	if status != nil {
		filterData["status"] = *status
	}

	data := gin.H{
		"applications": applications,
		"pagination": gin.H{
			"page":  page,
			"size":  size,
			"total": total,
		},
		"filter": filterData,
	}

	response.SuccessWithMessage(c, "获取权益申请成功", data)
}

// GetBenefitApplicationDetail 获取权益申请详情
func (h *BenefitApplicationHandler) GetBenefitApplicationDetail(c *gin.Context) {
	idStr := c.Param("id")
	id, err := strconv.ParseUint(idStr, 10, 64)
	if err != nil {
		response.Error(c, http.StatusBadRequest, "无效的申请ID")
		return
	}

	detail, err := h.benefitService.GetBenefitApplicationDetail(c.Request.Context(), id)
	if err != nil {
		response.Error(c, http.StatusInternalServerError, "获取权益申请详情失败: "+err.Error())
		return
	}

	response.SuccessWithMessage(c, "获取权益申请详情成功", detail)
}

// ApproveBenefitApplication 审核权益申请
func (h *BenefitApplicationHandler) ApproveBenefitApplication(c *gin.Context) {
	idStr := c.Param("id")
	id, err := strconv.ParseUint(idStr, 10, 64)
	if err != nil {
		response.Error(c, http.StatusBadRequest, "无效的申请ID")
		return
	}

	var req services.ApproveBenefitApplicationRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		response.Error(c, http.StatusBadRequest, "参数错误: "+err.Error())
		return
	}

	// 从JWT中获取审核员ID
	approverID, err := getUserIDFromContext(c)
	if err != nil {
		response.Error(c, http.StatusUnauthorized, err.Error())
		return
	}

	if err := h.benefitService.ApproveBenefitApplication(c.Request.Context(), id, &req, approverID); err != nil {
		response.Error(c, http.StatusInternalServerError, "审核权益申请失败: "+err.Error())
		return
	}

	response.SuccessWithMessage(c, "审核权益申请成功", nil)
}

// ReapplyBenefitApplication 重新申请权益
func (h *BenefitApplicationHandler) ReapplyBenefitApplication(c *gin.Context) {
	idStr := c.Param("id")
	id, err := strconv.ParseUint(idStr, 10, 64)
	if err != nil {
		response.Error(c, http.StatusBadRequest, "无效的申请ID")
		return
	}

	var req services.ReapplyBenefitApplicationRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		response.Error(c, http.StatusBadRequest, "参数错误: "+err.Error())
		return
	}

	// 从JWT中获取申请人ID
	applicantID, err := getUserIDFromContext(c)
	if err != nil {
		response.Error(c, http.StatusUnauthorized, err.Error())
		return
	}

	application, err := h.benefitService.ReapplyBenefitApplication(c.Request.Context(), id, &req, applicantID)
	if err != nil {
		response.Error(c, http.StatusInternalServerError, "重新申请权益失败: "+err.Error())
		return
	}

	response.SuccessWithMessage(c, "重新申请权益成功", application)
}
