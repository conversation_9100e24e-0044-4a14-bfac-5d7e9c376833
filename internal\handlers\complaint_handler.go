package handlers

import (
	"fmt"
	"strconv"
	"time"

	"github.com/gin-gonic/gin"

	"wangfujing_admin/internal/models"
	"wangfujing_admin/pkg/json"
	"wangfujing_admin/pkg/response"
)

// CreateComplaintRequest 创建客诉请求
type CreateComplaintRequest struct {
	MerchantID        string   `json:"merchant_id" binding:"required"`
	Title             string   `json:"title" binding:"required,max=30"`
	Content           string   `json:"content" binding:"max=300"`
	SupplementaryInfo []string `json:"supplementary_info"`
	LatestSolveTime   string   `json:"latest_solve_time" binding:"required"`
}

// UpdateComplaintRequest 更新客诉请求
type UpdateComplaintRequest struct {
	Title             string   `json:"title" binding:"required,max=30"`
	Content           string   `json:"content" binding:"max=300"`
	SupplementaryInfo []string `json:"supplementary_info"`
	LatestSolveTime   string   `json:"latest_solve_time" binding:"required"`
}

// ApproveComplaintRequest 审核客诉请求
type ApproveComplaintRequest struct {
	ApprovalNote string `json:"approval_note" binding:"required"`
}

// RejectComplaintRequest 拒绝客诉请求
type RejectComplaintRequest struct {
	ApprovalNote string `json:"approval_note" binding:"required"`
}

// GetComplaintsAdmin 获取客诉列表（管理端）
func (h *Handler) GetComplaintsAdmin(c *gin.Context) {
	page, _ := strconv.Atoi(c.DefaultQuery("page", "1"))
	size, _ := strconv.Atoi(c.DefaultQuery("size", "10"))
	merchantID := c.Query("merchant_id")

	var status *models.ApprovalStatus
	if statusStr := c.Query("status"); statusStr != "" {
		if s, err := strconv.Atoi(statusStr); err == nil {
			statusVal := models.ApprovalStatus(s)
			status = &statusVal
		}
	}

	complaints, total, err := h.complaintService.GetComplaints(c.Request.Context(), page, size, merchantID, status)
	if err != nil {
		response.InternalServerError(c, "获取客诉列表失败")
		return
	}

	response.Page(c, complaints, total, page, size)
}

// GetComplaintAdmin 获取客诉详情（管理端）
func (h *Handler) GetComplaintAdmin(c *gin.Context) {
	id := c.Param("id")

	complaint, err := h.complaintService.GetComplaintByID(c.Request.Context(), id)
	if err != nil {
		if err.Error() == "record not found" {
			response.NotFound(c, "客诉记录不存在")
			return
		}
		response.InternalServerError(c, "获取客诉详情失败")
		return
	}

	response.Success(c, complaint)
}

// CreateComplaintAdmin 创建客诉（管理端）
func (h *Handler) CreateComplaintAdmin(c *gin.Context) {
	var req CreateComplaintRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		response.BadRequest(c, "请求参数错误: "+err.Error())
		return
	}

	// 获取当前用户ID
	userID, exists := c.Get("user_id")
	if !exists {
		response.Unauthorized(c, "用户未认证")
		return
	}

	// 转换商家ID
	merchantIDUint, err := strconv.ParseUint(req.MerchantID, 10, 64)
	if err != nil {
		response.BadRequest(c, "商家ID格式错误")
		return
	}

	// 转换用户ID
	userIDUint, err := strconv.ParseUint(userID.(string), 10, 64)
	if err != nil {
		response.BadRequest(c, "用户ID格式错误")
		return
	}

	// 解析最晚解决时间，明确使用中国时区
	loc, err := time.LoadLocation("Asia/Shanghai")
	if err != nil {
		response.InternalServerError(c, "时区设置错误")
		return
	}
	latestSolveTime, err := time.ParseInLocation("2006-01-02 15:04", req.LatestSolveTime, loc)
	if err != nil {
		response.BadRequest(c, "最晚解决时间格式错误，应为：2025-07-15 23:00")
		return
	}

	// 处理补充信息
	var supplementaryInfoJSON string
	if len(req.SupplementaryInfo) > 0 {
		if jsonStr, err := json.MarshalToString(req.SupplementaryInfo); err == nil {
			supplementaryInfoJSON = jsonStr
		}
	}

	complaint := &models.Complaint{
		MerchantID:        merchantIDUint,
		Title:             req.Title,
		Content:           req.Content,
		SupplementaryInfo: supplementaryInfoJSON,
		LatestSolveTime:   &latestSolveTime,
		SubmitterID:       userIDUint,
		ApprovalStatus:    models.ApprovalStatusPending,
	}

	if err := h.complaintService.CreateComplaint(c.Request.Context(), complaint); err != nil {
		response.InternalServerError(c, "创建客诉失败")
		return
	}

	// 重新获取完整的客诉信息（包含关联数据）
	complaintWithDetails, err := h.complaintService.GetComplaintByID(c.Request.Context(), fmt.Sprintf("%d", complaint.ID))
	if err != nil {
		// 如果获取详情失败，仍然返回创建成功，但数据可能不完整
		response.SuccessWithMessage(c, "客诉创建成功", complaint)
		return
	}

	response.SuccessWithMessage(c, "客诉创建成功", complaintWithDetails)
}

// UpdateComplaintAdmin 更新客诉（管理端）
func (h *Handler) UpdateComplaintAdmin(c *gin.Context) {
	id := c.Param("id")

	var req UpdateComplaintRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		response.BadRequest(c, "请求参数错误: "+err.Error())
		return
	}

	// 获取当前用户ID
	userID, exists := c.Get("user_id")
	if !exists {
		response.Unauthorized(c, "用户未认证")
		return
	}

	// 解析最晚解决时间，明确使用中国时区
	loc, err := time.LoadLocation("Asia/Shanghai")
	if err != nil {
		response.InternalServerError(c, "时区设置错误")
		return
	}
	latestSolveTime, err := time.ParseInLocation("2006-01-02 15:04", req.LatestSolveTime, loc)
	if err != nil {
		response.BadRequest(c, "最晚解决时间格式错误，应为：2025-07-15 23:00")
		return
	}

	// 处理补充信息
	var supplementaryInfoJSON string
	if len(req.SupplementaryInfo) > 0 {
		if jsonStr, err := json.MarshalToString(req.SupplementaryInfo); err == nil {
			supplementaryInfoJSON = jsonStr
		}
	}

	updateData := map[string]interface{}{
		"title":              req.Title,
		"content":            req.Content,
		"supplementary_info": supplementaryInfoJSON,
		"latest_solve_time":  &latestSolveTime,
	}

	if err := h.complaintService.UpdateComplaint(c.Request.Context(), id, userID.(string), updateData); err != nil {
		response.InternalServerError(c, "更新客诉失败")
		return
	}

	response.SuccessWithMessage(c, "客诉更新成功", nil)
}

// ApproveComplaintAdmin 审核通过客诉（管理端）
func (h *Handler) ApproveComplaintAdmin(c *gin.Context) {
	id := c.Param("id")

	var req ApproveComplaintRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		response.BadRequest(c, "请求参数错误: "+err.Error())
		return
	}

	// 获取当前用户ID
	userID, exists := c.Get("user_id")
	if !exists {
		response.Unauthorized(c, "用户未认证")
		return
	}

	if err := h.complaintService.ApproveComplaint(c.Request.Context(), id, userID.(string), req.ApprovalNote); err != nil {
		response.InternalServerError(c, "审核通过失败")
		return
	}

	response.SuccessWithMessage(c, "审核通过成功", nil)
}

// RejectComplaintAdmin 审核拒绝客诉（管理端）
func (h *Handler) RejectComplaintAdmin(c *gin.Context) {
	id := c.Param("id")

	var req RejectComplaintRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		response.BadRequest(c, "请求参数错误: "+err.Error())
		return
	}

	// 获取当前用户ID
	userID, exists := c.Get("user_id")
	if !exists {
		response.Unauthorized(c, "用户未认证")
		return
	}

	if err := h.complaintService.RejectComplaint(c.Request.Context(), id, userID.(string), req.ApprovalNote); err != nil {
		response.InternalServerError(c, "审核拒绝失败")
		return
	}

	response.SuccessWithMessage(c, "审核拒绝成功", nil)
}

// GetPendingComplaints 获取待审核客诉列表
func (h *Handler) GetPendingComplaints(c *gin.Context) {
	page, _ := strconv.Atoi(c.DefaultQuery("page", "1"))
	size, _ := strconv.Atoi(c.DefaultQuery("size", "10"))

	status := models.ApprovalStatusPending
	complaints, total, err := h.complaintService.GetComplaints(c.Request.Context(), page, size, "", &status)
	if err != nil {
		response.InternalServerError(c, "获取待审核客诉列表失败")
		return
	}

	response.Page(c, complaints, total, page, size)
}
