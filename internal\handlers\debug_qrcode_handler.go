package handlers

import (
	"strconv"
	"wangfujing_admin/internal/models"
	"wangfujing_admin/pkg/response"

	"github.com/gin-gonic/gin"
)

// DebugOrderQRCode 调试订单二维码状态
func (h *Handler) DebugOrderQRCode(c *gin.Context) {
	orderIDStr := c.Param("order_id")
	orderID, err := strconv.ParseUint(orderIDStr, 10, 64)
	if err != nil {
		response.BadRequest(c, "订单ID格式错误")
		return
	}

	// 查询订单信息
	var order models.Order
	if err := h.db.Where("id = ?", orderID).First(&order).Error; err != nil {
		response.NotFound(c, "订单不存在")
		return
	}

	// 检查二维码服务状态
	qrCodeServiceStatus := "未初始化"
	if h.qrCodeService != nil {
		qrCodeServiceStatus = "已初始化"
	}

	// 检查OSS管理器状态
	ossManagerStatus := "未知"
	if h.ossManager != nil {
		ossManagerStatus = "已初始化"
	} else {
		ossManagerStatus = "未初始化"
	}

	response.Success(c, gin.H{
		"order_info": gin.H{
			"id":           order.ID,
			"order_no":     order.OrderNo,
			"qr_code":      order.QRCode,
			"qr_code_url":  order.QRCodeURL,
			"status":       order.Status,
			"created_at":   order.CreatedAt,
		},
		"service_status": gin.H{
			"qr_code_service": qrCodeServiceStatus,
			"oss_manager":     ossManagerStatus,
		},
		"debug_info": gin.H{
			"has_qr_code":     order.QRCode != "",
			"has_qr_code_url": order.QRCodeURL != "",
			"can_generate":    h.qrCodeService != nil && h.ossManager != nil,
		},
	})
}

// ForceGenerateOrderQRCode 强制生成订单二维码（同步）
func (h *Handler) ForceGenerateOrderQRCode(c *gin.Context) {
	orderIDStr := c.Param("order_id")
	orderID, err := strconv.ParseUint(orderIDStr, 10, 64)
	if err != nil {
		response.BadRequest(c, "订单ID格式错误")
		return
	}

	// 检查服务状态
	if h.qrCodeService == nil {
		response.InternalServerError(c, "二维码服务未初始化")
		return
	}

	// 同步生成二维码
	qrCodeURL, err := h.qrCodeService.GenerateOrderQRCode(c.Request.Context(), orderID)
	if err != nil {
		response.InternalServerError(c, "生成二维码失败: "+err.Error())
		return
	}

	response.Success(c, gin.H{
		"order_id":    orderID,
		"qr_code_url": qrCodeURL,
		"message":     "二维码生成成功",
	})
}

// CheckAllOrdersQRCodeStatus 检查所有订单的二维码状态
func (h *Handler) CheckAllOrdersQRCodeStatus(c *gin.Context) {
	var stats struct {
		TotalOrders       int64 `json:"total_orders"`
		OrdersWithQRCode  int64 `json:"orders_with_qr_code"`
		OrdersWithQRURL   int64 `json:"orders_with_qr_url"`
		OrdersWithoutQR   int64 `json:"orders_without_qr"`
		OrdersWithoutURL  int64 `json:"orders_without_url"`
	}

	// 统计总订单数
	h.db.Model(&models.Order{}).Count(&stats.TotalOrders)

	// 统计有二维码文本的订单
	h.db.Model(&models.Order{}).Where("qr_code != '' AND qr_code IS NOT NULL").Count(&stats.OrdersWithQRCode)

	// 统计有二维码URL的订单
	h.db.Model(&models.Order{}).Where("qr_code_url != '' AND qr_code_url IS NOT NULL").Count(&stats.OrdersWithQRURL)

	// 计算缺失的订单数
	stats.OrdersWithoutQR = stats.TotalOrders - stats.OrdersWithQRCode
	stats.OrdersWithoutURL = stats.TotalOrders - stats.OrdersWithQRURL

	// 获取最近的几个订单示例
	var recentOrders []models.Order
	h.db.Select("id, order_no, qr_code, qr_code_url, created_at").
		Order("created_at DESC").
		Limit(5).
		Find(&recentOrders)

	response.Success(c, gin.H{
		"statistics":    stats,
		"recent_orders": recentOrders,
		"service_status": gin.H{
			"qr_code_service": h.qrCodeService != nil,
			"oss_manager":     h.ossManager != nil,
		},
	})
}
