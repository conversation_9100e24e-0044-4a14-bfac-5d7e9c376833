package handlers

import (
	"strconv"
	"wangfujing_admin/internal/services"
	"wangfujing_admin/pkg/response"

	"github.com/gin-gonic/gin"
)

// GetExpiredProductsInfo 获取过期商品信息
func (h *Handler) GetExpiredProductsInfo(c *gin.Context) {
	cleanupService := services.NewExpiredProductCleanupService(h.db, h.rdb)

	info, err := cleanupService.GetExpiredProductsInfo(c.Request.Context())
	if err != nil {
		response.InternalServerError(c, "获取过期商品信息失败")
		return
	}

	response.Success(c, info)
}

// CleanupExpiredProducts 手动清理过期商品
func (h *Handler) CleanupExpiredProducts(c *gin.Context) {
	cleanupService := services.NewExpiredProductCleanupService(h.db, h.rdb)

	if err := cleanupService.ForceCleanupAllExpired(c.Request.Context()); err != nil {
		response.InternalServerError(c, "清理过期商品失败")
		return
	}

	response.Success(c, gin.H{
		"message": "过期商品清理完成",
	})
}

// CleanupSpecificProduct 清理指定商品
func (h *Handler) CleanupSpecificProduct(c *gin.Context) {
	productIDStr := c.Param("id")
	productID, err := strconv.ParseUint(productIDStr, 10, 64)
	if err != nil {
		response.BadRequest(c, "商品ID格式错误")
		return
	}

	cleanupService := services.NewExpiredProductCleanupService(h.db, h.rdb)

	if err := cleanupService.CleanupSpecificProduct(c.Request.Context(), productID); err != nil {
		response.InternalServerError(c, err.Error())
		return
	}

	response.Success(c, gin.H{
		"message": "商品清理完成",
	})
}
