package handlers

import (
	"net/http"
	"strconv"

	"wangfujing_admin/internal/services"
	"wangfujing_admin/pkg/response"

	"github.com/gin-gonic/gin"
)

// GameHandler 游戏处理器
type GameHandler struct {
	gameService *services.GameService
}

// NewGameHandler 创建游戏处理器
func NewGameHandler(gameService *services.GameService) *GameHandler {
	return &GameHandler{
		gameService: gameService,
	}
}

// GetGameOptions 获取游戏选项
func (h *GameHandler) GetGameOptions(ctx *gin.Context) {
	activityIDStr := ctx.Param("activityId")
	activityID, err := strconv.ParseUint(activityIDStr, 10, 64)
	if err != nil {
		response.Error(ctx, http.StatusBadRequest, "无效的活动ID")
		return
	}

	options, err := h.gameService.GetGameOptions(activityID)
	if err != nil {
		if err.Error() == "activity not found" {
			response.Error(ctx, http.StatusNotFound, "活动不存在")
			return
		}
		if err.Error() == "activity is not a game type" {
			response.Error(ctx, http.StatusBadRequest, "该活动不是游戏类型")
			return
		}
		response.Error(ctx, http.StatusInternalServerError, err.Error())
		return
	}

	response.SuccessWithMessage(ctx, "获取成功", options)
}

// PlayLottery 进行抽奖
func (h *GameHandler) PlayLottery(ctx *gin.Context) {
	// 获取用户ID
	userID, exists := ctx.Get("user_id")
	if !exists {
		response.Error(ctx, http.StatusUnauthorized, "未授权")
		return
	}

	userIDUint, err := strconv.ParseUint(userID.(string), 10, 64)
	if err != nil {
		response.Error(ctx, http.StatusBadRequest, "无效的用户ID")
		return
	}

	var req services.LotteryRequest
	if err := ctx.ShouldBindJSON(&req); err != nil {
		response.Error(ctx, http.StatusBadRequest, "参数错误: "+err.Error())
		return
	}

	result, err := h.gameService.PlayLottery(userIDUint, &req)
	if err != nil {
		if err.Error() == "activity not found" {
			response.Error(ctx, http.StatusNotFound, "活动不存在")
			return
		}
		if err.Error() == "activity is not a game type" {
			response.Error(ctx, http.StatusBadRequest, "该活动不是游戏类型")
			return
		}
		if err.Error() == "activity is not active" {
			response.Error(ctx, http.StatusBadRequest, "活动未开始或已结束")
			return
		}
		if err.Error() == "activity has not started yet" {
			response.Error(ctx, http.StatusBadRequest, "活动尚未开始")
			return
		}
		if err.Error() == "activity has ended" {
			response.Error(ctx, http.StatusBadRequest, "活动已结束")
			return
		}
		if err.Error() == "user not found" {
			response.Error(ctx, http.StatusNotFound, "用户不存在")
			return
		}
		if err.Error() == "daily participation limit exceeded" {
			response.Error(ctx, http.StatusBadRequest, "今日参与次数已达上限")
			return
		}
		// 检查积分不足的错误
		if len(err.Error()) > 20 && err.Error()[:20] == "insufficient points," {
			response.Error(ctx, http.StatusBadRequest, "积分不足，无法参与")
			return
		}
		response.Error(ctx, http.StatusInternalServerError, err.Error())
		return
	}

	response.SuccessWithMessage(ctx, "抽奖成功", result)
}
