package handlers

import (
	"time"
	"wangfujing_admin/internal/config"
	"wangfujing_admin/internal/services"
	"wangfujing_admin/pkg/auth"
	"wangfujing_admin/pkg/oss"

	"github.com/redis/go-redis/v9"
	"gorm.io/gorm"
)

// Handler 处理器结构
type Handler struct {
	db                *gorm.DB
	rdb               *redis.Client
	config            *config.Config
	wechatManager     *auth.WeChatManager
	permissionManager *auth.PermissionManager
	ossManager        *oss.OSSManager

	// 服务
	userService               *services.UserService
	roleService               *services.RoleService
	permissionService         *services.PermissionService
	merchantService           *services.MerchantService
	floorService              *services.FloorService
	productService            *services.ProductService
	orderService              *services.OrderService
	JWTService                services.JWTService
	TokenStorageService       *services.TokenStorageService
	complaintService          *services.ComplaintService
	opinionService            *services.OpinionService
	activityService           *services.ActivityService
	userManagementService     *services.UserManagementService
	merchantManagementService *services.MerchantManagementService
	levelManagementService    *services.LevelManagementService
	benefitApplicationService *services.BenefitApplicationService
	marketingService          *services.MarketingService
	merchantActivityService   *services.MerchantActivityService
	pointsMallService         *services.PointsMallService
	memberRuleService         *services.MemberRuleService
	programService            *services.ProgramService
	qrCodeService             *services.QRCodeService
	personalQRService         *services.PersonalQRService

	// 子处理器
	UploadHandler                        *UploadHandler
	MerchantManagementHandler            *MerchantManagementHandler
	LevelManagementHandler               *LevelManagementHandler
	BenefitApplicationHandler            *BenefitApplicationHandler
	MarketingHandler                     *MarketingHandler
	MerchantActivityHandler              *MerchantActivityHandler
	MerchantActivityParticipationHandler *MerchantActivityParticipationHandler
	ActivityRewardHandler                *ActivityRewardHandler
	PointsMallHandler                    *PointsMallHandler
	MemberRuleHandler                    *MemberRuleHandler
	ProgramHandler                       *ProgramHandler
}

// NewHandler 创建处理器
func NewHandler(db *gorm.DB, rdb *redis.Client, cfg *config.Config) *Handler {
	// 移除未使用的JWT管理器

	// 初始化微信管理器
	wechatConfig := &auth.WeChatConfig{
		UserAppID:         cfg.WeChat.UserAppID,
		UserAppSecret:     cfg.WeChat.UserAppSecret,
		MerchantAppID:     cfg.WeChat.MerchantAppID,
		MerchantAppSecret: cfg.WeChat.MerchantAppSecret,
	}
	wechatManager := auth.NewWeChatManager(wechatConfig)

	// 初始化权限管理器
	permissionManager := auth.NewPermissionManager(db)

	// 初始化OSS管理器
	ossConfig := &oss.OSSConfig{
		Endpoint:        cfg.OSS.Endpoint,
		AccessKeyID:     cfg.OSS.AccessKeyID,
		AccessKeySecret: cfg.OSS.AccessKeySecret,
		BucketName:      cfg.OSS.BucketName,
		Domain:          cfg.OSS.Domain,
	}
	ossManager, _ := oss.NewOSSManager(ossConfig)

	// 初始化服务
	userService := services.NewUserService(db)
	roleService := services.NewRoleService(db)
	permissionService := services.NewPermissionService(db)
	merchantService := services.NewMerchantService(db)
	floorService := services.NewFloorService(db)
	productService := services.NewProductService(db)
	orderService := services.NewOrderService(db)
	complaintService := services.NewComplaintService(db)
	opinionService := services.NewOpinionService(db)
	activityService := services.NewActivityService(db)
	userManagementService := services.NewUserManagementService(db)
	merchantManagementService := services.NewMerchantManagementService(db)
	levelManagementService := services.NewLevelManagementService(db)
	benefitApplicationService := services.NewBenefitApplicationService(db)
	marketingService := services.NewMarketingService(db)
	merchantActivityService := services.NewMerchantActivityService(db)
	pointsMallService := services.NewPointsMallService(db)
	memberRuleService := services.NewMemberRuleService(db)
	programService := services.NewProgramService(db)
	qrCodeService := services.NewQRCodeService(db, ossManager)

	// 初始化OSS服务（用于个人二维码上传）
	ossService := services.NewOSSService(ossManager)
	personalQRService := services.NewPersonalQRService(db, ossService, cfg)

	// 初始化JWT服务
	jwtService := services.NewJWTService(
		cfg.JWT.APISecret,
		time.Duration(cfg.JWT.APIExpireTime)*time.Hour,
		time.Duration(cfg.JWT.RefreshExpireTime)*time.Hour,
		"wangfujing_admin",
	)

	// 初始化Token存储服务
	tokenStorageService := services.NewTokenStorageService(rdb)

	handler := &Handler{
		db:                        db,
		rdb:                       rdb,
		config:                    cfg,
		wechatManager:             wechatManager,
		permissionManager:         permissionManager,
		ossManager:                ossManager,
		userService:               userService,
		roleService:               roleService,
		permissionService:         permissionService,
		merchantService:           merchantService,
		floorService:              floorService,
		productService:            productService,
		orderService:              orderService,
		JWTService:                jwtService,
		TokenStorageService:       tokenStorageService,
		complaintService:          complaintService,
		opinionService:            opinionService,
		activityService:           activityService,
		userManagementService:     userManagementService,
		merchantManagementService: merchantManagementService,
		levelManagementService:    levelManagementService,
		benefitApplicationService: benefitApplicationService,
		marketingService:          marketingService,
		merchantActivityService:   merchantActivityService,
		pointsMallService:         pointsMallService,
		memberRuleService:         memberRuleService,
		programService:            programService,
		qrCodeService:             qrCodeService,
		personalQRService:         personalQRService,
	}

	// 初始化子处理器
	handler.UploadHandler = NewUploadHandler(handler)
	handler.MerchantManagementHandler = NewMerchantManagementHandler(merchantManagementService)
	handler.LevelManagementHandler = NewLevelManagementHandler(levelManagementService)
	handler.BenefitApplicationHandler = NewBenefitApplicationHandler(benefitApplicationService)
	handler.MarketingHandler = NewMarketingHandler(marketingService)
	handler.MerchantActivityHandler = NewMerchantActivityHandler(merchantActivityService)
	handler.MerchantActivityParticipationHandler = NewMerchantActivityParticipationHandler(db)
	handler.ActivityRewardHandler = NewActivityRewardHandler(db)
	handler.PointsMallHandler = NewPointsMallHandler(pointsMallService)
	handler.MemberRuleHandler = NewMemberRuleHandler(memberRuleService)
	handler.ProgramHandler = NewProgramHandler(programService, db)

	return handler
}

// GetQRCodeService 获取二维码服务
func (h *Handler) GetQRCodeService() *services.QRCodeService {
	return h.qrCodeService
}

// GetDB 获取数据库连接
func (h *Handler) GetDB() *gorm.DB {
	return h.db
}

// GetRedis 获取Redis连接
func (h *Handler) GetRedis() *redis.Client {
	return h.rdb
}
