package handlers

import (
	"fmt"
	"strconv"
	"strings"
	"time"

	"wangfujing_admin/internal/config"
	"wangfujing_admin/internal/models"
	"wangfujing_admin/internal/services"
	"wangfujing_admin/pkg/response"

	"github.com/gin-gonic/gin"
	"gorm.io/gorm"
)

// PurchaseProductRequest 抢购商品请求
type PurchaseProductRequest struct {
	ProductID uint64 `json:"product_id" binding:"required"`
}

// PurchaseProduct 抢购商品（高并发版本）
func (h *Handler) PurchaseProduct(c *gin.Context) {
	var req PurchaseProductRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		response.ValidateError(c, err)
		return
	}

	// 获取当前用户ID
	userIDInterface, exists := c.Get("user_id")
	if !exists {
		response.Unauthorized(c, "请先登录")
		return
	}

	// 转换用户ID类型
	userIDStr, ok := userIDInterface.(string)
	if !ok {
		response.InternalServerError(c, "用户ID格式错误")
		return
	}

	userID, err := strconv.ParseUint(userIDStr, 10, 64)
	if err != nil {
		response.InternalServerError(c, "用户ID转换失败")
		return
	}

	// 获取商品信息
	var product models.Product
	if err := h.db.Preload("Merchant").
		Where("id = ? AND type = ? AND status = ? AND approval_status = ?",
			req.ProductID, models.ProductTypeMerchant, models.StatusActive, models.ApprovalStatusApproved).
		First(&product).Error; err != nil {
		if err == gorm.ErrRecordNotFound {
			response.NotFound(c, "商品不存在或已下架")
			return
		}
		response.InternalServerError(c, "获取商品信息失败")
		return
	}

	// 检查商品是否在有效期内
	now := time.Now()
	if product.ValidFrom != nil && now.Before(*product.ValidFrom) {
		response.ErrorWithCode(c, 400, response.CodeProductExpired, "商品尚未开始")
		return
	}
	if product.ValidTo != nil && now.After(*product.ValidTo) {
		response.ErrorWithCode(c, 400, response.CodeProductExpired, "商品已过期")
		return
	}

	// 获取用户信息
	var user models.User
	if err := h.db.Where("id = ?", userID).First(&user).Error; err != nil {
		response.InternalServerError(c, "获取用户信息失败")
		return
	}

	// 检查用户积分是否足够
	if user.Points < product.Points {
		response.ErrorWithCode(c, 400, response.CodeInvalidParams, "积分不足")
		return
	}

	// 使用高并发秒杀服务处理抢购（需要传递配置）
	// 这里需要从Handler中获取配置，暂时使用默认配置
	defaultConfig := &config.SeckillConfig{
		GlobalRateLimit:   1000,
		GlobalBucketSize:  2000,
		ProductRateLimit:  500,
		ProductBucketSize: 1000,
	}
	seckillService := services.NewHighConcurrencySeckillService(h.db, h.rdb, defaultConfig)
	purchaseResp, err := seckillService.Purchase(c.Request.Context(), req.ProductID, userID)
	if err != nil {
		response.InternalServerError(c, "抢购失败: "+err.Error())
		return
	}

	// 如果直接失败（限流或队列满）
	if purchaseResp.Status == "failed" {
		response.ErrorWithCode(c, 429, response.CodeInvalidParams, purchaseResp.Message)
		return
	}

	// 返回排队信息
	response.Success(c, gin.H{
		"status":     purchaseResp.Status,
		"position":   purchaseResp.Position,
		"request_id": purchaseResp.RequestID,
		"message":    purchaseResp.Message,
	})
}

// CheckPurchaseResult 查询抢购结果
func (h *Handler) CheckPurchaseResult(c *gin.Context) {
	requestID := c.Query("request_id")
	if requestID == "" {
		response.ErrorWithCode(c, 400, response.CodeInvalidParams, "请求ID不能为空")
		return
	}

	// 获取当前用户ID
	userID, exists := c.Get("user_id")
	if !exists {
		response.Unauthorized(c, "请先登录")
		return
	}

	// 检查结果
	defaultConfig := &config.SeckillConfig{
		GlobalRateLimit:   1000,
		GlobalBucketSize:  2000,
		ProductRateLimit:  500,
		ProductBucketSize: 1000,
	}
	seckillService := services.NewHighConcurrencySeckillService(h.db, h.rdb, defaultConfig)
	status, err := seckillService.CheckResult(c.Request.Context(), requestID)
	if err != nil {
		response.InternalServerError(c, "查询结果失败")
		return
	}

	result := gin.H{
		"request_id": requestID,
		"status":     status,
	}

	// 根据状态返回不同信息
	switch status {
	case "queued":
		result["message"] = "排队中，请稍后查询"
	case "success":
		// 抢购成功，查询已创建的订单
		userIDStr, ok := userID.(string)
		if !ok {
			result["status"] = "error"
			result["message"] = "用户ID格式错误"
		} else {
			userIDUint, err := strconv.ParseUint(userIDStr, 10, 64)
			if err != nil {
				result["status"] = "error"
				result["message"] = "用户ID转换失败"
			} else {
				orderResp, err := h.getOrderByRequestID(c, requestID, userIDUint)
				if err != nil {
					result["status"] = "error"
					result["message"] = "查询订单失败: " + err.Error()
				} else {
					result["order"] = orderResp
					result["message"] = "抢购成功"
				}
			}
		}
	case "sold_out":
		result["message"] = "商品已售完"
	case "not_found":
		result["message"] = "请求不存在或已过期"
	default:
		if len(status) > 6 && status[:6] == "error:" {
			result["message"] = status[6:]
		} else {
			result["message"] = "未知状态"
		}
	}

	response.Success(c, result)
}

// GetQueuePosition 获取队列位置
func (h *Handler) GetQueuePosition(c *gin.Context) {
	requestID := c.Query("request_id")
	productIDStr := c.Query("product_id")

	if requestID == "" || productIDStr == "" {
		response.ErrorWithCode(c, 400, response.CodeInvalidParams, "参数不完整")
		return
	}

	productID, err := strconv.ParseUint(productIDStr, 10, 64)
	if err != nil {
		response.ErrorWithCode(c, 400, response.CodeInvalidParams, "商品ID格式错误")
		return
	}

	// 获取队列位置
	defaultConfig := &config.SeckillConfig{
		GlobalRateLimit:   1000,
		GlobalBucketSize:  2000,
		ProductRateLimit:  500,
		ProductBucketSize: 1000,
	}
	seckillService := services.NewHighConcurrencySeckillService(h.db, h.rdb, defaultConfig)
	position, err := seckillService.GetQueuePosition(c.Request.Context(), productID, requestID)
	if err != nil {
		response.ErrorWithCode(c, 404, response.CodeNotFound, "请求不在队列中")
		return
	}

	response.Success(c, gin.H{
		"request_id": requestID,
		"position":   position,
		"message":    fmt.Sprintf("当前排队位置：%d", position),
	})
}

// getOrderByRequestID 通过请求ID查询订单
func (h *Handler) getOrderByRequestID(c *gin.Context, requestID string, userID uint64) (gin.H, error) {
	var order models.Order

	// 首先尝试通过request_id查询
	err := h.db.Preload("Product").Preload("Merchant").
		Where("request_id = ? AND user_id = ?", requestID, userID).
		First(&order).Error

	if err == gorm.ErrRecordNotFound {
		// 如果通过request_id找不到，尝试查找该用户最近5分钟内的订单
		fiveMinutesAgo := time.Now().Add(-5 * time.Minute)
		err = h.db.Preload("Product").Preload("Merchant").
			Where("user_id = ? AND created_at > ?", userID, fiveMinutesAgo).
			Order("created_at DESC").
			First(&order).Error
	}

	if err != nil {
		if err == gorm.ErrRecordNotFound {
			return nil, fmt.Errorf("订单不存在")
		}
		return nil, fmt.Errorf("查询订单失败: %w", err)
	}

	// 构建商家信息
	var merchantInfo gin.H
	if order.Merchant != nil {
		merchantInfo = gin.H{
			"id":   order.Merchant.ID,
			"name": order.Merchant.Name,
		}
	}

	// 返回订单信息
	return gin.H{
		"order_id":    order.ID,
		"order_no":    order.OrderNo,
		"request_id":  order.RequestID,
		"qr_code_url": order.QRCodeURL,
		"verify_code": order.VerifyCode,
		"points":      order.Points,
		"status":      order.GetStatusText(),
		"created_at":  order.CreatedAt.Format("2006-01-02 15:04:05"),
		"product": gin.H{
			"id":   order.Product.ID,
			"name": order.Product.Name,
		},
		"merchant": merchantInfo,
	}, nil
}

// createSeckillOrder 创建秒杀订单（异步处理成功后调用）
func (h *Handler) createSeckillOrder(c *gin.Context, requestID string, userID uint64) (gin.H, error) {
	// 从requestID中解析productID
	// requestID格式: {productID}_{userID}_{timestamp}_{random}
	parts := strings.Split(requestID, "_")
	if len(parts) < 2 {
		return nil, fmt.Errorf("invalid request ID format")
	}

	productID, err := strconv.ParseUint(parts[0], 10, 64)
	if err != nil {
		return nil, fmt.Errorf("invalid product ID in request ID")
	}

	// 获取商品信息
	var product models.Product
	if err := h.db.Preload("Merchant").First(&product, productID).Error; err != nil {
		return nil, fmt.Errorf("product not found")
	}

	// 获取用户信息
	var user models.User
	if err := h.db.First(&user, userID).Error; err != nil {
		return nil, fmt.Errorf("user not found")
	}

	// 开始数据库事务
	tx := h.db.Begin()
	defer func() {
		if r := recover(); r != nil {
			tx.Rollback()
		}
	}()

	// 扣减用户积分
	if err := tx.Model(&user).Update("points", gorm.Expr("points - ?", product.Points)).Error; err != nil {
		tx.Rollback()
		return nil, fmt.Errorf("failed to deduct points")
	}

	// 生成订单号
	orderNo := fmt.Sprintf("SK%d%d", time.Now().Unix(), userID%1000)

	// 生成二维码内容
	qrCode := fmt.Sprintf("SECKILL_ORDER:%s", orderNo)

	// 创建订单
	order := &models.Order{
		OrderNo:    orderNo,
		RequestID:  requestID, // 保存请求ID，建立关联
		UserID:     userID,
		ProductID:  productID,
		MerchantID: product.MerchantID,
		Quantity:   1,
		Price:      0, // 积分兑换，价格为0
		TotalPrice: 0,
		Points:     product.Points,
		Status:     models.OrderStatusPending,
		QRCode:     qrCode,
	}

	if err := tx.Create(order).Error; err != nil {
		tx.Rollback()
		return nil, fmt.Errorf("failed to create order")
	}

	// 创建积分记录
	pointsRecord := &models.PointsRecord{
		UserID:      userID,
		Type:        models.PointsTypeSpend,
		Points:      product.Points,
		Balance:     user.Points - product.Points,
		Source:      "秒杀商品",
		SourceID:    &productID,
		RelatedID:   &order.ID,
		RelatedType: "seckill_order",
		Description: fmt.Sprintf("秒杀兑换商品：%s", product.Name),
	}

	if err := tx.Create(pointsRecord).Error; err != nil {
		tx.Rollback()
		return nil, fmt.Errorf("failed to create points record")
	}

	// 提交事务
	if err := tx.Commit().Error; err != nil {
		return nil, fmt.Errorf("failed to commit transaction")
	}

	// 返回订单信息
	return gin.H{
		"order_id":   order.ID,
		"order_no":   order.OrderNo,
		"qr_code":    order.QRCode,
		"points":     order.Points,
		"created_at": order.CreatedAt.Format("2006-01-02 15:04:05"),
		"product": gin.H{
			"id":   product.ID,
			"name": product.Name,
		},
	}, nil
}
