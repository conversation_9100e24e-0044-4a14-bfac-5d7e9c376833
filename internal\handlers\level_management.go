package handlers

import (
	"net/http"
	"strconv"
	"wangfujing_admin/internal/models"
	"wangfujing_admin/internal/services"
	"wangfujing_admin/pkg/response"

	"github.com/gin-gonic/gin"
)

// LevelManagementHandler 等级管理处理器
type LevelManagementHandler struct {
	levelService *services.LevelManagementService
}

// NewLevelManagementHandler 创建等级管理处理器
func NewLevelManagementHandler(levelService *services.LevelManagementService) *LevelManagementHandler {
	return &LevelManagementHandler{
		levelService: levelService,
	}
}

// GetScoreRules 获取分值规则
func (h *LevelManagementHandler) GetScoreRules(c *gin.Context) {
	rule, err := h.levelService.GetScoreRules(c.Request.Context())
	if err != nil {
		response.Error(c, http.StatusInternalServerError, "获取分值规则失败: "+err.Error())
		return
	}

	response.SuccessWithMessage(c, "获取分值规则成功", rule)
}

// UpdateScoreRules 更新分值规则
func (h *LevelManagementHandler) UpdateScoreRules(c *gin.Context) {
	var req services.UpdateScoreRulesRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		response.Error(c, http.StatusBadRequest, "参数错误: "+err.Error())
		return
	}

	rule, err := h.levelService.UpdateScoreRules(c.Request.Context(), &req)
	if err != nil {
		response.Error(c, http.StatusInternalServerError, "更新分值规则失败: "+err.Error())
		return
	}

	response.SuccessWithMessage(c, "更新分值规则成功", rule)
}

// GetScoreItems 获取增减分项目
func (h *LevelManagementHandler) GetScoreItems(c *gin.Context) {
	// 获取type参数
	var scoreType *models.ScoreType
	if typeStr := c.Query("type"); typeStr != "" {
		if typeInt, err := strconv.Atoi(typeStr); err == nil {
			if typeInt == 1 || typeInt == 2 {
				st := models.ScoreType(typeInt)
				scoreType = &st
			} else {
				response.Error(c, http.StatusBadRequest, "无效的type参数，只能是1（加分）或2（扣分）")
				return
			}
		} else {
			response.Error(c, http.StatusBadRequest, "type参数格式错误")
			return
		}
	}

	items, err := h.levelService.GetScoreItems(c.Request.Context(), scoreType)
	if err != nil {
		response.Error(c, http.StatusInternalServerError, "获取增减分项目失败: "+err.Error())
		return
	}

	response.SuccessWithMessage(c, "获取增减分项目成功", gin.H{"items": items})
}

// CreateScoreItem 创建增减分项目
func (h *LevelManagementHandler) CreateScoreItem(c *gin.Context) {
	var req services.CreateScoreItemRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		response.Error(c, http.StatusBadRequest, "参数错误: "+err.Error())
		return
	}

	item, err := h.levelService.CreateScoreItem(c.Request.Context(), &req)
	if err != nil {
		response.Error(c, http.StatusInternalServerError, "创建增减分项目失败: "+err.Error())
		return
	}

	response.SuccessWithMessage(c, "创建增减分项目成功", item)
}

// UpdateScoreItem 更新增减分项目
func (h *LevelManagementHandler) UpdateScoreItem(c *gin.Context) {
	idStr := c.Param("id")
	id, err := strconv.ParseUint(idStr, 10, 64)
	if err != nil {
		response.Error(c, http.StatusBadRequest, "无效的项目ID")
		return
	}

	var req services.UpdateScoreItemRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		response.Error(c, http.StatusBadRequest, "参数错误: "+err.Error())
		return
	}

	item, err := h.levelService.UpdateScoreItem(c.Request.Context(), id, &req)
	if err != nil {
		response.Error(c, http.StatusInternalServerError, "更新增减分项目失败: "+err.Error())
		return
	}

	response.SuccessWithMessage(c, "更新增减分项目成功", item)
}

// DeleteScoreItem 删除增减分项目
func (h *LevelManagementHandler) DeleteScoreItem(c *gin.Context) {
	idStr := c.Param("id")
	id, err := strconv.ParseUint(idStr, 10, 64)
	if err != nil {
		response.Error(c, http.StatusBadRequest, "无效的项目ID")
		return
	}

	if err := h.levelService.DeleteScoreItem(c.Request.Context(), id); err != nil {
		response.Error(c, http.StatusInternalServerError, "删除增减分项目失败: "+err.Error())
		return
	}

	response.SuccessWithMessage(c, "删除增减分项目成功", nil)
}

// GetLevelBenefit 获取等级权益（全局配置）
func (h *LevelManagementHandler) GetLevelBenefit(c *gin.Context) {
	benefit, err := h.levelService.GetLevelBenefit(c.Request.Context())
	if err != nil {
		response.Error(c, http.StatusInternalServerError, "获取等级权益失败: "+err.Error())
		return
	}

	response.SuccessWithMessage(c, "获取等级权益成功", benefit)
}

// UpdateLevelBenefit 更新等级权益（全局配置）
func (h *LevelManagementHandler) UpdateLevelBenefit(c *gin.Context) {
	var req services.UpdateLevelBenefitRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		response.Error(c, http.StatusBadRequest, "参数错误: "+err.Error())
		return
	}

	benefit, err := h.levelService.UpdateLevelBenefit(c.Request.Context(), &req)
	if err != nil {
		response.Error(c, http.StatusInternalServerError, "更新等级权益失败: "+err.Error())
		return
	}

	response.SuccessWithMessage(c, "更新等级权益成功", benefit)
}

// CreateLevelBenefit 创建等级权益
func (h *LevelManagementHandler) CreateLevelBenefit(c *gin.Context) {
	var req services.CreateLevelBenefitRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		response.Error(c, http.StatusBadRequest, "参数错误: "+err.Error())
		return
	}

	benefit, err := h.levelService.CreateLevelBenefit(c.Request.Context(), &req)
	if err != nil {
		response.Error(c, http.StatusInternalServerError, "创建等级权益失败: "+err.Error())
		return
	}

	response.SuccessWithMessage(c, "创建等级权益成功", benefit)
}

// DeleteLevelBenefit 删除等级权益（全局配置）
func (h *LevelManagementHandler) DeleteLevelBenefit(c *gin.Context) {
	if err := h.levelService.DeleteLevelBenefit(c.Request.Context()); err != nil {
		response.Error(c, http.StatusInternalServerError, "删除等级权益失败: "+err.Error())
		return
	}

	response.SuccessWithMessage(c, "删除等级权益成功", nil)
}

// ManageLevelBenefits 统一的等级权益管理接口
func (h *LevelManagementHandler) ManageLevelBenefits(c *gin.Context) {
	switch c.Request.Method {
	case "GET":
		h.GetLevelBenefit(c)
	case "PUT":
		h.UpdateLevelBenefit(c)
	default:
		response.Error(c, http.StatusMethodNotAllowed, "不支持的请求方法")
	}
}
