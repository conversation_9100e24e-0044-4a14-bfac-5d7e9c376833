package handlers

import (
	"crypto/hmac"
	"crypto/sha256"
	"encoding/hex"
	"encoding/json"
	"fmt"
	"strconv"
	"strings"
	"time"

	"wangfujing_admin/internal/models"
	"wangfujing_admin/pkg/response"

	"github.com/gin-gonic/gin"
	"gorm.io/gorm"
)

// GetMallVerifyRecords 获取商场核销记录列表
func (h *Handler) GetMallVerifyRecords(c *gin.Context) {
	// 获取查询参数
	page, _ := strconv.Atoi(c.Default<PERSON>uery("page", "1"))
	size, _ := strconv.Atoi(c.Default<PERSON>("size", "10"))
	startDate := c.Query("start_date")
	endDate := c.Query("end_date")

	if page < 1 {
		page = 1
	}
	if size < 1 || size > 100 {
		size = 10
	}

	// 构建查询 - 查询核销记录（只包含有订单的记录）
	query := h.db.Model(&models.VerifyRecord{}).
		Preload("User").
		Preload("Order").
		Where("order_id > 0") // 只查询有订单的核销记录

	// 日期筛选
	if startDate != "" {
		query = query.Where("created_at >= ?", startDate+" 00:00:00")
	}
	if endDate != "" {
		query = query.Where("created_at <= ?", endDate+" 23:59:59")
	}

	// 获取总数
	var total int64
	if err := query.Count(&total).Error; err != nil {
		response.InternalServerError(c, "查询记录总数失败")
		return
	}

	// 获取记录列表
	var records []models.VerifyRecord
	offset := (page - 1) * size
	if err := query.Order("created_at DESC").Offset(offset).Limit(size).Find(&records).Error; err != nil {
		response.InternalServerError(c, "查询核销记录失败")
		return
	}

	// 构建返回数据
	var recordList []gin.H
	for _, record := range records {
		recordData := gin.H{
			"id":               record.ID,
			"verify_time":      record.CreatedAt.Format("2006-01-02 15:04:05"),
			"verify_type":      record.VerifyType,
			"verify_type_name": record.VerifyType.String(),
			"location":         record.Location,
			"remark":           record.Remark,
			"points":           record.Points,
			"verify_method":    record.VerifyMethod,
			"user": gin.H{
				"id":       record.User.ID,
				"nickname": record.User.Nickname,
				"phone":    record.User.Phone,
				"avatar":   record.User.Avatar,
			},
		}

		// 添加订单信息
		if record.OrderID > 0 {
			recordData["order"] = gin.H{
				"id":         record.Order.ID,
				"order_no":   record.Order.OrderNo,
				"points":     record.Order.Points,
				"order_type": record.Order.OrderType,
			}
		}

		// 根据核销类型添加不同的信息
		switch record.VerifyType {
		case models.VerifyRecordTypePointsMall:
			// 积分商城核销
			if record.ProductID != nil {
				var pointsMallItem models.PointsMallItem
				if err := h.db.Where("id = ?", *record.ProductID).First(&pointsMallItem).Error; err == nil {
					recordData["product"] = gin.H{
						"id":          pointsMallItem.ID,
						"name":        pointsMallItem.Name,
						"description": pointsMallItem.Description,
						"points":      pointsMallItem.Points,
						"valid_to":    formatTimePtr(pointsMallItem.EndTime),
					}
				}
			}
		case models.VerifyRecordTypeMerchant:
			// 商家核销
			if record.ProductID != nil {
				var product models.Product
				if err := h.db.Where("id = ?", *record.ProductID).First(&product).Error; err == nil {
					recordData["product"] = gin.H{
						"id":          product.ID,
						"name":        product.Name,
						"description": product.Description,
						"points":      product.Points,
					}
				}
			}
			if record.MerchantID != nil {
				var merchant models.Merchant
				if err := h.db.Where("id = ?", *record.MerchantID).First(&merchant).Error; err == nil {
					recordData["merchant"] = gin.H{
						"id":   merchant.ID,
						"name": merchant.Name,
					}
				}
			}
		case models.VerifyRecordTypeActivity:
			// 活动核销
			if record.ActivityID != nil {
				recordData["activity_id"] = *record.ActivityID
			}
		case models.VerifyRecordTypeNPC:
			// NPC送积分
			recordData["npc_points"] = record.Points
		}

		recordList = append(recordList, recordData)
	}

	response.Success(c, gin.H{
		"list":  recordList,
		"total": total,
		"page":  page,
		"size":  size,
	})
}

// ScanQRCode 扫描二维码（商场端）
func (h *Handler) ScanQRCode(c *gin.Context) {
	var req struct {
		QRContent string `json:"qr_content" binding:"required"`
	}

	if err := c.ShouldBindJSON(&req); err != nil {
		response.BadRequest(c, "参数错误: "+err.Error())
		return
	}

	fmt.Printf("商场扫码 - QRContent: %s\n", req.QRContent)

	// 检查是否为用户个人二维码（纯数字，用于NPC送积分）
	if userID, err := strconv.ParseUint(req.QRContent, 10, 64); err == nil {
		h.handleUserQRCodeByID(c, userID)
		return
	}

	// 解析完整的订单二维码内容（包含安全验证）
	if err := h.handleSecureOrderQRCode(c, req.QRContent); err != nil {
		response.BadRequest(c, err.Error())
		return
	}
}

// handleSecureOrderQRCode 处理安全的订单二维码（包含完整验证）
func (h *Handler) handleSecureOrderQRCode(c *gin.Context, qrContent string) error {
	// 解析二维码内容
	parts := strings.Split(qrContent, "|")
	fmt.Printf("商场二维码解析: 总共%d个部分\n", len(parts))
	for i, part := range parts {
		fmt.Printf("  部分%d: %s\n", i, part)
	}

	if len(parts) != 9 {
		fmt.Printf("商场二维码格式错误: 期望9个部分，实际%d个部分\n", len(parts))
		return fmt.Errorf("无效的二维码格式")
	}

	// 解析订单ID
	orderID, err := strconv.ParseUint(parts[0], 10, 64)
	if err != nil {
		fmt.Printf("解析订单ID失败: %v\n", err)
		return fmt.Errorf("无效的订单ID")
	}

	// 获取订单号
	orderNo := parts[1]

	// 获取用户ID
	userID, err := strconv.ParseUint(parts[2], 10, 64)
	if err != nil {
		return fmt.Errorf("无效的用户ID")
	}

	// 获取商品ID
	productID, err := strconv.ParseUint(parts[3], 10, 64)
	if err != nil {
		return fmt.Errorf("无效的商品ID")
	}

	// 获取核销码
	verifyCode := parts[5]

	// 获取时间戳
	timestamp, err := strconv.ParseInt(parts[6], 10, 64)
	if err != nil {
		return fmt.Errorf("无效的时间戳")
	}

	// 获取过期时间
	expiresAt, err := strconv.ParseInt(parts[7], 10, 64)
	if err != nil {
		return fmt.Errorf("无效的过期时间")
	}

	// 获取签名
	signature := parts[8]

	fmt.Printf("商场解析结果: orderID=%d, orderNo=%s, userID=%d, productID=%d, verifyCode=%s, signature=%s\n",
		orderID, orderNo, userID, productID, verifyCode, signature)

	// 验证HMAC签名
	signStr := fmt.Sprintf("%d|%s|%d|%d|0|%s|%d|%d", orderID, orderNo, userID, productID, verifyCode, timestamp, expiresAt)
	secretKey := "wangfujing_qrcode_secret_key_2024" // 与生成时使用相同的密钥

	// 正确的HMAC计算方式
	hmacHash := hmac.New(sha256.New, []byte(secretKey))
	hmacHash.Write([]byte(signStr))
	expectedSign := fmt.Sprintf("%x", hmacHash.Sum(nil))

	fmt.Printf("商场签名验证: signStr=%s, expectedSign=%s, actualSign=%s\n", signStr, expectedSign, signature)

	if !strings.EqualFold(expectedSign, signature) {
		fmt.Printf("商场签名验证失败\n")
		return fmt.Errorf("签名验证失败")
	}

	// 查询订单信息
	var order models.Order
	if err := h.db.Preload("User").Where("id = ? AND order_no = ? AND user_id = ? AND verify_code = ?",
		orderID, orderNo, userID, verifyCode).First(&order).Error; err != nil {
		if err == gorm.ErrRecordNotFound {
			return fmt.Errorf("订单不存在或验证信息不匹配")
		}
		return fmt.Errorf("查询订单失败")
	}

	// 检查订单状态
	if order.IsVerified() {
		return fmt.Errorf("订单已核销")
	}

	if order.Status == models.OrderStatusCancelled {
		return fmt.Errorf("订单已取消")
	}

	// 检查订单类型（只处理积分商城订单）
	if order.OrderType != models.ProductTypeMall {
		return fmt.Errorf("此订单不是积分商城订单")
	}

	// 查询积分商城商品信息
	var pointsMallItem models.PointsMallItem
	if err := h.db.Where("id = ?", productID).First(&pointsMallItem).Error; err != nil {
		return fmt.Errorf("查询商品信息失败")
	}

	// 根据商品类型验证有效期
	now := time.Now()
	if err := h.validateProductExpiry(&pointsMallItem, &order, now, expiresAt); err != nil {
		return err
	}

	// 解析商品图片
	var images []string
	if pointsMallItem.Images != "" {
		json.Unmarshal([]byte(pointsMallItem.Images), &images)
	}

	// 返回订单详情供确认
	response.Success(c, gin.H{
		"type": "points_mall",
		"order": gin.H{
			"id":         order.ID,
			"order_no":   order.OrderNo,
			"points":     order.Points,
			"status":     "待核销",
			"created_at": order.CreatedAt.Format("2006-01-02 15:04:05"),
		},
		"user": gin.H{
			"id":       order.User.ID,
			"nickname": order.User.Nickname,
			"phone":    order.User.Phone,
			"avatar":   order.User.Avatar,
		},
		"product": gin.H{
			"id":          pointsMallItem.ID,
			"name":        pointsMallItem.Name,
			"description": pointsMallItem.Description,
			"images":      images,
			"points":      pointsMallItem.Points,
			"valid_from":  formatTimePtr(pointsMallItem.StartTime),
			"valid_to":    formatTimePtr(pointsMallItem.EndTime),
		},
	})

	return nil
}

// handleUserQRCodeByID 处理用户个人二维码（通过用户ID）
func (h *Handler) handleUserQRCodeByID(c *gin.Context, userID uint64) {
	// 查询用户信息
	var user models.User
	if err := h.db.Where("id = ?", userID).First(&user).Error; err != nil {
		if err == gorm.ErrRecordNotFound {
			response.NotFound(c, "用户不存在")
			return
		}
		response.InternalServerError(c, "查询用户失败")
		return
	}

	// 返回用户信息供NPC送积分
	response.Success(c, gin.H{
		"type": "npc",
		"user": gin.H{
			"id":       user.ID,
			"nickname": user.Nickname,
			"phone":    user.Phone,
			"avatar":   user.Avatar,
			"points":   user.Points,
		},
	})
}

// validateProductExpiry 验证商品有效期（根据商品类型使用不同的验证逻辑）
func (h *Handler) validateProductExpiry(item *models.PointsMallItem, order *models.Order, now time.Time, qrExpiresAt int64) error {
	switch item.Type {
	case models.PointsMallItemTypeAvatar:
		// VIP形象：有效期是当天（兑换当日有效）
		orderDate := order.CreatedAt.Truncate(24 * time.Hour) // 订单创建日期（去掉时分秒）
		currentDate := now.Truncate(24 * time.Hour)           // 当前日期（去掉时分秒）

		if currentDate.After(orderDate) {
			return fmt.Errorf("VIP形象已过期（仅限兑换当日使用）")
		}

		// VIP形象也验证二维码过期时间（二维码过期时间已设置为当天23:59:59）
		currentTime := now.Unix()
		if currentTime > qrExpiresAt {
			return fmt.Errorf("VIP形象二维码已过期")
		}

		fmt.Printf("VIP形象有效期验证通过 - 订单日期: %s, 当前日期: %s, 二维码过期时间: %d\n",
			orderDate.Format("2006-01-02"), currentDate.Format("2006-01-02"), qrExpiresAt)

	case models.PointsMallItemTypeProduct:
		// 普通商品：使用商品设置的有效期 + 二维码过期时间双重验证
		if item.EndTime != nil && now.After(*item.EndTime) {
			return fmt.Errorf("商品已过期")
		}

		// 验证二维码过期时间（应该与商品有效期一致）
		currentTime := now.Unix()
		if currentTime > qrExpiresAt {
			return fmt.Errorf("二维码已过期")
		}

		fmt.Printf("普通商品有效期验证通过 - 商品有效期: %v, 二维码过期时间: %d\n",
			item.EndTime, qrExpiresAt)

	default:
		// 未知类型，使用二维码过期时间
		currentTime := now.Unix()
		if currentTime > qrExpiresAt {
			return fmt.Errorf("二维码已过期")
		}
	}

	return nil
}

// formatTimePtr 格式化时间指针为字符串
func formatTimePtr(t *time.Time) string {
	if t == nil {
		return ""
	}
	return t.Format("2006-01-02 15:04:05")
}

// VerifyPointsMallOrder 核销积分商城订单
func (h *Handler) VerifyPointsMallOrder(c *gin.Context) {
	var req struct {
		OrderID uint64 `json:"order_id" binding:"required"`
		Remark  string `json:"remark"`
	}

	if err := c.ShouldBindJSON(&req); err != nil {
		response.BadRequest(c, "参数错误: "+err.Error())
		return
	}

	// 获取核销人ID（商场管理员）
	verifierID, exists := c.Get("user_id")
	if !exists {
		response.Unauthorized(c, "请先登录")
		return
	}

	var verifierIDUint64 uint64
	switch v := verifierID.(type) {
	case string:
		if id, err := strconv.ParseUint(v, 10, 64); err != nil {
			response.BadRequest(c, "用户ID格式错误")
			return
		} else {
			verifierIDUint64 = id
		}
	case uint64:
		verifierIDUint64 = v
	default:
		response.BadRequest(c, "用户ID类型错误")
		return
	}

	// 开始事务
	tx := h.db.Begin()
	defer func() {
		if r := recover(); r != nil {
			tx.Rollback()
		}
	}()

	// 查询订单
	var order models.Order
	if err := tx.Preload("User").Where("id = ?", req.OrderID).First(&order).Error; err != nil {
		tx.Rollback()
		if err == gorm.ErrRecordNotFound {
			response.NotFound(c, "订单不存在")
			return
		}
		response.InternalServerError(c, "查询订单失败")
		return
	}

	// 检查订单状态
	if order.IsVerified() {
		tx.Rollback()
		response.BadRequest(c, "订单已核销")
		return
	}

	if order.OrderType != models.ProductTypeMall {
		tx.Rollback()
		response.BadRequest(c, "此订单不是积分商城订单")
		return
	}

	// 更新订单状态
	now := time.Now()
	if err := tx.Model(&order).Updates(map[string]interface{}{
		"status":        models.OrderStatusCompleted,
		"verified_at":   &now,
		"verifier_id":   verifierIDUint64,
		"verify_remark": req.Remark,
		"completed_at":  &now,
	}).Error; err != nil {
		tx.Rollback()
		response.InternalServerError(c, "更新订单状态失败")
		return
	}

	// 创建核销记录（使用扩展字段）
	verifyRecord := models.VerifyRecord{
		OrderID:          order.ID,
		UserID:           order.UserID,
		ScannerID:        verifierIDUint64,
		Location:         "商场",
		Remark:           req.Remark,
		VerifyType:       models.VerifyRecordTypePointsMall,
		Points:           order.Points,
		PointsChangeType: models.PointsTypeSpend, // 积分商城是消费积分
		Price:            order.TotalPrice,
		ProductID:        &order.ProductID,
		VerifyMethod:     "qr_code",
	}

	if err := tx.Create(&verifyRecord).Error; err != nil {
		tx.Rollback()
		response.InternalServerError(c, "创建核销记录失败")
		return
	}

	// 更新对应的积分记录，关联核销记录ID
	if err := tx.Model(&models.PointsRecord{}).
		Where("related_id = ? AND related_type = ? AND source = ?", order.ID, "order", "积分商城").
		Update("verification_id", verifyRecord.ID).Error; err != nil {
		tx.Rollback()
		response.InternalServerError(c, "更新积分记录失败")
		return
	}

	// 提交事务
	if err := tx.Commit().Error; err != nil {
		response.InternalServerError(c, "核销失败")
		return
	}

	fmt.Printf("积分商城订单核销成功 - 订单: %s, 用户: %d, 核销人: %d\n",
		order.OrderNo, order.UserID, verifierIDUint64)

	response.SuccessWithMessage(c, "核销成功", gin.H{
		"order_id":    order.ID,
		"order_no":    order.OrderNo,
		"verified_at": now.Format("2006-01-02 15:04:05"),
	})
}

// GiveNPCPoints NPC送积分
func (h *Handler) GiveNPCPoints(c *gin.Context) {
	var req struct {
		QRData string `json:"qr_data" binding:"required"`              // 个人二维码数据（必填）
		Points int    `json:"points" binding:"required,min=1,max=100"` // 积分数量
		Remark string `json:"remark"`                                  // 备注
	}

	if err := c.ShouldBindJSON(&req); err != nil {
		response.BadRequest(c, "参数错误: "+err.Error())
		return
	}

	// 解析个人二维码数据，获取用户ID
	if h.personalQRService == nil {
		response.InternalServerError(c, "个人二维码服务未初始化")
		return
	}

	qrInfo, err := h.personalQRService.ParsePersonalQR(req.QRData)
	if err != nil {
		response.BadRequest(c, "个人二维码解析失败: "+err.Error())
		return
	}

	targetUserID := qrInfo.UserID
	fmt.Printf("通过个人二维码获取用户ID: %d\n", targetUserID)

	// 获取操作人ID（NPC/商场管理员）
	operatorID, exists := c.Get("user_id")
	if !exists {
		response.Unauthorized(c, "请先登录")
		return
	}

	var operatorIDUint64 uint64
	switch v := operatorID.(type) {
	case string:
		if id, err := strconv.ParseUint(v, 10, 64); err != nil {
			response.BadRequest(c, "用户ID格式错误")
			return
		} else {
			operatorIDUint64 = id
		}
	case uint64:
		operatorIDUint64 = v
	default:
		response.BadRequest(c, "用户ID类型错误")
		return
	}

	// 开始事务
	tx := h.db.Begin()
	defer func() {
		if r := recover(); r != nil {
			tx.Rollback()
		}
	}()

	// 查询用户
	var user models.User
	if err := tx.Where("id = ?", targetUserID).First(&user).Error; err != nil {
		tx.Rollback()
		if err == gorm.ErrRecordNotFound {
			response.NotFound(c, "用户不存在")
			return
		}
		response.InternalServerError(c, "查询用户失败")
		return
	}

	// 增加用户积分
	if err := tx.Model(&user).Update("points", gorm.Expr("points + ?", req.Points)).Error; err != nil {
		tx.Rollback()
		response.InternalServerError(c, "增加积分失败")
		return
	}

	// 创建核销记录（不关联订单）
	verifyRecord := models.VerifyRecord{
		OrderID:          0, // 不关联订单
		UserID:           targetUserID,
		ScannerID:        operatorIDUint64,
		Location:         "商场-NPC送积分",
		Remark:           fmt.Sprintf("NPC送积分: %s", req.Remark),
		VerifyType:       models.VerifyRecordTypeNPC,
		Points:           req.Points,
		PointsChangeType: models.PointsTypeEarn, // NPC送积分是获得积分
		Price:            0,                     // NPC送积分无价格
		VerifyMethod:     "manual",
	}

	if err := tx.Create(&verifyRecord).Error; err != nil {
		tx.Rollback()
		response.InternalServerError(c, "创建核销记录失败")
		return
	}

	// 创建积分记录（关联核销记录）
	pointsRecord := models.PointsRecord{
		UserID:         targetUserID,
		Type:           models.PointsTypeEarn,
		Points:         req.Points,
		Balance:        user.Points + req.Points,
		Source:         "NPC送积分",
		SourceID:       &operatorIDUint64,
		RelatedID:      &verifyRecord.ID, // 关联核销记录ID
		RelatedType:    "verify_record",
		Description:    fmt.Sprintf("NPC送积分: %s", req.Remark),
		VerificationID: &verifyRecord.ID, // 关联核销记录
	}

	if err := tx.Create(&pointsRecord).Error; err != nil {
		tx.Rollback()
		response.InternalServerError(c, "创建积分记录失败")
		return
	}

	// 提交事务
	if err := tx.Commit().Error; err != nil {
		response.InternalServerError(c, "送积分失败")
		return
	}

	fmt.Printf("NPC送积分成功 - 用户: %d, 积分: %d, 操作人: %d\n",
		targetUserID, req.Points, operatorIDUint64)

	response.SuccessWithMessage(c, "送积分成功", gin.H{
		"user_id":     targetUserID,
		"points":      req.Points,
		"new_balance": user.Points + req.Points,
	})
}

// GetPersonalQR 获取用户个人二维码
func (h *Handler) GetPersonalQR(c *gin.Context) {
	// 获取当前用户ID
	userID, exists := c.Get("user_id")
	if !exists {
		response.Unauthorized(c, "未登录")
		return
	}

	var userIDUint64 uint64
	switch v := userID.(type) {
	case string:
		if id, err := strconv.ParseUint(v, 10, 64); err != nil {
			response.BadRequest(c, "用户ID格式错误")
			return
		} else {
			userIDUint64 = id
		}
	case uint64:
		userIDUint64 = v
	default:
		response.BadRequest(c, "用户ID类型错误")
		return
	}

	// 获取或生成个人二维码
	if h.personalQRService == nil {
		response.InternalServerError(c, "个人二维码服务未初始化")
		return
	}

	qrURL, err := h.personalQRService.GetPersonalQR(userIDUint64)
	if err != nil {
		response.InternalServerError(c, "获取个人二维码失败: "+err.Error())
		return
	}

	response.Success(c, gin.H{
		"qr_url":  qrURL,
		"user_id": userIDUint64,
	})
}

// VerifyActivity 核销活动
func (h *Handler) VerifyActivity(c *gin.Context) {
	var req struct {
		UserID     uint64 `json:"user_id" binding:"required"`
		ActivityID uint64 `json:"activity_id" binding:"required"`
		Points     int    `json:"points" binding:"required,min=1"`
		Remark     string `json:"remark"`
	}

	if err := c.ShouldBindJSON(&req); err != nil {
		response.BadRequest(c, "参数错误: "+err.Error())
		return
	}

	// 获取操作人ID（商场管理员）
	operatorID, exists := c.Get("user_id")
	if !exists {
		response.Unauthorized(c, "请先登录")
		return
	}

	var operatorIDUint64 uint64
	switch v := operatorID.(type) {
	case string:
		if id, err := strconv.ParseUint(v, 10, 64); err != nil {
			response.BadRequest(c, "用户ID格式错误")
			return
		} else {
			operatorIDUint64 = id
		}
	case uint64:
		operatorIDUint64 = v
	default:
		response.BadRequest(c, "用户ID类型错误")
		return
	}

	// 开始事务
	tx := h.db.Begin()
	defer func() {
		if r := recover(); r != nil {
			tx.Rollback()
		}
	}()

	// 查询用户
	var user models.User
	if err := tx.Where("id = ?", req.UserID).First(&user).Error; err != nil {
		tx.Rollback()
		if err == gorm.ErrRecordNotFound {
			response.NotFound(c, "用户不存在")
			return
		}
		response.InternalServerError(c, "查询用户失败")
		return
	}

	// 检查是否已经参与过该活动（防止重复核销）
	var existingRecord models.PointsRecord
	if err := tx.Where("user_id = ? AND source = ? AND related_id = ?",
		req.UserID, "活动奖励", req.ActivityID).
		First(&existingRecord).Error; err == nil {
		tx.Rollback()
		response.BadRequest(c, "该用户已参与过此活动")
		return
	}

	// 增加用户积分
	if err := tx.Model(&user).Update("points", gorm.Expr("points + ?", req.Points)).Error; err != nil {
		tx.Rollback()
		response.InternalServerError(c, "增加积分失败")
		return
	}

	// 创建核销记录（不关联订单）
	verifyRecord := models.VerifyRecord{
		OrderID:          0, // 不关联订单
		UserID:           req.UserID,
		ScannerID:        operatorIDUint64,
		Location:         "商场-活动核销",
		Remark:           fmt.Sprintf("活动核销: %s (活动ID:%d)", req.Remark, req.ActivityID),
		VerifyType:       models.VerifyRecordTypeActivity,
		Points:           req.Points,
		PointsChangeType: models.PointsTypeEarn, // 活动核销是获得积分
		Price:            0,                     // 活动核销无价格
		ActivityID:       &req.ActivityID,
		VerifyMethod:     "manual",
	}

	if err := tx.Create(&verifyRecord).Error; err != nil {
		tx.Rollback()
		response.InternalServerError(c, "创建核销记录失败")
		return
	}

	// 创建积分记录（关联核销记录）
	pointsRecord := models.PointsRecord{
		UserID:         req.UserID,
		Type:           models.PointsTypeEarn,
		Points:         req.Points,
		Balance:        user.Points + req.Points,
		Source:         "活动奖励",
		SourceID:       &operatorIDUint64,
		RelatedID:      &verifyRecord.ID, // 关联核销记录ID
		RelatedType:    "verify_record",
		Description:    fmt.Sprintf("参与活动获得积分: %s (活动ID:%d)", req.Remark, req.ActivityID),
		VerificationID: &verifyRecord.ID, // 关联核销记录
	}

	if err := tx.Create(&pointsRecord).Error; err != nil {
		tx.Rollback()
		response.InternalServerError(c, "创建积分记录失败")
		return
	}

	// 提交事务
	if err := tx.Commit().Error; err != nil {
		response.InternalServerError(c, "活动核销失败")
		return
	}

	fmt.Printf("活动核销成功 - 用户: %d, 活动: %d, 积分: %d, 操作人: %d\n",
		req.UserID, req.ActivityID, req.Points, operatorIDUint64)

	response.SuccessWithMessage(c, "活动核销成功", gin.H{
		"user_id":     req.UserID,
		"activity_id": req.ActivityID,
		"points":      req.Points,
		"new_balance": user.Points + req.Points,
	})
}

// VerifyTaskQRCode 核销任务二维码（商场端）
func (h *Handler) VerifyTaskQRCode(c *gin.Context) {
	var req struct {
		QRContent string `json:"qr_content" binding:"required"`
		Remark    string `json:"remark"`
	}

	if err := c.ShouldBindJSON(&req); err != nil {
		response.BadRequest(c, "参数错误: "+err.Error())
		return
	}

	// 获取操作人ID（商场管理员）
	operatorID, exists := c.Get("user_id")
	if !exists {
		response.Unauthorized(c, "请先登录")
		return
	}

	var operatorIDUint64 uint64
	switch v := operatorID.(type) {
	case string:
		if id, err := strconv.ParseUint(v, 10, 64); err != nil {
			response.BadRequest(c, "用户ID格式错误")
			return
		} else {
			operatorIDUint64 = id
		}
	case uint64:
		operatorIDUint64 = v
	default:
		response.BadRequest(c, "用户ID类型错误")
		return
	}

	// 解析任务二维码内容
	parts := strings.Split(req.QRContent, "|")
	if len(parts) != 6 {
		response.BadRequest(c, "无效的任务二维码格式")
		return
	}

	// 解析活动ID
	activityID, err := strconv.ParseUint(parts[0], 10, 64)
	if err != nil {
		response.BadRequest(c, "无效的活动ID")
		return
	}

	// 解析用户ID
	userID, err := strconv.ParseUint(parts[1], 10, 64)
	if err != nil {
		response.BadRequest(c, "无效的用户ID")
		return
	}

	// 获取验证码
	verifyCode := parts[2]

	// 解析时间戳
	timestamp, err := strconv.ParseInt(parts[3], 10, 64)
	if err != nil {
		response.BadRequest(c, "无效的时间戳")
		return
	}

	// 解析过期时间
	expiresAt, err := strconv.ParseInt(parts[4], 10, 64)
	if err != nil {
		response.BadRequest(c, "无效的过期时间")
		return
	}

	// 获取签名
	signature := parts[5]

	// 验证签名
	signStr := fmt.Sprintf("%d|%d|%s|%d|%d", activityID, userID, verifyCode, timestamp, expiresAt)
	secretKey := "wangfujing_qrcode_secret_key_2024"
	hmacHash := hmac.New(sha256.New, []byte(secretKey))
	hmacHash.Write([]byte(signStr))
	expectedSign := hex.EncodeToString(hmacHash.Sum(nil))

	if !strings.EqualFold(expectedSign, signature) {
		response.BadRequest(c, "签名验证失败")
		return
	}

	// 检查是否已过期
	currentTime := time.Now().Unix()
	if currentTime > expiresAt {
		response.BadRequest(c, "二维码已过期")
		return
	}

	// 开始事务
	tx := h.db.Begin()
	defer func() {
		if r := recover(); r != nil {
			tx.Rollback()
		}
	}()

	// 查询任务报名记录
	var registration models.TaskRegistration
	if err := tx.Where("activity_id = ? AND user_id = ? AND verify_code = ?",
		activityID, userID, verifyCode).First(&registration).Error; err != nil {
		tx.Rollback()
		if err == gorm.ErrRecordNotFound {
			response.NotFound(c, "任务报名记录不存在")
			return
		}
		response.InternalServerError(c, "查询任务报名记录失败")
		return
	}

	// 检查报名状态
	if registration.Status != models.ParticipationStatusRegistered {
		tx.Rollback()
		response.BadRequest(c, "任务状态不正确，无法核销")
		return
	}

	// 检查是否已核销
	if registration.VerifiedAt != nil {
		tx.Rollback()
		response.BadRequest(c, "任务已核销")
		return
	}

	// 查询活动信息
	var activity models.MarketingActivity
	if err := tx.First(&activity, activityID).Error; err != nil {
		tx.Rollback()
		response.InternalServerError(c, "查询活动信息失败")
		return
	}

	// 查询用户信息
	var user models.User
	if err := tx.First(&user, userID).Error; err != nil {
		tx.Rollback()
		response.InternalServerError(c, "查询用户信息失败")
		return
	}

	// 更新用户积分
	user.Points += activity.RewardPoints
	if err := tx.Save(&user).Error; err != nil {
		tx.Rollback()
		response.InternalServerError(c, "更新用户积分失败")
		return
	}

	// 更新任务报名记录状态
	now := time.Now()
	registration.Status = models.ParticipationStatusCompleted
	registration.VerifiedAt = &now
	registration.VerifiedBy = &operatorIDUint64
	if err := tx.Save(&registration).Error; err != nil {
		tx.Rollback()
		response.InternalServerError(c, "更新任务状态失败")
		return
	}

	// 创建核销记录
	verifyRecord := models.VerifyRecord{
		OrderID:          0, // 任务核销不关联订单
		UserID:           userID,
		ScannerID:        operatorIDUint64,
		Location:         "商场-任务核销",
		Remark:           fmt.Sprintf("任务核销: %s (活动ID:%d)", req.Remark, activityID),
		VerifyType:       models.VerifyRecordTypeActivity,
		Points:           activity.RewardPoints,
		PointsChangeType: models.PointsTypeEarn,
		Price:            0,
		ActivityID:       &activityID,
		VerifyCode:       verifyCode,
		VerifyMethod:     "qr_code",
	}

	if err := tx.Create(&verifyRecord).Error; err != nil {
		tx.Rollback()
		response.InternalServerError(c, "创建核销记录失败")
		return
	}

	// 创建积分记录
	pointsRecord := models.PointsRecord{
		UserID:         userID,
		Type:           models.PointsTypeEarn,
		Points:         activity.RewardPoints,
		Balance:        user.Points,
		Source:         "任务奖励",
		SourceID:       &operatorIDUint64,
		RelatedID:      &verifyRecord.ID,
		RelatedType:    "verify_record",
		Description:    fmt.Sprintf("完成任务获得积分: %s (活动ID:%d)", activity.Name, activityID),
		VerificationID: &verifyRecord.ID,
	}

	if err := tx.Create(&pointsRecord).Error; err != nil {
		tx.Rollback()
		response.InternalServerError(c, "创建积分记录失败")
		return
	}

	// 提交事务
	if err := tx.Commit().Error; err != nil {
		response.InternalServerError(c, "任务核销失败")
		return
	}

	response.Success(c, gin.H{
		"message": "任务核销成功",
		"user": gin.H{
			"id":       user.ID,
			"nickname": user.Nickname,
			"phone":    user.Phone,
			"points":   user.Points,
		},
		"activity": gin.H{
			"id":     activity.ID,
			"name":   activity.Name,
			"points": activity.RewardPoints,
		},
		"verify_record": gin.H{
			"id":         verifyRecord.ID,
			"points":     verifyRecord.Points,
			"created_at": verifyRecord.CreatedAt.Format("2006-01-02 15:04:05"),
		},
	})
}
