package handlers

import (
	"fmt"
	"net/http"
	"strconv"
	"unicode/utf8"

	"wangfujing_admin/internal/models"
	"wangfujing_admin/internal/services"
	"wangfujing_admin/pkg/response"

	"github.com/gin-gonic/gin"
)

// MarketingHandler 营销处理器
type MarketingHandler struct {
	marketingService *services.MarketingService
}

// NewMarketingHandler 创建营销处理器
func NewMarketingHandler(marketingService *services.MarketingService) *MarketingHandler {
	return &MarketingHandler{
		marketingService: marketingService,
	}
}

// CreateActivity 创建营销活动
func (h *MarketingHandler) CreateActivity(ctx *gin.Context) {
	var req services.CreateActivityRequest
	if err := ctx.ShouldBindJSON(&req); err != nil {
		response.Error(ctx, http.StatusBadRequest, "参数错误: "+err.Error())
		return
	}

	// 自定义验证逻辑
	if err := h.validateCreateActivityRequest(&req); err != nil {
		response.Error(ctx, http.StatusBadRequest, err.Error())
		return
	}

	// 获取当前用户ID
	userID, exists := ctx.Get("user_id")
	if !exists {
		response.Error(ctx, http.StatusUnauthorized, "未授权")
		return
	}

	if err := h.marketingService.CreateActivity(&req, userID.(string)); err != nil {
		response.Error(ctx, http.StatusInternalServerError, err.Error())
		return
	}

	response.SuccessWithMessage(ctx, "创建成功", nil)
}

// GetActivityList 获取活动列表
func (h *MarketingHandler) GetActivityList(ctx *gin.Context) {
	var req services.ActivityListRequest
	if err := ctx.ShouldBindQuery(&req); err != nil {
		response.Error(ctx, http.StatusBadRequest, "参数错误: "+err.Error())
		return
	}

	activities, total, err := h.marketingService.GetActivityList(&req)
	if err != nil {
		response.Error(ctx, http.StatusInternalServerError, err.Error())
		return
	}

	response.Page(ctx, activities, total, req.Page, req.Size)
}

// GetActivity 获取活动详情
func (h *MarketingHandler) GetActivity(ctx *gin.Context) {
	idStr := ctx.Param("id")
	id, err := strconv.ParseUint(idStr, 10, 64)
	if err != nil {
		response.Error(ctx, http.StatusBadRequest, "无效的活动ID")
		return
	}

	activity, err := h.marketingService.GetActivity(id)
	if err != nil {
		if err.Error() == "activity not found" {
			response.Error(ctx, http.StatusNotFound, "活动不存在")
			return
		}
		response.Error(ctx, http.StatusInternalServerError, err.Error())
		return
	}

	response.SuccessWithMessage(ctx, "获取成功", activity)
}

// UpdateActivity 更新活动
func (h *MarketingHandler) UpdateActivity(ctx *gin.Context) {
	idStr := ctx.Param("id")
	id, err := strconv.ParseUint(idStr, 10, 64)
	if err != nil {
		response.Error(ctx, http.StatusBadRequest, "无效的活动ID")
		return
	}

	var req services.UpdateActivityRequest
	if err := ctx.ShouldBindJSON(&req); err != nil {
		response.Error(ctx, http.StatusBadRequest, "参数错误: "+err.Error())
		return
	}

	// 先获取活动信息以确定类型
	activity, err := h.marketingService.GetActivity(id)
	if err != nil {
		if err.Error() == "activity not found" {
			response.Error(ctx, http.StatusNotFound, "活动不存在")
			return
		}
		response.Error(ctx, http.StatusInternalServerError, err.Error())
		return
	}

	// 自定义验证逻辑
	if err := h.validateUpdateActivityRequest(&req, activity.Category); err != nil {
		response.Error(ctx, http.StatusBadRequest, err.Error())
		return
	}

	if err := h.marketingService.UpdateActivity(id, &req); err != nil {
		if err.Error() == "activity not found" {
			response.Error(ctx, http.StatusNotFound, "活动不存在")
			return
		}
		response.Error(ctx, http.StatusInternalServerError, err.Error())
		return
	}

	response.SuccessWithMessage(ctx, "更新成功", nil)
}

// UpdateActivityStatus 更新活动状态
func (h *MarketingHandler) UpdateActivityStatus(ctx *gin.Context) {
	idStr := ctx.Param("id")
	id, err := strconv.ParseUint(idStr, 10, 64)
	if err != nil {
		response.Error(ctx, http.StatusBadRequest, "无效的活动ID")
		return
	}

	var req struct {
		Status models.MarketingActivityStatus `json:"status" binding:"required,oneof=0 1 2 3"`
	}
	if err := ctx.ShouldBindJSON(&req); err != nil {
		response.Error(ctx, http.StatusBadRequest, "参数错误: "+err.Error())
		return
	}

	if err := h.marketingService.UpdateActivityStatus(id, req.Status); err != nil {
		if err.Error() == "activity not found" {
			response.Error(ctx, http.StatusNotFound, "活动不存在")
			return
		}
		response.Error(ctx, http.StatusInternalServerError, err.Error())
		return
	}

	response.SuccessWithMessage(ctx, "状态更新成功", nil)
}

// DeleteActivity 删除活动
func (h *MarketingHandler) DeleteActivity(ctx *gin.Context) {
	idStr := ctx.Param("id")
	id, err := strconv.ParseUint(idStr, 10, 64)
	if err != nil {
		response.Error(ctx, http.StatusBadRequest, "无效的活动ID")
		return
	}

	if err := h.marketingService.DeleteActivity(id); err != nil {
		if err.Error() == "activity not found" {
			response.Error(ctx, http.StatusNotFound, "活动不存在")
			return
		}
		response.Error(ctx, http.StatusInternalServerError, err.Error())
		return
	}

	response.SuccessWithMessage(ctx, "删除成功", nil)
}

// RestartActivity 重新发起活动
func (h *MarketingHandler) RestartActivity(ctx *gin.Context) {
	idStr := ctx.Param("id")
	id, err := strconv.ParseUint(idStr, 10, 64)
	if err != nil {
		response.Error(ctx, http.StatusBadRequest, "无效的活动ID")
		return
	}

	var req services.CreateActivityRequest
	if err := ctx.ShouldBindJSON(&req); err != nil {
		response.Error(ctx, http.StatusBadRequest, "参数错误: "+err.Error())
		return
	}

	// 获取当前用户ID
	userID, exists := ctx.Get("user_id")
	if !exists {
		response.Error(ctx, http.StatusUnauthorized, "未授权")
		return
	}

	if err := h.marketingService.RestartActivity(id, &req, userID.(string)); err != nil {
		if err.Error() == "activity not found" {
			response.Error(ctx, http.StatusNotFound, "活动不存在")
			return
		}
		response.Error(ctx, http.StatusInternalServerError, err.Error())
		return
	}

	response.SuccessWithMessage(ctx, "重新发起成功", nil)
}

// GetActivityStatistics 获取活动统计
func (h *MarketingHandler) GetActivityStatistics(ctx *gin.Context) {
	stats, err := h.marketingService.GetActivityStatistics()
	if err != nil {
		response.Error(ctx, http.StatusInternalServerError, err.Error())
		return
	}

	response.SuccessWithMessage(ctx, "获取成功", stats)
}

// validateCreateActivityRequest 验证创建活动请求
func (h *MarketingHandler) validateCreateActivityRequest(req *services.CreateActivityRequest) error {
	// 分享类型活动的特殊验证
	if req.Category == models.MarketingCategoryShare {
		// 分享类型必须有video_name和video_url
		if req.VideoName == "" {
			return fmt.Errorf("分享类型活动必须填写视频名称")
		}
		if utf8.RuneCountInString(req.VideoName) > 100 {
			return fmt.Errorf("视频名称长度不能超过100字符")
		}
		if req.VideoURL == "" {
			return fmt.Errorf("分享类型活动必须填写视频URL")
		}
		// 分享类型不需要name字段
	} else {
		// 活动和游戏类型必须有name
		if req.Name == "" {
			return fmt.Errorf("活动名称不能为空")
		}
		nameLen := utf8.RuneCountInString(req.Name)
		if nameLen < 1 || nameLen > 20 {
			return fmt.Errorf("活动名称长度必须在1-20字符之间")
		}
	}

	// 验证描述长度
	if req.Description != "" {
		descLen := utf8.RuneCountInString(req.Description)
		if descLen > 100 {
			return fmt.Errorf("活动描述长度不能超过100字符")
		}
	}

	return nil
}

// validateUpdateActivityRequest 验证更新活动请求
func (h *MarketingHandler) validateUpdateActivityRequest(req *services.UpdateActivityRequest, category models.MarketingCategory) error {
	// 分享类型活动的特殊验证
	if category == models.MarketingCategoryShare {
		// 分享类型必须有video_name
		if req.VideoName == "" {
			return fmt.Errorf("分享类型活动必须填写视频名称")
		}
		if utf8.RuneCountInString(req.VideoName) > 100 {
			return fmt.Errorf("视频名称长度不能超过100字符")
		}
		// 分享类型不需要name字段
	} else {
		// 活动和游戏类型必须有name
		if req.Name == "" {
			return fmt.Errorf("活动名称不能为空")
		}
		nameLen := utf8.RuneCountInString(req.Name)
		if nameLen < 1 || nameLen > 20 {
			return fmt.Errorf("活动名称长度必须在1-20字符之间")
		}
	}

	// 验证描述长度
	if req.Description != "" {
		descLen := utf8.RuneCountInString(req.Description)
		if descLen > 100 {
			return fmt.Errorf("活动描述长度不能超过100字符")
		}
	}

	return nil
}
