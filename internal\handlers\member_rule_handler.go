package handlers

import (
	"net/http"
	"strconv"

	"github.com/gin-gonic/gin"

	"wangfujing_admin/internal/services"
	"wangfujing_admin/pkg/response"
)

// MemberRuleHandler 会员规则处理器
type MemberRuleHandler struct {
	memberRuleService *services.MemberRuleService
}

// NewMemberRuleHandler 创建会员规则处理器实例
func NewMemberRuleHandler(memberRuleService *services.MemberRuleService) *MemberRuleHandler {
	return &MemberRuleHandler{
		memberRuleService: memberRuleService,
	}
}

// CreateMemberRule 创建会员规则
func (h *MemberRuleHandler) CreateMemberRule(ctx *gin.Context) {
	var req services.CreateMemberRuleRequest
	if err := ctx.ShouldBindJSON(&req); err != nil {
		response.Error(ctx, http.StatusBadRequest, "参数错误: "+err.Error())
		return
	}

	// 获取当前用户ID
	userID, exists := ctx.Get("user_id")
	if !exists {
		response.Error(ctx, http.StatusUnauthorized, "用户未认证")
		return
	}

	if err := h.memberRuleService.CreateMemberRule(ctx.Request.Context(), &req, userID.(string)); err != nil {
		response.Error(ctx, http.StatusConflict, err.Error())
		return
	}

	response.SuccessWithMessage(ctx, "创建成功", nil)
}

// GetMemberRuleList 获取会员规则列表
func (h *MemberRuleHandler) GetMemberRuleList(ctx *gin.Context) {
	rules, err := h.memberRuleService.GetMemberRuleList(ctx.Request.Context())
	if err != nil {
		response.Error(ctx, http.StatusInternalServerError, err.Error())
		return
	}

	// 转换为响应格式
	var result []map[string]interface{}
	for _, rule := range rules {
		benefits := rule.GetBenefits()
		benefitsStr := ""
		if len(benefits) > 0 {
			for i, benefit := range benefits {
				if i > 0 {
					benefitsStr += ", "
				}
				benefitsStr += benefit
			}
		}

		item := map[string]interface{}{
			"id":              rule.ID,
			"type":            rule.Type,
			"type_name":       rule.Type.String(),
			"name":            rule.Name,
			"enable_points":   rule.EnablePoints,
			"points":          rule.Points,
			"enable_discount": rule.EnableDiscount,
			"discount":        rule.Discount,
			"benefits":        benefitsStr,
			"status":          rule.Status,
			"creator_id":      rule.CreatorID,
			"created_at":      rule.CreatedAt,
			"updated_at":      rule.UpdatedAt,
		}

		if rule.Creator != nil {
			item["creator"] = map[string]interface{}{
				"id":       rule.Creator.ID,
				"nickname": rule.Creator.Nickname,
				"phone":    rule.Creator.Phone,
			}
		}

		result = append(result, item)
	}

	response.Success(ctx, result)
}

// GetMemberRule 获取会员规则详情
func (h *MemberRuleHandler) GetMemberRule(ctx *gin.Context) {
	idStr := ctx.Param("id")
	id, err := strconv.ParseUint(idStr, 10, 64)
	if err != nil {
		response.Error(ctx, http.StatusBadRequest, "无效的规则ID")
		return
	}

	rule, err := h.memberRuleService.GetMemberRule(ctx.Request.Context(), id)
	if err != nil {
		if err.Error() == "会员规则不存在" {
			response.Error(ctx, http.StatusNotFound, "规则不存在")
			return
		}
		response.Error(ctx, http.StatusInternalServerError, err.Error())
		return
	}

	// 转换为响应格式
	benefits := rule.GetBenefits()
	benefitsStr := ""
	if len(benefits) > 0 {
		for i, benefit := range benefits {
			if i > 0 {
				benefitsStr += ", "
			}
			benefitsStr += benefit
		}
	}

	result := map[string]interface{}{
		"id":              rule.ID,
		"type":            rule.Type,
		"type_name":       rule.Type.String(),
		"name":            rule.Name,
		"enable_points":   rule.EnablePoints,
		"points":          rule.Points,
		"enable_discount": rule.EnableDiscount,
		"discount":        rule.Discount,
		"benefits":        benefitsStr,
		"status":          rule.Status,
		"creator_id":      rule.CreatorID,
		"created_at":      rule.CreatedAt,
		"updated_at":      rule.UpdatedAt,
	}

	if rule.Creator != nil {
		result["creator"] = map[string]interface{}{
			"id":       rule.Creator.ID,
			"nickname": rule.Creator.Nickname,
			"phone":    rule.Creator.Phone,
		}
	}

	response.Success(ctx, result)
}

// UpdateMemberRule 更新会员规则
func (h *MemberRuleHandler) UpdateMemberRule(ctx *gin.Context) {
	idStr := ctx.Param("id")
	id, err := strconv.ParseUint(idStr, 10, 64)
	if err != nil {
		response.Error(ctx, http.StatusBadRequest, "无效的规则ID")
		return
	}

	var req services.UpdateMemberRuleRequest
	if err := ctx.ShouldBindJSON(&req); err != nil {
		response.Error(ctx, http.StatusBadRequest, "参数错误: "+err.Error())
		return
	}

	if err := h.memberRuleService.UpdateMemberRule(ctx.Request.Context(), id, &req); err != nil {
		if err.Error() == "会员规则不存在" {
			response.Error(ctx, http.StatusNotFound, "规则不存在")
			return
		}
		response.Error(ctx, http.StatusInternalServerError, err.Error())
		return
	}

	response.SuccessWithMessage(ctx, "更新成功", nil)
}

// DeleteMemberRule 删除会员规则
func (h *MemberRuleHandler) DeleteMemberRule(ctx *gin.Context) {
	idStr := ctx.Param("id")
	id, err := strconv.ParseUint(idStr, 10, 64)
	if err != nil {
		response.Error(ctx, http.StatusBadRequest, "无效的规则ID")
		return
	}

	if err := h.memberRuleService.DeleteMemberRule(ctx.Request.Context(), id); err != nil {
		if err.Error() == "会员规则不存在" {
			response.Error(ctx, http.StatusNotFound, "规则不存在")
			return
		}
		response.Error(ctx, http.StatusInternalServerError, err.Error())
		return
	}

	response.SuccessWithMessage(ctx, "删除成功", nil)
}
