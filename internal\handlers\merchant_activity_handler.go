package handlers

import (
	"fmt"
	"net/http"
	"strconv"
	"unicode/utf8"

	"wangfujing_admin/internal/models"
	"wangfujing_admin/internal/services"
	"wangfujing_admin/pkg/response"

	"github.com/gin-gonic/gin"
)

// MerchantActivityHandler 商家活动处理器
type MerchantActivityHandler struct {
	merchantActivityService *services.MerchantActivityService
}

// NewMerchantActivityHandler 创建商家活动处理器
func NewMerchantActivityHandler(merchantActivityService *services.MerchantActivityService) *MerchantActivityHandler {
	return &MerchantActivityHandler{
		merchantActivityService: merchantActivityService,
	}
}

// CreateMerchantActivity 创建商家活动
func (h *MerchantActivityHandler) CreateMerchantActivity(ctx *gin.Context) {
	var req services.CreateMerchantActivityRequest
	if err := ctx.ShouldBindJSON(&req); err != nil {
		response.Error(ctx, http.StatusBadRequest, "参数错误: "+err.Error())
		return
	}

	// 自定义验证逻辑
	if err := h.validateCreateMerchantActivityRequest(&req); err != nil {
		response.Error(ctx, http.StatusBadRequest, err.Error())
		return
	}

	// 获取当前用户ID
	userID, exists := ctx.Get("user_id")
	if !exists {
		response.Error(ctx, http.StatusUnauthorized, "未授权")
		return
	}

	if err := h.merchantActivityService.CreateMerchantActivity(&req, userID.(string)); err != nil {
		response.Error(ctx, http.StatusInternalServerError, err.Error())
		return
	}

	response.SuccessWithMessage(ctx, "创建成功", nil)
}

// GetMerchantActivityList 获取商家活动列表
func (h *MerchantActivityHandler) GetMerchantActivityList(ctx *gin.Context) {
	var req services.MerchantActivityListRequest
	if err := ctx.ShouldBindQuery(&req); err != nil {
		response.Error(ctx, http.StatusBadRequest, "参数错误: "+err.Error())
		return
	}

	activities, total, err := h.merchantActivityService.GetMerchantActivityList(&req)
	if err != nil {
		response.Error(ctx, http.StatusInternalServerError, err.Error())
		return
	}

	response.Page(ctx, activities, total, req.Page, req.Size)
}

// GetMerchantActivity 获取商家活动详情
func (h *MerchantActivityHandler) GetMerchantActivity(ctx *gin.Context) {
	idStr := ctx.Param("id")
	id, err := strconv.ParseUint(idStr, 10, 64)
	if err != nil {
		response.Error(ctx, http.StatusBadRequest, "无效的活动ID")
		return
	}

	activity, err := h.merchantActivityService.GetMerchantActivity(id)
	if err != nil {
		if err.Error() == "merchant activity not found" {
			response.Error(ctx, http.StatusNotFound, "活动不存在")
			return
		}
		response.Error(ctx, http.StatusInternalServerError, err.Error())
		return
	}

	response.SuccessWithMessage(ctx, "获取成功", activity)
}

// UpdateMerchantActivity 更新商家活动
func (h *MerchantActivityHandler) UpdateMerchantActivity(ctx *gin.Context) {
	idStr := ctx.Param("id")
	id, err := strconv.ParseUint(idStr, 10, 64)
	if err != nil {
		response.Error(ctx, http.StatusBadRequest, "无效的活动ID")
		return
	}

	var req services.UpdateMerchantActivityRequest
	if err := ctx.ShouldBindJSON(&req); err != nil {
		response.Error(ctx, http.StatusBadRequest, "参数错误: "+err.Error())
		return
	}

	// 自定义验证逻辑
	if err := h.validateUpdateMerchantActivityRequest(&req); err != nil {
		response.Error(ctx, http.StatusBadRequest, err.Error())
		return
	}

	if err := h.merchantActivityService.UpdateMerchantActivity(id, &req); err != nil {
		if err.Error() == "merchant activity not found" {
			response.Error(ctx, http.StatusNotFound, "活动不存在")
			return
		}
		response.Error(ctx, http.StatusInternalServerError, err.Error())
		return
	}

	response.SuccessWithMessage(ctx, "更新成功", nil)
}

// UpdateMerchantActivityStatus 更新商家活动状态
func (h *MerchantActivityHandler) UpdateMerchantActivityStatus(ctx *gin.Context) {
	idStr := ctx.Param("id")
	id, err := strconv.ParseUint(idStr, 10, 64)
	if err != nil {
		response.Error(ctx, http.StatusBadRequest, "无效的活动ID")
		return
	}

	var req struct {
		Status models.MerchantActivityStatus `json:"status" binding:"required,oneof=0 1 2 3"`
	}
	if err := ctx.ShouldBindJSON(&req); err != nil {
		response.Error(ctx, http.StatusBadRequest, "参数错误: "+err.Error())
		return
	}

	if err := h.merchantActivityService.UpdateMerchantActivityStatus(id, req.Status); err != nil {
		if err.Error() == "merchant activity not found" {
			response.Error(ctx, http.StatusNotFound, "活动不存在")
			return
		}
		response.Error(ctx, http.StatusInternalServerError, err.Error())
		return
	}

	response.SuccessWithMessage(ctx, "状态更新成功", nil)
}

// DeleteMerchantActivity 删除商家活动
func (h *MerchantActivityHandler) DeleteMerchantActivity(ctx *gin.Context) {
	idStr := ctx.Param("id")
	id, err := strconv.ParseUint(idStr, 10, 64)
	if err != nil {
		response.Error(ctx, http.StatusBadRequest, "无效的活动ID")
		return
	}

	if err := h.merchantActivityService.DeleteMerchantActivity(id); err != nil {
		if err.Error() == "merchant activity not found" {
			response.Error(ctx, http.StatusNotFound, "活动不存在")
			return
		}
		response.Error(ctx, http.StatusInternalServerError, err.Error())
		return
	}

	response.SuccessWithMessage(ctx, "删除成功", nil)
}

// RestartMerchantActivity 重新发起商家活动
func (h *MerchantActivityHandler) RestartMerchantActivity(ctx *gin.Context) {
	idStr := ctx.Param("id")
	id, err := strconv.ParseUint(idStr, 10, 64)
	if err != nil {
		response.Error(ctx, http.StatusBadRequest, "无效的活动ID")
		return
	}

	var req services.CreateMerchantActivityRequest
	if err := ctx.ShouldBindJSON(&req); err != nil {
		response.Error(ctx, http.StatusBadRequest, "参数错误: "+err.Error())
		return
	}

	// 自定义验证逻辑
	if err := h.validateCreateMerchantActivityRequest(&req); err != nil {
		response.Error(ctx, http.StatusBadRequest, err.Error())
		return
	}

	// 获取当前用户ID
	userID, exists := ctx.Get("user_id")
	if !exists {
		response.Error(ctx, http.StatusUnauthorized, "未授权")
		return
	}

	if err := h.merchantActivityService.RestartMerchantActivity(id, &req, userID.(string)); err != nil {
		if err.Error() == "merchant activity not found" {
			response.Error(ctx, http.StatusNotFound, "活动不存在")
			return
		}
		response.Error(ctx, http.StatusInternalServerError, err.Error())
		return
	}

	response.SuccessWithMessage(ctx, "重新发起成功", nil)
}

// validateCreateMerchantActivityRequest 验证创建商家活动请求
func (h *MerchantActivityHandler) validateCreateMerchantActivityRequest(req *services.CreateMerchantActivityRequest) error {
	// 验证活动名称
	if req.Name == "" {
		return fmt.Errorf("活动名称不能为空")
	}
	nameLen := utf8.RuneCountInString(req.Name)
	if nameLen < 1 || nameLen > 20 {
		return fmt.Errorf("活动名称长度必须在1-20字符之间")
	}

	// 验证活动介绍
	if req.Description != "" {
		descLen := utf8.RuneCountInString(req.Description)
		if descLen > 100 {
			return fmt.Errorf("活动介绍长度不能超过100字符")
		}
	}

	return nil
}

// validateUpdateMerchantActivityRequest 验证更新商家活动请求
func (h *MerchantActivityHandler) validateUpdateMerchantActivityRequest(req *services.UpdateMerchantActivityRequest) error {
	// 验证活动名称
	if req.Name == "" {
		return fmt.Errorf("活动名称不能为空")
	}
	nameLen := utf8.RuneCountInString(req.Name)
	if nameLen < 1 || nameLen > 20 {
		return fmt.Errorf("活动名称长度必须在1-20字符之间")
	}

	// 验证活动介绍
	if req.Description != "" {
		descLen := utf8.RuneCountInString(req.Description)
		if descLen > 100 {
			return fmt.Errorf("活动介绍长度不能超过100字符")
		}
	}

	return nil
}
