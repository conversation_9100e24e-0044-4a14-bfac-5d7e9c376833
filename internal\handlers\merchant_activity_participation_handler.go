package handlers

import (
	"fmt"
	"net/http"
	"strconv"

	"github.com/gin-gonic/gin"
	"gorm.io/gorm"

	"wangfujing_admin/internal/models"
	"wangfujing_admin/internal/services"
	"wangfujing_admin/pkg/response"
)

// MerchantActivityParticipationHandler 商家活动参与处理器
type MerchantActivityParticipationHandler struct {
	db      *gorm.DB
	service *services.MerchantActivityParticipationService
}

// NewMerchantActivityParticipationHandler 创建商家活动参与处理器实例
func NewMerchantActivityParticipationHandler(db *gorm.DB) *MerchantActivityParticipationHandler {
	return &MerchantActivityParticipationHandler{
		db:      db,
		service: services.NewMerchantActivityParticipationService(db),
	}
}

// GetActivities 获取活动列表（包含已报名的活动，不包含已过期的）
func (h *MerchantActivityParticipationHandler) GetActivities(ctx *gin.Context) {
	// 获取分页参数
	page, _ := strconv.Atoi(ctx.DefaultQuery("page", "1"))
	size, _ := strconv.Atoi(ctx.DefaultQuery("size", "10"))
	status := ctx.DefaultQuery("status", "") // ongoing, upcoming

	// 获取商家ID
	merchantID, err := h.getMerchantID(ctx)
	if err != nil {
		response.Error(ctx, http.StatusBadRequest, err.Error())
		return
	}

	// 获取活动列表
	activities, total, err := h.service.GetActivities(ctx.Request.Context(), merchantID, page, size, status)
	if err != nil {
		response.Error(ctx, http.StatusInternalServerError, "获取活动列表失败: "+err.Error())
		return
	}

	// 返回分页结果
	result := map[string]interface{}{
		"list":  activities,
		"total": total,
		"page":  page,
		"size":  size,
	}

	response.Success(ctx, result)
}

// GetRegisteredActivities 获取已报名的活动列表
func (h *MerchantActivityParticipationHandler) GetRegisteredActivities(ctx *gin.Context) {
	// 获取分页参数
	page, _ := strconv.Atoi(ctx.DefaultQuery("page", "1"))
	size, _ := strconv.Atoi(ctx.DefaultQuery("size", "10"))
	status := ctx.DefaultQuery("status", "") // ongoing, upcoming, finished

	// 获取商家ID
	merchantID, err := h.getMerchantID(ctx)
	if err != nil {
		response.Error(ctx, http.StatusBadRequest, err.Error())
		return
	}

	// 获取已报名活动列表
	activities, total, err := h.service.GetRegisteredActivities(ctx.Request.Context(), merchantID, status, page, size)
	if err != nil {
		response.Error(ctx, http.StatusInternalServerError, "获取已报名活动列表失败: "+err.Error())
		return
	}

	// 返回分页结果
	result := map[string]interface{}{
		"list":  activities,
		"total": total,
		"page":  page,
		"size":  size,
	}

	response.Success(ctx, result)
}

// GetActivityDetail 获取活动详情
func (h *MerchantActivityParticipationHandler) GetActivityDetail(ctx *gin.Context) {
	// 获取活动ID
	activityIDStr := ctx.Param("id")
	activityID, err := strconv.ParseUint(activityIDStr, 10, 64)
	if err != nil {
		response.Error(ctx, http.StatusBadRequest, "无效的活动ID")
		return
	}

	// 获取商家ID
	merchantID, err := h.getMerchantID(ctx)
	if err != nil {
		response.Error(ctx, http.StatusBadRequest, err.Error())
		return
	}

	// 获取活动详情
	detail, err := h.service.GetActivityDetail(ctx.Request.Context(), merchantID, activityID)
	if err != nil {
		if err.Error() == "activity not found" {
			response.Error(ctx, http.StatusNotFound, "活动不存在")
			return
		}
		response.Error(ctx, http.StatusInternalServerError, "获取活动详情失败: "+err.Error())
		return
	}

	response.Success(ctx, detail)
}

// RegisterActivity 报名活动
func (h *MerchantActivityParticipationHandler) RegisterActivity(ctx *gin.Context) {
	// 获取活动ID
	activityIDStr := ctx.Param("id")
	activityID, err := strconv.ParseUint(activityIDStr, 10, 64)
	if err != nil {
		response.Error(ctx, http.StatusBadRequest, "无效的活动ID")
		return
	}

	// 获取商家ID
	merchantID, err := h.getMerchantID(ctx)
	if err != nil {
		response.Error(ctx, http.StatusBadRequest, err.Error())
		return
	}

	// 报名活动
	if err := h.service.RegisterActivity(ctx.Request.Context(), merchantID, activityID); err != nil {
		switch err.Error() {
		case "activity not found":
			response.Error(ctx, http.StatusNotFound, "活动不存在")
		case "activity has expired":
			response.Error(ctx, http.StatusForbidden, "活动已过期")
		case "merchant ranking does not meet activity requirements":
			response.Error(ctx, http.StatusForbidden, "您的积分排名不符合活动要求")
		case "already registered for this activity":
			response.Error(ctx, http.StatusConflict, "您已经报名了此活动")
		default:
			response.Error(ctx, http.StatusInternalServerError, "报名失败: "+err.Error())
		}
		return
	}

	response.SuccessWithMessage(ctx, "报名成功", nil)
}

// getMerchantID 获取当前用户关联的商家ID
func (h *MerchantActivityParticipationHandler) getMerchantID(ctx *gin.Context) (uint64, error) {
	// 获取用户ID
	userIDStr := ctx.GetString("user_id")
	userID, err := strconv.ParseUint(userIDStr, 10, 64)
	if err != nil {
		return 0, fmt.Errorf("用户ID无效")
	}

	// 查询用户信息
	var adminUser models.AdminUser
	if err := h.db.First(&adminUser, userID).Error; err != nil {
		return 0, fmt.Errorf("查询用户信息失败")
	}

	// 检查是否为商家用户
	if adminUser.UserType != models.UserTypeMerchant {
		return 0, fmt.Errorf("仅限商家用户访问")
	}

	// 检查是否关联了商家
	if adminUser.MerchantID == nil {
		return 0, fmt.Errorf("用户未关联商家")
	}

	return *adminUser.MerchantID, nil
}
