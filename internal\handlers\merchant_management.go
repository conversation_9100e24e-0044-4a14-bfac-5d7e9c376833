package handlers

import (
	"net/http"
	"strconv"
	"wangfujing_admin/internal/models"
	"wangfujing_admin/internal/services"
	"wangfujing_admin/pkg/response"

	"github.com/gin-gonic/gin"
)

// MerchantManagementHandler 商家管理处理器
type MerchantManagementHandler struct {
	merchantService *services.MerchantManagementService
}

// NewMerchantManagementHandler 创建商家管理处理器
func NewMerchantManagementHandler(merchantService *services.MerchantManagementService) *MerchantManagementHandler {
	return &MerchantManagementHandler{
		merchantService: merchantService,
	}
}

// GetMerchantRanking 获取商家排行
func (h *MerchantManagementHandler) GetMerchantRanking(c *gin.Context) {
	// 获取查询参数
	rankingType := c.Default<PERSON>uery("type", "sales")
	sortOrder := c.DefaultQuery("order", "desc")
	page, _ := strconv.Atoi(c.DefaultQuery("page", "1"))
	size, _ := strconv.Atoi(c.DefaultQuery("size", "10"))

	// 参数验证
	if page < 1 {
		page = 1
	}
	if size < 1 || size > 100 {
		size = 10
	}

	// 验证排行类型
	var rankingTypeEnum services.RankingType
	switch rankingType {
	case "sales":
		rankingTypeEnum = services.RankingTypeSales
	case "score":
		rankingTypeEnum = services.RankingTypeScore
	case "efficiency":
		rankingTypeEnum = services.RankingTypeEfficiency
	default:
		response.Error(c, http.StatusBadRequest, "无效的排行类型")
		return
	}

	// 验证排序方式
	var sortOrderEnum services.SortOrder
	switch sortOrder {
	case "desc":
		sortOrderEnum = services.SortOrderDesc
	case "asc":
		sortOrderEnum = services.SortOrderAsc
	default:
		response.Error(c, http.StatusBadRequest, "无效的排序方式")
		return
	}

	// 获取排行数据
	items, total, err := h.merchantService.GetMerchantRanking(c.Request.Context(), rankingTypeEnum, sortOrderEnum, page, size)
	if err != nil {
		response.Error(c, http.StatusInternalServerError, "获取商家排行失败: "+err.Error())
		return
	}

	// 构建响应数据
	data := gin.H{
		"merchants": items,
		"pagination": gin.H{
			"page":  page,
			"size":  size,
			"total": total,
		},
		"filter": gin.H{
			"type":  rankingType,
			"order": sortOrder,
		},
	}

	response.SuccessWithMessage(c, "获取商家排行成功", data)
}

// CreateMerchant 创建商家
func (h *MerchantManagementHandler) CreateMerchant(c *gin.Context) {
	var req services.CreateMerchantRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		response.Error(c, http.StatusBadRequest, "参数错误: "+err.Error())
		return
	}

	merchant, err := h.merchantService.CreateMerchant(c.Request.Context(), &req)
	if err != nil {
		response.Error(c, http.StatusInternalServerError, "创建商家失败: "+err.Error())
		return
	}

	response.SuccessWithMessage(c, "创建商家成功", merchant)
}

// UpdateMerchant 更新商家
func (h *MerchantManagementHandler) UpdateMerchant(c *gin.Context) {
	idStr := c.Param("id")
	id, err := strconv.ParseUint(idStr, 10, 64)
	if err != nil {
		response.Error(c, http.StatusBadRequest, "无效的商家ID")
		return
	}

	var req services.UpdateMerchantRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		response.Error(c, http.StatusBadRequest, "参数错误: "+err.Error())
		return
	}

	merchant, err := h.merchantService.UpdateMerchant(c.Request.Context(), id, &req)
	if err != nil {
		response.Error(c, http.StatusInternalServerError, "更新商家失败: "+err.Error())
		return
	}

	response.SuccessWithMessage(c, "更新商家成功", merchant)
}

// DeleteMerchant 删除商家
func (h *MerchantManagementHandler) DeleteMerchant(c *gin.Context) {
	idStr := c.Param("id")
	id, err := strconv.ParseUint(idStr, 10, 64)
	if err != nil {
		response.Error(c, http.StatusBadRequest, "无效的商家ID")
		return
	}

	if err := h.merchantService.DeleteMerchant(c.Request.Context(), id); err != nil {
		response.Error(c, http.StatusInternalServerError, "删除商家失败: "+err.Error())
		return
	}

	response.SuccessWithMessage(c, "删除商家成功", nil)
}

// GetMerchantDetail 获取商家详情
func (h *MerchantManagementHandler) GetMerchantDetail(c *gin.Context) {
	idStr := c.Param("id")
	id, err := strconv.ParseUint(idStr, 10, 64)
	if err != nil {
		response.Error(c, http.StatusBadRequest, "无效的商家ID")
		return
	}

	merchant, err := h.merchantService.GetMerchantByID(c.Request.Context(), id)
	if err != nil {
		response.Error(c, http.StatusInternalServerError, "获取商家详情失败: "+err.Error())
		return
	}

	response.SuccessWithMessage(c, "获取商家详情成功", merchant)
}

// GetMerchantScoreRecords 获取商家分值记录
func (h *MerchantManagementHandler) GetMerchantScoreRecords(c *gin.Context) {
	idStr := c.Param("id")
	id, err := strconv.ParseUint(idStr, 10, 64)
	if err != nil {
		response.Error(c, http.StatusBadRequest, "无效的商家ID")
		return
	}

	page, _ := strconv.Atoi(c.DefaultQuery("page", "1"))
	size, _ := strconv.Atoi(c.DefaultQuery("size", "20"))

	if page < 1 {
		page = 1
	}
	if size < 1 || size > 100 {
		size = 20
	}

	records, total, err := h.merchantService.GetMerchantScoreRecords(c.Request.Context(), id, page, size)
	if err != nil {
		response.Error(c, http.StatusInternalServerError, "获取分值记录失败: "+err.Error())
		return
	}

	data := gin.H{
		"records": records,
		"pagination": gin.H{
			"page":  page,
			"size":  size,
			"total": total,
		},
	}

	response.SuccessWithMessage(c, "获取分值记录成功", data)
}

// GetScoreApplications 获取分值申请列表
func (h *MerchantManagementHandler) GetScoreApplications(c *gin.Context) {
	statusStr := c.DefaultQuery("status", "")
	page, _ := strconv.Atoi(c.DefaultQuery("page", "1"))
	size, _ := strconv.Atoi(c.DefaultQuery("size", "20"))

	if page < 1 {
		page = 1
	}
	if size < 1 || size > 100 {
		size = 20
	}

	// 处理状态参数
	var status *models.ApprovalStatus
	if statusStr != "" {
		if statusInt, err := strconv.Atoi(statusStr); err == nil {
			if statusInt >= 0 && statusInt <= 2 {
				st := models.ApprovalStatus(statusInt)
				status = &st
			} else {
				response.Error(c, http.StatusBadRequest, "无效的状态参数")
				return
			}
		} else {
			response.Error(c, http.StatusBadRequest, "状态参数格式错误")
			return
		}
	}

	records, total, err := h.merchantService.GetScoreApplicationsWithOptionalStatus(c.Request.Context(), status, page, size)
	if err != nil {
		response.Error(c, http.StatusInternalServerError, "获取分值申请失败: "+err.Error())
		return
	}

	data := gin.H{
		"applications": records,
		"pagination": gin.H{
			"page":  page,
			"size":  size,
			"total": total,
		},
		"filter": gin.H{
			"status": status,
		},
	}

	response.SuccessWithMessage(c, "获取分值申请成功", data)
}

// CreateScoreApplication 创建分值申请
func (h *MerchantManagementHandler) CreateScoreApplication(c *gin.Context) {
	var req services.CreateScoreApplicationRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		response.Error(c, http.StatusBadRequest, "参数错误: "+err.Error())
		return
	}

	// 从JWT中获取操作员ID
	operatorID, err := getUserIDFromContext(c)
	if err != nil {
		response.Error(c, http.StatusUnauthorized, err.Error())
		return
	}

	record, err := h.merchantService.CreateScoreApplication(c.Request.Context(), &req, operatorID)
	if err != nil {
		response.Error(c, http.StatusInternalServerError, "创建分值申请失败: "+err.Error())
		return
	}

	response.SuccessWithMessage(c, "创建分值申请成功", record)
}

// ApproveScoreApplication 审核分值申请
func (h *MerchantManagementHandler) ApproveScoreApplication(c *gin.Context) {
	idStr := c.Param("id")
	id, err := strconv.ParseUint(idStr, 10, 64)
	if err != nil {
		response.Error(c, http.StatusBadRequest, "无效的申请ID")
		return
	}

	var req services.ApproveScoreApplicationRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		response.Error(c, http.StatusBadRequest, "参数错误: "+err.Error())
		return
	}

	// 从JWT中获取审核员ID
	approverID, err := getUserIDFromContext(c)
	if err != nil {
		response.Error(c, http.StatusUnauthorized, err.Error())
		return
	}

	if err := h.merchantService.ApproveScoreApplication(c.Request.Context(), id, &req, approverID); err != nil {
		response.Error(c, http.StatusInternalServerError, "审核分值申请失败: "+err.Error())
		return
	}

	response.SuccessWithMessage(c, "审核分值申请成功", nil)
}

// GetScoreApplicationsForReview 获取待审核分值申请列表
func (h *MerchantManagementHandler) GetScoreApplicationsForReview(c *gin.Context) {
	// 获取状态参数，默认为待审核
	statusStr := c.DefaultQuery("status", "0")
	status, err := strconv.Atoi(statusStr)
	if err != nil || (status != 0 && status != 1 && status != 2) {
		response.Error(c, http.StatusBadRequest, "无效的状态参数")
		return
	}

	// 获取分页参数
	pageStr := c.DefaultQuery("page", "1")
	page, err := strconv.Atoi(pageStr)
	if err != nil || page < 1 {
		page = 1
	}

	sizeStr := c.DefaultQuery("size", "20")
	size, err := strconv.Atoi(sizeStr)
	if err != nil || size < 1 || size > 100 {
		size = 20
	}

	// 使用明确的状态筛选
	approvalStatus := models.ApprovalStatus(status)
	records, total, err := h.merchantService.GetScoreApplications(c.Request.Context(), approvalStatus, page, size)
	if err != nil {
		response.Error(c, http.StatusInternalServerError, "获取审核列表失败: "+err.Error())
		return
	}

	response.SuccessWithMessage(c, "获取审核列表成功", gin.H{
		"records": records,
		"pagination": gin.H{
			"page":  page,
			"size":  size,
			"total": total,
		},
	})
}

// GetScoreApplicationDetail 获取分值申请详情
func (h *MerchantManagementHandler) GetScoreApplicationDetail(c *gin.Context) {
	idStr := c.Param("id")
	id, err := strconv.ParseUint(idStr, 10, 64)
	if err != nil {
		response.Error(c, http.StatusBadRequest, "无效的申请ID")
		return
	}

	record, err := h.merchantService.GetScoreApplicationDetail(c.Request.Context(), id)
	if err != nil {
		response.Error(c, http.StatusInternalServerError, "获取申请详情失败: "+err.Error())
		return
	}

	response.SuccessWithMessage(c, "获取申请详情成功", record)
}

// ResubmitScoreApplication 重新提交分值申请
func (h *MerchantManagementHandler) ResubmitScoreApplication(c *gin.Context) {
	idStr := c.Param("id")
	id, err := strconv.ParseUint(idStr, 10, 64)
	if err != nil {
		response.Error(c, http.StatusBadRequest, "无效的申请ID")
		return
	}

	var req services.ResubmitScoreApplicationRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		response.Error(c, http.StatusBadRequest, "参数错误: "+err.Error())
		return
	}

	// 从JWT中获取操作员ID
	operatorID, err := getUserIDFromContext(c)
	if err != nil {
		response.Error(c, http.StatusUnauthorized, err.Error())
		return
	}

	record, err := h.merchantService.ResubmitScoreApplication(c.Request.Context(), id, &req, operatorID)
	if err != nil {
		response.Error(c, http.StatusInternalServerError, "重新提交失败: "+err.Error())
		return
	}

	response.SuccessWithMessage(c, "重新提交成功", record)
}
