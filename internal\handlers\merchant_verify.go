package handlers

import (
	"crypto/hmac"
	"crypto/sha256"
	"encoding/hex"
	"encoding/json"
	"fmt"
	"strconv"
	"strings"
	"time"

	"wangfujing_admin/internal/middleware"
	"wangfujing_admin/internal/models"
	"wangfujing_admin/internal/services"
	"wangfujing_admin/pkg/response"

	"github.com/gin-gonic/gin"
	"gorm.io/gorm"
)

// 注意：商家端不需要单独的登录接口
// 商家用户通过管理端统一登录，然后使用相同的token访问商家端功能
// 这里的MerchantLogin方法已移除，商家端直接使用JWT中间件验证用户身份和权限

// GetVerificationRecords 获取核销记录列表（商家端）
func (h *Handler) GetVerificationRecords(c *gin.Context) {
	page, _ := strconv.Atoi(c.DefaultQuery("page", "1"))
	size, _ := strconv.Atoi(c.DefaultQuery("size", "10"))

	// 获取当前商家用户ID
	userID, exists := c.Get("user_id")
	if !exists {
		response.Unauthorized(c, "请先登录")
		return
	}

	// 获取商家信息（如果是商家用户）
	var merchant models.Merchant
	if err := h.db.Where("login_phone = (SELECT phone FROM users WHERE id = ?)", userID).
		First(&merchant).Error; err != nil {
		response.ErrorWithCode(c, 403, response.CodeForbidden, "无权限访问")
		return
	}

	// 查询该商家的核销记录
	query := h.db.Model(&models.Order{}).
		Where("merchant_id = ? AND verified_at IS NOT NULL", merchant.ID)

	// 计算总数
	var total int64
	if err := query.Count(&total).Error; err != nil {
		response.InternalServerError(c, "获取核销记录总数失败")
		return
	}

	// 分页查询
	var orders []models.Order
	offset := (page - 1) * size
	if err := query.Offset(offset).Limit(size).
		Order("verified_at DESC").
		Find(&orders).Error; err != nil {
		response.InternalServerError(c, "获取核销记录失败")
		return
	}

	// 获取关联的用户和商品信息
	var userIDs []uint64
	var productIDs []uint64
	for _, order := range orders {
		userIDs = append(userIDs, order.UserID)
		productIDs = append(productIDs, order.ProductID)
	}

	// 批量获取用户信息
	var users []models.User
	userMap := make(map[uint64]models.User)
	if len(userIDs) > 0 {
		if err := h.db.Where("id IN ?", userIDs).Find(&users).Error; err == nil {
			for _, user := range users {
				userMap[user.ID] = user
			}
		}
	}

	// 批量获取商品信息
	var products []models.Product
	productMap := make(map[uint64]models.Product)
	if len(productIDs) > 0 {
		if err := h.db.Where("id IN ?", productIDs).Find(&products).Error; err == nil {
			for _, product := range products {
				productMap[product.ID] = product
			}
		}
	}

	// 转换为响应格式
	var result []gin.H
	for _, order := range orders {
		user := userMap[order.UserID]
		product := productMap[order.ProductID]

		// 解析商品图片
		var images []string
		if product.Images != "" {
			json.Unmarshal([]byte(product.Images), &images)
		}

		orderData := gin.H{
			"id":            order.ID,
			"order_no":      order.OrderNo,
			"points":        order.Points,
			"status":        order.GetStatusText(),
			"verified_at":   order.VerifiedAt.Format("2006-01-02 15:04:05"),
			"verify_remark": order.VerifyRemark,
			"created_at":    order.CreatedAt.Format("2006-01-02 15:04:05"),
		}

		// 添加用户信息
		orderData["user"] = gin.H{
			"id":       user.ID,
			"nickname": user.Nickname,
			"phone":    user.Phone,
			"avatar":   user.Avatar,
		}

		// 添加商品信息
		orderData["product"] = gin.H{
			"id":          product.ID,
			"name":        product.Name,
			"description": product.Description,
			"images":      images,
			"points":      product.Points,
		}

		// 添加有效期信息
		if product.ValidFrom != nil {
			orderData["valid_from"] = product.ValidFrom.Format("2006-01-02 15:04")
		}
		if product.ValidTo != nil {
			orderData["valid_to"] = product.ValidTo.Format("2006-01-02 15:04")
		}

		result = append(result, orderData)
	}

	response.Page(c, result, total, page, size)
}

// GetVerificationDetail 获取核销详情（商家端）
func (h *Handler) GetVerificationDetail(c *gin.Context) {
	orderID := c.Param("id")

	// 获取当前商家用户ID
	userID, exists := c.Get("user_id")
	if !exists {
		response.Unauthorized(c, "请先登录")
		return
	}

	// 获取商家信息
	var merchant models.Merchant
	if err := h.db.Where("login_phone = (SELECT phone FROM users WHERE id = ?)", userID).
		First(&merchant).Error; err != nil {
		response.ErrorWithCode(c, 403, response.CodeForbidden, "无权限访问")
		return
	}

	// 获取订单详情
	var order models.Order
	if err := h.db.Where("id = ? AND merchant_id = ?", orderID, merchant.ID).
		First(&order).Error; err != nil {
		if err == gorm.ErrRecordNotFound {
			response.NotFound(c, "订单不存在")
			return
		}
		response.InternalServerError(c, "获取订单详情失败")
		return
	}

	// 获取用户信息
	var user models.User
	if err := h.db.Where("id = ?", order.UserID).First(&user).Error; err != nil {
		response.InternalServerError(c, "获取用户信息失败")
		return
	}

	// 获取商品信息
	var product models.Product
	if err := h.db.Where("id = ?", order.ProductID).First(&product).Error; err != nil {
		response.InternalServerError(c, "获取商品信息失败")
		return
	}

	// 解析商品图片
	var images []string
	if product.Images != "" {
		json.Unmarshal([]byte(product.Images), &images)
	}

	// 构建响应数据
	result := gin.H{
		"id":            order.ID,
		"order_no":      order.OrderNo,
		"points":        order.Points,
		"status":        order.GetStatusText(),
		"qr_code_url":   order.QRCodeURL,
		"verify_code":   order.VerifyCode,
		"verify_remark": order.VerifyRemark,
		"created_at":    order.CreatedAt.Format("2006-01-02 15:04:05"),
	}

	// 添加核销时间
	if order.VerifiedAt != nil {
		result["verified_at"] = order.VerifiedAt.Format("2006-01-02 15:04:05")
		result["is_verified"] = true
	} else {
		result["is_verified"] = false
	}

	// 添加用户信息
	result["user"] = gin.H{
		"id":       user.ID,
		"nickname": user.Nickname,
		"phone":    user.Phone,
		"avatar":   user.Avatar,
	}

	// 添加商品信息
	result["product"] = gin.H{
		"id":          product.ID,
		"name":        product.Name,
		"description": product.Description,
		"images":      images,
		"points":      product.Points,
	}

	// 添加有效期信息
	if product.ValidFrom != nil {
		result["valid_from"] = product.ValidFrom.Format("2006-01-02 15:04")
	}
	if product.ValidTo != nil {
		result["valid_to"] = product.ValidTo.Format("2006-01-02 15:04")
	}

	// 检查是否可以核销
	canVerify := order.CanVerify()
	result["can_verify"] = canVerify

	// 检查商品是否在有效期内
	now := time.Now()
	isValid := true
	if product.ValidTo != nil && now.After(*product.ValidTo) {
		isValid = false
	}
	result["is_valid"] = isValid

	response.Success(c, result)
}

// VerifyOrder 确认核销订单（商家端）
func (h *Handler) VerifyOrder(c *gin.Context) {
	orderID := c.Param("id")

	var req struct {
		Remark string `json:"remark"`
	}
	if err := c.ShouldBindJSON(&req); err != nil {
		response.ValidateError(c, err)
		return
	}

	// 获取当前商家用户ID
	userID, exists := c.Get("user_id")
	if !exists {
		response.Unauthorized(c, "请先登录")
		return
	}

	// 获取商家信息
	var merchant models.Merchant
	if err := h.db.Where("login_phone = (SELECT phone FROM users WHERE id = ?)", userID).
		First(&merchant).Error; err != nil {
		response.ErrorWithCode(c, 403, response.CodeForbidden, "无权限访问")
		return
	}

	// 获取订单信息
	var order models.Order
	if err := h.db.Where("id = ? AND merchant_id = ?", orderID, merchant.ID).
		First(&order).Error; err != nil {
		if err == gorm.ErrRecordNotFound {
			response.NotFound(c, "订单不存在")
			return
		}
		response.InternalServerError(c, "获取订单信息失败")
		return
	}

	// 检查订单是否可以核销
	if !order.CanVerify() {
		response.ErrorWithCode(c, 400, response.CodeInvalidParams, "订单不可核销")
		return
	}

	// 获取商品信息检查有效期
	var product models.Product
	if err := h.db.Where("id = ?", order.ProductID).First(&product).Error; err != nil {
		response.InternalServerError(c, "获取商品信息失败")
		return
	}

	// 检查商品是否在有效期内
	now := time.Now()
	if product.ValidTo != nil && now.After(*product.ValidTo) {
		response.ErrorWithCode(c, 400, response.CodeProductExpired, "商品已过期，无法核销")
		return
	}

	// 更新订单状态
	updates := map[string]interface{}{
		"verified_at":   &now,
		"verifier_id":   userID,
		"verify_remark": req.Remark,
		"status":        models.OrderStatusCompleted,
		"completed_at":  &now,
	}

	if err := h.db.Model(&order).Where("id = ?", orderID).Updates(updates).Error; err != nil {
		response.InternalServerError(c, "核销失败")
		return
	}

	// 核销成功后，发放消费积分奖励
	if h.memberRuleService != nil {
		orderIDUint, _ := strconv.ParseUint(orderID, 10, 64)
		if err := h.memberRuleService.GiveConsumePoints(c.Request.Context(), order.UserID, orderIDUint); err != nil {
			// 积分发放失败不影响核销流程，只记录错误日志
			middleware.LogBusinessError(c, "GiveConsumePointsFailed", err, map[string]interface{}{
				"user_id":  order.UserID,
				"order_id": orderID,
			})
		} else {
			// 记录成功发放积分的日志
			middleware.LogBusinessError(c, "ConsumePointsGiven", nil, map[string]interface{}{
				"user_id":  order.UserID,
				"order_id": orderID,
			})
		}
	}

	response.SuccessWithMessage(c, "核销成功", nil)
}

// VerifyQRCode 通过二维码内容核销订单（商家端）
func (h *Handler) VerifyQRCode(c *gin.Context) {
	var req struct {
		QRContent string `json:"qr_content" binding:"required"`
		Remark    string `json:"remark"`
	}
	if err := c.ShouldBindJSON(&req); err != nil {
		response.ValidateError(c, err)
		return
	}

	// 获取当前商家用户ID
	userID, exists := c.Get("user_id")
	if !exists {
		response.Unauthorized(c, "请先登录")
		return
	}

	// 使用核销服务进行核销
	verifyService := services.NewVerifyService(h.db)
	order, err := verifyService.VerifyQRCode(c.Request.Context(), req.QRContent, userID.(uint64), req.Remark)
	if err != nil {
		response.BadRequest(c, err.Error())
		return
	}

	// 核销成功后，发放消费积分奖励
	if h.memberRuleService != nil {
		if err := h.memberRuleService.GiveConsumePoints(c.Request.Context(), order.UserID, order.ID); err != nil {
			// 积分发放失败不影响核销流程，只记录错误日志
			middleware.LogBusinessError(c, "GiveConsumePointsFailed", err, map[string]interface{}{
				"user_id":  order.UserID,
				"order_id": order.ID,
			})
		} else {
			// 记录成功发放积分的日志
			middleware.LogBusinessError(c, "ConsumePointsGiven", nil, map[string]interface{}{
				"user_id":  order.UserID,
				"order_id": order.ID,
			})
		}
	}

	response.SuccessWithMessage(c, "核销成功", gin.H{
		"order_id":    order.ID,
		"order_no":    order.OrderNo,
		"verified_at": order.VerifiedAt,
	})
}

// ScanQRCodeForOrderDetail 扫描二维码获取订单详情（商家端）
func (h *Handler) ScanQRCodeForOrderDetail(c *gin.Context) {
	var req struct {
		QRContent string `json:"qr_content" binding:"required"`
	}
	if err := c.ShouldBindJSON(&req); err != nil {
		response.ValidateError(c, err)
		return
	}

	// 获取当前商家用户ID
	userID, exists := c.Get("user_id")
	if !exists {
		response.Unauthorized(c, "请先登录")
		return
	}

	// 获取商家信息
	var merchant models.Merchant
	if err := h.db.Where("login_phone = (SELECT phone FROM users WHERE id = ?)", userID).
		First(&merchant).Error; err != nil {
		response.ErrorWithCode(c, 403, response.CodeForbidden, "无权限访问")
		return
	}

	// 解析二维码内容
	parts := strings.Split(req.QRContent, "|")
	fmt.Printf("二维码解析: 总共%d个部分\n", len(parts))
	for i, part := range parts {
		fmt.Printf("  部分%d: %s\n", i, part)
	}

	if len(parts) != 9 {
		fmt.Printf("二维码格式错误: 期望9个部分，实际%d个部分\n", len(parts))
		response.ErrorWithCode(c, 400, response.CodeInvalidParams, "无效的二维码格式")
		return
	}

	// 解析订单ID
	orderID, err := strconv.ParseUint(parts[0], 10, 64)
	if err != nil {
		fmt.Printf("解析订单ID失败: %v\n", err)
		response.ErrorWithCode(c, 400, response.CodeInvalidParams, "无效的订单ID")
		return
	}

	// 获取订单号
	orderNo := parts[1]

	// 获取核销码
	verifyCode := parts[5]

	// 获取签名
	signature := parts[8]

	fmt.Printf("解析结果: orderID=%d, orderNo=%s, verifyCode=%s, signature=%s\n",
		orderID, orderNo, verifyCode, signature)

	// 验证签名
	// 构建签名字符串
	signStr := fmt.Sprintf("%s|%s|%s|%s|%s|%s|%s|%s",
		parts[0], parts[1], parts[2], parts[3], parts[4], parts[5], parts[6], parts[7])

	fmt.Printf("构建签名字符串: %s\n", signStr)

	// 使用HMAC-SHA256生成签名
	secretKey := "wangfujing_qrcode_secret_key_2024" // 与pkg/security/hmac_signer.go中的默认密钥一致
	hmacHash := hmac.New(sha256.New, []byte(secretKey))
	hmacHash.Write([]byte(signStr))
	expectedSign := hex.EncodeToString(hmacHash.Sum(nil))

	fmt.Printf("期望签名: %s\n", expectedSign)
	fmt.Printf("实际签名: %s\n", signature)
	fmt.Printf("签名匹配: %v\n", strings.EqualFold(expectedSign, signature))

	if !strings.EqualFold(expectedSign, signature) {
		// 记录到日志文件
		middleware.LogBusinessError(c, "SignatureVerificationFailed", fmt.Errorf("签名验证失败"), map[string]interface{}{
			"qr_content":    req.QRContent,
			"expected_sign": expectedSign,
			"actual_sign":   signature,
			"sign_string":   signStr,
			"user_id":       userID,
		})
		response.ErrorWithCode(c, 400, response.CodeInvalidParams, "签名验证失败")
		return
	}

	// 验证过期时间
	expiresAt, err := strconv.ParseInt(parts[7], 10, 64)
	if err != nil {
		response.ErrorWithCode(c, 400, response.CodeInvalidParams, "无效的过期时间")
		return
	}

	// 检查是否已过期
	currentTime := time.Now().Unix()
	if currentTime > expiresAt {
		response.ErrorWithCode(c, 400, response.CodeInvalidParams, "二维码已过期")
		return
	}

	// 查询订单详情
	var order models.Order
	if err := h.db.Where("id = ? AND order_no = ?", orderID, orderNo).First(&order).Error; err != nil {
		if err == gorm.ErrRecordNotFound {
			response.ErrorWithCode(c, 404, response.CodeNotFound, "订单不存在")
			return
		}
		response.InternalServerError(c, "查询订单失败")
		return
	}

	// 验证核销码
	if order.VerifyCode != verifyCode {
		response.ErrorWithCode(c, 400, response.CodeInvalidParams, "核销码无效")
		return
	}

	// 查询用户信息
	var user models.User
	if err := h.db.Where("id = ?", order.UserID).First(&user).Error; err != nil {
		response.InternalServerError(c, "查询用户信息失败")
		return
	}

	// 查询商品信息
	var product models.Product
	if err := h.db.Where("id = ?", order.ProductID).First(&product).Error; err != nil {
		response.InternalServerError(c, "查询商品信息失败")
		return
	}

	// 解析商品图片
	var images []string
	if product.Images != "" {
		json.Unmarshal([]byte(product.Images), &images)
	}

	// 获取订单状态文本
	var statusText string
	switch order.Status {
	case models.OrderStatusPending:
		statusText = "待核销"
	case models.OrderStatusCompleted:
		statusText = "已核销"
	case models.OrderStatusCancelled:
		statusText = "已取消"
	case -2: // 假设-2是过期状态
		statusText = "已过期"
	default:
		statusText = "未知状态"
	}

	// 构建响应数据
	response.Success(c, gin.H{
		"order_id": order.ID,
		"order_no": order.OrderNo,
		"user": gin.H{
			"id":       user.ID,
			"nickname": user.Nickname,
			"phone":    user.Phone,
		},
		"product": gin.H{
			"id":          product.ID,
			"name":        product.Name,
			"description": product.Description,
			"images":      images,
			"points":      product.Points,
			"valid_from":  formatTime(product.ValidFrom),
			"valid_to":    formatTime(product.ValidTo),
		},
		"points":            order.Points,
		"status":            order.Status,
		"status_text":       statusText,
		"verification_code": order.VerifyCode,
		"verified_at":       formatTime(order.VerifiedAt),
		"created_at":        order.CreatedAt.Format("2006-01-02 15:04:05"),
		"can_verify":        order.Status == models.OrderStatusPending && order.VerifiedAt == nil,
	})
}

// ScanAndVerifyQRCode 扫描二维码并直接核销（商家端）
func (h *Handler) ScanAndVerifyQRCode(c *gin.Context) {
	var req struct {
		QRContent string `json:"qr_content" binding:"required"`
		Remark    string `json:"remark"`
	}
	if err := c.ShouldBindJSON(&req); err != nil {
		response.ValidateError(c, err)
		return
	}

	// 获取当前商家用户ID
	userID, exists := c.Get("user_id")
	if !exists {
		response.Unauthorized(c, "请先登录")
		return
	}

	// 解析二维码内容
	parts := strings.Split(req.QRContent, "|")
	fmt.Printf("二维码解析: 总共%d个部分\n", len(parts))
	for i, part := range parts {
		fmt.Printf("  部分%d: %s\n", i, part)
	}

	if len(parts) != 9 {
		fmt.Printf("二维码格式错误: 期望9个部分，实际%d个部分\n", len(parts))
		response.ErrorWithCode(c, 400, response.CodeInvalidParams, "无效的二维码格式")
		return
	}

	// 解析订单ID
	orderID, err := strconv.ParseUint(parts[0], 10, 64)
	if err != nil {
		fmt.Printf("解析订单ID失败: %v\n", err)
		response.ErrorWithCode(c, 400, response.CodeInvalidParams, "无效的订单ID")
		return
	}

	// 获取订单号
	orderNo := parts[1]

	// 获取核销码
	verifyCode := parts[5]

	// 获取签名
	signature := parts[8]

	fmt.Printf("解析结果: orderID=%d, orderNo=%s, verifyCode=%s, signature=%s\n",
		orderID, orderNo, verifyCode, signature)

	// 验证签名
	// 构建签名字符串
	signStr := fmt.Sprintf("%s|%s|%s|%s|%s|%s|%s|%s",
		parts[0], parts[1], parts[2], parts[3], parts[4], parts[5], parts[6], parts[7])

	fmt.Printf("构建签名字符串: %s\n", signStr)

	// 使用HMAC-SHA256生成签名
	secretKey := "wangfujing_qrcode_secret_key_2024" // 与pkg/security/hmac_signer.go中的默认密钥一致
	hmacHash := hmac.New(sha256.New, []byte(secretKey))
	hmacHash.Write([]byte(signStr))
	expectedSign := hex.EncodeToString(hmacHash.Sum(nil))

	fmt.Printf("期望签名: %s\n", expectedSign)
	fmt.Printf("实际签名: %s\n", signature)
	fmt.Printf("签名匹配: %v\n", strings.EqualFold(expectedSign, signature))

	if !strings.EqualFold(expectedSign, signature) {
		// 记录到日志文件
		middleware.LogBusinessError(c, "SignatureVerificationFailed", fmt.Errorf("签名验证失败"), map[string]interface{}{
			"qr_content":    req.QRContent,
			"expected_sign": expectedSign,
			"actual_sign":   signature,
			"sign_string":   signStr,
			"user_id":       userID,
		})
		response.ErrorWithCode(c, 400, response.CodeInvalidParams, "签名验证失败")
		return
	}

	// 验证过期时间
	expiresAt, err := strconv.ParseInt(parts[7], 10, 64)
	if err != nil {
		response.ErrorWithCode(c, 400, response.CodeInvalidParams, "无效的过期时间")
		return
	}

	// 检查是否已过期
	currentTime := time.Now().Unix()
	if currentTime > expiresAt {
		response.ErrorWithCode(c, 400, response.CodeInvalidParams, "二维码已过期")
		return
	}

	// 查询订单
	var order models.Order
	if err := h.db.Where("id = ? AND order_no = ?", orderID, orderNo).First(&order).Error; err != nil {
		if err == gorm.ErrRecordNotFound {
			response.ErrorWithCode(c, 404, response.CodeNotFound, "订单不存在")
			return
		}
		response.InternalServerError(c, "查询订单失败")
		return
	}

	// 验证核销码
	if order.VerifyCode != verifyCode {
		response.ErrorWithCode(c, 400, response.CodeInvalidParams, "核销码无效")
		return
	}

	// 检查订单状态
	if order.Status != models.OrderStatusPending {
		response.ErrorWithCode(c, 400, response.CodeInvalidParams, "订单状态不正确，无法核销")
		return
	}

	// 检查是否已核销
	if order.VerifiedAt != nil {
		response.ErrorWithCode(c, 400, response.CodeInvalidParams, "订单已核销")
		return
	}

	// 开始核销流程
	// 更新订单状态
	now := time.Now()
	updates := map[string]interface{}{
		"status":        models.OrderStatusCompleted,
		"verified_at":   now,
		"verifier_id":   userID,
		"verify_remark": req.Remark,
		"completed_at":  now,
	}

	if err := h.db.Model(&order).Updates(updates).Error; err != nil {
		response.InternalServerError(c, "核销失败")
		return
	}

	// 核销成功后，发放消费积分奖励
	if h.memberRuleService != nil {
		if err := h.memberRuleService.GiveConsumePoints(c.Request.Context(), order.UserID, orderID); err != nil {
			// 积分发放失败不影响核销流程，只记录错误日志
			middleware.LogBusinessError(c, "GiveConsumePointsFailed", err, map[string]interface{}{
				"user_id":  order.UserID,
				"order_id": orderID,
			})
		} else {
			// 记录成功发放积分的日志
			middleware.LogBusinessError(c, "ConsumePointsGiven", nil, map[string]interface{}{
				"user_id":  order.UserID,
				"order_id": orderID,
			})
		}
	}

	response.SuccessWithMessage(c, "核销成功", gin.H{
		"order_id":    order.ID,
		"order_no":    order.OrderNo,
		"verified_at": now.Format("2006-01-02 15:04:05"),
	})
}

// generateSignature 生成签名
func generateSignature(qrCode, timestamp, verificationCode string, merchantID uint64) string {
	// 构建签名字符串
	signStr := fmt.Sprintf("%s|%s|%s|%d", qrCode, timestamp, verificationCode, merchantID)

	// 使用SHA256生成签名
	h := sha256.New()
	h.Write([]byte(signStr))

	return fmt.Sprintf("%x", h.Sum(nil))
}
