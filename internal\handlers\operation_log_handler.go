package handlers

import (
	"fmt"
	"strconv"
	"wangfujing_admin/internal/services"
	"wangfujing_admin/pkg/logger"
	"wangfujing_admin/pkg/response"

	"github.com/gin-gonic/gin"
)

// GetOperationLogs 获取操作日志列表
func (h *Handler) GetOperationLogs(c *gin.Context) {
	// 获取分页参数
	page, _ := strconv.Atoi(c.<PERSON>("page", "1"))
	size, _ := strconv.Atoi(c<PERSON>("size", "20"))

	if page < 1 {
		page = 1
	}
	if size < 1 || size > 100 {
		size = 20
	}

	// 获取过滤参数
	filters := make(map[string]interface{})
	if operatorID := c.Query("operator_id"); operatorID != "" {
		filters["operator_id"] = operatorID
	}
	if module := c.Query("module"); module != "" {
		filters["module"] = module
	}
	if action := c.Que<PERSON>("action"); action != "" {
		filters["action"] = action
	}
	if resource := c.Query("resource"); resource != "" {
		filters["resource"] = resource
	}
	if status := c.Query("status"); status != "" {
		filters["status"] = status
	}
	if startTime := c.Query("start_time"); startTime != "" {
		filters["start_time"] = startTime
	}
	if endTime := c.Query("end_time"); endTime != "" {
		filters["end_time"] = endTime
	}

	operationLogService := services.NewOperationLogService(h.db)
	logs, total, err := operationLogService.GetOperationLogs(c.Request.Context(), page, size, filters)
	if err != nil {
		logger.Error("获取操作日志列表失败",
			logger.String("error", err.Error()),
			logger.String("client_ip", c.ClientIP()),
		)
		response.InternalServerError(c, "获取操作日志列表失败: "+err.Error())
		return
	}

	response.Success(c, gin.H{
		"logs": logs,
		"pagination": gin.H{
			"page":  page,
			"size":  size,
			"total": total,
		},
	})
}

// GetOperationLog 获取操作日志详情
func (h *Handler) GetOperationLog(c *gin.Context) {
	id, err := strconv.Atoi(c.Param("id"))
	if err != nil {
		response.BadRequest(c, "Invalid log ID")
		return
	}

	operationLogService := services.NewOperationLogService(h.db)
	idStr := strconv.Itoa(id)
	log, err := operationLogService.GetOperationLogByID(c.Request.Context(), idStr)
	if err != nil {
		if err.Error() == "operation log not found" {
			response.NotFound(c, "操作日志不存在")
			return
		}
		logger.Error("获取操作日志详情失败",
			logger.String("id", idStr),
			logger.String("error", err.Error()),
			logger.String("client_ip", c.ClientIP()),
		)
		response.InternalServerError(c, "获取操作日志详情失败: "+err.Error())
		return
	}

	response.Success(c, gin.H{
		"log": log,
	})
}

// GetOperationStatistics 获取操作统计信息
func (h *Handler) GetOperationStatistics(c *gin.Context) {
	// 获取时间范围参数
	filters := make(map[string]interface{})
	if startTime := c.Query("start_time"); startTime != "" {
		filters["start_time"] = startTime
	}
	if endTime := c.Query("end_time"); endTime != "" {
		filters["end_time"] = endTime
	}

	operationLogService := services.NewOperationLogService(h.db)
	stats, err := operationLogService.GetOperationStatistics(c.Request.Context(), filters)
	if err != nil {
		logger.Error("获取操作统计信息失败",
			logger.String("error", err.Error()),
			logger.String("client_ip", c.ClientIP()),
		)
		response.InternalServerError(c, "获取操作统计信息失败: "+err.Error())
		return
	}

	response.Success(c, gin.H{
		"statistics": stats,
	})
}

// GetOperationModules 获取操作模块列表
func (h *Handler) GetOperationModules(c *gin.Context) {
	modules := []map[string]string{
		{"value": "权限管理", "label": "权限管理"},
		{"value": "角色管理", "label": "角色管理"},
		{"value": "用户管理", "label": "用户管理"},
		{"value": "商家管理", "label": "商家管理"},
		{"value": "商品管理", "label": "商品管理"},
		{"value": "订单管理", "label": "订单管理"},
		{"value": "客诉管理", "label": "客诉管理"},
		{"value": "营销管理", "label": "营销管理"},
		{"value": "统计报表", "label": "统计报表"},
	}

	response.Success(c, gin.H{
		"modules": modules,
	})
}

// GetOperationActions 获取操作动作列表
func (h *Handler) GetOperationActions(c *gin.Context) {
	actions := []map[string]string{
		{"value": "列表", "label": "列表查询"},
		{"value": "查看", "label": "查看详情"},
		{"value": "创建", "label": "创建"},
		{"value": "更新", "label": "更新"},
		{"value": "删除", "label": "删除"},
		{"value": "分配权限", "label": "分配权限"},
		{"value": "分配角色", "label": "分配角色"},
		{"value": "启用", "label": "启用"},
		{"value": "禁用", "label": "禁用"},
		{"value": "审核", "label": "审核"},
	}

	response.Success(c, gin.H{
		"actions": actions,
	})
}

// ExportOperationLogs 导出操作日志
func (h *Handler) ExportOperationLogs(c *gin.Context) {
	// 获取过滤参数
	filters := make(map[string]interface{})
	if operatorID := c.Query("operator_id"); operatorID != "" {
		filters["operator_id"] = operatorID
	}
	if module := c.Query("module"); module != "" {
		filters["module"] = module
	}
	if action := c.Query("action"); action != "" {
		filters["action"] = action
	}
	if resource := c.Query("resource"); resource != "" {
		filters["resource"] = resource
	}
	if status := c.Query("status"); status != "" {
		filters["status"] = status
	}
	if startTime := c.Query("start_time"); startTime != "" {
		filters["start_time"] = startTime
	}
	if endTime := c.Query("end_time"); endTime != "" {
		filters["end_time"] = endTime
	}

	operationLogService := services.NewOperationLogService(h.db)

	// 获取所有符合条件的日志（不分页）
	logs, _, err := operationLogService.GetOperationLogs(c.Request.Context(), 1, 10000, filters)
	if err != nil {
		logger.Error("导出操作日志失败",
			logger.String("error", err.Error()),
			logger.String("client_ip", c.ClientIP()),
		)
		response.InternalServerError(c, "导出操作日志失败: "+err.Error())
		return
	}

	// 设置响应头
	c.Header("Content-Type", "application/vnd.ms-excel")
	c.Header("Content-Disposition", "attachment; filename=operation_logs.csv")

	// 写入CSV头部
	csvHeader := "ID,操作者ID,操作者姓名,操作模块,操作动作,操作资源,资源ID,操作描述,客户端IP,操作状态,错误信息,操作时间\n"
	c.Writer.WriteString(csvHeader)

	// 写入数据
	for _, log := range logs {
		statusText := "成功"
		if log.Status == 0 {
			statusText = "失败"
		}

		csvRow := fmt.Sprintf("%d,%d,%s,%s,%s,%s,%s,%s,%s,%s,%s,%s\n",
			log.ID,
			log.OperatorID,
			log.OperatorName,
			log.Module,
			log.Action,
			log.Resource,
			log.ResourceID,
			log.Description,
			log.ClientIP,
			statusText,
			log.ErrorMessage,
			log.CreatedAt.Format("2006-01-02 15:04:05"),
		)
		c.Writer.WriteString(csvRow)
	}
}
