package handlers

import (
	"wangfujing_admin/internal/services"
	"wangfujing_admin/pkg/logger"
	"wangfujing_admin/pkg/response"

	"github.com/gin-gonic/gin"
	"gorm.io/gorm"
)

// OpinionsHandler 意见箱处理器
type OpinionsHandler struct {
	*Handler
	OpinionService *services.OpinionService
}

// NewProgramHandler 创建节目单处理器
func NewOpinionsService(OpinionService *services.OpinionService, db *gorm.DB) *OpinionsHandler {
	return &OpinionsHandler{
		Handler:        &Handler{db: db},
		OpinionService: OpinionService,
	}
}

// 查询意见箱列表
func (h *Handler) GetOpinionsAdmin(ctx *gin.Context) {
	// 获取分页参数
	page, size := getPaginationParams(ctx)
	startTime := ctx.Query("start_time")
	endTime := ctx.Query("end_time")
	// 获取商品列表
	opinions, total, err := h.opinionService.GetOpinions(ctx.Request.Context(), page, size, startTime, endTime)
	if err != nil {
		logger.Error("获取意见箱列表失败",
			logger.String("error", err.Error()),
			logger.String("client_ip", ctx.ClientIP()),
		)
		response.InternalServerError(ctx, "获取意见箱列表失败")
		return
	}

	// 转换响应数据
	var opinionsList []gin.H
	for _, opinion := range opinions {
		opinionsList = append(opinionsList, gin.H{
			"id":         opinion.ID,
			"content":    opinion.Content,
			"contact":    opinion.Contact,
			"created_at": opinion.CreatedAt,
		})
	}

	response.Success(ctx, gin.H{
		"list":  opinionsList,
		"total": total,
		"page":  page,
		"size":  size,
	})
}

func (h *Handler) GetOpinionAdmin(c *gin.Context) {
	h.GetOpinion(c)
}

func (h *Handler) UpdateOpinionStatus(c *gin.Context) {
	response.Success(c, gin.H{"message": "UpdateOpinionStatus - TODO"})
}
