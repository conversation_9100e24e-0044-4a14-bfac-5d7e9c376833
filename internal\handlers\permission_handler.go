package handlers

import (
	"strconv"
	"wangfujing_admin/internal/models"
	"wangfujing_admin/internal/services"
	"wangfujing_admin/pkg/logger"
	"wangfujing_admin/pkg/response"

	"github.com/gin-gonic/gin"
)

// GetPermissions 获取权限列表
func (h *Handler) GetPermissions(c *gin.Context) {
	module := c.Query("module")

	permissionService := services.NewPermissionService(h.db)

	if module != "" {
		// 根据模块获取权限
		permissions, err := permissionService.GetPermissionsByModule(c.Request.Context(), module)
		if err != nil {
			response.InternalServerError(c, "获取权限列表失败")
			return
		}
		response.Success(c, gin.H{
			"permissions": permissions,
		})
	} else {
		// 获取所有权限
		permissions, err := permissionService.GetAllPermissions(c.Request.Context())
		if err != nil {
			response.InternalServerError(c, "获取权限列表失败")
			return
		}
		response.Success(c, gin.H{
			"permissions": permissions,
		})
	}
}

// GetPermission 获取权限详情
func (h *Handler) GetPermission(c *gin.Context) {
	id := c.Param("id")
	if id == "" {
		response.BadRequest(c, "权限ID不能为空")
		return
	}

	permissionService := services.NewPermissionService(h.db)
	permission, err := permissionService.GetPermissionByID(c.Request.Context(), id)
	if err != nil {
		response.NotFound(c, "权限不存在")
		return
	}

	response.Success(c, gin.H{
		"permission": permission,
	})
}

// CreatePermission 创建权限
func (h *Handler) CreatePermission(c *gin.Context) {
	var req struct {
		Name        string `json:"name" binding:"required"`
		DisplayName string `json:"display_name"`
		Description string `json:"description"`
		Module      string `json:"module"`
		Action      string `json:"action"`
		Resource    string `json:"resource"`
	}

	if err := c.ShouldBindJSON(&req); err != nil {
		response.BadRequest(c, "参数错误: "+err.Error())
		return
	}

	permission := &models.Permission{
		Name:        req.Name,
		DisplayName: req.DisplayName,
		Description: req.Description,
		Module:      req.Module,
		Action:      req.Action,
		Resource:    req.Resource,
	}

	permissionService := services.NewPermissionService(h.db)
	if err := permissionService.CreatePermission(c.Request.Context(), permission); err != nil {
		// 记录详细错误日志
		logger.Error("创建权限失败",
			logger.String("name", req.Name),
			logger.String("module", req.Module),
			logger.String("error", err.Error()),
			logger.String("client_ip", c.ClientIP()),
		)
		response.InternalServerError(c, "创建权限失败: "+err.Error())
		return
	}

	response.Success(c, gin.H{
		"permission": permission,
	})
}

// UpdatePermission 更新权限
func (h *Handler) UpdatePermission(c *gin.Context) {
	id, err := strconv.Atoi(c.Param("id"))
	if err != nil {
		response.BadRequest(c, "Invalid permission ID")
		return
	}

	var req struct {
		Name        string `json:"name" binding:"required"`
		DisplayName string `json:"display_name"`
		Description string `json:"description"`
		Module      string `json:"module"`
		Action      string `json:"action"`
		Resource    string `json:"resource"`
	}

	if err := c.ShouldBindJSON(&req); err != nil {
		response.BadRequest(c, "参数错误: "+err.Error())
		return
	}

	permissionService := services.NewPermissionService(h.db)

	// 构建更新数据
	updateData := map[string]interface{}{
		"name":         req.Name,
		"display_name": req.DisplayName,
		"description":  req.Description,
		"module":       req.Module,
		"action":       req.Action,
		"resource":     req.Resource,
	}

	idStr := strconv.Itoa(id)
	if err := permissionService.UpdatePermission(c.Request.Context(), idStr, updateData); err != nil {
		// 记录详细错误日志
		logger.Error("更新权限失败",
			logger.String("id", idStr),
			logger.String("name", req.Name),
			logger.String("module", req.Module),
			logger.String("error", err.Error()),
			logger.String("client_ip", c.ClientIP()),
		)
		response.InternalServerError(c, "更新权限失败: "+err.Error())
		return
	}

	// 获取更新后的权限
	permission, err := permissionService.GetPermissionByID(c.Request.Context(), idStr)
	if err != nil {
		logger.Error("获取更新后的权限失败",
			logger.String("id", idStr),
			logger.String("error", err.Error()),
			logger.String("client_ip", c.ClientIP()),
		)
		response.InternalServerError(c, "获取更新后的权限失败: "+err.Error())
		return
	}

	response.Success(c, gin.H{
		"permission": permission,
	})
}

// DeletePermission 删除权限
func (h *Handler) DeletePermission(c *gin.Context) {
	id := c.Param("id")
	if id == "" {
		response.BadRequest(c, "权限ID不能为空")
		return
	}

	permissionService := services.NewPermissionService(h.db)
	if err := permissionService.DeletePermission(c.Request.Context(), id); err != nil {
		response.InternalServerError(c, "删除权限失败")
		return
	}

	response.Success(c, gin.H{
		"message": "删除成功",
	})
}

// GetPermissionModules 获取权限模块列表
func (h *Handler) GetPermissionModules(c *gin.Context) {
	permissionService := services.NewPermissionService(h.db)
	modules, err := permissionService.GetModules(c.Request.Context())
	if err != nil {
		response.InternalServerError(c, "获取权限模块失败")
		return
	}

	response.Success(c, gin.H{
		"modules": modules,
	})
}
