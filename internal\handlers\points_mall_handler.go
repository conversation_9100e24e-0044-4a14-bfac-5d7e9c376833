package handlers

import (
	"fmt"
	"net/http"
	"strconv"
	"unicode/utf8"

	"wangfujing_admin/internal/models"
	"wangfujing_admin/internal/services"
	"wangfujing_admin/pkg/response"

	"github.com/gin-gonic/gin"
)

// PointsMallHandler 积分商城处理器
type PointsMallHandler struct {
	pointsMallService *services.PointsMallService
}

// NewPointsMallHandler 创建积分商城处理器
func NewPointsMallHandler(pointsMallService *services.PointsMallService) *PointsMallHandler {
	return &PointsMallHandler{
		pointsMallService: pointsMallService,
	}
}

// CreateProduct 创建兑换商品
func (h *PointsMallHandler) CreateProduct(ctx *gin.Context) {
	var req services.CreateProductRequest
	if err := ctx.ShouldBindJSON(&req); err != nil {
		response.Error(ctx, http.StatusBadRequest, "参数错误: "+err.Error())
		return
	}

	// 自定义验证逻辑
	if err := h.validateCreateProductRequest(&req); err != nil {
		response.Error(ctx, http.StatusBadRequest, err.Error())
		return
	}

	// 获取当前用户ID
	userID, exists := ctx.Get("user_id")
	if !exists {
		response.Error(ctx, http.StatusUnauthorized, "未授权")
		return
	}

	if err := h.pointsMallService.CreateProduct(&req, userID.(string)); err != nil {
		response.Error(ctx, http.StatusInternalServerError, err.Error())
		return
	}

	response.SuccessWithMessage(ctx, "创建成功", nil)
}

// CreateAvatar 创建VIP形象
func (h *PointsMallHandler) CreateAvatar(ctx *gin.Context) {
	var req services.CreateAvatarRequest
	if err := ctx.ShouldBindJSON(&req); err != nil {
		response.Error(ctx, http.StatusBadRequest, "参数错误: "+err.Error())
		return
	}

	// 自定义验证逻辑
	if err := h.validateCreateAvatarRequest(&req); err != nil {
		response.Error(ctx, http.StatusBadRequest, err.Error())
		return
	}

	// 获取当前用户ID
	userID, exists := ctx.Get("user_id")
	if !exists {
		response.Error(ctx, http.StatusUnauthorized, "未授权")
		return
	}

	if err := h.pointsMallService.CreateAvatar(&req, userID.(string)); err != nil {
		response.Error(ctx, http.StatusInternalServerError, err.Error())
		return
	}

	response.SuccessWithMessage(ctx, "创建成功", nil)
}

// GetPointsMallList 获取积分商城列表
func (h *PointsMallHandler) GetPointsMallList(ctx *gin.Context) {
	var req services.PointsMallListRequest
	if err := ctx.ShouldBindQuery(&req); err != nil {
		response.Error(ctx, http.StatusBadRequest, "参数错误: "+err.Error())
		return
	}

	items, total, err := h.pointsMallService.GetPointsMallList(&req)
	if err != nil {
		response.Error(ctx, http.StatusInternalServerError, err.Error())
		return
	}

	response.Page(ctx, items, total, req.Page, req.Size)
}

// GetPointsMallItem 获取积分商城商品详情
func (h *PointsMallHandler) GetPointsMallItem(ctx *gin.Context) {
	idStr := ctx.Param("id")
	id, err := strconv.ParseUint(idStr, 10, 64)
	if err != nil {
		response.Error(ctx, http.StatusBadRequest, "无效的商品ID")
		return
	}

	item, err := h.pointsMallService.GetPointsMallItem(id)
	if err != nil {
		if err.Error() == "points mall item not found" {
			response.Error(ctx, http.StatusNotFound, "商品不存在")
			return
		}
		response.Error(ctx, http.StatusInternalServerError, err.Error())
		return
	}

	response.SuccessWithMessage(ctx, "获取成功", item)
}

// UpdateProduct 更新兑换商品
func (h *PointsMallHandler) UpdateProduct(ctx *gin.Context) {
	idStr := ctx.Param("id")
	id, err := strconv.ParseUint(idStr, 10, 64)
	if err != nil {
		response.Error(ctx, http.StatusBadRequest, "无效的商品ID")
		return
	}

	var req services.UpdateProductRequest
	if err := ctx.ShouldBindJSON(&req); err != nil {
		response.Error(ctx, http.StatusBadRequest, "参数错误: "+err.Error())
		return
	}

	// 自定义验证逻辑
	if err := h.validateUpdateProductRequest(&req); err != nil {
		response.Error(ctx, http.StatusBadRequest, err.Error())
		return
	}

	if err := h.pointsMallService.UpdateProduct(id, &req); err != nil {
		if err.Error() == "points mall item not found" {
			response.Error(ctx, http.StatusNotFound, "商品不存在")
			return
		}
		response.Error(ctx, http.StatusInternalServerError, err.Error())
		return
	}

	response.SuccessWithMessage(ctx, "更新成功", nil)
}

// UpdateAvatar 更新VIP形象
func (h *PointsMallHandler) UpdateAvatar(ctx *gin.Context) {
	idStr := ctx.Param("id")
	id, err := strconv.ParseUint(idStr, 10, 64)
	if err != nil {
		response.Error(ctx, http.StatusBadRequest, "无效的商品ID")
		return
	}

	var req services.UpdateAvatarRequest
	if err := ctx.ShouldBindJSON(&req); err != nil {
		response.Error(ctx, http.StatusBadRequest, "参数错误: "+err.Error())
		return
	}

	// 自定义验证逻辑
	if err := h.validateUpdateAvatarRequest(&req); err != nil {
		response.Error(ctx, http.StatusBadRequest, err.Error())
		return
	}

	if err := h.pointsMallService.UpdateAvatar(id, &req); err != nil {
		if err.Error() == "points mall item not found" {
			response.Error(ctx, http.StatusNotFound, "商品不存在")
			return
		}
		response.Error(ctx, http.StatusInternalServerError, err.Error())
		return
	}

	response.SuccessWithMessage(ctx, "更新成功", nil)
}

// UpdatePointsMallItemStatus 更新积分商城商品状态
func (h *PointsMallHandler) UpdatePointsMallItemStatus(ctx *gin.Context) {
	idStr := ctx.Param("id")
	id, err := strconv.ParseUint(idStr, 10, 64)
	if err != nil {
		response.Error(ctx, http.StatusBadRequest, "无效的商品ID")
		return
	}

	var req struct {
		Status models.PointsMallItemStatus `json:"status" binding:"required,oneof=0 1 2 3"`
	}
	if err := ctx.ShouldBindJSON(&req); err != nil {
		response.Error(ctx, http.StatusBadRequest, "参数错误: "+err.Error())
		return
	}

	if err := h.pointsMallService.UpdatePointsMallItemStatus(id, req.Status); err != nil {
		if err.Error() == "points mall item not found" {
			response.Error(ctx, http.StatusNotFound, "商品不存在")
			return
		}
		response.Error(ctx, http.StatusInternalServerError, err.Error())
		return
	}

	response.SuccessWithMessage(ctx, "状态更新成功", nil)
}

// DeletePointsMallItem 删除积分商城商品
func (h *PointsMallHandler) DeletePointsMallItem(ctx *gin.Context) {
	idStr := ctx.Param("id")
	id, err := strconv.ParseUint(idStr, 10, 64)
	if err != nil {
		response.Error(ctx, http.StatusBadRequest, "无效的商品ID")
		return
	}

	if err := h.pointsMallService.DeletePointsMallItem(id); err != nil {
		if err.Error() == "points mall item not found" {
			response.Error(ctx, http.StatusNotFound, "商品不存在")
			return
		}
		response.Error(ctx, http.StatusInternalServerError, err.Error())
		return
	}

	response.SuccessWithMessage(ctx, "删除成功", nil)
}

// RepublishPointsMallItem 重新发布积分商城商品
func (h *PointsMallHandler) RepublishPointsMallItem(ctx *gin.Context) {
	idStr := ctx.Param("id")
	id, err := strconv.ParseUint(idStr, 10, 64)
	if err != nil {
		response.Error(ctx, http.StatusBadRequest, "无效的商品ID")
		return
	}

	// 获取商品信息以确定类型
	item, err := h.pointsMallService.GetPointsMallItem(id)
	if err != nil {
		if err.Error() == "points mall item not found" {
			response.Error(ctx, http.StatusNotFound, "商品不存在")
			return
		}
		response.Error(ctx, http.StatusInternalServerError, err.Error())
		return
	}

	// 根据商品类型处理请求
	if item.Type == models.PointsMallItemTypeProduct {
		var req services.UpdateProductRequest
		if err := ctx.ShouldBindJSON(&req); err != nil {
			response.Error(ctx, http.StatusBadRequest, "参数错误: "+err.Error())
			return
		}

		// 自定义验证逻辑
		if err := h.validateUpdateProductRequest(&req); err != nil {
			response.Error(ctx, http.StatusBadRequest, err.Error())
			return
		}

		if err := h.pointsMallService.RepublishPointsMallItem(id, &req, nil); err != nil {
			response.Error(ctx, http.StatusInternalServerError, err.Error())
			return
		}
	} else if item.Type == models.PointsMallItemTypeAvatar {
		var req services.UpdateAvatarRequest
		if err := ctx.ShouldBindJSON(&req); err != nil {
			response.Error(ctx, http.StatusBadRequest, "参数错误: "+err.Error())
			return
		}

		// 自定义验证逻辑
		if err := h.validateUpdateAvatarRequest(&req); err != nil {
			response.Error(ctx, http.StatusBadRequest, err.Error())
			return
		}

		if err := h.pointsMallService.RepublishPointsMallItem(id, nil, &req); err != nil {
			response.Error(ctx, http.StatusInternalServerError, err.Error())
			return
		}
	} else {
		response.Error(ctx, http.StatusBadRequest, "未知的商品类型")
		return
	}

	response.SuccessWithMessage(ctx, "重新发布成功", nil)
}

// validateCreateProductRequest 验证创建兑换商品请求
func (h *PointsMallHandler) validateCreateProductRequest(req *services.CreateProductRequest) error {
	// 验证商品名称
	if req.Name == "" {
		return fmt.Errorf("商品名称不能为空")
	}
	nameLen := utf8.RuneCountInString(req.Name)
	if nameLen > 30 {
		return fmt.Errorf("商品名称长度不能超过30字符")
	}

	// 验证图片数量
	if len(req.Images) > 10 {
		return fmt.Errorf("图片数量不能超过10张")
	}

	// 验证商品说明
	if req.Description != "" {
		descLen := utf8.RuneCountInString(req.Description)
		if descLen > 300 {
			return fmt.Errorf("商品说明长度不能超过300字符")
		}
	}

	return nil
}

// validateCreateAvatarRequest 验证创建VIP形象请求
func (h *PointsMallHandler) validateCreateAvatarRequest(req *services.CreateAvatarRequest) error {
	// 验证形象名称
	if req.Name == "" {
		return fmt.Errorf("形象名称不能为空")
	}

	return nil
}

// validateUpdateProductRequest 验证更新兑换商品请求
func (h *PointsMallHandler) validateUpdateProductRequest(req *services.UpdateProductRequest) error {
	// 验证商品名称
	if req.Name == "" {
		return fmt.Errorf("商品名称不能为空")
	}
	nameLen := utf8.RuneCountInString(req.Name)
	if nameLen > 30 {
		return fmt.Errorf("商品名称长度不能超过30字符")
	}

	// 验证图片数量
	if len(req.Images) > 10 {
		return fmt.Errorf("图片数量不能超过10张")
	}

	// 验证商品说明
	if req.Description != "" {
		descLen := utf8.RuneCountInString(req.Description)
		if descLen > 300 {
			return fmt.Errorf("商品说明长度不能超过300字符")
		}
	}

	return nil
}

// validateUpdateAvatarRequest 验证更新VIP形象请求
func (h *PointsMallHandler) validateUpdateAvatarRequest(req *services.UpdateAvatarRequest) error {
	// 验证形象名称
	if req.Name == "" {
		return fmt.Errorf("形象名称不能为空")
	}

	return nil
}
