package handlers

import (
	"strconv"
	"time"

	"wangfujing_admin/internal/models"
	"wangfujing_admin/internal/services"
	"wangfujing_admin/pkg/logger"
	"wangfujing_admin/pkg/response"

	"github.com/gin-gonic/gin"
	"github.com/redis/go-redis/v9"
	"gorm.io/gorm"
)

// ProductHandler 商品处理器
type ProductHandler struct {
	*Handler
	productService *services.ProductService
}

// NewProductHandler 创建商品处理器
func NewProductHandler(db *gorm.DB, rdb *redis.Client) *ProductHandler {
	return &ProductHandler{
		Handler:        &Handler{db: db, rdb: rdb},
		productService: services.NewProductService(db),
	}
}

// CreateProductRequest 创建商品请求
type CreateProductRequest struct {
	Name        string   `json:"name" binding:"required,min=1,max=20"`
	Description string   `json:"description" binding:"max=300"`
	Images      []string `json:"images" binding:"max=10"`
	Points      int      `json:"points" binding:"required,min=1,max=1000"`
	DailyLimit  int      `json:"daily_limit" binding:"required,min=1,max=1000"`
	ValidPeriod string   `json:"valid_period" binding:"required,oneof=today month three_months"`
}

// UpdateProductRequest 更新商品请求
type UpdateProductRequest struct {
	Name        string   `json:"name" binding:"required,min=1,max=20"`
	Description string   `json:"description" binding:"max=300"`
	Images      []string `json:"images" binding:"max=10"`
	Points      int      `json:"points" binding:"required,min=1,max=1000"`
	DailyLimit  int      `json:"daily_limit" binding:"required,min=1,max=1000"`
	ValidPeriod string   `json:"valid_period" binding:"required,oneof=today month three_months"`
}

// ApprovalRequest 审核请求
type ApprovalRequest struct {
	Approved *bool  `json:"approved" binding:"required"`
	Note     string `json:"note" binding:"max=100"`
}

// CreateProduct 创建商品（商家端）
func (h *ProductHandler) CreateProduct(c *gin.Context) {
	var req CreateProductRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		logger.Error("创建商品参数绑定失败",
			logger.String("error", err.Error()),
			logger.String("client_ip", c.ClientIP()),
		)
		response.BadRequest(c, "参数错误: "+err.Error())
		return
	}

	// 获取商家用户ID
	userIDStr := c.GetString("user_id")
	userID, err := strconv.ParseUint(userIDStr, 10, 64)
	if err != nil {
		response.BadRequest(c, "用户ID无效")
		return
	}

	// 查询商家信息
	var adminUser models.AdminUser
	if err := h.db.First(&adminUser, userID).Error; err != nil {
		response.InternalServerError(c, "查询用户信息失败")
		return
	}

	if adminUser.MerchantID == nil {
		response.BadRequest(c, "用户未关联商家")
		return
	}

	// 计算有效期
	validTo := calculateValidTo(req.ValidPeriod)

	// 创建商品
	product := &models.Product{
		Name:        req.Name,
		Description: req.Description,
		Images:      convertImagesToJSON(req.Images),
		Points:      req.Points,
		DailyLimit:  req.DailyLimit,
		Type:        models.ProductTypeMerchant,
		MerchantID:  adminUser.MerchantID,
		ValidTo:     &validTo,
	}

	if err := h.productService.CreateProduct(c.Request.Context(), product); err != nil {
		logger.Error("创建商品失败",
			logger.String("user_id", userIDStr),
			logger.String("error", err.Error()),
			logger.String("client_ip", c.ClientIP()),
		)
		response.InternalServerError(c, "创建商品失败")
		return
	}

	response.Success(c, gin.H{
		"message": "商品创建成功，已提交审核",
		"id":      product.ID,
	})
}

// UpdateProduct 更新商品（商家端）
func (h *ProductHandler) UpdateProduct(c *gin.Context) {
	id, err := strconv.ParseUint(c.Param("id"), 10, 64)
	if err != nil {
		response.BadRequest(c, "商品ID无效")
		return
	}

	var req UpdateProductRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		logger.Error("更新商品参数绑定失败",
			logger.String("product_id", c.Param("id")),
			logger.String("error", err.Error()),
			logger.String("client_ip", c.ClientIP()),
		)
		response.BadRequest(c, "参数错误: "+err.Error())
		return
	}

	// 获取商家用户ID
	userIDStr := c.GetString("user_id")
	userID, err := strconv.ParseUint(userIDStr, 10, 64)
	if err != nil {
		response.BadRequest(c, "用户ID无效")
		return
	}

	// 查询商品
	product, err := h.productService.GetProductByID(c.Request.Context(), id)
	if err != nil {
		response.NotFound(c, "商品不存在")
		return
	}

	// 检查权限
	var adminUser models.AdminUser
	if err := h.db.First(&adminUser, userID).Error; err != nil {
		response.InternalServerError(c, "查询用户信息失败")
		return
	}

	if adminUser.MerchantID == nil || *adminUser.MerchantID != *product.MerchantID {
		response.Forbidden(c, "无权限操作此商品")
		return
	}

	// 检查是否可以编辑
	if !product.CanEdit() {
		response.BadRequest(c, "商品当前状态不允许编辑")
		return
	}

	// 计算有效期
	validTo := calculateValidTo(req.ValidPeriod)

	// 更新商品
	updates := map[string]interface{}{
		"name":        req.Name,
		"description": req.Description,
		"images":      convertImagesToJSON(req.Images),
		"points":      req.Points,
		"daily_limit": req.DailyLimit,
		"valid_to":    validTo,
	}

	if err := h.productService.UpdateProduct(c.Request.Context(), id, updates); err != nil {
		logger.Error("更新商品失败",
			logger.String("product_id", c.Param("id")),
			logger.String("user_id", userIDStr),
			logger.String("error", err.Error()),
			logger.String("client_ip", c.ClientIP()),
		)
		response.InternalServerError(c, "更新商品失败")
		return
	}

	response.Success(c, gin.H{
		"message": "商品更新成功，已重新提交审核",
	})
}

// GetMerchantProducts 获取商家商品列表（商家端）
func (h *ProductHandler) GetMerchantProducts(c *gin.Context) {
	// 获取分页参数
	page, size := getPaginationParams(c)
	status := c.Query("status") // pending, approved, rejected, offline

	// 获取商家用户ID
	userIDStr := c.GetString("user_id")
	userID, err := strconv.ParseUint(userIDStr, 10, 64)
	if err != nil {
		response.BadRequest(c, "用户ID无效")
		return
	}

	// 查询商家信息
	var adminUser models.AdminUser
	if err := h.db.First(&adminUser, userID).Error; err != nil {
		response.InternalServerError(c, "查询用户信息失败")
		return
	}

	if adminUser.MerchantID == nil {
		response.BadRequest(c, "用户未关联商家")
		return
	}

	// 获取商品列表
	products, total, err := h.productService.GetMerchantProducts(c.Request.Context(), *adminUser.MerchantID, page, size, status)
	if err != nil {
		logger.Error("获取商家商品列表失败",
			logger.String("merchant_id", strconv.FormatUint(*adminUser.MerchantID, 10)),
			logger.String("error", err.Error()),
			logger.String("client_ip", c.ClientIP()),
		)
		response.InternalServerError(c, "获取商品列表失败")
		return
	}

	// 转换响应数据
	var productList []gin.H
	for _, product := range products {
		// 获取剩余库存
		remainStock := product.DailyLimit

		// 如果商品已上架且审核通过，查询Redis中的实际库存
		if product.Status == models.StatusActive && product.ApprovalStatus == models.ApprovalStatusApproved {
			seckillService := services.NewSeckillService(h.db, h.rdb)
			stockInfo, err := seckillService.GetProductStock(c.Request.Context(), product.ID)
			if err == nil && stockInfo != nil {
				remainStock = stockInfo.Stock
			} else {
				// 如果Redis中没有库存信息，查询数据库计算剩余库存
				today := time.Now().Format("2006-01-02")
				var usedCount int64
				h.db.Model(&models.Order{}).
					Where("product_id = ? AND DATE(created_at) = ? AND status != ?",
						product.ID, today, models.OrderStatusCancelled).
					Count(&usedCount)

				remainStock = product.DailyLimit - int(usedCount)
				if remainStock < 0 {
					remainStock = 0
				}
			}
		}

		productList = append(productList, gin.H{
			"id":              product.ID,
			"name":            product.Name,
			"description":     product.Description,
			"images":          convertJSONToImages(product.Images),
			"points":          product.Points,
			"daily_limit":     product.DailyLimit,
			"remain_stock":    remainStock,
			"valid_to":        formatTime(product.ValidTo),
			"approval_status": product.ApprovalStatus,
			"approval_note":   product.ApprovalNote,
			"status":          product.Status,
			"status_text":     product.GetStatusText(),
			"can_edit":        product.CanEdit(),
			"can_delete":      product.CanDelete(),
			"can_toggle":      product.CanToggleStatus(),
			"submitted_at":    formatTime(product.SubmittedAt),
			"approved_at":     formatTime(product.ApprovedAt),
			"created_at":      formatStandardTime(&product.CreatedAt),
			"updated_at":      formatStandardTime(&product.UpdatedAt),
		})
	}

	response.Success(c, gin.H{
		"list":  productList,
		"total": total,
		"page":  page,
		"size":  size,
	})
}

// GetProductDetail 获取商品详情（商家端）
func (h *ProductHandler) GetProductDetail(c *gin.Context) {
	id, err := strconv.ParseUint(c.Param("id"), 10, 64)
	if err != nil {
		response.BadRequest(c, "商品ID无效")
		return
	}

	// 获取商家用户ID
	userIDStr := c.GetString("user_id")
	userID, err := strconv.ParseUint(userIDStr, 10, 64)
	if err != nil {
		response.BadRequest(c, "用户ID无效")
		return
	}

	// 查询商品
	product, err := h.productService.GetProductByID(c.Request.Context(), id)
	if err != nil {
		response.NotFound(c, "商品不存在")
		return
	}

	// 检查权限
	var adminUser models.AdminUser
	if err := h.db.First(&adminUser, userID).Error; err != nil {
		response.InternalServerError(c, "查询用户信息失败")
		return
	}

	if adminUser.MerchantID == nil || *adminUser.MerchantID != *product.MerchantID {
		response.Forbidden(c, "无权限查看此商品")
		return
	}

	// 获取剩余库存
	remainStock := product.DailyLimit

	// 如果商品已上架且审核通过，查询Redis中的实际库存
	if product.Status == models.StatusActive && product.ApprovalStatus == models.ApprovalStatusApproved {
		seckillService := services.NewSeckillService(h.db, h.rdb)
		stockInfo, err := seckillService.GetProductStock(c.Request.Context(), product.ID)
		if err == nil && stockInfo != nil {
			remainStock = stockInfo.Stock
		} else {
			// 如果Redis中没有库存信息，查询数据库计算剩余库存
			today := time.Now().Format("2006-01-02")
			var usedCount int64
			h.db.Model(&models.Order{}).
				Where("product_id = ? AND DATE(created_at) = ? AND status != ?",
					product.ID, today, models.OrderStatusCancelled).
				Count(&usedCount)

			remainStock = product.DailyLimit - int(usedCount)
			if remainStock < 0 {
				remainStock = 0
			}
		}
	}

	response.Success(c, gin.H{
		"id":              product.ID,
		"name":            product.Name,
		"description":     product.Description,
		"images":          convertJSONToImages(product.Images),
		"points":          product.Points,
		"daily_limit":     product.DailyLimit,
		"remain_stock":    remainStock,
		"valid_to":        formatTime(product.ValidTo),
		"approval_status": product.ApprovalStatus,
		"approval_note":   product.ApprovalNote,
		"status":          product.Status,
		"status_text":     product.GetStatusText(),
		"can_edit":        product.CanEdit(),
		"can_delete":      product.CanDelete(),
		"can_toggle":      product.CanToggleStatus(),
		"submitted_at":    formatTime(product.SubmittedAt),
		"approved_at":     formatTime(product.ApprovedAt),
		"created_at":      formatStandardTime(&product.CreatedAt),
		"updated_at":      formatStandardTime(&product.UpdatedAt),
	})
}

// ToggleProductStatus 切换商品上下架状态（商家端）
func (h *ProductHandler) ToggleProductStatus(c *gin.Context) {
	id, err := strconv.ParseUint(c.Param("id"), 10, 64)
	if err != nil {
		response.BadRequest(c, "商品ID无效")
		return
	}

	// 获取商家用户ID
	userIDStr := c.GetString("user_id")
	userID, err := strconv.ParseUint(userIDStr, 10, 64)
	if err != nil {
		response.BadRequest(c, "用户ID无效")
		return
	}

	// 查询商品
	product, err := h.productService.GetProductByID(c.Request.Context(), id)
	if err != nil {
		response.NotFound(c, "商品不存在")
		return
	}

	// 检查权限
	var adminUser models.AdminUser
	if err := h.db.First(&adminUser, userID).Error; err != nil {
		response.InternalServerError(c, "查询用户信息失败")
		return
	}

	if adminUser.MerchantID == nil || *adminUser.MerchantID != *product.MerchantID {
		response.Forbidden(c, "无权限操作此商品")
		return
	}

	// 切换状态
	if err := h.productService.ToggleProductStatus(c.Request.Context(), id); err != nil {
		logger.Error("切换商品状态失败",
			logger.String("product_id", c.Param("id")),
			logger.String("user_id", userIDStr),
			logger.String("error", err.Error()),
			logger.String("client_ip", c.ClientIP()),
		)
		response.BadRequest(c, err.Error())
		return
	}

	response.Success(c, gin.H{
		"message": "商品状态切换成功",
	})
}

// DeleteProduct 删除商品（商家端）
func (h *ProductHandler) DeleteProduct(c *gin.Context) {
	id, err := strconv.ParseUint(c.Param("id"), 10, 64)
	if err != nil {
		response.BadRequest(c, "商品ID无效")
		return
	}

	// 获取商家用户ID
	userIDStr := c.GetString("user_id")
	userID, err := strconv.ParseUint(userIDStr, 10, 64)
	if err != nil {
		response.BadRequest(c, "用户ID无效")
		return
	}

	// 查询商品
	product, err := h.productService.GetProductByID(c.Request.Context(), id)
	if err != nil {
		response.NotFound(c, "商品不存在")
		return
	}

	// 检查权限
	var adminUser models.AdminUser
	if err := h.db.First(&adminUser, userID).Error; err != nil {
		response.InternalServerError(c, "查询用户信息失败")
		return
	}

	if adminUser.MerchantID == nil || *adminUser.MerchantID != *product.MerchantID {
		response.Forbidden(c, "无权限操作此商品")
		return
	}

	// 删除商品
	if err := h.productService.DeleteProduct(c.Request.Context(), id); err != nil {
		logger.Error("删除商品失败",
			logger.String("product_id", c.Param("id")),
			logger.String("user_id", userIDStr),
			logger.String("error", err.Error()),
			logger.String("client_ip", c.ClientIP()),
		)
		response.BadRequest(c, err.Error())
		return
	}

	response.Success(c, gin.H{
		"message": "商品删除成功",
	})
}

// GetProductsForApproval 获取待审核商品列表（商场端）
func (h *ProductHandler) GetProductsForApproval(c *gin.Context) {
	// 获取分页参数
	page, size := getPaginationParams(c)
	status := c.Query("status") // pending, approved, rejected

	// 获取商品列表
	products, total, err := h.productService.GetProductsForApproval(c.Request.Context(), page, size, status)
	if err != nil {
		logger.Error("获取审核商品列表失败",
			logger.String("error", err.Error()),
			logger.String("client_ip", c.ClientIP()),
		)
		response.InternalServerError(c, "获取商品列表失败")
		return
	}

	// 转换响应数据
	var productList []gin.H
	for _, product := range products {
		merchantName := ""
		if product.Merchant != nil {
			merchantName = product.Merchant.Name
		}

		productList = append(productList, gin.H{
			"id":              product.ID,
			"name":            product.Name,
			"description":     product.Description,
			"images":          convertJSONToImages(product.Images),
			"points":          product.Points,
			"daily_limit":     product.DailyLimit,
			"valid_to":        formatTime(product.ValidTo),
			"approval_status": product.ApprovalStatus,
			"approval_note":   product.ApprovalNote,
			"merchant_id":     product.MerchantID,
			"merchant_name":   merchantName,
			"submitted_at":    formatTime(product.SubmittedAt),
			"approved_at":     formatTime(product.ApprovedAt),
			"created_at":      formatStandardTime(&product.CreatedAt),
			"updated_at":      formatStandardTime(&product.UpdatedAt),
		})
	}

	response.Success(c, gin.H{
		"list":  productList,
		"total": total,
		"page":  page,
		"size":  size,
	})
}

// ApproveProduct 审核商品（商场端）
func (h *ProductHandler) ApproveProduct(c *gin.Context) {
	id, err := strconv.ParseUint(c.Param("id"), 10, 64)
	if err != nil {
		response.BadRequest(c, "商品ID无效")
		return
	}

	var req ApprovalRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		logger.Error("审核商品参数绑定失败",
			logger.String("product_id", c.Param("id")),
			logger.String("error", err.Error()),
			logger.String("client_ip", c.ClientIP()),
		)
		response.BadRequest(c, "参数错误: "+err.Error())
		return
	}

	// 获取审核人ID
	userIDStr := c.GetString("user_id")
	userID, err := strconv.ParseUint(userIDStr, 10, 64)
	if err != nil {
		response.BadRequest(c, "用户ID无效")
		return
	}

	// 查询商品
	product, err := h.productService.GetProductByID(c.Request.Context(), id)
	if err != nil {
		response.NotFound(c, "商品不存在")
		return
	}

	// 检查是否可以审核
	if product.ApprovalStatus != models.ApprovalStatusPending {
		response.BadRequest(c, "商品不在待审核状态")
		return
	}

	// 检查参数
	if req.Approved == nil {
		response.BadRequest(c, "审核结果不能为空")
		return
	}

	// 审核商品
	if err := h.productService.ApproveProduct(c.Request.Context(), id, userID, *req.Approved, req.Note); err != nil {
		logger.Error("审核商品失败",
			logger.String("product_id", c.Param("id")),
			logger.String("user_id", userIDStr),
			logger.Bool("approved", *req.Approved),
			logger.String("error", err.Error()),
			logger.String("client_ip", c.ClientIP()),
		)
		response.InternalServerError(c, "审核商品失败")
		return
	}

	// 如果审核通过，自动初始化Redis库存
	if *req.Approved {
		stockAutoInitService := services.NewProductStockAutoInitService(h.db, h.rdb)
		if err := stockAutoInitService.AutoInitStockOnApproval(c.Request.Context(), id); err != nil {
			logger.Error("自动初始化库存失败",
				logger.String("product_id", c.Param("id")),
				logger.String("error", err.Error()),
			)
			// 记录错误但不影响审核结果，因为可以后续手动初始化
		} else {
			logger.Info("商品审核通过，库存已自动初始化",
				logger.String("product_id", c.Param("id")),
			)
		}
	}

	statusText := "驳回"
	if *req.Approved {
		statusText = "通过"
	}

	response.Success(c, gin.H{
		"message": "商品审核" + statusText + "成功",
	})
}

// 工具函数
func convertImagesToJSON(images []string) string {
	if len(images) == 0 {
		return "[]"
	}
	// 这里应该使用 JSON 序列化，简化处理
	result := "["
	for i, img := range images {
		if i > 0 {
			result += ","
		}
		result += `"` + img + `"`
	}
	result += "]"
	return result
}

func convertJSONToImages(jsonStr string) []string {
	if jsonStr == "" || jsonStr == "[]" {
		return []string{}
	}
	// 这里应该使用 JSON 反序列化，简化处理
	// 实际项目中应该使用 json.Unmarshal
	return []string{} // 简化返回
}

func formatTime(t *time.Time) string {
	if t == nil {
		return ""
	}
	return t.Format("2006-01-02 15:04")
}

// formatStandardTime 格式化标准时间为 "2025-07-11 10:46:16" 格式
func formatStandardTime(t *time.Time) string {
	if t == nil || t.IsZero() {
		return ""
	}
	return t.Format("2006-01-02 15:04:05")
}

// calculateValidTo 根据有效期类型计算结束时间
func calculateValidTo(validPeriod string) time.Time {
	now := time.Now()

	switch validPeriod {
	case "today":
		// 当日23:59:59
		return time.Date(now.Year(), now.Month(), now.Day(), 23, 59, 59, 0, now.Location())
	case "month":
		// 本月最后一天23:59:59
		nextMonth := now.AddDate(0, 1, 0)
		lastDayOfMonth := time.Date(nextMonth.Year(), nextMonth.Month(), 0, 23, 59, 59, 0, now.Location())
		return lastDayOfMonth
	case "three_months":
		// 三个月后的最后一天23:59:59（包含本月）
		threeMonthsLater := now.AddDate(0, 3, 0)
		lastDayOfThirdMonth := time.Date(threeMonthsLater.Year(), threeMonthsLater.Month(), 0, 23, 59, 59, 0, now.Location())
		return lastDayOfThirdMonth
	default:
		// 默认当日
		return time.Date(now.Year(), now.Month(), now.Day(), 23, 59, 59, 0, now.Location())
	}
}
