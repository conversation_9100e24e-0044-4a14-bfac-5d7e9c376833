package handlers

import (
	"fmt"
	"log"
	"net/http"
	"strconv"
	"wangfujing_admin/internal/services"
	"wangfujing_admin/pkg/json"
	"wangfujing_admin/pkg/logger"
	"wangfujing_admin/pkg/response"

	"github.com/gin-gonic/gin"
	"gorm.io/gorm"
)

// ProgramHandler 节目单处理器
type ProgramHandler struct {
	*Handler
	programService *services.ProgramService
}

// NewProgramHandler 创建节目单处理器
func NewProgramHandler(programService *services.ProgramService, db *gorm.DB) *ProgramHandler {
	return &ProgramHandler{
		Handler:        &Handler{db: db},
		programService: programService,
	}
}

type TimeSlot struct {
	StartHour string `json:"start_hour"`
	EndHour   string `json:"end_hour"`
}

// CreateProgram 创建节目单
func (h *ProgramHandler) CreateProgram(ctx *gin.Context) {

	var req services.CreateProgramRequest
	if err := ctx.ShouldBindJSON(&req); err != nil {
		response.Error(ctx, http.StatusBadRequest, "参数错误: "+err.Error())
		return
	}
	// 自定义验证逻辑
	if err := h.validateCreateProgramRequest(&req, ctx); err != nil {
		response.Error(ctx, http.StatusBadRequest, err.Error())
		return
	}

	// 获取当前用户ID
	userID, exists := ctx.Get("user_id")
	if !exists {
		response.Error(ctx, http.StatusUnauthorized, "未授权")
		return
	}

	if err := h.programService.CreateProgram(&req, userID.(string)); err != nil {
		response.Error(ctx, http.StatusInternalServerError, err.Error())
		return
	}

	response.SuccessWithMessage(ctx, "创建成功", nil)
}

// validateCreateProgramRequest 验证创建节目单请求
func (h *ProgramHandler) validateCreateProgramRequest(req *services.CreateProgramRequest, c *gin.Context) error {
	FloorId := req.FloorId
	FloorIdStr := strconv.Itoa(FloorId)
	floorService := services.NewFloorService(h.db)
	_, err := floorService.GetFloorByID(c.Request.Context(), FloorIdStr)
	if err != nil {
		if err.Error() == "floor not found" {
			return fmt.Errorf("Floor not found")
		}
		return fmt.Errorf("Failed to get floor")
	}
	return nil
}

func (h *ProgramHandler) validateUpdateProgramRequest(req *services.UpdateProgramRequest, c *gin.Context) error {
	FloorId := req.FloorId
	FloorIdStr := strconv.Itoa(FloorId)
	floorService := services.NewFloorService(h.db)
	_, err := floorService.GetFloorByID(c.Request.Context(), FloorIdStr)
	if err != nil {
		if err.Error() == "floor not found" {
			return fmt.Errorf("Floor not found")
		}
		return fmt.Errorf("Failed to get floor")
	}
	return nil
}
func (h *ProgramHandler) GetPrograms(ctx *gin.Context) {
	// 获取分页参数
	page, size := getPaginationParams(ctx)
	types := ctx.Query("type") // now 今日节目 future 未来节目 expired 过期节目 all 所有节目

	// 获取商品列表
	programs, total, err := h.programService.GetPrograms(ctx.Request.Context(), page, size, types)
	if err != nil {
		logger.Error("获取节目单列表失败",
			logger.String("error", err.Error()),
			logger.String("client_ip", ctx.ClientIP()),
		)
		response.InternalServerError(ctx, "获取节目单列表失败")
		return
	}

	// 转换响应数据
	var programList []gin.H
	for _, program := range programs {
		var timeSlots []TimeSlot
		if err := json.Unmarshal([]byte(program.ShowTimeHour), &timeSlots); err != nil {
			// 解析失败时返回空数组
			timeSlots = []TimeSlot{}
			log.Printf("JSON解析失败: %v | 原始数据: %s", err, program.ShowTimeHour)
		}
		var urls []string
		if err := json.Unmarshal([]byte(program.Url), &urls); err != nil {
			urls = []string{} // 解析失败返回空数组
			log.Printf("URL解析失败: %v | 原始数据: %s", err, program.Url)
		}
		programList = append(programList, gin.H{
			"id":                   program.ID,
			"program_name":         program.ProgramName,
			"program_introduction": program.ProgramIntroduction,
			"show_time_hour":       timeSlots,
			"floor_name":           program.FloorName,
			"show_time_start":      program.ShowTimeStart,
			"show_time_end":        program.ShowTimeEnd,
			"url":                  urls,
		})
	}

	response.Success(ctx, gin.H{
		"list":  programList,
		"total": total,
		"page":  page,
		"size":  size,
	})
}

// 更新节目单
func (h *ProgramHandler) UpdateProgram(ctx *gin.Context) {
	idStr := ctx.Param("id")
	id, err := strconv.ParseUint(idStr, 10, 64)
	if err != nil {
		response.Error(ctx, http.StatusBadRequest, "无效的节目单ID")
		return
	}
	var req services.UpdateProgramRequest
	if err := ctx.ShouldBindJSON(&req); err != nil {
		response.Error(ctx, http.StatusBadRequest, "参数错误: "+err.Error())
		return
	}
	// 自定义验证逻辑
	if err := h.validateUpdateProgramRequest(&req, ctx); err != nil {
		response.Error(ctx, http.StatusBadRequest, err.Error())
		return
	}

	if err := h.programService.UpdateProgram(id, &req); err != nil {
		response.Error(ctx, http.StatusInternalServerError, err.Error())
		return
	}

	response.SuccessWithMessage(ctx, "更新成功", nil)
}

func (h *ProgramHandler) DeleteProgram(ctx *gin.Context) {
	idStr := ctx.Param("id")
	id, err := strconv.ParseUint(idStr, 10, 64)
	if err != nil {
		response.Error(ctx, http.StatusBadRequest, "无效的商品ID")
		return
	}
	if err := h.programService.DeleteProgram(ctx.Request.Context(), id); err != nil {
		if err.Error() == "points mall item not found" {
			response.Error(ctx, http.StatusNotFound, "无效的节目单ID")
			return
		}
		response.Error(ctx, http.StatusInternalServerError, err.Error())
		return
	}

	response.SuccessWithMessage(ctx, "删除成功", nil)
}
