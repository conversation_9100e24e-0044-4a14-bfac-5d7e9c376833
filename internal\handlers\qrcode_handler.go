package handlers

import (
	"strconv"
	"wangfujing_admin/pkg/response"

	"github.com/gin-gonic/gin"
)

// GenerateOrderQRCode 生成订单二维码
func (h *Handler) GenerateOrderQRCode(c *gin.Context) {
	orderIDStr := c.Param("order_id")
	orderID, err := strconv.ParseUint(orderIDStr, 10, 64)
	if err != nil {
		response.BadRequest(c, "订单ID格式错误")
		return
	}

	qrCodeURL, err := h.qrCodeService.GenerateOrderQRCode(c.Request.Context(), orderID)
	if err != nil {
		response.InternalServerError(c, "生成二维码失败: "+err.Error())
		return
	}

	response.Success(c, gin.H{
		"qr_code_url": qrCodeURL,
	})
}

// RegenerateOrderQRCode 重新生成订单二维码
func (h *Handler) RegenerateOrderQRCode(c *gin.Context) {
	orderIDStr := c.Param("order_id")
	orderID, err := strconv.ParseUint(orderIDStr, 10, 64)
	if err != nil {
		response.BadRequest(c, "订单ID格式错误")
		return
	}

	qrCodeURL, err := h.qrCodeService.RegenerateQRCode(c.Request.Context(), orderID)
	if err != nil {
		response.InternalServerError(c, "重新生成二维码失败: "+err.Error())
		return
	}

	response.Success(c, gin.H{
		"qr_code_url": qrCodeURL,
	})
}

// BatchGenerateQRCodes 批量生成二维码
func (h *Handler) BatchGenerateQRCodes(c *gin.Context) {
	type BatchGenerateRequest struct {
		Limit int `json:"limit" binding:"required,min=1,max=1000"`
	}

	var req BatchGenerateRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		response.BadRequest(c, "参数错误: "+err.Error())
		return
	}

	err := h.qrCodeService.BatchGenerateQRCodes(c.Request.Context(), req.Limit)
	if err != nil {
		response.InternalServerError(c, "批量生成二维码失败: "+err.Error())
		return
	}

	response.SuccessWithMessage(c, "批量生成二维码任务已启动", nil)
}

// GetQRCodeStats 获取二维码生成统计
func (h *Handler) GetQRCodeStats(c *gin.Context) {
	stats, err := h.qrCodeService.GetQRCodeStats(c.Request.Context())
	if err != nil {
		response.InternalServerError(c, "获取统计信息失败: "+err.Error())
		return
	}

	response.Success(c, stats)
}
