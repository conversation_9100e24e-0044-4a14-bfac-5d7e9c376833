package handlers

import (
	"strconv"
	"wangfujing_admin/internal/models"
	"wangfujing_admin/internal/services"
	"wangfujing_admin/pkg/logger"
	"wangfujing_admin/pkg/response"

	"github.com/gin-gonic/gin"
)

// GetRoles 获取角色列表
func (h *Handler) GetRoles(c *gin.Context) {
	roleService := services.NewRoleService(h.db)
	roles, err := roleService.GetAllRoles(c.Request.Context())
	if err != nil {
		// 记录详细错误日志
		logger.Error("获取角色列表失败",
			logger.String("error", err.Error()),
			logger.String("client_ip", c.ClientIP()),
		)
		response.InternalServerError(c, "获取角色列表失败: "+err.Error())
		return
	}

	response.Success(c, gin.H{
		"roles": roles,
	})
}

// GetRole 获取角色详情
func (h *Handler) GetRole(c *gin.Context) {
	id, err := strconv.Atoi(c.Param("id"))
	if err != nil {
		response.BadRequest(c, "Invalid role ID")
		return
	}

	roleService := services.NewRoleService(h.db)
	idStr := strconv.Itoa(id)
	role, err := roleService.GetRoleWithPermissions(c.Request.Context(), idStr)
	if err != nil {
		if err.Error() == "role not found" {
			response.NotFound(c, "角色不存在")
			return
		}
		logger.Error("获取角色失败",
			logger.String("id", idStr),
			logger.String("error", err.Error()),
			logger.String("client_ip", c.ClientIP()),
		)
		response.InternalServerError(c, "获取角色失败: "+err.Error())
		return
	}

	response.Success(c, gin.H{
		"role": role,
	})
}

// CreateRole 创建角色
func (h *Handler) CreateRole(c *gin.Context) {
	var req struct {
		Name        string `json:"name" binding:"required"`
		DisplayName string `json:"display_name"`
		Description string `json:"description"`
	}

	if err := c.ShouldBindJSON(&req); err != nil {
		response.BadRequest(c, "参数错误: "+err.Error())
		return
	}

	role := &models.Role{
		Name:        req.Name,
		DisplayName: req.DisplayName,
		Description: req.Description,
	}

	roleService := services.NewRoleService(h.db)
	if err := roleService.CreateRole(c.Request.Context(), role); err != nil {
		logger.Error("创建角色失败",
			logger.String("name", req.Name),
			logger.String("display_name", req.DisplayName),
			logger.String("error", err.Error()),
			logger.String("client_ip", c.ClientIP()),
		)
		response.InternalServerError(c, "创建角色失败: "+err.Error())
		return
	}

	response.Success(c, gin.H{
		"role": role,
	})
}

// UpdateRole 更新角色
func (h *Handler) UpdateRole(c *gin.Context) {
	id, err := strconv.Atoi(c.Param("id"))
	if err != nil {
		response.BadRequest(c, "Invalid role ID")
		return
	}

	var req struct {
		Name        string `json:"name" binding:"required"`
		DisplayName string `json:"display_name"`
		Description string `json:"description"`
	}

	if err := c.ShouldBindJSON(&req); err != nil {
		response.BadRequest(c, "参数错误: "+err.Error())
		return
	}

	roleService := services.NewRoleService(h.db)

	// 先检查角色是否存在
	idStr := strconv.Itoa(id)
	_, err = roleService.GetRoleByID(c.Request.Context(), idStr)
	if err != nil {
		if err.Error() == "role not found" {
			response.NotFound(c, "角色不存在")
			return
		}
		logger.Error("查询角色失败",
			logger.String("id", idStr),
			logger.String("error", err.Error()),
			logger.String("client_ip", c.ClientIP()),
		)
		response.InternalServerError(c, "查询角色失败: "+err.Error())
		return
	}

	// 构建更新数据
	updateData := map[string]interface{}{
		"name":         req.Name,
		"display_name": req.DisplayName,
		"description":  req.Description,
	}

	if err := roleService.UpdateRole(c.Request.Context(), idStr, updateData); err != nil {
		logger.Error("更新角色失败",
			logger.String("id", idStr),
			logger.String("name", req.Name),
			logger.String("error", err.Error()),
			logger.String("client_ip", c.ClientIP()),
		)
		response.InternalServerError(c, "更新角色失败: "+err.Error())
		return
	}

	// 获取更新后的角色
	role, err := roleService.GetRoleByID(c.Request.Context(), idStr)
	if err != nil {
		logger.Error("获取更新后的角色失败",
			logger.String("id", idStr),
			logger.String("error", err.Error()),
			logger.String("client_ip", c.ClientIP()),
		)
		response.InternalServerError(c, "获取更新后的角色失败: "+err.Error())
		return
	}

	response.Success(c, gin.H{
		"role": role,
	})
}

// DeleteRole 删除角色
func (h *Handler) DeleteRole(c *gin.Context) {
	id, err := strconv.Atoi(c.Param("id"))
	if err != nil {
		response.BadRequest(c, "Invalid role ID")
		return
	}

	roleService := services.NewRoleService(h.db)
	idStr := strconv.Itoa(id)

	// 先检查角色是否存在
	_, err = roleService.GetRoleByID(c.Request.Context(), idStr)
	if err != nil {
		if err.Error() == "role not found" {
			response.NotFound(c, "角色不存在")
			return
		}
		logger.Error("查询角色失败",
			logger.String("id", idStr),
			logger.String("error", err.Error()),
			logger.String("client_ip", c.ClientIP()),
		)
		response.InternalServerError(c, "查询角色失败: "+err.Error())
		return
	}

	if err := roleService.DeleteRole(c.Request.Context(), idStr); err != nil {
		logger.Error("删除角色失败",
			logger.String("id", idStr),
			logger.String("error", err.Error()),
			logger.String("client_ip", c.ClientIP()),
		)
		response.InternalServerError(c, "删除角色失败: "+err.Error())
		return
	}

	response.Success(c, gin.H{
		"message": "删除成功",
	})
}

// AssignPermissionsToRole 为角色分配权限
func (h *Handler) AssignPermissionsToRole(c *gin.Context) {
	id, err := strconv.Atoi(c.Param("id"))
	if err != nil {
		response.BadRequest(c, "Invalid role ID")
		return
	}

	var req struct {
		PermissionIDs []int `json:"permission_ids" binding:"required"`
	}

	if err := c.ShouldBindJSON(&req); err != nil {
		logger.Error("权限分配参数绑定失败",
			logger.String("role_id", strconv.Itoa(id)),
			logger.String("error", err.Error()),
			logger.String("client_ip", c.ClientIP()),
		)
		response.BadRequest(c, "参数错误: "+err.Error())
		return
	}

	// 转换int数组为string数组
	permissionIDs := make([]string, len(req.PermissionIDs))
	for i, permID := range req.PermissionIDs {
		permissionIDs[i] = strconv.Itoa(permID)
	}

	roleService := services.NewRoleService(h.db)
	idStr := strconv.Itoa(id)
	if err := roleService.AssignPermissions(c.Request.Context(), idStr, permissionIDs); err != nil {
		logger.Error("分配权限失败",
			logger.String("role_id", idStr),
			logger.Any("permission_ids", req.PermissionIDs),
			logger.String("error", err.Error()),
			logger.String("client_ip", c.ClientIP()),
		)
		response.InternalServerError(c, "分配权限失败: "+err.Error())
		return
	}

	response.Success(c, gin.H{
		"message": "权限分配成功",
	})
}

// AssignRoleToUser 为用户分配角色 - 待实现
func (h *Handler) AssignRoleToUser(c *gin.Context) {
	response.Success(c, gin.H{
		"message": "用户角色分配功能待实现",
	})
}

// RemoveRoleFromUser 移除用户角色 - 待实现
func (h *Handler) RemoveRoleFromUser(c *gin.Context) {
	response.Success(c, gin.H{
		"message": "用户角色移除功能待实现",
	})
}

// GetUserRoles 获取用户角色列表 - 待实现
func (h *Handler) GetUserRoles(c *gin.Context) {
	response.Success(c, gin.H{
		"message": "获取用户角色功能待实现",
	})
}
