package handlers

import (
	"context"
	"fmt"
	"sync"
	"time"

	"wangfujing_admin/internal/config"
	"wangfujing_admin/internal/services"
	"wangfujing_admin/pkg/logger"
	"wangfujing_admin/pkg/response"

	"github.com/gin-gonic/gin"
)

// AdminTestPurchaseRequest 管理端测试抢购请求
type AdminTestPurchaseRequest struct {
	ProductID uint64 `json:"product_id" binding:"required"`
	UserID    uint64 `json:"user_id" binding:"required"`
}

// AdminTestConcurrentRequest 并发测试请求
type AdminTestConcurrentRequest struct {
	ProductID     uint64 `json:"product_id" binding:"required"`
	UserID        uint64 `json:"user_id" binding:"required"`
	ConcurrentNum int    `json:"concurrent_num" binding:"required,min=1,max=1000"` // 并发数量，最大1000
	Duration      int    `json:"duration" binding:"required,min=1,max=300"`        // 持续时间（秒），最大5分钟
}

// AdminTestLoadRequest 负载测试请求
type AdminTestLoadRequest struct {
	ProductID     uint64 `json:"product_id" binding:"required"`
	StartUserID   uint64 `json:"start_user_id" binding:"required"`                 // 起始用户ID
	UserCount     int    `json:"user_count" binding:"required,min=1,max=10000"`    // 用户数量，最大10000
	ConcurrentNum int    `json:"concurrent_num" binding:"required,min=1,max=1000"` // 并发数量，最大1000
	Duration      int    `json:"duration" binding:"required,min=1,max=300"`        // 持续时间（秒），最大5分钟
}

// AdminTestPurchase 管理端测试抢购
func (h *Handler) AdminTestPurchase(c *gin.Context) {
	var req AdminTestPurchaseRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		response.ValidateError(c, err)
		return
	}

	// 检查商品是否存在
	var product struct {
		ID     uint64 `json:"id"`
		Name   string `json:"name"`
		Points int    `json:"points"`
		Status int    `json:"status"`
	}
	if err := h.db.Table("products").Select("id, name, points, status").
		Where("id = ? AND status = ?", req.ProductID, 1).First(&product).Error; err != nil {
		response.ErrorWithCode(c, 404, response.CodeNotFound, "商品不存在或已下架")
		return
	}

	// 检查用户是否存在
	var user struct {
		ID     uint64 `json:"id"`
		Points int    `json:"points"`
		Status int    `json:"status"`
	}
	if err := h.db.Table("users").Select("id, points, status").
		Where("id = ? AND status = ?", req.UserID, 1).First(&user).Error; err != nil {
		response.ErrorWithCode(c, 404, response.CodeNotFound, "用户不存在或已禁用")
		return
	}

	// 检查用户积分是否足够
	if user.Points < product.Points {
		// 添加调试日志
		logger.Info("积分检查失败",
			logger.Int("user_points", user.Points),
			logger.Int("product_points", product.Points),
			logger.Any("user_id", req.UserID),
			logger.Any("product_id", req.ProductID))
		response.ErrorWithCode(c, 400, response.CodeInvalidParams, "积分不足")
		return
	}

	// 使用高并发秒杀服务处理抢购
	defaultConfig := &config.SeckillConfig{
		GlobalRateLimit:   1000,
		GlobalBucketSize:  2000,
		ProductRateLimit:  500,
		ProductBucketSize: 1000,
	}
	seckillService := services.NewHighConcurrencySeckillService(h.db, h.rdb, defaultConfig)
	purchaseResp, err := seckillService.Purchase(c.Request.Context(), req.ProductID, req.UserID)
	if err != nil {
		response.InternalServerError(c, "抢购失败: "+err.Error())
		return
	}

	response.Success(c, gin.H{
		"status":     purchaseResp.Status,
		"position":   purchaseResp.Position,
		"request_id": purchaseResp.RequestID,
		"message":    purchaseResp.Message,
		"product":    product,
		"user":       user,
	})
}

// AdminTestPurchaseResult 查询测试抢购结果
func (h *Handler) AdminTestPurchaseResult(c *gin.Context) {
	requestID := c.Query("request_id")
	if requestID == "" {
		response.ErrorWithCode(c, 400, response.CodeInvalidParams, "请求ID不能为空")
		return
	}

	// 检查结果
	defaultConfig := &config.SeckillConfig{
		GlobalRateLimit:   1000,
		GlobalBucketSize:  2000,
		ProductRateLimit:  500,
		ProductBucketSize: 1000,
	}
	seckillService := services.NewHighConcurrencySeckillService(h.db, h.rdb, defaultConfig)
	status, err := seckillService.CheckResult(c.Request.Context(), requestID)
	if err != nil {
		response.InternalServerError(c, "查询结果失败")
		return
	}

	result := gin.H{
		"request_id": requestID,
		"status":     status,
	}

	// 根据状态返回不同信息
	switch status {
	case "queued":
		result["message"] = "排队中，请稍后查询"
	case "success":
		// 查询订单信息
		var order struct {
			ID        uint64 `json:"id"`
			OrderNo   string `json:"order_no"`
			QRCode    string `json:"qr_code"`
			Points    int    `json:"points"`
			CreatedAt string `json:"created_at"`
		}
		if err := h.db.Table("orders").Select("id, order_no, qr_code, points, created_at").
			Where("request_id = ?", requestID).First(&order).Error; err == nil {
			result["order"] = order
		}
		result["message"] = "抢购成功"
	case "sold_out":
		result["message"] = "商品已售完"
	case "not_found":
		result["message"] = "请求不存在或已过期"
	default:
		if len(status) > 6 && status[:6] == "error:" {
			result["message"] = status[6:]
		} else {
			result["message"] = "未知状态"
		}
	}

	response.Success(c, result)
}

// AdminTestConcurrentPurchase 并发测试抢购
func (h *Handler) AdminTestConcurrentPurchase(c *gin.Context) {
	var req AdminTestConcurrentRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		response.ValidateError(c, err)
		return
	}

	// 生成测试ID
	testID := fmt.Sprintf("concurrent_test_%d_%d", time.Now().Unix(), req.ProductID)

	// 启动并发测试
	go h.runConcurrentTest(testID, req)

	response.Success(c, gin.H{
		"test_id":        testID,
		"message":        "并发测试已启动",
		"concurrent_num": req.ConcurrentNum,
		"duration":       req.Duration,
		"product_id":     req.ProductID,
		"user_id":        req.UserID,
	})
}

// AdminGetConcurrentTestStatus 获取并发测试状态
func (h *Handler) AdminGetConcurrentTestStatus(c *gin.Context) {
	testID := c.Query("test_id")
	if testID == "" {
		response.ErrorWithCode(c, 400, response.CodeInvalidParams, "测试ID不能为空")
		return
	}

	// 从Redis获取测试状态
	statusKey := fmt.Sprintf("seckill:test:status:%s", testID)
	status, err := h.rdb.Get(c.Request.Context(), statusKey).Result()
	if err != nil {
		response.ErrorWithCode(c, 404, response.CodeNotFound, "测试不存在或已过期")
		return
	}

	// 获取测试结果
	resultKey := fmt.Sprintf("seckill:test:result:%s", testID)
	result, err := h.rdb.Get(c.Request.Context(), resultKey).Result()
	if err != nil {
		result = "{}"
	}

	response.Success(c, gin.H{
		"test_id": testID,
		"status":  status,
		"result":  result,
	})
}

// AdminTestLoadPurchase 负载测试
func (h *Handler) AdminTestLoadPurchase(c *gin.Context) {
	var req AdminTestLoadRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		response.ValidateError(c, err)
		return
	}

	// 生成测试ID
	testID := fmt.Sprintf("load_test_%d_%d", time.Now().Unix(), req.ProductID)

	// 启动负载测试
	go h.runLoadTest(testID, req)

	response.Success(c, gin.H{
		"test_id":        testID,
		"message":        "负载测试已启动",
		"concurrent_num": req.ConcurrentNum,
		"duration":       req.Duration,
		"product_id":     req.ProductID,
		"user_count":     req.UserCount,
		"start_user_id":  req.StartUserID,
	})
}

// AdminClearTestData 清理测试数据
func (h *Handler) AdminClearTestData(c *gin.Context) {
	// 清理Redis中的测试数据
	keys, err := h.rdb.Keys(c.Request.Context(), "seckill:test:*").Result()
	if err != nil {
		response.InternalServerError(c, "获取测试数据失败")
		return
	}

	if len(keys) > 0 {
		if err := h.rdb.Del(c.Request.Context(), keys...).Err(); err != nil {
			response.InternalServerError(c, "清理测试数据失败")
			return
		}
	}

	response.Success(c, gin.H{
		"message":      "测试数据已清理",
		"cleared_keys": len(keys),
	})
}

// runConcurrentTest 运行并发测试
func (h *Handler) runConcurrentTest(testID string, req AdminTestConcurrentRequest) {
	ctx := context.Background()
	statusKey := fmt.Sprintf("seckill:test:status:%s", testID)
	resultKey := fmt.Sprintf("seckill:test:result:%s", testID)

	// 设置测试状态
	h.rdb.Set(ctx, statusKey, "running", 10*time.Minute)

	var wg sync.WaitGroup
	var mu sync.Mutex
	successCount := 0
	failCount := 0
	errorCount := 0

	startTime := time.Now()
	endTime := startTime.Add(time.Duration(req.Duration) * time.Second)

	// 启动并发goroutine
	for i := 0; i < req.ConcurrentNum; i++ {
		wg.Add(1)
		go func(workerID int) {
			defer wg.Done()

			defaultConfig := &config.SeckillConfig{
				GlobalRateLimit:   1000,
				GlobalBucketSize:  2000,
				ProductRateLimit:  500,
				ProductBucketSize: 1000,
			}
			seckillService := services.NewHighConcurrencySeckillService(h.db, h.rdb, defaultConfig)

			for time.Now().Before(endTime) {
				_, err := seckillService.Purchase(ctx, req.ProductID, req.UserID)

				mu.Lock()
				if err != nil {
					errorCount++
				} else {
					successCount++
				}
				mu.Unlock()

				time.Sleep(100 * time.Millisecond) // 避免过于频繁的请求
			}
		}(i)
	}

	wg.Wait()

	// 保存测试结果
	result := gin.H{
		"test_id":       testID,
		"success_count": successCount,
		"fail_count":    failCount,
		"error_count":   errorCount,
		"total_count":   successCount + failCount + errorCount,
		"duration":      req.Duration,
		"concurrent":    req.ConcurrentNum,
		"completed_at":  time.Now().Format("2006-01-02 15:04:05"),
	}

	h.rdb.Set(ctx, statusKey, "completed", 10*time.Minute)
	h.rdb.Set(ctx, resultKey, fmt.Sprintf("%+v", result), 10*time.Minute)
}

// runLoadTest 运行负载测试
func (h *Handler) runLoadTest(testID string, req AdminTestLoadRequest) {
	// 实现负载测试逻辑（类似并发测试，但使用多个用户ID）
	// 这里简化实现，实际可以根据需要扩展
	h.runConcurrentTest(testID, AdminTestConcurrentRequest{
		ProductID:     req.ProductID,
		UserID:        req.StartUserID,
		ConcurrentNum: req.ConcurrentNum,
		Duration:      req.Duration,
	})
}
