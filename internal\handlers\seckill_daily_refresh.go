package handlers

import (
	"fmt"
	"strconv"

	"wangfujing_admin/internal/services"
	"wangfujing_admin/pkg/response"

	"github.com/gin-gonic/gin"
)

// ManualRefreshAllStock 手动刷新所有商品库存
func (h *Handler) ManualRefreshAllStock(c *gin.Context) {
	dailyRefreshService := services.NewSeckillDailyRefreshService(h.db, h.rdb)

	// 执行每日库存刷新
	if err := dailyRefreshService.DailyRefreshStock(c.Request.Context()); err != nil {
		response.InternalServerError(c, "手动刷新库存失败: "+err.Error())
		return
	}

	response.Success(c, gin.H{
		"message": "所有商品库存已手动刷新完成",
	})
}

// ManualRefreshProductStock 手动刷新指定商品库存
func (h *Handler) ManualRefreshProductStock(c *gin.Context) {
	productIDStr := c.Param("id")
	productID, err := strconv.ParseUint(productIDStr, 10, 64)
	if err != nil {
		response.ErrorWithCode(c, 400, response.CodeInvalidParams, "商品ID格式错误")
		return
	}

	dailyRefreshService := services.NewSeckillDailyRefreshService(h.db, h.rdb)

	// 手动刷新指定商品库存
	if err := dailyRefreshService.ManualRefreshProduct(c.Request.Context(), productID); err != nil {
		response.InternalServerError(c, "手动刷新商品库存失败: "+err.Error())
		return
	}

	response.Success(c, gin.H{
		"message":    "商品库存已手动刷新完成",
		"product_id": productID,
	})
}

// GetDailyRefreshStatus 获取每日刷新状态
func (h *Handler) GetDailyRefreshStatus(c *gin.Context) {
	dailyRefreshService := services.NewSeckillDailyRefreshService(h.db, h.rdb)

	status, err := dailyRefreshService.GetRefreshStatus(c.Request.Context())
	if err != nil {
		response.InternalServerError(c, "获取刷新状态失败: "+err.Error())
		return
	}

	response.Success(c, status)
}

// ResetProductStock 重置商品库存（危险操作，需要管理员权限）
func (h *Handler) ResetProductStock(c *gin.Context) {
	productIDStr := c.Param("id")
	productID, err := strconv.ParseUint(productIDStr, 10, 64)
	if err != nil {
		response.ErrorWithCode(c, 400, response.CodeInvalidParams, "商品ID格式错误")
		return
	}

	// 获取重置数量参数
	var req struct {
		Stock int `json:"stock" binding:"required,min=0"`
	}
	if err := c.ShouldBindJSON(&req); err != nil {
		response.ValidateError(c, err)
		return
	}

	// 直接操作Redis重置库存
	stockCountKey := fmt.Sprintf("seckill:product:%d:count", productID)

	// 重置库存计数
	if err := h.rdb.Set(c.Request.Context(), stockCountKey, req.Stock, 0).Err(); err != nil {
		response.InternalServerError(c, "重置库存失败: "+err.Error())
		return
	}

	response.Success(c, gin.H{
		"message":    "商品库存已重置",
		"product_id": productID,
		"new_stock":  req.Stock,
	})
}

// GetProductStockInfo 获取商品库存信息
func (h *Handler) GetProductStockInfo(c *gin.Context) {
	productIDStr := c.Param("id")
	productID, err := strconv.ParseUint(productIDStr, 10, 64)
	if err != nil {
		response.ErrorWithCode(c, 400, response.CodeInvalidParams, "商品ID格式错误")
		return
	}

	// 先检查商品是否存在
	var product struct {
		ID         uint64 `json:"id"`
		Name       string `json:"name"`
		DailyLimit int    `json:"daily_limit"`
		Type       int    `json:"type"`
		Status     int    `json:"status"`
	}

	if err := h.db.Table("products").Select("id, name, daily_limit, type, status").
		Where("id = ?", productID).First(&product).Error; err != nil {
		response.ErrorWithCode(c, 404, response.CodeNotFound, "商品不存在")
		return
	}

	// 获取Redis库存信息
	stockCountKey := fmt.Sprintf("seckill:product:%d:count", productID)
	stockKey := fmt.Sprintf("seckill:product:%d:stock", productID)

	// 获取库存计数
	stockCount, err := h.rdb.Get(c.Request.Context(), stockCountKey).Result()
	if err != nil {
		stockCount = "0"
	}

	// 获取库存详情
	stockInfo, err := h.rdb.Get(c.Request.Context(), stockKey).Result()
	if err != nil {
		stockInfo = "{}"
	}

	response.Success(c, gin.H{
		"product_id":    productID,
		"product_name":  product.Name,
		"daily_limit":   product.DailyLimit,
		"current_stock": stockCount,
		"stock_info":    stockInfo,
		"status":        product.Status,
	})
}

// BatchResetStock 批量重置库存（超级管理员功能）
func (h *Handler) BatchResetStock(c *gin.Context) {
	var req struct {
		ProductIDs []uint64 `json:"product_ids" binding:"required"`
		ResetType  string   `json:"reset_type" binding:"required"` // "daily_limit" 或 "zero"
	}
	if err := c.ShouldBindJSON(&req); err != nil {
		response.ValidateError(c, err)
		return
	}

	if req.ResetType != "daily_limit" && req.ResetType != "zero" {
		response.ErrorWithCode(c, 400, response.CodeInvalidParams, "重置类型只能是 daily_limit 或 zero")
		return
	}

	dailyRefreshService := services.NewSeckillDailyRefreshService(h.db, h.rdb)
	successCount := 0
	errorCount := 0
	errors := make([]string, 0)

	for _, productID := range req.ProductIDs {
		if req.ResetType == "daily_limit" {
			// 重置为每日限量
			if err := dailyRefreshService.ManualRefreshProduct(c.Request.Context(), productID); err != nil {
				errorCount++
				errors = append(errors, fmt.Sprintf("product_id: %d, error: %s", productID, err.Error()))
			} else {
				successCount++
			}
		} else {
			// 重置为0
			stockCountKey := fmt.Sprintf("seckill:product:%d:count", productID)
			if err := h.rdb.Set(c.Request.Context(), stockCountKey, 0, 0).Err(); err != nil {
				errorCount++
				errors = append(errors, fmt.Sprintf("product_id: %d, error: %s", productID, err.Error()))
			} else {
				successCount++
			}
		}
	}

	response.Success(c, gin.H{
		"message":       "批量重置完成",
		"success_count": successCount,
		"error_count":   errorCount,
		"errors":        errors,
		"reset_type":    req.ResetType,
	})
}

// GetAllProductsStockSummary 获取所有商品库存汇总
func (h *Handler) GetAllProductsStockSummary(c *gin.Context) {
	// 获取所有秒杀商品
	var products []struct {
		ID         uint64 `json:"id"`
		Name       string `json:"name"`
		DailyLimit int    `json:"daily_limit"`
		Status     int    `json:"status"`
	}

	if err := h.db.Table("products").Select("id, name, daily_limit, status").
		Where("type = ? AND status = ?", 1, 1).
		Find(&products).Error; err != nil {
		response.InternalServerError(c, "获取商品列表失败")
		return
	}

	// 获取每个商品的Redis库存
	summary := make([]gin.H, 0, len(products))
	for _, product := range products {
		stockCountKey := fmt.Sprintf("seckill:product:%d:count", product.ID)
		stockCount, err := h.rdb.Get(c.Request.Context(), stockCountKey).Result()
		if err != nil {
			stockCount = "未初始化"
		}

		summary = append(summary, gin.H{
			"product_id":    product.ID,
			"product_name":  product.Name,
			"daily_limit":   product.DailyLimit,
			"current_stock": stockCount,
			"status":        product.Status,
		})
	}

	response.Success(c, gin.H{
		"total_products": len(products),
		"products":       summary,
	})
}
