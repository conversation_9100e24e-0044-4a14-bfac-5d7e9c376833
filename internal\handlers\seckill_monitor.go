package handlers

import (
	"strconv"

	"wangfujing_admin/internal/config"
	"wangfujing_admin/internal/services"
	"wangfujing_admin/pkg/response"

	"github.com/gin-gonic/gin"
)

// GetSeckillStats 获取秒杀系统统计信息
func (h *Handler) GetSeckillStats(c *gin.Context) {
	// 创建全局限流器实例
	defaultConfig := &config.SeckillConfig{
		GlobalRateLimit:   1000,
		GlobalBucketSize:  2000,
		ProductRateLimit:  500,
		ProductBucketSize: 1000,
		WorkerCount:       10,
	}

	globalLimiter := services.NewGlobalRateLimiter(h.rdb, defaultConfig)

	// 获取所有限流统计
	stats, err := globalLimiter.GetAllStats(c.Request.Context())
	if err != nil {
		response.InternalServerError(c, "获取统计信息失败: "+err.Error())
		return
	}

	// 获取Worker管理器统计
	workerManager := services.NewSeckillWorkerManager(h.db, h.rdb, defaultConfig)
	workerStats := workerManager.GetStats()

	// 合并统计信息
	result := gin.H{
		"rate_limit": stats,
		"worker":     workerStats,
		"timestamp": gin.H{
			"current": gin.H{
				"unix":   gin.H{"seconds": gin.H{}},
				"format": "",
			},
		},
	}

	response.Success(c, result)
}

// GetGlobalRateLimit 获取全局限流状态
func (h *Handler) GetGlobalRateLimit(c *gin.Context) {
	defaultConfig := &config.SeckillConfig{
		GlobalRateLimit:   1000,
		GlobalBucketSize:  2000,
		ProductRateLimit:  500,
		ProductBucketSize: 1000,
	}

	globalLimiter := services.NewGlobalRateLimiter(h.rdb, defaultConfig)

	stats, err := globalLimiter.GetGlobalStats(c.Request.Context())
	if err != nil {
		response.InternalServerError(c, "获取全局限流状态失败: "+err.Error())
		return
	}

	response.Success(c, stats)
}

// GetProductRateLimit 获取商品限流状态
func (h *Handler) GetProductRateLimit(c *gin.Context) {
	productIDStr := c.Param("id")
	productID, err := strconv.ParseUint(productIDStr, 10, 64)
	if err != nil {
		response.ErrorWithCode(c, 400, response.CodeInvalidParams, "商品ID格式错误")
		return
	}

	defaultConfig := &config.SeckillConfig{
		GlobalRateLimit:   1000,
		GlobalBucketSize:  2000,
		ProductRateLimit:  500,
		ProductBucketSize: 1000,
	}

	globalLimiter := services.NewGlobalRateLimiter(h.rdb, defaultConfig)

	stats, err := globalLimiter.GetProductStats(c.Request.Context(), productID)
	if err != nil {
		response.InternalServerError(c, "获取商品限流状态失败: "+err.Error())
		return
	}

	response.Success(c, stats)
}

// ResetGlobalRateLimit 重置全局限流
func (h *Handler) ResetGlobalRateLimit(c *gin.Context) {
	defaultConfig := &config.SeckillConfig{
		GlobalRateLimit:   1000,
		GlobalBucketSize:  2000,
		ProductRateLimit:  500,
		ProductBucketSize: 1000,
	}

	globalLimiter := services.NewGlobalRateLimiter(h.rdb, defaultConfig)

	if err := globalLimiter.ResetGlobalLimit(c.Request.Context()); err != nil {
		response.InternalServerError(c, "重置全局限流失败: "+err.Error())
		return
	}

	response.Success(c, gin.H{
		"message": "全局限流已重置",
	})
}

// ResetProductRateLimit 重置商品限流
func (h *Handler) ResetProductRateLimit(c *gin.Context) {
	productIDStr := c.Param("id")
	productID, err := strconv.ParseUint(productIDStr, 10, 64)
	if err != nil {
		response.ErrorWithCode(c, 400, response.CodeInvalidParams, "商品ID格式错误")
		return
	}

	defaultConfig := &config.SeckillConfig{
		GlobalRateLimit:   1000,
		GlobalBucketSize:  2000,
		ProductRateLimit:  500,
		ProductBucketSize: 1000,
	}

	globalLimiter := services.NewGlobalRateLimiter(h.rdb, defaultConfig)

	if err := globalLimiter.ResetProductLimit(c.Request.Context(), productID); err != nil {
		response.InternalServerError(c, "重置商品限流失败: "+err.Error())
		return
	}

	response.Success(c, gin.H{
		"message":    "商品限流已重置",
		"product_id": productID,
	})
}

// GetWorkerStats 获取Worker统计信息
func (h *Handler) GetWorkerStats(c *gin.Context) {
	defaultConfig := &config.SeckillConfig{
		GlobalRateLimit:   1000,
		GlobalBucketSize:  2000,
		ProductRateLimit:  500,
		ProductBucketSize: 1000,
		WorkerCount:       10,
	}

	workerManager := services.NewSeckillWorkerManager(h.db, h.rdb, defaultConfig)
	stats := workerManager.GetStats()

	response.Success(c, stats)
}

// RestartWorkers 重启Worker
func (h *Handler) RestartWorkers(c *gin.Context) {
	defaultConfig := &config.SeckillConfig{
		GlobalRateLimit:   1000,
		GlobalBucketSize:  2000,
		ProductRateLimit:  500,
		ProductBucketSize: 1000,
		WorkerCount:       10,
	}

	workerManager := services.NewSeckillWorkerManager(h.db, h.rdb, defaultConfig)

	// 先启动Worker（如果还没启动）
	if !workerManager.IsRunning() {
		if err := workerManager.Start(); err != nil {
			response.InternalServerError(c, "启动Worker失败: "+err.Error())
			return
		}
		response.Success(c, gin.H{
			"message": "Worker已启动",
		})
		return
	}

	// 如果已经在运行，则重启
	if err := workerManager.RestartWorkers(); err != nil {
		response.InternalServerError(c, "重启Worker失败: "+err.Error())
		return
	}

	response.Success(c, gin.H{
		"message": "Worker已重启",
	})
}

// UpdateSeckillConfig 更新秒杀配置（热更新）
func (h *Handler) UpdateSeckillConfig(c *gin.Context) {
	var req config.SeckillConfig
	if err := c.ShouldBindJSON(&req); err != nil {
		response.ValidateError(c, err)
		return
	}

	// 验证配置参数
	if req.GlobalRateLimit <= 0 || req.GlobalBucketSize <= 0 {
		response.ErrorWithCode(c, 400, response.CodeInvalidParams, "全局限流配置无效")
		return
	}

	if req.ProductRateLimit <= 0 || req.ProductBucketSize <= 0 {
		response.ErrorWithCode(c, 400, response.CodeInvalidParams, "商品限流配置无效")
		return
	}

	if req.WorkerCount <= 0 {
		response.ErrorWithCode(c, 400, response.CodeInvalidParams, "Worker数量必须大于0")
		return
	}

	// 创建新的限流器并更新配置
	globalLimiter := services.NewGlobalRateLimiter(h.rdb, &req)
	globalLimiter.UpdateConfig(&req)

	response.Success(c, gin.H{
		"message": "配置已更新",
		"config":  req,
	})
}

// GetSeckillConfig 获取当前秒杀配置
func (h *Handler) GetSeckillConfig(c *gin.Context) {
	defaultConfig := &config.SeckillConfig{
		GlobalRateLimit:   1000,
		GlobalBucketSize:  2000,
		ProductRateLimit:  500,
		ProductBucketSize: 1000,
		WorkerCount:       10,
		WorkerBatchSize:   50,
		QueueTimeout:      30,
		ResultCacheTime:   600,
		MonitorInterval:   30,
		AlertQueueSize:    5000,
	}

	response.Success(c, defaultConfig)
}
