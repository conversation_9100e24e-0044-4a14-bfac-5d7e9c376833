package handlers

import (
	"fmt"
	"mime/multipart"
	"net/http"
	"wangfujing_admin/pkg/response"

	"github.com/gin-gonic/gin"
)

// UploadHandler 文件上传处理器
type UploadHandler struct {
	*Handler
}

// NewUploadHandler 创建上传处理器
func NewUploadHandler(h *Handler) *UploadHandler {
	return &UploadHandler{
		Handler: h,
	}
}

// UploadFile 通用文件上传接口
func (h *UploadHandler) UploadFile(c *gin.Context) {
	// 获取上传类型参数
	uploadType := c.DefaultQuery("type", "image")
	folder := c.DefaultQuery("folder", "common")

	// 获取上传的文件
	file, header, err := c.Request.FormFile("file")
	if err != nil {
		response.ErrorWithCode(c, http.StatusBadRequest, response.CodeInvalidParams, "请选择要上传的文件")
		return
	}
	defer file.Close()

	// 根据上传类型验证文件
	if err := h.validateUploadFile(header, uploadType); err != nil {
		response.ErrorWithCode(c, http.StatusBadRequest, response.CodeInvalidParams, err.Error())
		return
	}

	// 上传到OSS
	fileURL, err := h.ossManager.UploadFile(file, header, folder)
	if err != nil {
		response.InternalServerError(c, "文件上传失败")
		return
	}

	// 返回文件URL和相关信息
	response.SuccessWithMessage(c, "文件上传成功", gin.H{
		"url":      fileURL,
		"filename": header.Filename,
		"size":     header.Size,
		"type":     uploadType,
		"folder":   folder,
	})
}

// UploadMultipleFiles 批量文件上传接口
func (h *UploadHandler) UploadMultipleFiles(c *gin.Context) {
	// 获取上传类型参数
	uploadType := c.DefaultQuery("type", "image")
	folder := c.DefaultQuery("folder", "common")

	// 解析多文件表单
	form, err := c.MultipartForm()
	if err != nil {
		response.ErrorWithCode(c, http.StatusBadRequest, response.CodeInvalidParams, "解析表单失败")
		return
	}

	files := form.File["files"]
	if len(files) == 0 {
		response.ErrorWithCode(c, http.StatusBadRequest, response.CodeInvalidParams, "请选择要上传的文件")
		return
	}

	// 限制批量上传数量
	const maxFiles = 10
	if len(files) > maxFiles {
		response.ErrorWithCode(c, http.StatusBadRequest, response.CodeInvalidParams, fmt.Sprintf("一次最多上传%d个文件", maxFiles))
		return
	}

	var results []gin.H
	var errors []string

	// 逐个上传文件
	for _, header := range files {
		file, err := header.Open()
		if err != nil {
			errors = append(errors, fmt.Sprintf("文件 %s 打开失败: %s", header.Filename, err.Error()))
			continue
		}

		// 验证文件
		if err := h.validateUploadFile(header, uploadType); err != nil {
			file.Close()
			errors = append(errors, fmt.Sprintf("文件 %s 验证失败: %s", header.Filename, err.Error()))
			continue
		}

		// 上传文件
		fileURL, err := h.ossManager.UploadFile(file, header, folder)
		file.Close()

		if err != nil {
			errors = append(errors, fmt.Sprintf("文件 %s 上传失败: %s", header.Filename, err.Error()))
			continue
		}

		results = append(results, gin.H{
			"url":      fileURL,
			"filename": header.Filename,
			"size":     header.Size,
		})
	}

	// 返回结果
	responseData := gin.H{
		"success_count": len(results),
		"error_count":   len(errors),
		"files":         results,
	}

	if len(errors) > 0 {
		responseData["errors"] = errors
	}

	if len(results) == 0 {
		response.ErrorWithCode(c, http.StatusBadRequest, response.CodeInvalidParams, "所有文件上传失败")
		return
	}

	message := fmt.Sprintf("成功上传 %d 个文件", len(results))
	if len(errors) > 0 {
		message += fmt.Sprintf("，%d 个文件失败", len(errors))
	}

	response.SuccessWithMessage(c, message, responseData)
}

// validateUploadFile 验证上传文件
func (h *UploadHandler) validateUploadFile(header *multipart.FileHeader, uploadType string) error {
	switch uploadType {
	case "image":
		return h.validateImageFile(header)
	case "video":
		return h.validateVideoFile(header)
	case "document":
		return h.validateDocumentFile(header)
	case "audio":
		return h.validateAudioFile(header)
	default:
		return h.validateCommonFile(header)
	}
}

// validateImageFile 验证图片文件
func (h *UploadHandler) validateImageFile(header *multipart.FileHeader) error {
	// 验证文件类型
	if !h.ossManager.IsValidImageType(header.Filename) {
		return fmt.Errorf("只支持 jpg、jpeg、png、gif、bmp、webp 格式的图片")
	}

	// 验证文件大小（10MB限制）
	const maxFileSize = 10 * 1024 * 1024 // 10MB
	if header.Size > maxFileSize {
		return fmt.Errorf("图片文件大小不能超过10MB")
	}

	return nil
}

// validateVideoFile 验证视频文件
func (h *UploadHandler) validateVideoFile(header *multipart.FileHeader) error {
	// 验证文件类型
	if !h.ossManager.IsValidVideoType(header.Filename) {
		return fmt.Errorf("只支持 mp4、avi、mov、wmv、flv、mkv 格式的视频")
	}

	// 验证文件大小（100MB限制）
	const maxFileSize = 100 * 1024 * 1024 // 100MB
	if header.Size > maxFileSize {
		return fmt.Errorf("视频文件大小不能超过100MB")
	}

	return nil
}

// validateDocumentFile 验证文档文件
func (h *UploadHandler) validateDocumentFile(header *multipart.FileHeader) error {
	// 验证文件类型
	if !h.ossManager.IsValidDocumentType(header.Filename) {
		return fmt.Errorf("只支持 pdf、doc、docx、xls、xlsx、ppt、pptx、txt 格式的文档")
	}

	// 验证文件大小（20MB限制）
	const maxFileSize = 20 * 1024 * 1024 // 20MB
	if header.Size > maxFileSize {
		return fmt.Errorf("文档文件大小不能超过20MB")
	}

	return nil
}

// validateAudioFile 验证音频文件
func (h *UploadHandler) validateAudioFile(header *multipart.FileHeader) error {
	// 验证文件类型
	if !h.ossManager.IsValidAudioType(header.Filename) {
		return fmt.Errorf("只支持 mp3、wav、flac、aac、ogg 格式的音频")
	}

	// 验证文件大小（50MB限制）
	const maxFileSize = 50 * 1024 * 1024 // 50MB
	if header.Size > maxFileSize {
		return fmt.Errorf("音频文件大小不能超过50MB")
	}

	return nil
}

// validateCommonFile 验证通用文件
func (h *UploadHandler) validateCommonFile(header *multipart.FileHeader) error {
	// 验证文件大小（50MB限制）
	const maxFileSize = 50 * 1024 * 1024 // 50MB
	if header.Size > maxFileSize {
		return fmt.Errorf("文件大小不能超过50MB")
	}

	return nil
}

// GetUploadConfig 获取上传配置信息
func (h *UploadHandler) GetUploadConfig(c *gin.Context) {
	config := gin.H{
		"image": gin.H{
			"max_size":    10 * 1024 * 1024, // 10MB
			"extensions":  []string{"jpg", "jpeg", "png", "gif", "bmp", "webp"},
			"description": "支持常见图片格式，最大10MB。超过请前端直传到OSS",
		},
		"video": gin.H{
			"max_size":    100 * 1024 * 1024, // 100MB
			"extensions":  []string{"mp4", "avi", "mov", "wmv", "flv", "mkv"},
			"description": "支持常见视频格式，最大100MB。超过请前端直传到OSS",
		},
		"document": gin.H{
			"max_size":    20 * 1024 * 1024, // 20MB
			"extensions":  []string{"pdf", "doc", "docx", "xls", "xlsx", "ppt", "pptx", "txt"},
			"description": "支持常见文档格式，最大20MB。超过请前端直传到OSS",
		},
		"audio": gin.H{
			"max_size":    50 * 1024 * 1024, // 50MB
			"extensions":  []string{"mp3", "wav", "flac", "aac", "ogg"},
			"description": "支持常见音频格式，最大50MB。超过请前端直传到OSS",
		},
		"common": gin.H{
			"max_size":    50 * 1024 * 1024, // 50MB
			"description": "通用文件上传，最大50MB。超过请前端直传到OSS",
		},
		"batch": gin.H{
			"max_files":   10,
			"description": "批量上传最多10个文件",
		},
	}

	response.SuccessWithMessage(c, "获取上传配置成功", config)
}
