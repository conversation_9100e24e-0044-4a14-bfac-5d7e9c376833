package handlers

import (
	"strconv"

	"wangfujing_admin/internal/models"
	"wangfujing_admin/pkg/response"

	"github.com/gin-gonic/gin"
)

// CreateUserRequest 创建用户请求
type CreateUserRequest struct {
	Phone    string          `json:"phone" binding:"required"`
	Nickname string          `json:"nickname"`
	Avatar   string          `json:"avatar"`
	UserType models.UserType `json:"user_type" binding:"required"`
	Status   models.Status   `json:"status"`
	RoleIDs  []string        `json:"role_ids"`
}

// UpdateUserRequest 更新用户请求
type UpdateUserRequest struct {
	Nickname string        `json:"nickname"`
	Avatar   string        `json:"avatar"`
	Status   models.Status `json:"status"`
}

// AssignRolesRequest 分配角色请求
type AssignRolesRequest struct {
	RoleIDs []string `json:"role_ids" binding:"required"`
}

// UpdateUserStatusRequest 更新用户状态请求
type UpdateUserStatusRequest struct {
	Status models.Status `json:"status" binding:"required"`
}

// GetUsers 获取用户列表
func (h *Handler) GetUsers(c *gin.Context) {
	page, _ := strconv.Atoi(c.DefaultQuery("page", "1"))
	size, _ := strconv.Atoi(c.DefaultQuery("size", "10"))

	var userType *models.UserType
	if userTypeStr := c.Query("user_type"); userTypeStr != "" {
		if ut, err := strconv.Atoi(userTypeStr); err == nil {
			userTypeVal := models.UserType(ut)
			userType = &userTypeVal
		}
	}

	var status *models.Status
	if statusStr := c.Query("status"); statusStr != "" {
		if s, err := strconv.Atoi(statusStr); err == nil {
			statusVal := models.Status(s)
			status = &statusVal
		}
	}

	users, total, err := h.userService.GetUsers(c.Request.Context(), page, size, userType, status)
	if err != nil {
		response.InternalServerError(c, "Failed to get users")
		return
	}

	response.Page(c, users, total, page, size)
}

// GetUser 获取用户详情
func (h *Handler) GetUser(c *gin.Context) {
	id := c.Param("id")

	user, err := h.userService.GetUserByID(c.Request.Context(), id)
	if err != nil {
		if err.Error() == "user not found" {
			response.NotFound(c, "User not found")
			return
		}
		response.InternalServerError(c, "Failed to get user")
		return
	}

	response.Success(c, user)
}

// CreateUser 创建用户
func (h *Handler) CreateUser(c *gin.Context) {
	var req CreateUserRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		response.ValidateError(c, err)
		return
	}

	user := &models.User{
		Phone:    req.Phone,
		Nickname: req.Nickname,
		Avatar:   req.Avatar,
		UserType: req.UserType,
		Status:   req.Status,
	}

	if user.Status == 0 {
		user.Status = models.StatusActive
	}

	if err := h.userService.CreateUser(c.Request.Context(), user); err != nil {
		if err.Error() == "phone number already exists" {
			response.BadRequest(c, "Phone number already exists")
			return
		}
		response.InternalServerError(c, "Failed to create user")
		return
	}

	// 分配角色
	if len(req.RoleIDs) > 0 {
		if err := h.userService.AssignRoles(c.Request.Context(), strconv.FormatUint(user.ID, 10), req.RoleIDs); err != nil {
			response.InternalServerError(c, "Failed to assign roles")
			return
		}
	}

	response.SuccessWithMessage(c, "User created successfully", user)
}

// UpdateUser 更新用户
func (h *Handler) UpdateUser(c *gin.Context) {
	id := c.Param("id")

	var req UpdateUserRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		response.ValidateError(c, err)
		return
	}

	updates := make(map[string]interface{})
	if req.Nickname != "" {
		updates["nickname"] = req.Nickname
	}
	if req.Avatar != "" {
		updates["avatar"] = req.Avatar
	}
	if req.Status != 0 {
		updates["status"] = req.Status
	}

	if err := h.userService.UpdateUser(c.Request.Context(), id, updates); err != nil {
		if err.Error() == "user not found" {
			response.NotFound(c, "User not found")
			return
		}
		response.InternalServerError(c, "Failed to update user")
		return
	}

	response.SuccessWithMessage(c, "User updated successfully", nil)
}

// DeleteUser 删除用户
func (h *Handler) DeleteUser(c *gin.Context) {
	id := c.Param("id")

	if err := h.userService.DeleteUser(c.Request.Context(), id); err != nil {
		if err.Error() == "user not found" {
			response.NotFound(c, "User not found")
			return
		}
		response.InternalServerError(c, "Failed to delete user")
		return
	}

	response.SuccessWithMessage(c, "User deleted successfully", nil)
}

// 用户角色分配和状态更新方法已移至admin_user_handler.go

// GetUserPoints 获取用户积分明细
func (h *Handler) GetUserPoints(c *gin.Context) {
	id := c.Param("id")
	page, _ := strconv.Atoi(c.DefaultQuery("page", "1"))
	size, _ := strconv.Atoi(c.DefaultQuery("size", "10"))

	records, total, err := h.userService.GetPointsRecords(c.Request.Context(), id, page, size)
	if err != nil {
		response.InternalServerError(c, "Failed to get points records")
		return
	}

	response.Page(c, records, total, page, size)
}

// 管理员用户相关方法已移至admin_user_handler.go

// GetCustomers 获取客户列表
func (h *Handler) GetCustomers(c *gin.Context) {
	page, _ := strconv.Atoi(c.DefaultQuery("page", "1"))
	size, _ := strconv.Atoi(c.DefaultQuery("size", "10"))

	// 只获取普通用户
	userType := models.UserTypeCustomer
	var status *models.Status
	if statusStr := c.Query("status"); statusStr != "" {
		if s, err := strconv.Atoi(statusStr); err == nil {
			statusVal := models.Status(s)
			status = &statusVal
		}
	}

	users, total, err := h.userService.GetUsers(c.Request.Context(), page, size, &userType, status)
	if err != nil {
		response.InternalServerError(c, "Failed to get customers")
		return
	}

	response.Page(c, users, total, page, size)
}

// GetCustomer 获取客户详情
func (h *Handler) GetCustomer(c *gin.Context) {
	h.GetUser(c)
}

// GetCustomerPoints 获取客户积分明细
func (h *Handler) GetCustomerPoints(c *gin.Context) {
	h.GetUserPoints(c)
}
