package handlers

import (
	"math/rand"
	"net/http"
	"strconv"
	"time"

	"wangfujing_admin/internal/models"
	"wangfujing_admin/pkg/response"

	"github.com/gin-gonic/gin"
	"gorm.io/gorm"
)

// updateExpiredExchanges 更新过期的兑换记录状态
func (h *Handler) updateExpiredExchanges() {
	// 当天开始时间点（00:00:00）
	now := time.Now()
	today := time.Date(now.Year(), now.Month(), now.Day(), 0, 0, 0, 0, now.Location())

	// 更新过期记录的状态为已过期
	// 1. 更新已兑换状态的过期记录
	h.db.Model(&models.PointsMallExchange{}).
		Where("status = ? AND exchange_time < ?", models.ExchangeStatusRedeemed, today).
		Update("status", models.ExchangeStatusExpired)

	// 2. 更新正在使用状态的过期记录（重要：即使已应用也要过期）
	h.db.Model(&models.PointsMallExchange{}).
		Where("status = ? AND exchange_time < ?", models.ExchangeStatusInUse, today).
		Update("status", models.ExchangeStatusExpired)
}

// getExchangeStatusText 获取兑换状态文本
func getExchangeStatusText(status int) string {
	switch status {
	case 1:
		return "已兑换"
	case 2:
		return "正在使用"
	case 3:
		return "已过期"
	case 4:
		return "已停用"
	default:
		return "未知"
	}
}

// GetAvatarCarousel 获取VIP形象轮播列表
func (h *Handler) GetAvatarCarousel(c *gin.Context) {
	// 获取当前用户ID
	userID, err := getUserIDFromContext(c)
	if err != nil {
		response.Unauthorized(c, err.Error())
		return
	}

	var avatars []models.PointsMallItem

	// 获取最新的25条VIP形象记录，只获取已上架的
	if err := h.db.Where("type = ? AND status = ?",
		models.PointsMallItemTypeAvatar,
		models.PointsMallItemStatusActive).
		Order("created_at DESC").
		Limit(25).
		Find(&avatars).Error; err != nil {
		response.InternalServerError(c, "获取形象列表失败")
		return
	}

	// 查询用户当前正在使用的形象
	var currentUsingExchange models.PointsMallExchange
	var currentUsingAvatarID uint64 = 0
	if err := h.db.Where("user_id = ? AND status = ?", userID, models.ExchangeStatusInUse).
		First(&currentUsingExchange).Error; err == nil {
		currentUsingAvatarID = currentUsingExchange.ItemID
	}

	// 查询用户所有的兑换记录
	var userExchanges []models.PointsMallExchange
	h.db.Where("user_id = ? AND lottery_win = ?", userID, true).Find(&userExchanges)

	// 创建形象ID到兑换记录的映射
	exchangeMap := make(map[uint64]*models.PointsMallExchange)
	for i := range userExchanges {
		exchangeMap[userExchanges[i].ItemID] = &userExchanges[i]
	}

	// 转换为响应格式
	var result []gin.H
	for _, avatar := range avatars {
		// 判断是否为当前正在使用的形象
		isCurrentUsing := avatar.ID == currentUsingAvatarID

		// 获取用户对该形象的兑换记录
		exchange := exchangeMap[avatar.ID]
		var exchangeStatus int = 0
		var exchangeStatusText string = "兑换"
		var exchangeID uint64 = 0
		var exchangeTime string = ""
		var appliedAt *string = nil
		var isExpired bool = false

		if exchange != nil {
			exchangeStatus = exchange.Status
			exchangeStatusText = getExchangeStatusText(exchange.Status)
			exchangeID = exchange.ID
			exchangeTime = exchange.ExchangeTime.Format("2006-01-02 15:04")
			if exchange.AppliedAt != nil {
				appliedAtStr := exchange.AppliedAt.Format("2006-01-02 15:04:05")
				appliedAt = &appliedAtStr
			}
			isExpired = exchange.Status == models.ExchangeStatusExpired
		}

		result = append(result, gin.H{
			"id":               avatar.ID,
			"type":             avatar.Type,
			"name":             avatar.Name,
			"images":           avatar.Images,
			"description":      avatar.Description,
			"start_time":       avatar.StartTime,
			"end_time":         avatar.EndTime,
			"stock":            avatar.Stock,
			"points":           avatar.Points,
			"status":           avatar.Status,
			"creator_id":       avatar.CreatorID,
			"sort":             avatar.Sort,
			"background_image": avatar.BackgroundImage,
			"album_image":      avatar.AlbumImage,
			"discount_rate":    avatar.DiscountRate,
			"enable_lottery":   avatar.EnableLottery,
			"lottery_rate":     avatar.LotteryRate,
			"is_enabled":       avatar.IsEnabled,
			"is_current_using": isCurrentUsing,
			"created_at":       avatar.CreatedAt.Format("2006-01-02 15:04:05"),
			"updated_at":       avatar.UpdatedAt.Format("2006-01-02 15:04:05"),
			// 兑换状态相关字段
			"exchange_status":      exchangeStatus,
			"exchange_status_text": exchangeStatusText,
			"exchange_id":          exchangeID,
			"exchange_time":        exchangeTime,
			"applied_at":           appliedAt,
			"is_expired":           isExpired,
		})
	}

	response.Success(c, result)
}

// ExchangeAvatarRequest 兑换形象请求
type ExchangeAvatarRequest struct {
	// 可以添加其他参数，目前暂时为空
}

// ExchangeAvatar 兑换VIP形象
func (h *Handler) ExchangeAvatar(c *gin.Context) {
	// 获取形象ID
	avatarIDStr := c.Param("id")
	avatarID, err := strconv.ParseUint(avatarIDStr, 10, 64)
	if err != nil {
		response.ErrorWithCode(c, http.StatusBadRequest, response.CodeInvalidParams, "形象ID格式错误")
		return
	}

	// 获取当前用户ID
	userID, err := getUserIDFromContext(c)
	if err != nil {
		response.Unauthorized(c, err.Error())
		return
	}

	// 解析请求参数
	var req ExchangeAvatarRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		response.ValidateError(c, err)
		return
	}

	// 开始事务
	tx := h.db.Begin()
	defer func() {
		if r := recover(); r != nil {
			tx.Rollback()
		}
	}()

	// 获取形象信息
	var avatar models.PointsMallItem
	if err := tx.Where("id = ? AND type = ? AND status = ?",
		avatarID,
		models.PointsMallItemTypeAvatar,
		models.PointsMallItemStatusActive).
		First(&avatar).Error; err != nil {
		tx.Rollback()
		if err == gorm.ErrRecordNotFound {
			response.NotFound(c, "形象不存在或已下架")
			return
		}
		response.InternalServerError(c, "获取形象信息失败")
		return
	}

	// 检查形象是否启用（上架状态）
	if !avatar.IsEnabled {
		tx.Rollback()
		response.ErrorWithCode(c, http.StatusBadRequest, response.CodeInvalidParams, "该形象暂未开放兑换")
		return
	}

	// 获取用户信息
	var user models.User
	if err := tx.First(&user, userID).Error; err != nil {
		tx.Rollback()
		response.InternalServerError(c, "获取用户信息失败")
		return
	}

	// VIP形象固定使用1积分
	requiredPoints := 1

	// 检查用户积分是否足够
	if user.Points < requiredPoints {
		tx.Rollback()
		response.ErrorWithCode(c, http.StatusBadRequest, response.CodeInsufficientPoints, "积分余额不足")
		return
	}

	// 先更新过期状态
	h.updateExpiredExchanges()

	// 检查是否已经兑换过该形象（只检查已兑换状态的记录）
	var existingExchange models.PointsMallExchange
	if err := tx.Where("user_id = ? AND item_id = ? AND status = ?",
		userID, avatarID, models.ExchangeStatusRedeemed).
		First(&existingExchange).Error; err == nil {
		tx.Rollback()
		response.ErrorWithCode(c, http.StatusConflict, response.CodeInvalidParams, "您已经兑换过该形象，请等待过期后再次兑换")
		return
	} else if err != gorm.ErrRecordNotFound {
		tx.Rollback()
		response.InternalServerError(c, "检查兑换记录失败")
		return
	}

	// 处理概率逻辑
	var isLottery, lotteryWin bool
	var exchangeResult string

	if avatar.EnableLottery {
		// 开启抽卡，按概率返回
		isLottery = true
		// 生成随机数判断是否中奖
		rand.Seed(time.Now().UnixNano())
		randomNum := rand.Intn(100) + 1 // 1-100
		lotteryWin = randomNum <= avatar.LotteryRate

		if lotteryWin {
			exchangeResult = "success"
		} else {
			exchangeResult = "failed"
			// 抽卡失败，扣除1积分但不创建兑换记录
			if err := tx.Model(&user).Update("points", gorm.Expr("points - ?", requiredPoints)).Error; err != nil {
				tx.Rollback()
				response.InternalServerError(c, "扣除积分失败")
				return
			}

			// 创建积分消费记录
			pointsRecord := &models.PointsRecord{
				UserID:      userID,
				Type:        models.PointsTypeSpend,
				Points:      requiredPoints,
				Balance:     user.Points - requiredPoints,
				Source:      "VIP形象抽卡",
				SourceID:    &avatarID,
				RelatedID:   &avatarID,
				RelatedType: "avatar_lottery",
				Description: "VIP形象抽卡失败：" + avatar.Name,
			}
			if err := tx.Create(pointsRecord).Error; err != nil {
				tx.Rollback()
				response.InternalServerError(c, "创建积分记录失败")
				return
			}

			tx.Commit()
			response.Success(c, gin.H{
				"result":      exchangeResult,
				"message":     "很遗憾~没逮到，再试试吧",
				"is_lottery":  isLottery,
				"lottery_win": lotteryWin,
			})
			return
		}
	} else {
		// 不开启抽卡，直接成功返回
		isLottery = false
		lotteryWin = true
		exchangeResult = "success"
	}

	// 兑换成功，扣除1积分
	if err := tx.Model(&user).Update("points", gorm.Expr("points - ?", requiredPoints)).Error; err != nil {
		tx.Rollback()
		response.InternalServerError(c, "扣除积分失败")
		return
	}

	// 创建兑换记录
	exchange := &models.PointsMallExchange{
		ItemID:       avatarID,
		UserID:       userID,
		Points:       requiredPoints,
		ExchangeTime: time.Now(),
		Status:       models.ExchangeStatusRedeemed, // 已兑换
		IsLottery:    isLottery,
		LotteryWin:   lotteryWin,
	}
	if err := tx.Create(exchange).Error; err != nil {
		tx.Rollback()
		response.InternalServerError(c, "创建兑换记录失败")
		return
	}

	// 创建积分消费记录
	pointsRecord := &models.PointsRecord{
		UserID:      userID,
		Type:        models.PointsTypeSpend,
		Points:      requiredPoints,
		Balance:     user.Points - requiredPoints,
		Source:      "VIP形象兑换",
		SourceID:    &avatarID,
		RelatedID:   &exchange.ID, // 关联兑换记录ID
		RelatedType: "points_mall_exchange",
		Description: "VIP形象兑换：" + avatar.Name,
	}
	if err := tx.Create(pointsRecord).Error; err != nil {
		tx.Rollback()
		response.InternalServerError(c, "创建积分记录失败")
		return
	}

	tx.Commit()

	response.Success(c, gin.H{
		"result":      exchangeResult,
		"message":     "兑换成功",
		"is_lottery":  isLottery,
		"lottery_win": lotteryWin,
		"exchange_id": exchange.ID,
		"exchange": gin.H{
			"id":            exchange.ID,
			"item_id":       exchange.ItemID,
			"user_id":       exchange.UserID,
			"points":        exchange.Points,
			"exchange_time": exchange.ExchangeTime.Format("2006-01-02 15:04"),
			"status":        exchange.Status,
			"is_lottery":    exchange.IsLottery,
			"lottery_win":   exchange.LotteryWin,
			"created_at":    exchange.CreatedAt.Format("2006-01-02 15:04:05"),
			"updated_at":    exchange.UpdatedAt.Format("2006-01-02 15:04:05"),
		},
		"avatar": gin.H{
			"id":               avatar.ID,
			"type":             avatar.Type,
			"name":             avatar.Name,
			"images":           avatar.Images,
			"description":      avatar.Description,
			"start_time":       avatar.StartTime,
			"end_time":         avatar.EndTime,
			"stock":            avatar.Stock,
			"points":           avatar.Points,
			"status":           avatar.Status,
			"creator_id":       avatar.CreatorID,
			"sort":             avatar.Sort,
			"background_image": avatar.BackgroundImage,
			"album_image":      avatar.AlbumImage,
			"discount_rate":    avatar.DiscountRate,
			"enable_lottery":   avatar.EnableLottery,
			"lottery_rate":     avatar.LotteryRate,
			"is_enabled":       avatar.IsEnabled,
			"created_at":       avatar.CreatedAt.Format("2006-01-02 15:04:05"),
			"updated_at":       avatar.UpdatedAt.Format("2006-01-02 15:04:05"),
		},
		"user_points": user.Points - requiredPoints, // 兑换后的积分余额
	})
}

// GetMyAvatarExchanges 获取我的形象兑换记录
func (h *Handler) GetMyAvatarExchanges(c *gin.Context) {
	// 获取当前用户ID
	userID, err := getUserIDFromContext(c)
	if err != nil {
		response.Unauthorized(c, err.Error())
		return
	}

	// 获取分页参数
	page, _ := strconv.Atoi(c.DefaultQuery("page", "1"))
	size, _ := strconv.Atoi(c.DefaultQuery("size", "10"))
	if page < 1 {
		page = 1
	}
	if size < 1 || size > 100 {
		size = 10
	}

	// 获取筛选参数：expired_filter (all=全部, active=未过期, expired=已过期)
	expiredFilter := c.DefaultQuery("expired_filter", "active")

	offset := (page - 1) * size

	// 先更新过期状态
	h.updateExpiredExchanges()

	// 构建查询条件
	query := h.db.Model(&models.PointsMallExchange{}).Where("user_id = ? AND lottery_win = ?", userID, true)

	switch expiredFilter {
	case "active":
		// 只查询未过期的记录（已兑换、正在使用和已停用）
		query = query.Where("status IN (?)", []int{
			models.ExchangeStatusRedeemed,
			models.ExchangeStatusInUse,
			models.ExchangeStatusDeactivated,
		})
	case "expired":
		// 只查询已过期的记录
		query = query.Where("status = ?", models.ExchangeStatusExpired)
	case "all":
		// 查询全部记录
		query = query.Where("status IN (?)", []int{
			models.ExchangeStatusRedeemed,
			models.ExchangeStatusInUse,
			models.ExchangeStatusExpired,
			models.ExchangeStatusDeactivated,
		})
	default:
		// 默认只查询未过期的记录（已兑换、正在使用和已停用）
		query = query.Where("status IN (?)", []int{
			models.ExchangeStatusRedeemed,
			models.ExchangeStatusInUse,
			models.ExchangeStatusDeactivated,
		})
	}

	// 查询总数
	var total int64
	if err := query.Count(&total).Error; err != nil {
		response.InternalServerError(c, "查询记录总数失败")
		return
	}

	// 查询记录列表，预加载形象信息
	var exchanges []models.PointsMallExchange
	if err := query.Preload("Item").
		Order("exchange_time DESC").
		Offset(offset).
		Limit(size).
		Find(&exchanges).Error; err != nil {
		response.InternalServerError(c, "获取兑换记录失败")
		return
	}

	// 转换为响应格式
	var result []gin.H
	for _, exchange := range exchanges {
		// 根据status字段判断是否过期
		isExpired := exchange.Status == models.ExchangeStatusExpired

		exchangeData := gin.H{
			"id":            exchange.ID,
			"item_id":       exchange.ItemID,
			"user_id":       exchange.UserID,
			"points":        exchange.Points,
			"exchange_time": exchange.ExchangeTime.Format("2006-01-02 15:04"),
			"status":        exchange.Status,
			"status_text":   getExchangeStatusText(exchange.Status),
			"is_lottery":    exchange.IsLottery,
			"lottery_win":   exchange.LotteryWin,
			"is_expired":    isExpired,
			"created_at":    exchange.CreatedAt.Format("2006-01-02 15:04:05"),
			"updated_at":    exchange.UpdatedAt.Format("2006-01-02 15:04:05"),
		}

		// 添加应用时间（如果已应用）
		if exchange.AppliedAt != nil {
			exchangeData["applied_at"] = exchange.AppliedAt.Format("2006-01-02 15:04:05")
		}

		// 添加形象信息
		if exchange.Item != nil {
			exchangeData["avatar"] = gin.H{
				"id":               exchange.Item.ID,
				"type":             exchange.Item.Type,
				"name":             exchange.Item.Name,
				"images":           exchange.Item.Images,
				"description":      exchange.Item.Description,
				"start_time":       exchange.Item.StartTime,
				"end_time":         exchange.Item.EndTime,
				"stock":            exchange.Item.Stock,
				"points":           exchange.Item.Points,
				"status":           exchange.Item.Status,
				"creator_id":       exchange.Item.CreatorID,
				"sort":             exchange.Item.Sort,
				"background_image": exchange.Item.BackgroundImage,
				"album_image":      exchange.Item.AlbumImage,
				"discount_rate":    exchange.Item.DiscountRate,
				"enable_lottery":   exchange.Item.EnableLottery,
				"lottery_rate":     exchange.Item.LotteryRate,
				"is_enabled":       exchange.Item.IsEnabled,
				"created_at":       exchange.Item.CreatedAt.Format("2006-01-02 15:04:05"),
				"updated_at":       exchange.Item.UpdatedAt.Format("2006-01-02 15:04:05"),
			}
		}

		result = append(result, exchangeData)
	}

	response.Page(c, result, total, page, size)
}

// ApplyAvatar 应用VIP形象
func (h *Handler) ApplyAvatar(c *gin.Context) {
	// 获取兑换记录ID
	exchangeIDStr := c.Param("exchange_id")
	exchangeID, err := strconv.ParseUint(exchangeIDStr, 10, 64)
	if err != nil {
		response.ErrorWithCode(c, http.StatusBadRequest, response.CodeInvalidParams, "兑换记录ID格式错误")
		return
	}

	// 获取当前用户ID
	userID, err := getUserIDFromContext(c)
	if err != nil {
		response.Unauthorized(c, err.Error())
		return
	}

	// 开始事务
	tx := h.db.Begin()
	defer func() {
		if r := recover(); r != nil {
			tx.Rollback()
		}
	}()

	// 先更新过期状态
	h.updateExpiredExchanges()

	// 查询兑换记录
	var exchange models.PointsMallExchange
	if err := tx.Where("id = ? AND user_id = ?", exchangeID, userID).
		Preload("Item").
		First(&exchange).Error; err != nil {
		tx.Rollback()
		if err == gorm.ErrRecordNotFound {
			response.ErrorWithCode(c, http.StatusNotFound, response.CodeNotFound, "兑换记录不存在")
			return
		}
		response.InternalServerError(c, "查询兑换记录失败")
		return
	}

	// 检查兑换记录状态
	switch exchange.Status {
	case models.ExchangeStatusInUse:
		// 正在使用
		tx.Rollback()
		response.ErrorWithCode(c, http.StatusBadRequest, response.CodeInvalidParams, "该形象正在使用中")
		return
	case models.ExchangeStatusDeactivated:
		// 已停用
		tx.Rollback()
		response.ErrorWithCode(c, http.StatusBadRequest, response.CodeInvalidParams, "该形象已停用，无法重新应用")
		return
	case models.ExchangeStatusExpired:
		// 已过期
		tx.Rollback()
		response.ErrorWithCode(c, http.StatusBadRequest, response.CodeInvalidParams, "该形象已过期，无法应用")
		return
	case models.ExchangeStatusRedeemed:
		// 已兑换，可以应用
		break
	default:
		tx.Rollback()
		response.ErrorWithCode(c, http.StatusBadRequest, response.CodeInvalidParams, "兑换记录状态异常")
		return
	}

	// 检查是否中奖（只有中奖的才能应用）
	if !exchange.LotteryWin {
		tx.Rollback()
		response.ErrorWithCode(c, http.StatusBadRequest, response.CodeInvalidParams, "该兑换记录未中奖，无法应用")
		return
	}

	// 先将该用户所有正在使用的形象设为已停用
	if err := tx.Model(&models.PointsMallExchange{}).
		Where("user_id = ? AND status = ?", userID, models.ExchangeStatusInUse).
		Update("status", models.ExchangeStatusDeactivated).Error; err != nil {
		tx.Rollback()
		response.InternalServerError(c, "更新其他形象状态失败")
		return
	}

	// 更新当前兑换记录状态为正在使用
	now := time.Now()
	if err := tx.Model(&exchange).Updates(map[string]interface{}{
		"status":     models.ExchangeStatusInUse, // 正在使用
		"applied_at": now,
	}).Error; err != nil {
		tx.Rollback()
		response.InternalServerError(c, "更新兑换记录状态失败")
		return
	}

	tx.Commit()

	// 重新查询更新后的记录
	if err := h.db.Where("id = ?", exchangeID).
		Preload("Item").
		First(&exchange).Error; err != nil {
		response.InternalServerError(c, "查询更新后的兑换记录失败")
		return
	}

	// 构建响应数据
	exchangeData := gin.H{
		"id":            exchange.ID,
		"item_id":       exchange.ItemID,
		"user_id":       exchange.UserID,
		"points":        exchange.Points,
		"exchange_time": exchange.ExchangeTime.Format("2006-01-02 15:04"),
		"status":        exchange.Status,
		"status_text":   getExchangeStatusText(exchange.Status),
		"is_lottery":    exchange.IsLottery,
		"lottery_win":   exchange.LotteryWin,
		"is_expired":    exchange.Status == 3,
		"created_at":    exchange.CreatedAt.Format("2006-01-02 15:04:05"),
		"updated_at":    exchange.UpdatedAt.Format("2006-01-02 15:04:05"),
	}

	// 添加应用时间
	if exchange.AppliedAt != nil {
		exchangeData["applied_at"] = exchange.AppliedAt.Format("2006-01-02 15:04:05")
	}

	// 添加形象信息
	if exchange.Item != nil {
		exchangeData["avatar"] = gin.H{
			"id":               exchange.Item.ID,
			"type":             exchange.Item.Type,
			"name":             exchange.Item.Name,
			"images":           exchange.Item.Images,
			"description":      exchange.Item.Description,
			"background_image": exchange.Item.BackgroundImage,
			"album_image":      exchange.Item.AlbumImage,
			"discount_rate":    exchange.Item.DiscountRate,
			"enable_lottery":   exchange.Item.EnableLottery,
			"lottery_rate":     exchange.Item.LotteryRate,
			"is_enabled":       exchange.Item.IsEnabled,
			"created_at":       exchange.Item.CreatedAt.Format("2006-01-02 15:04:05"),
			"updated_at":       exchange.Item.UpdatedAt.Format("2006-01-02 15:04:05"),
		}
	}

	response.Success(c, gin.H{
		"message":  "形象应用成功",
		"exchange": exchangeData,
	})
}

// GetCurrentAvatar 获取用户当前正在使用的VIP形象
func (h *Handler) GetCurrentAvatar(c *gin.Context) {
	// 获取当前用户ID
	userID, err := getUserIDFromContext(c)
	if err != nil {
		response.Unauthorized(c, err.Error())
		return
	}

	// 先更新过期状态
	h.updateExpiredExchanges()

	// 查询当前正在使用的形象
	var exchange models.PointsMallExchange
	if err := h.db.Where("user_id = ? AND status = ?", userID, models.ExchangeStatusInUse).
		Preload("Item").
		First(&exchange).Error; err != nil {
		if err == gorm.ErrRecordNotFound {
			response.Success(c, gin.H{
				"current_avatar": nil,
				"message":        "当前没有正在使用的形象",
			})
			return
		}
		response.InternalServerError(c, "查询当前形象失败")
		return
	}

	// 构建响应数据
	exchangeData := gin.H{
		"id":            exchange.ID,
		"item_id":       exchange.ItemID,
		"user_id":       exchange.UserID,
		"points":        exchange.Points,
		"exchange_time": exchange.ExchangeTime.Format("2006-01-02 15:04"),
		"status":        exchange.Status,
		"status_text":   getExchangeStatusText(exchange.Status),
		"is_lottery":    exchange.IsLottery,
		"lottery_win":   exchange.LotteryWin,
		"is_expired":    exchange.Status == models.ExchangeStatusExpired,
		"created_at":    exchange.CreatedAt.Format("2006-01-02 15:04:05"),
		"updated_at":    exchange.UpdatedAt.Format("2006-01-02 15:04:05"),
	}

	// 添加应用时间
	if exchange.AppliedAt != nil {
		exchangeData["applied_at"] = exchange.AppliedAt.Format("2006-01-02 15:04:05")
	}

	// 添加形象信息
	if exchange.Item != nil {
		exchangeData["avatar"] = gin.H{
			"id":               exchange.Item.ID,
			"type":             exchange.Item.Type,
			"name":             exchange.Item.Name,
			"images":           exchange.Item.Images,
			"description":      exchange.Item.Description,
			"background_image": exchange.Item.BackgroundImage,
			"album_image":      exchange.Item.AlbumImage,
			"discount_rate":    exchange.Item.DiscountRate,
			"enable_lottery":   exchange.Item.EnableLottery,
			"lottery_rate":     exchange.Item.LotteryRate,
			"is_enabled":       exchange.Item.IsEnabled,
			"created_at":       exchange.Item.CreatedAt.Format("2006-01-02 15:04:05"),
			"updated_at":       exchange.Item.UpdatedAt.Format("2006-01-02 15:04:05"),
		}
	}

	// 检查是否是当天兑换的（判断是否即将过期）
	now := time.Now()
	today := time.Date(now.Year(), now.Month(), now.Day(), 0, 0, 0, 0, now.Location())
	isToday := exchange.ExchangeTime.After(today) || exchange.ExchangeTime.Equal(today)

	response.Success(c, gin.H{
		"current_avatar": exchangeData,
		"is_today":       isToday,
		"expires_at":     today.Add(24 * time.Hour).Format("2006-01-02 15:04:05"),
	})
}
