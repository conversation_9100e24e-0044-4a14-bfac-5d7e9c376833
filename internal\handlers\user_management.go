package handlers

import (
	"net/http"
	"strconv"
	"time"
	"wangfujing_admin/internal/models"
	"wangfujing_admin/pkg/response"

	"github.com/gin-gonic/gin"
)

// UserManagementHandler 用户管理处理器
type UserManagementHandler struct {
	*Handler
}

// NewUserManagementHandler 创建用户管理处理器
func NewUserManagementHandler(h *Handler) *UserManagementHandler {
	return &UserManagementHandler{
		Handler: h,
	}
}

// GetUsers 获取用户列表
func (h *UserManagementHandler) GetUsers(c *gin.Context) {
	// 获取查询参数
	startDateStr := c.DefaultQuery("start_date", time.Now().Format("2006-01-02"))
	endDateStr := c.<PERSON>fault<PERSON>y("end_date", time.Now().Format("2006-01-02"))
	pageStr := c.<PERSON><PERSON>ult<PERSON>("page", "1")
	sizeStr := c.<PERSON><PERSON>ult<PERSON><PERSON>y("size", "20")
	keyword := c.Query("keyword")

	// 解析参数
	page, err := strconv.Atoi(pageStr)
	if err != nil || page < 1 {
		page = 1
	}

	size, err := strconv.Atoi(sizeStr)
	if err != nil || size < 1 || size > 100 {
		size = 20
	}

	// 解析日期
	startDate, err := time.Parse("2006-01-02", startDateStr)
	if err != nil {
		response.ErrorWithCode(c, http.StatusBadRequest, response.CodeInvalidParams, "开始日期格式错误")
		return
	}

	endDate, err := time.Parse("2006-01-02", endDateStr)
	if err != nil {
		response.ErrorWithCode(c, http.StatusBadRequest, response.CodeInvalidParams, "结束日期格式错误")
		return
	}

	// 确保结束日期不早于开始日期
	if endDate.Before(startDate) {
		response.ErrorWithCode(c, http.StatusBadRequest, response.CodeInvalidParams, "结束日期不能早于开始日期")
		return
	}

	var users []*models.User
	var total int64

	// 根据是否有搜索关键词选择不同的查询方法
	if keyword != "" {
		users, total, err = h.userManagementService.SearchUsers(c.Request.Context(), keyword, page, size)
	} else {
		users, total, err = h.userManagementService.GetUsersByDateRange(c.Request.Context(), startDate, endDate, page, size)
	}

	if err != nil {
		response.InternalServerError(c, "获取用户列表失败")
		return
	}

	// 构建响应数据
	userList := make([]gin.H, 0, len(users))
	for _, user := range users {
		userInfo := gin.H{
			"id":            user.ID,
			"phone":         user.Phone,
			"nickname":      user.Nickname,
			"avatar":        user.Avatar,
			"gender":        user.Gender,
			"points":        user.Points,
			"status":        user.Status,
			"register_date": user.RegisterDate.Format("2006-01-02"),
			"created_at":    user.CreatedAt.Format("2006-01-02 15:04:05"),
			"updated_at":    user.UpdatedAt.Format("2006-01-02 15:04:05"),
		}

		// 处理可选字段
		if user.Birthday != nil {
			userInfo["birthday"] = user.Birthday.Format("2006-01-02")
		}
		if user.LastLoginAt != nil {
			userInfo["last_login_at"] = user.LastLoginAt.Format("2006-01-02 15:04:05")
		}

		userList = append(userList, userInfo)
	}

	response.SuccessWithMessage(c, "获取用户列表成功", gin.H{
		"users": userList,
		"pagination": gin.H{
			"page":  page,
			"size":  size,
			"total": total,
		},
		"filter": gin.H{
			"start_date": startDateStr,
			"end_date":   endDateStr,
			"keyword":    keyword,
		},
	})
}

// GetUserDetail 获取用户详情
func (h *UserManagementHandler) GetUserDetail(c *gin.Context) {
	userIDStr := c.Param("id")
	userID, err := strconv.ParseUint(userIDStr, 10, 64)
	if err != nil {
		response.ErrorWithCode(c, http.StatusBadRequest, response.CodeInvalidParams, "用户ID格式错误")
		return
	}

	// 获取用户基本信息
	user, err := h.userManagementService.GetUserByID(c.Request.Context(), userID)
	if err != nil {
		if err.Error() == "user not found" {
			response.NotFound(c, "用户不存在")
			return
		}
		response.InternalServerError(c, "获取用户详情失败")
		return
	}

	// 获取积分汇总信息
	pointsSummary, err := h.userManagementService.GetUserPointsSummary(c.Request.Context(), userID)
	if err != nil {
		response.InternalServerError(c, "获取用户积分信息失败")
		return
	}

	// 构建用户详情响应
	userDetail := gin.H{
		"id":            user.ID,
		"phone":         user.Phone,
		"nickname":      user.Nickname,
		"avatar":        user.Avatar,
		"gender":        user.Gender,
		"points":        user.Points,
		"user_type":     user.UserType,
		"status":        user.Status,
		"register_date": user.RegisterDate.Format("2006-01-02"),
		"created_at":    user.CreatedAt.Format("2006-01-02 15:04:05"),
		"updated_at":    user.UpdatedAt.Format("2006-01-02 15:04:05"),
	}

	// 处理可选字段
	if user.Birthday != nil {
		userDetail["birthday"] = user.Birthday.Format("2006-01-02")
	}
	if user.LastLoginAt != nil {
		userDetail["last_login_at"] = user.LastLoginAt.Format("2006-01-02 15:04:05")
	}

	response.SuccessWithMessage(c, "获取用户详情成功", gin.H{
		"user":           userDetail,
		"points_summary": pointsSummary,
	})
}

// GetUserPointsRecords 获取用户积分明细
func (h *UserManagementHandler) GetUserPointsRecords(c *gin.Context) {
	userIDStr := c.Param("id")
	userID, err := strconv.ParseUint(userIDStr, 10, 64)
	if err != nil {
		response.ErrorWithCode(c, http.StatusBadRequest, response.CodeInvalidParams, "用户ID格式错误")
		return
	}

	// 获取查询参数
	yearStr := c.DefaultQuery("year", strconv.Itoa(time.Now().Year()))
	monthStr := c.DefaultQuery("month", strconv.Itoa(int(time.Now().Month())))
	pageStr := c.DefaultQuery("page", "1")
	sizeStr := c.DefaultQuery("size", "20")

	// 解析参数
	year, err := strconv.Atoi(yearStr)
	if err != nil || year < 2020 || year > 2030 {
		year = time.Now().Year()
	}

	month, err := strconv.Atoi(monthStr)
	if err != nil || month < 1 || month > 12 {
		month = int(time.Now().Month())
	}

	page, err := strconv.Atoi(pageStr)
	if err != nil || page < 1 {
		page = 1
	}

	size, err := strconv.Atoi(sizeStr)
	if err != nil || size < 1 || size > 100 {
		size = 20
	}

	// 获取积分记录
	records, total, err := h.userManagementService.GetUserPointsRecords(c.Request.Context(), userID, year, month, page, size)
	if err != nil {
		response.InternalServerError(c, "获取积分明细失败")
		return
	}

	// 构建响应数据
	recordList := make([]gin.H, 0, len(records))
	for _, record := range records {
		recordInfo := gin.H{
			"id":          record.ID,
			"type":        record.Type,
			"points":      record.Points,
			"balance":     record.Balance,
			"source":      record.Source,
			"description": record.Description,
			"created_at":  record.CreatedAt.Format("2006-01-02 15:04:05"),
		}

		// 处理可选字段
		if record.SourceID != nil {
			recordInfo["source_id"] = *record.SourceID
		}
		if record.RelatedID != nil {
			recordInfo["related_id"] = *record.RelatedID
		}
		if record.RelatedType != "" {
			recordInfo["related_type"] = record.RelatedType
		}
		if record.ExpiredAt != nil {
			recordInfo["expired_at"] = record.ExpiredAt.Format("2006-01-02 15:04:05")
		}

		recordList = append(recordList, recordInfo)
	}

	response.SuccessWithMessage(c, "获取积分明细成功", gin.H{
		"records": recordList,
		"pagination": gin.H{
			"page":  page,
			"size":  size,
			"total": total,
		},
		"filter": gin.H{
			"year":  year,
			"month": month,
		},
	})
}
