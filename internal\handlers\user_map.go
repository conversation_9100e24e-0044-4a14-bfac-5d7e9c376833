package handlers

import (
	"wangfujing_admin/internal/models"
	"wangfujing_admin/pkg/response"

	"github.com/gin-gonic/gin"
)

// GetFloorsForUser 获取楼层地图信息（用户端）
func (h *Handler) GetFloorsForUser(c *gin.Context) {
	// 获取所有启用的楼层，按排序号升序
	var floors []models.Floor
	if err := h.db.Where("status = ?", models.StatusActive).
		Order("sort ASC").
		Find(&floors).Error; err != nil {
		response.InternalServerError(c, "获取楼层信息失败")
		return
	}

	// 转换为响应格式，返回楼层的全部字段
	var result []gin.H
	for _, floor := range floors {
		floorData := gin.H{
			"id":          floor.ID,
			"name":        floor.Name,
			"sort":        floor.Sort,
			"map_image":   floor.MapImage,
			"description": floor.Description,
			"status":      floor.Status,
			"created_at":  floor.CreatedAt.Format("2006-01-02 15:04:05"),
			"updated_at":  floor.UpdatedAt.Format("2006-01-02 15:04:05"),
		}

		result = append(result, floorData)
	}

	response.Success(c, result)
}
