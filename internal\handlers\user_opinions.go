package handlers

import (
	"errors"
	"strconv"
	"strings"
	"wangfujing_admin/internal/models"
	"wangfujing_admin/pkg/response"

	"github.com/gin-gonic/gin"
	"gorm.io/gorm"
)

type OpinionRequest struct {
	Content string `json:"content" binding:"required,min=10,max=500"`
	Anonym  int    `json:"anonym" binding:"oneof=0 1"`
}

// GetOpinion 获取当前用户意见反馈（用户端）
func (h *Handler) GetOpinionByUser(c *gin.Context) {
	// 获取当前用户ID
	userID, exists := c.Get("user_id")
	if !exists {
		response.Unauthorized(c, "未登录")
		return
	}

	var userIDUint64 uint64
	switch v := userID.(type) {
	case string:
		if id, err := strconv.ParseUint(v, 10, 64); err != nil {
			response.BadRequest(c, "用户ID格式错误")
			return
		} else {
			userIDUint64 = id
		}
	case uint64:
		userIDUint64 = v
	default:
		response.BadRequest(c, "用户ID类型错误")
		return
	}

	// 获取用户当前意见反馈信息
	var opinion models.Opinion
	err := h.db.
		Select("id", "created_at", "content").
		Where("user_id = ?", userIDUint64).
		First(&opinion).Error
	// 处理查询结果
	if err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			// 用户没有意见反馈数据
			response.Success(c, gin.H{
				"exists":  false,
				"opinion": nil,
			})
		} else {
			// 其他数据库错误
			response.InternalServerError(c, "查询用户意见反馈失败")
		}
		return
	}
	// 返回存在的意见反馈
	response.Success(c, gin.H{
		"exists": true,
		"opinion": gin.H{
			"created_at": opinion.CreatedAt.Format("2006-01-02 15:04:05"),
			"content":    opinion.Content,
		},
	})
}

// Get 上传意见（用户端）
func (h *Handler) SubmitOpinion(c *gin.Context) {
	var req OpinionRequest
	// 获取当前用户ID
	userID, exists := c.Get("user_id")
	if !exists {
		response.Unauthorized(c, "未登录")
		return
	}

	if err := c.ShouldBindJSON(&req); err != nil {
		response.BadRequest(c, "无效的请求参数")
		return
	}
	// 手动验证
	var errMsgs []string
	if len(req.Content) < 10 || len(req.Content) > 500 {
		errMsgs = append(errMsgs, "内容长度需10-500字符")
	}
	if req.Anonym != 0 && req.Anonym != 1 {
		errMsgs = append(errMsgs, "匿名参数必须为0或1")
	}
	if len(errMsgs) > 0 {
		response.BadRequest(c, strings.Join(errMsgs, "；"))
		return
	}

	var userIDUint64 uint64
	switch v := userID.(type) {
	case string:
		if id, err := strconv.ParseUint(v, 10, 64); err != nil {
			response.BadRequest(c, "用户ID格式错误")
			return
		} else {
			userIDUint64 = id
		}
	case uint64:
		userIDUint64 = v
	default:
		response.BadRequest(c, "用户ID类型错误")
		return
	}

	// 获取用户手机号（从用户表）
	var user models.User
	if err := h.db.Select("phone").First(&user, userID).Error; err != nil {
		response.InternalServerError(c, "获取用户信息失败")
		return
	}

	// 检查是否已提交过意见
	var count int64
	if err := h.db.Model(&models.Opinion{}).
		Where("user_id = ?", userIDUint64).
		Count(&count).Error; err != nil {
		response.InternalServerError(c, "检查意见记录失败")
		return
	}

	if count > 0 {
		response.BadRequest(c, "每个用户只能提交一条意见反馈")
		return
	}

	// 创建新意见
	opinion := models.Opinion{
		UserID:  &userIDUint64,
		Content: req.Content,
		Contact: GetDisplayPhone(req.Anonym, user.Phone),
	}
	if err := h.db.Create(&opinion).Error; err != nil {
		response.InternalServerError(c, "意见提交失败")
		return
	}

	response.Success(c, gin.H{
		"id": opinion.ID,
	})
}

// 获取展示用手机号
func GetDisplayPhone(anonym int, phone string) string {
	if anonym == 1 {
		return phone
	}
	return phone[:3] + "****" + phone[7:] // 138****1234
}
