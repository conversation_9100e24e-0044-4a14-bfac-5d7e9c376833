package handlers

import (
	"strconv"
	"time"

	"wangfujing_admin/internal/models"
	"wangfujing_admin/pkg/response"

	"github.com/gin-gonic/gin"
)

// GetMyPoints 获取当前用户积分信息和明细
func (h *Handler) GetMyPoints(c *gin.Context) {
	// 获取当前用户ID
	userID, exists := c.Get("user_id")
	if !exists {
		response.Unauthorized(c, "未登录")
		return
	}

	var userIDUint64 uint64
	switch v := userID.(type) {
	case string:
		if id, err := strconv.ParseUint(v, 10, 64); err != nil {
			response.BadRequest(c, "用户ID格式错误")
			return
		} else {
			userIDUint64 = id
		}
	case uint64:
		userIDUint64 = v
	default:
		response.BadRequest(c, "用户ID类型错误")
		return
	}

	// 获取用户当前积分
	var user models.User
	if err := h.db.Select("id, points").First(&user, userIDUint64).Error; err != nil {
		response.InternalServerError(c, "查询用户信息失败")
		return
	}

	// 获取月份参数，默认为当前月
	monthParam := c.Query("month")
	var targetMonth string
	if monthParam == "" {
		targetMonth = time.Now().Format("2006-01")
	} else {
		targetMonth = monthParam
	}

	// 查询积分记录
	records, err := h.getPointsRecordsByMonth(userIDUint64, targetMonth)
	if err != nil {
		response.InternalServerError(c, "查询积分记录失败")
		return
	}

	// 获取可用的月份列表
	months, err := h.getAvailableMonths(userIDUint64)
	if err != nil {
		response.InternalServerError(c, "查询月份列表失败")
		return
	}

	response.Success(c, gin.H{
		"current_points": user.Points,
		"current_month":  targetMonth,
		"months":         months,
		"records":        records,
	})
}

// getPointsRecordsByMonth 获取指定月份的积分记录
func (h *Handler) getPointsRecordsByMonth(userID uint64, month string) ([]gin.H, error) {
	var pointsRecords []models.PointsRecord

	// 构建时间范围
	startTime := month + "-01 00:00:00"
	endTime := month + "-31 23:59:59"

	// 查询积分记录，按时间倒序
	if err := h.db.Where("user_id = ? AND created_at >= ? AND created_at <= ?",
		userID, startTime, endTime).
		Order("created_at DESC").
		Find(&pointsRecords).Error; err != nil {
		return nil, err
	}

	// 转换为返回格式
	var records []gin.H
	for _, record := range pointsRecords {
		recordItem := gin.H{
			"id":          record.ID,
			"title":       h.getPointsRecordTitle(record),
			"points":      record.Points,
			"type":        record.Type,
			"type_text":   h.getPointsTypeText(record.Type),
			"balance":     record.Balance,
			"source":      record.Source,
			"description": record.Description,
			"created_at":  record.CreatedAt.Format("2006-01-02 15:04"),
		}

		// 添加关联信息
		if record.RelatedType != "" && record.RelatedID != nil {
			recordItem["related_type"] = record.RelatedType
			recordItem["related_id"] = *record.RelatedID
		}

		records = append(records, recordItem)
	}

	return records, nil
}

// getAvailableMonths 获取用户有积分记录的月份列表
func (h *Handler) getAvailableMonths(userID uint64) ([]gin.H, error) {
	var results []struct {
		Month string `json:"month"`
		Count int64  `json:"count"`
	}

	// 查询有记录的月份
	if err := h.db.Model(&models.PointsRecord{}).
		Select("DATE_FORMAT(created_at, '%Y-%m') as month, COUNT(*) as count").
		Where("user_id = ?", userID).
		Group("DATE_FORMAT(created_at, '%Y-%m')").
		Order("month DESC").
		Find(&results).Error; err != nil {
		return nil, err
	}

	// 转换为返回格式
	var months []gin.H
	currentMonth := time.Now().Format("2006-01")

	for _, result := range results {
		monthItem := gin.H{
			"month":      result.Month,
			"count":      result.Count,
			"is_current": result.Month == currentMonth,
		}

		// 格式化月份显示
		if result.Month == currentMonth {
			monthItem["display"] = "本月(" + result.Month + ")"
		} else {
			monthItem["display"] = result.Month
		}

		months = append(months, monthItem)
	}

	// 如果当前月没有记录，也要添加到列表中
	hasCurrentMonth := false
	for _, month := range months {
		if month["month"] == currentMonth {
			hasCurrentMonth = true
			break
		}
	}

	if !hasCurrentMonth {
		currentMonthItem := gin.H{
			"month":      currentMonth,
			"count":      0,
			"is_current": true,
			"display":    "本月(" + currentMonth + ")",
		}
		// 插入到第一位
		months = append([]gin.H{currentMonthItem}, months...)
	}

	return months, nil
}

// getPointsRecordTitle 获取积分记录标题
func (h *Handler) getPointsRecordTitle(record models.PointsRecord) string {
	// 根据来源和类型生成标题
	switch record.Source {
	case "积分商城兑换":
		return "积分商城兑换"
	case "羊毛商城兑换":
		return "羊毛商城兑换"
	case "积分赠送", "NPC送积分":
		return "积分赠送"
	case "任务奖励":
		return "任务奖励"
	case "分享任务":
		return "分享任务"
	case "任务报名":
		return "任务报名"
	case "签到奖励":
		return "签到奖励"
	case "注册奖励":
		return "注册奖励"
	case "消费奖励":
		return "消费奖励"
	default:
		// 如果有描述，使用描述作为标题
		if record.Description != "" {
			return record.Description
		}
		return record.Source
	}
}

// getPointsTypeText 获取积分类型文本
func (h *Handler) getPointsTypeText(pointsType models.PointsType) string {
	switch pointsType {
	case models.PointsTypeEarn:
		return "获得"
	case models.PointsTypeSpend:
		return "消费"
	default:
		return "未知"
	}
}

// GetPointsRecordDetail 获取积分记录详情（预留接口）
func (h *Handler) GetPointsRecordDetail(c *gin.Context) {
	recordIDStr := c.Param("id")
	recordID, err := strconv.ParseUint(recordIDStr, 10, 64)
	if err != nil {
		response.BadRequest(c, "记录ID格式错误")
		return
	}

	// 获取当前用户ID
	userID, exists := c.Get("user_id")
	if !exists {
		response.Unauthorized(c, "未登录")
		return
	}

	var userIDUint64 uint64
	switch v := userID.(type) {
	case string:
		if id, err := strconv.ParseUint(v, 10, 64); err != nil {
			response.BadRequest(c, "用户ID格式错误")
			return
		} else {
			userIDUint64 = id
		}
	case uint64:
		userIDUint64 = v
	default:
		response.BadRequest(c, "用户ID类型错误")
		return
	}

	// 查询积分记录详情
	var record models.PointsRecord
	if err := h.db.Where("id = ? AND user_id = ?", recordID, userIDUint64).
		First(&record).Error; err != nil {
		response.NotFound(c, "积分记录不存在")
		return
	}

	// 构建详情信息
	detail := gin.H{
		"id":          record.ID,
		"title":       h.getPointsRecordTitle(record),
		"points":      record.Points,
		"type":        record.Type,
		"type_text":   h.getPointsTypeText(record.Type),
		"balance":     record.Balance,
		"source":      record.Source,
		"description": record.Description,
		"created_at":  record.CreatedAt.Format("2006-01-02 15:04:05"),
	}

	// 添加关联信息
	if record.RelatedType != "" && record.RelatedID != nil {
		detail["related_type"] = record.RelatedType
		detail["related_id"] = *record.RelatedID
	}

	response.Success(c, detail)
}
