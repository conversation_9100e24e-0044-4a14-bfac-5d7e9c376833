package handlers

import (
	"log"
	"time"
	"wangfujing_admin/internal/models"
	"wangfujing_admin/pkg/json"
	"wangfujing_admin/pkg/response"

	"github.com/gin-gonic/gin"
)

type ProgramWithFloor struct {
	models.Program
	FloorName string `json:"floor_name" gorm:"column:floor_name"`
}

// Get 获取节目单（用户端）
func (h *Handler) GetProgramsForUser(c *gin.Context) {
	var programs []ProgramWithFloor
	currentDate := time.Now().UTC().Format("2006-01-02")
	if err := h.db.
		Select("programs.*, floors.name as floor_name").
		Joins("LEFT JOIN floors ON floors.id = programs.floor_id").
		Where("programs.show_time_end >= ?", currentDate). // 改为结束时间大于当前
		Order("programs.id DESC").
		Find(&programs).Error; err != nil {
		response.InternalServerError(c, "获取节目单信息失败")
		return
	}

	// 转换为响应格式
	var result []gin.H
	for _, program := range programs {
		var timeSlots []TimeSlot
		if err := json.Unmarshal([]byte(program.ShowTimeHour), &timeSlots); err != nil {
			// 解析失败时返回空数组
			timeSlots = []TimeSlot{}
			log.Printf("JSON解析失败: %v | 原始数据: %s", err, program.ShowTimeHour)
		}
		var urls []string
		if err := json.Unmarshal([]byte(program.Url), &urls); err != nil {
			urls = []string{} // 解析失败返回空数组
			log.Printf("URL解析失败: %v | 原始数据: %s", err, program.Url)
		}
		// 处理时间格式化
		startTime, _ := time.Parse("2006-01-02", program.ShowTimeStart)
		endTime, _ := time.Parse("2006-01-02", program.ShowTimeEnd)
		programData := gin.H{
			"program_name":         program.ProgramName,
			"program_introduction": program.ProgramIntroduction,
			"url":                  urls,
			"show_time_hour":       timeSlots,
			"floor_name":           program.FloorName,
			"show_time_start":      startTime.Format("2006-01-02"),
			"show_time_end":        endTime.Format("2006-01-02"),
		}

		result = append(result, programData)
	}

	response.Success(c, result)
}
