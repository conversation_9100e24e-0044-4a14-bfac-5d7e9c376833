package handlers

import (
	"context"
	"encoding/json"
	"fmt"
	"strconv"
	"time"

	"wangfujing_admin/internal/models"
	"wangfujing_admin/internal/services"
	"wangfujing_admin/pkg/response"
	"wangfujing_admin/pkg/utils"

	"github.com/gin-gonic/gin"
	"gorm.io/gorm"
)

// GetSeckillProducts 获取薅羊毛商品列表（用户端）
func (h *Handler) GetSeckillProducts(c *gin.Context) {
	page, _ := strconv.Atoi(c.Default<PERSON>uery("page", "1"))
	size, _ := strconv.Atoi(c.DefaultQuery("size", "10"))
	search := c.Query("search") // 商品名称搜索

	// 构建查询条件
	query := h.db.Model(&models.Product{}).
		Where("type = ? AND status = ? AND approval_status = ?",
			models.ProductTypeMerchant, models.StatusActive, models.ApprovalStatusApproved)

	// 添加搜索条件
	if search != "" {
		query = query.Where("name LIKE ?", "%"+search+"%")
	}

	// 添加有效期过滤
	now := time.Now()
	query = query.Where("(valid_from IS NULL OR valid_from <= ?) AND (valid_to IS NULL OR valid_to >= ?)", now, now)

	// 计算总数
	var total int64
	if err := query.Count(&total).Error; err != nil {
		response.InternalServerError(c, "获取商品总数失败")
		return
	}

	// 分页查询
	var products []models.Product
	offset := (page - 1) * size
	if err := query.Preload("Merchant").Preload("Merchant.Floor").
		Offset(offset).Limit(size).
		Order("created_at DESC").
		Find(&products).Error; err != nil {
		response.InternalServerError(c, "获取商品列表失败")
		return
	}

	// 转换为响应格式
	var result []gin.H
	for _, product := range products {
		// 解析商品图片
		var images []string
		if product.Images != "" {
			json.Unmarshal([]byte(product.Images), &images)
		}

		// 构建商品信息
		productData := gin.H{
			"id":          product.ID,
			"name":        product.Name,
			"description": product.Description,
			"images":      images,
			"points":      product.Points,
			"daily_limit": product.DailyLimit,
			"status":      product.Status,
			"created_at":  product.CreatedAt.Format("2006-01-02 15:04:05"),
			"updated_at":  product.UpdatedAt.Format("2006-01-02 15:04:05"),
		}

		// 添加有效期信息
		if product.ValidFrom != nil {
			productData["valid_from"] = product.ValidFrom.Format("2006-01-02 15:04")
		}
		if product.ValidTo != nil {
			productData["valid_to"] = product.ValidTo.Format("2006-01-02 15:04")
		}

		// 添加商家信息
		if product.Merchant != nil {
			merchantData := gin.H{
				"id":       product.Merchant.ID,
				"name":     product.Merchant.Name,
				"position": product.Merchant.Position,
			}

			// 添加楼层信息
			if product.Merchant.Floor.ID > 0 {
				merchantData["floor_name"] = product.Merchant.Floor.Name
			}

			productData["merchant"] = merchantData
		}

		result = append(result, productData)
	}

	response.Success(c, gin.H{
		"list":  result,
		"total": total,
		"page":  page,
		"size":  size,
	})
}

// GetSeckillProductDetail 获取薅羊毛商品详情（用户端）
func (h *Handler) GetSeckillProductDetail(c *gin.Context) {
	productID := c.Param("id")

	// 获取商品详情
	var product models.Product
	if err := h.db.Preload("Merchant").Preload("Merchant.Floor").
		Where("id = ? AND type = ? AND status = ? AND approval_status = ?",
			productID, models.ProductTypeMerchant, models.StatusActive, models.ApprovalStatusApproved).
		First(&product).Error; err != nil {
		if err == gorm.ErrRecordNotFound {
			response.NotFound(c, "商品不存在或已下架")
			return
		}
		response.InternalServerError(c, "获取商品详情失败")
		return
	}

	// 检查商品是否在有效期内
	now := time.Now()
	if product.ValidFrom != nil && now.Before(*product.ValidFrom) {
		response.ErrorWithCode(c, 400, response.CodeProductExpired, "商品尚未开始")
		return
	}
	if product.ValidTo != nil && now.After(*product.ValidTo) {
		response.ErrorWithCode(c, 400, response.CodeProductExpired, "商品已过期")
		return
	}

	// 解析商品图片
	var images []string
	if product.Images != "" {
		json.Unmarshal([]byte(product.Images), &images)
	}

	// 构建响应数据
	result := gin.H{
		"id":          product.ID,
		"name":        product.Name,
		"description": product.Description,
		"images":      images,
		"points":      product.Points,
		"daily_limit": product.DailyLimit,
		"status":      product.Status,
		"created_at":  product.CreatedAt.Format("2006-01-02 15:04:05"),
		"updated_at":  product.UpdatedAt.Format("2006-01-02 15:04:05"),
	}

	// 添加有效期信息
	if product.ValidFrom != nil {
		result["valid_from"] = product.ValidFrom.Format("2006-01-02 15:04")
	}
	if product.ValidTo != nil {
		result["valid_to"] = product.ValidTo.Format("2006-01-02 15:04")
	}

	// 添加商家信息
	if product.Merchant != nil {
		merchantData := gin.H{
			"id":       product.Merchant.ID,
			"name":     product.Merchant.Name,
			"position": product.Merchant.Position,
			"phone":    product.Merchant.Phone,
			"contact":  product.Merchant.Contact,
		}

		// 添加楼层信息
		if product.Merchant.Floor.ID > 0 {
			merchantData["floor_name"] = product.Merchant.Floor.Name
		}

		result["merchant"] = merchantData
	}

	// 获取当前用户ID
	userIDInterface, exists := c.Get("user_id")
	if exists {
		// 转换用户ID类型
		userIDStr, ok := userIDInterface.(string)
		if ok {
			userID, err := strconv.ParseUint(userIDStr, 10, 64)
			if err == nil {
				// 检查用户是否已兑换过
				seckillService := services.NewSeckillService(h.db, h.rdb)
				purchased, err := seckillService.CheckUserPurchased(c.Request.Context(), product.ID, userID)
				if err == nil {
					result["user_purchased"] = purchased
				}

				// 获取库存信息
				stock, err := seckillService.GetProductStock(c.Request.Context(), product.ID)
				if err == nil {
					result["stock"] = stock.Stock
					result["stock_available"] = stock.Stock > 0
				}
			}
		}
	}

	response.Success(c, result)
}

// ExchangeSeckillProduct 兑换薅羊毛商品（用户端）
func (h *Handler) ExchangeSeckillProduct(c *gin.Context) {
	productID := c.Param("id")

	// 获取当前用户ID
	userID, exists := c.Get("user_id")
	if !exists {
		response.Unauthorized(c, "请先登录")
		return
	}

	// 获取商品信息
	var product models.Product
	if err := h.db.Preload("Merchant").
		Where("id = ? AND type = ? AND status = ? AND approval_status = ?",
			productID, models.ProductTypeMerchant, models.StatusActive, models.ApprovalStatusApproved).
		First(&product).Error; err != nil {
		if err == gorm.ErrRecordNotFound {
			response.NotFound(c, "商品不存在或已下架")
			return
		}
		response.InternalServerError(c, "获取商品信息失败")
		return
	}

	// 检查商品是否在有效期内
	now := time.Now()
	if product.ValidFrom != nil && now.Before(*product.ValidFrom) {
		response.ErrorWithCode(c, 400, response.CodeProductExpired, "商品尚未开始")
		return
	}
	if product.ValidTo != nil && now.After(*product.ValidTo) {
		response.ErrorWithCode(c, 400, response.CodeProductExpired, "商品已过期")
		return
	}

	// 获取用户信息
	var user models.User
	if err := h.db.Where("id = ?", userID).First(&user).Error; err != nil {
		response.InternalServerError(c, "获取用户信息失败")
		return
	}

	// 检查用户积分是否足够
	if user.Points < product.Points {
		response.ErrorWithCode(c, 400, response.CodeInvalidParams, "积分不足")
		return
	}

	// 使用秒杀服务检查并扣减库存
	seckillService := services.NewSeckillService(h.db, h.rdb)
	success, err := seckillService.CheckAndDecrStock(c.Request.Context(), product.ID, userID.(uint64))
	if err != nil {
		response.ErrorWithCode(c, 400, response.CodeInvalidParams, err.Error())
		return
	}
	if !success {
		response.ErrorWithCode(c, 400, response.CodeProductOutOfStock, "商品已售完")
		return
	}

	// 开始数据库事务
	tx := h.db.Begin()
	defer func() {
		if r := recover(); r != nil {
			tx.Rollback()
		}
	}()

	// 扣减用户积分
	if err := tx.Model(&user).Update("points", gorm.Expr("points - ?", product.Points)).Error; err != nil {
		tx.Rollback()
		response.InternalServerError(c, "扣减积分失败")
		return
	}

	// 生成订单号
	orderNo := utils.GenerateOrderNo()

	// 生成二维码内容（订单号）
	qrCode := fmt.Sprintf("ORDER:%s", orderNo)

	// 创建订单
	order := &models.Order{
		OrderNo:    orderNo,
		UserID:     userID.(uint64),
		ProductID:  product.ID,
		MerchantID: product.MerchantID,
		Quantity:   1,
		Price:      0, // 积分兑换，价格为0
		TotalPrice: 0,
		Points:     product.Points,
		Status:     models.OrderStatusPending,
		QRCode:     qrCode,
	}

	if err := tx.Create(order).Error; err != nil {
		tx.Rollback()
		response.InternalServerError(c, "创建订单失败")
		return
	}

	// 记录订单创建成功的日志
	fmt.Printf("订单创建成功: orderID=%d, orderNo=%s, userID=%d, productID=%d\n",
		order.ID, order.OrderNo, order.UserID, order.ProductID)

	// 创建积分记录
	pointsRecord := &models.PointsRecord{
		UserID:      userID.(uint64),
		Type:        models.PointsTypeSpend,
		Points:      product.Points,
		Balance:     user.Points - product.Points,
		Source:      "商品兑换",
		SourceID:    &product.ID,
		RelatedID:   &order.ID,
		RelatedType: "order",
		Description: fmt.Sprintf("兑换商品：%s", product.Name),
	}

	if err := tx.Create(pointsRecord).Error; err != nil {
		tx.Rollback()
		response.InternalServerError(c, "创建积分记录失败")
		return
	}

	// 提交事务
	if err := tx.Commit().Error; err != nil {
		response.InternalServerError(c, "提交事务失败")
		return
	}

	// 事务提交成功后，生成二维码
	fmt.Printf("事务提交成功，开始生成订单二维码: orderID=%d, orderNo=%s\n", order.ID, order.OrderNo)

	if h.qrCodeService != nil {
		fmt.Printf("二维码服务已初始化，开始生成: orderID=%d\n", order.ID)
		qrCodeURL, err := h.qrCodeService.GenerateOrderQRCode(c.Request.Context(), order.ID)
		if err != nil {
			// 记录错误日志，但不影响订单创建
			fmt.Printf("生成订单二维码失败: orderID=%d, orderNo=%s, error=%v\n", order.ID, order.OrderNo, err)
		} else {
			fmt.Printf("成功生成订单二维码: orderID=%d, orderNo=%s, url=%s\n", order.ID, order.OrderNo, qrCodeURL)
		}
	} else {
		fmt.Printf("二维码服务未初始化，无法生成二维码: orderID=%d, orderNo=%s\n", order.ID, order.OrderNo)
	}

	// 返回订单信息
	result := gin.H{
		"order_id":    order.ID,
		"order_no":    order.OrderNo,
		"qr_code_url": order.QRCodeURL,
		"verify_code": order.VerifyCode,
		"points":      order.Points,
		"status":      order.GetStatusText(),
		"created_at":  order.CreatedAt.Format("2006-01-02 15:04:05"),
	}

	response.SuccessWithMessage(c, "兑换成功", result)
}

// GetUserOrders 获取用户订单列表
func (h *Handler) GetUserOrders(c *gin.Context) {
	// 获取当前用户ID
	userID, exists := c.Get("user_id")
	if !exists {
		response.Unauthorized(c, "请先登录")
		return
	}

	// 获取查询参数
	page, _ := strconv.Atoi(c.DefaultQuery("page", "1"))
	size, _ := strconv.Atoi(c.DefaultQuery("size", "10"))
	status := c.Query("status")  // 状态筛选：pending(待核销)、verified(已核销)、expired(已过期)
	orderType := c.Query("type") // 订单类型：1-商家商品, 2-积分商城

	// 构建查询条件 - 只显示真实订单，过滤虚拟订单
	query := h.db.Model(&models.Order{}).Where("user_id = ?", userID).
		Where("order_no NOT LIKE 'NPC%' AND order_no NOT LIKE 'ACT%'") // 过滤NPC送积分和活动核销的虚拟订单

	// 按订单类型筛选（只支持商家商品和积分商城）
	if orderType != "" {
		if typeInt, err := strconv.Atoi(orderType); err == nil && (typeInt == 1 || typeInt == 2) {
			query = query.Where("order_type = ?", typeInt)
		}
	}

	// 根据状态筛选
	now := time.Now()
	switch status {
	case "pending":
		// 待核销：未核销且未过期
		query = query.Where("verified_at IS NULL")
		// 需要关联商品表检查有效期
		query = query.Joins("LEFT JOIN products ON orders.product_id = products.id").
			Where("(products.valid_to IS NULL OR products.valid_to > ?)", now)
	case "verified":
		// 已核销
		query = query.Where("verified_at IS NOT NULL")
	case "expired":
		// 已过期：未核销且已过期
		query = query.Where("verified_at IS NULL")
		query = query.Joins("LEFT JOIN products ON orders.product_id = products.id").
			Where("products.valid_to IS NOT NULL AND products.valid_to <= ?", now)
	}

	// 计算总数
	var total int64
	if err := query.Count(&total).Error; err != nil {
		response.InternalServerError(c, "获取订单总数失败")
		return
	}

	// 分页查询
	offset := (page - 1) * size
	var orders []models.Order
	if err := query.Preload("Product").Preload("Merchant").
		Select("orders.*").
		Order("orders.created_at DESC").
		Offset(offset).Limit(size).
		Find(&orders).Error; err != nil {
		response.InternalServerError(c, "获取订单列表失败")
		return
	}

	// 获取积分商城商品信息
	var pointsMallItems []models.PointsMallItem
	var pointsMallItemMap = make(map[uint64]models.PointsMallItem)
	var pointsMallItemIDs []uint64
	for _, order := range orders {
		if order.OrderType == models.ProductTypeMall {
			pointsMallItemIDs = append(pointsMallItemIDs, order.ProductID)
		}
	}
	if len(pointsMallItemIDs) > 0 {
		if err := h.db.Where("id IN ?", pointsMallItemIDs).Find(&pointsMallItems).Error; err == nil {
			for _, item := range pointsMallItems {
				pointsMallItemMap[item.ID] = item
			}
		}
	}

	// 处理返回数据
	var orderList []gin.H
	for _, order := range orders {
		var orderStatus string
		var productInfo gin.H
		var images []string

		// 根据订单类型处理商品信息
		if order.OrderType == models.ProductTypeMall {
			// 积分商城商品
			if item, exists := pointsMallItemMap[order.ProductID]; exists {
				// 判断订单状态
				orderStatus = "pending" // 默认待核销
				if order.VerifiedAt != nil {
					orderStatus = "verified"
				} else if item.EndTime != nil && now.After(*item.EndTime) {
					orderStatus = "expired"
				}

				// 处理商品图片
				if item.Images != "" {
					json.Unmarshal([]byte(item.Images), &images)
				}

				// 格式化时间
				var validFromStr, validToStr string
				if item.StartTime != nil {
					validFromStr = item.StartTime.Format("2006-01-02 15:04")
				}
				if item.EndTime != nil {
					validToStr = item.EndTime.Format("2006-01-02 15:04")
				}

				productInfo = gin.H{
					"id":          item.ID,
					"name":        item.Name,
					"description": item.Description,
					"images":      images,
					"points":      item.Points,
					"type":        item.Type,
					"type_text":   item.Type.String(),
					"valid_from":  validFromStr,
					"valid_to":    validToStr,
				}
			}
		} else {
			// 商家商品
			orderStatus = "pending" // 默认待核销
			if order.VerifiedAt != nil {
				orderStatus = "verified"
			} else if order.Product.ValidTo != nil && now.After(*order.Product.ValidTo) {
				orderStatus = "expired"
			}

			// 处理商品图片
			if order.Product.Images != "" {
				json.Unmarshal([]byte(order.Product.Images), &images)
			}

			// 格式化时间
			var validFromStr, validToStr string
			if order.Product.ValidFrom != nil {
				validFromStr = order.Product.ValidFrom.Format("2006-01-02 15:04")
			}
			if order.Product.ValidTo != nil {
				validToStr = order.Product.ValidTo.Format("2006-01-02 15:04")
			}

			productInfo = gin.H{
				"id":          order.Product.ID,
				"name":        order.Product.Name,
				"description": order.Product.Description,
				"images":      images,
				"points":      order.Product.Points,
				"valid_from":  validFromStr,
				"valid_to":    validToStr,
			}
		}

		orderItem := gin.H{
			"id":            order.ID,
			"order_no":      order.OrderNo,
			"order_type":    order.OrderType,
			"product_id":    order.ProductID,
			"qr_code_url":   order.QRCodeURL,
			"verify_code":   order.VerifyCode,
			"product":       productInfo,
			"points":        order.Points,
			"status":        orderStatus,
			"status_text":   order.GetStatusText(),
			"verified_at":   order.VerifiedAt,
			"verify_remark": order.VerifyRemark,
			"created_at":    order.CreatedAt.Format("2006-01-02 15:04"),
		}

		// 如果有商家信息，添加商家名称
		if order.Merchant != nil {
			orderItem["merchant_name"] = order.Merchant.Name
		}

		orderList = append(orderList, orderItem)
	}

	response.Success(c, gin.H{
		"list":  orderList,
		"total": total,
		"page":  page,
		"size":  size,
	})
}

// GetUserOrderDetail 获取用户订单详情
func (h *Handler) GetUserOrderDetail(c *gin.Context) {
	orderID := c.Param("id")

	// 获取当前用户ID
	userID, exists := c.Get("user_id")
	if !exists {
		response.Unauthorized(c, "请先登录")
		return
	}

	// 获取订单详情 - 只允许查看真实订单，过滤虚拟订单
	var order models.Order
	if err := h.db.Preload("Product").Preload("Merchant").Preload("Merchant.Floor").
		Where("id = ? AND user_id = ? AND order_no NOT LIKE 'NPC%' AND order_no NOT LIKE 'ACT%'", orderID, userID).
		First(&order).Error; err != nil {
		if err == gorm.ErrRecordNotFound {
			response.NotFound(c, "订单不存在")
			return
		}
		response.InternalServerError(c, "获取订单详情失败")
		return
	}

	// 判断订单状态
	now := time.Now()
	orderStatus := "pending" // 默认待核销
	if order.VerifiedAt != nil {
		orderStatus = "verified"
	} else if order.Product.ValidTo != nil && now.After(*order.Product.ValidTo) {
		orderStatus = "expired"
	}

	// 处理商品图片
	var images []string
	if order.Product.Images != "" {
		json.Unmarshal([]byte(order.Product.Images), &images)
	}

	// 构建返回数据
	orderDetail := gin.H{
		"id":          order.ID,
		"order_no":    order.OrderNo,
		"qr_code_url": order.QRCodeURL,
		"verify_code": order.VerifyCode,
		"product_id":  order.ProductID,
		"product": gin.H{
			"id":          order.Product.ID,
			"name":        order.Product.Name,
			"description": order.Product.Description,
			"images":      images,
			"points":      order.Product.Points,
			"valid_from":  order.Product.ValidFrom,
			"valid_to":    order.Product.ValidTo,
		},
		"points":        order.Points,
		"status":        orderStatus,
		"status_text":   order.GetStatusText(),
		"verified_at":   order.VerifiedAt,
		"verify_remark": order.VerifyRemark,
		"created_at":    order.CreatedAt.Format("2006-01-02 15:04"),
	}

	// 如果有商家信息，添加商家详情
	if order.Merchant != nil {
		merchantInfo := gin.H{
			"id":   order.Merchant.ID,
			"name": order.Merchant.Name,
		}
		if order.Merchant.Floor.ID > 0 {
			merchantInfo["floor_name"] = order.Merchant.Floor.Name
		}
		orderDetail["merchant"] = merchantInfo
	}

	// 如果已核销，添加核销信息
	if order.VerifiedAt != nil {
		orderDetail["verified_at_formatted"] = order.VerifiedAt.Format("2006-01-02 15:04")
	}

	response.Success(c, orderDetail)
}

// GetPointsMallItems 获取积分商城商品列表（用户端）
func (h *Handler) GetPointsMallItems(c *gin.Context) {
	// 获取查询参数
	page, _ := strconv.Atoi(c.DefaultQuery("page", "1"))
	size, _ := strconv.Atoi(c.DefaultQuery("size", "10"))
	itemType := c.Query("type") // 商品类型：1-兑换商品，2-VIP形象

	// 构建查询条件
	query := h.db.Model(&models.PointsMallItem{}).
		Where("status = ?", models.PointsMallItemStatusActive)

	// 按类型筛选
	if itemType != "" {
		if typeInt, err := strconv.Atoi(itemType); err == nil {
			query = query.Where("type = ?", typeInt)
		}
	}

	// 计算总数
	var total int64
	if err := query.Count(&total).Error; err != nil {
		response.InternalServerError(c, "获取商品总数失败")
		return
	}

	// 分页查询
	offset := (page - 1) * size
	var items []models.PointsMallItem
	if err := query.Order("sort DESC, created_at DESC").
		Offset(offset).Limit(size).
		Find(&items).Error; err != nil {
		response.InternalServerError(c, "获取商品列表失败")
		return
	}

	// 处理返回数据
	var itemList []gin.H
	for _, item := range items {
		// 解析商品图片
		var images []string
		if item.Images != "" {
			json.Unmarshal([]byte(item.Images), &images)
		}

		// 格式化时间
		var validFromStr, validToStr string
		if item.StartTime != nil {
			validFromStr = item.StartTime.Format("2006-01-02 15:04")
		}
		if item.EndTime != nil {
			validToStr = item.EndTime.Format("2006-01-02 15:04")
		}

		itemData := gin.H{
			"id":          item.ID,
			"name":        item.Name,
			"description": item.Description,
			"images":      images,
			"type":        item.Type,
			"type_text":   item.Type.String(),
			"points":      item.Points,
			"valid_from":  validFromStr,
			"valid_to":    validToStr,
			"sort":        item.Sort,
			"created_at":  item.CreatedAt.Format("2006-01-02 15:04"),
		}

		// VIP形象特殊字段
		if item.Type == models.PointsMallItemTypeAvatar {
			itemData["lottery_rate"] = item.LotteryRate
			itemData["background_image"] = item.BackgroundImage
			itemData["album_image"] = item.AlbumImage
		}

		itemList = append(itemList, itemData)
	}

	response.Success(c, gin.H{
		"list":  itemList,
		"total": total,
		"page":  page,
		"size":  size,
	})
}

// GetPointsMallItemDetail 获取积分商城商品详情（用户端）
func (h *Handler) GetPointsMallItemDetail(c *gin.Context) {
	itemID := c.Param("id")

	// 获取商品详情
	var item models.PointsMallItem
	if err := h.db.Where("id = ? AND status = ?", itemID, models.PointsMallItemStatusActive).
		First(&item).Error; err != nil {
		if err == gorm.ErrRecordNotFound {
			response.NotFound(c, "商品不存在")
			return
		}
		response.InternalServerError(c, "获取商品详情失败")
		return
	}

	// 解析商品图片
	var images []string
	if item.Images != "" {
		json.Unmarshal([]byte(item.Images), &images)
	}

	// 格式化时间
	var validFromStr, validToStr string
	if item.StartTime != nil {
		validFromStr = item.StartTime.Format("2006-01-02 15:04")
	}
	if item.EndTime != nil {
		validToStr = item.EndTime.Format("2006-01-02 15:04")
	}

	// 构建返回数据
	itemDetail := gin.H{
		"id":          item.ID,
		"name":        item.Name,
		"description": item.Description,
		"images":      images,
		"type":        item.Type,
		"type_text":   item.Type.String(),
		"points":      item.Points,
		"valid_from":  validFromStr,
		"valid_to":    validToStr,
		"sort":        item.Sort,
		"created_at":  item.CreatedAt.Format("2006-01-02 15:04"),
	}

	// VIP形象特殊字段
	if item.Type == models.PointsMallItemTypeAvatar {
		itemDetail["lottery_rate"] = item.LotteryRate
		itemDetail["background_image"] = item.BackgroundImage
		itemDetail["album_image"] = item.AlbumImage
	}

	response.Success(c, itemDetail)
}

// ExchangePointsMallItem 兑换积分商城商品（用户端）
func (h *Handler) ExchangePointsMallItem(c *gin.Context) {
	itemID := c.Param("id")

	// 添加调试日志
	fmt.Printf("=== 积分商城兑换开始 - itemID: %s ===\n", itemID)

	// 检查数据库连接
	if h.db == nil {
		fmt.Printf("ERROR: 数据库连接为空\n")
		response.InternalServerError(c, "数据库连接错误")
		return
	}

	// 获取当前用户ID
	userIDInterface, exists := c.Get("user_id")
	if !exists {
		fmt.Printf("DEBUG: 用户未登录，context中没有user_id\n")
		response.Unauthorized(c, "请先登录")
		return
	}

	// 转换用户ID为uint64
	var userID uint64
	switch v := userIDInterface.(type) {
	case string:
		if id, err := strconv.ParseUint(v, 10, 64); err != nil {
			fmt.Printf("ERROR: 用户ID转换失败: %v\n", err)
			response.BadRequest(c, "用户ID格式错误")
			return
		} else {
			userID = id
		}
	case uint64:
		userID = v
	case int:
		userID = uint64(v)
	case int64:
		userID = uint64(v)
	default:
		fmt.Printf("ERROR: 不支持的用户ID类型: %T\n", v)
		response.BadRequest(c, "用户ID类型错误")
		return
	}

	fmt.Printf("用户ID: %d, 兑换商品ID: %s\n", userID, itemID)

	// 获取商品信息
	var item models.PointsMallItem
	if err := h.db.Where("id = ? AND status = ?", itemID, models.PointsMallItemStatusActive).
		First(&item).Error; err != nil {
		if err == gorm.ErrRecordNotFound {
			fmt.Printf("商品不存在或已下架 - itemID: %s\n", itemID)
			response.NotFound(c, "商品不存在或已下架")
			return
		}
		fmt.Printf("ERROR: 获取商品信息失败 - error: %v\n", err)
		response.InternalServerError(c, "获取商品信息失败")
		return
	}

	fmt.Printf("找到商品 - %s (需要%d积分)\n", item.Name, item.Points)

	// 检查商品是否在有效期内
	now := time.Now()
	if item.StartTime != nil && now.Before(*item.StartTime) {
		response.BadRequest(c, "商品尚未开始兑换")
		return
	}
	if item.EndTime != nil && now.After(*item.EndTime) {
		response.BadRequest(c, "商品兑换已结束")
		return
	}

	// 检查库存
	if item.Stock > 0 {
		var exchangeCount int64
		h.db.Model(&models.Order{}).Where("product_id = ? AND order_type = ? AND status != ?",
			item.ID, models.ProductTypeMall, models.OrderStatusCancelled).Count(&exchangeCount)

		if int(exchangeCount) >= item.Stock {
			fmt.Printf("商品库存不足 - 已兑换: %d, 库存: %d\n", exchangeCount, item.Stock)
			response.BadRequest(c, "商品库存不足")
			return
		}
	}

	// 获取用户信息
	var user models.User
	if err := h.db.Where("id = ?", userID).First(&user).Error; err != nil {
		fmt.Printf("ERROR: 获取用户信息失败 - error: %v\n", err)
		response.InternalServerError(c, "获取用户信息失败")
		return
	}

	// 检查用户积分是否足够
	if user.Points < item.Points {
		fmt.Printf("用户积分不足 - 当前: %d, 需要: %d\n", user.Points, item.Points)
		response.BadRequest(c, "积分不足")
		return
	}

	// VIP形象不在这里处理，应该使用专门的ExchangeAvatar接口
	if item.Type == models.PointsMallItemTypeAvatar {
		response.BadRequest(c, "VIP形象请使用专门的兑换接口")
		return
	}

	// 开始事务
	tx := h.db.Begin()
	defer func() {
		if r := recover(); r != nil {
			fmt.Printf("ERROR: 事务panic，回滚: %v\n", r)
			tx.Rollback()
		}
	}()

	// 扣除用户积分
	if err := tx.Model(&user).Update("points", gorm.Expr("points - ?", item.Points)).Error; err != nil {
		fmt.Printf("ERROR: 扣除积分失败: %v\n", err)
		tx.Rollback()
		response.InternalServerError(c, "扣除积分失败")
		return
	}

	// 创建订单
	order := models.Order{
		OrderNo:    utils.GenerateOrderNo(),
		UserID:     userID,
		ProductID:  item.ID,
		OrderType:  models.ProductTypeMall, // 积分商城类型
		Quantity:   1,
		Price:      0,
		TotalPrice: 0,
		Points:     item.Points,
		Status:     models.OrderStatusPending,
	}

	if err := tx.Create(&order).Error; err != nil {
		fmt.Printf("ERROR: 创建订单失败: %v\n", err)
		tx.Rollback()
		response.InternalServerError(c, "创建订单失败")
		return
	}

	// 记录积分消费
	pointsRecord := models.PointsRecord{
		UserID:      userID,
		Type:        models.PointsTypeSpend,
		Points:      item.Points,
		Balance:     user.Points - item.Points,
		Source:      "积分商城",
		SourceID:    &item.ID,
		RelatedID:   &order.ID,
		RelatedType: "order",
		Description: "兑换积分商城商品：" + item.Name,
	}

	if err := tx.Create(&pointsRecord).Error; err != nil {
		fmt.Printf("ERROR: 记录积分失败: %v\n", err)
		tx.Rollback()
		response.InternalServerError(c, "记录积分失败")
		return
	}

	// 提交事务
	if err := tx.Commit().Error; err != nil {
		fmt.Printf("ERROR: 事务提交失败: %v\n", err)
		response.InternalServerError(c, "兑换失败")
		return
	}

	// 同步生成二维码
	if h.qrCodeService != nil {
		qrCodeURL, err := h.qrCodeService.GenerateOrderQRCode(context.Background(), order.ID)
		if err != nil {
			fmt.Printf("WARNING: 生成二维码失败: orderID=%d, error=%v\n", order.ID, err)
			// 二维码生成失败不影响兑换流程，只记录错误
		} else {
			// 更新order对象中的二维码URL，用于返回给用户
			order.QRCodeURL = qrCodeURL
		}

		// 重新查询订单以获取最新的verify_code
		if err := h.db.Where("id = ?", order.ID).First(&order).Error; err != nil {
			fmt.Printf("WARNING: 重新查询订单失败: %v\n", err)
		}
	}

	// 返回订单信息
	result := gin.H{
		"order_id":    order.ID,
		"order_no":    order.OrderNo,
		"qr_code_url": order.QRCodeURL,
		"verify_code": order.VerifyCode,
		"points":      order.Points,
		"status":      order.GetStatusText(),
		"created_at":  order.CreatedAt.Format("2006-01-02 15:04:05"),
	}

	fmt.Printf("积分商城兑换成功 - 订单: %s, 用户: %d, 商品: %s\n", order.OrderNo, userID, item.Name)

	response.SuccessWithMessage(c, "兑换成功", result)
}

// TestPointsMallExchange 测试积分商城兑换接口
func (h *Handler) TestPointsMallExchange(c *gin.Context) {
	fmt.Printf("DEBUG: 测试接口被调用\n")
	response.SuccessWithMessage(c, "测试成功", gin.H{
		"message": "积分商城兑换接口正常工作",
		"path":    c.Request.URL.Path,
		"method":  c.Request.Method,
	})
}
