package handlers

import (
	"bytes"
	"crypto/hmac"
	"crypto/sha256"
	"encoding/hex"
	"fmt"
	"image/png"
	"strconv"
	"time"

	"wangfujing_admin/internal/models"
	"wangfujing_admin/pkg/response"

	"github.com/gin-gonic/gin"
	"github.com/google/uuid"
	"github.com/skip2/go-qrcode"
	"gorm.io/gorm"
)

// GetTaskList 获取任务中心列表
func (h *Handler) GetTaskList(c *gin.Context) {
	// 获取当前用户ID
	userID, exists := c.Get("user_id")
	if !exists {
		response.Unauthorized(c, "未登录")
		return
	}

	var userIDUint64 uint64
	switch v := userID.(type) {
	case string:
		if id, err := strconv.ParseUint(v, 10, 64); err != nil {
			response.BadRequest(c, "用户ID格式错误")
			return
		} else {
			userIDUint64 = id
		}
	case uint64:
		userIDUint64 = v
	default:
		response.BadRequest(c, "用户ID类型错误")
		return
	}

	// 获取筛选参数
	taskType := c.DefaultQuery("type", "1") // 默认日任务
	page, _ := strconv.Atoi(c.DefaultQuery("page", "1"))
	size, _ := strconv.Atoi(c.DefaultQuery("size", "10"))

	if page < 1 {
		page = 1
	}
	if size < 1 || size > 100 {
		size = 10
	}

	// 构建查询条件 - 使用子查询过滤已结束且未参与的活动
	now := time.Now()
	baseQuery := h.db.Model(&models.MarketingActivity{}).
		Where("marketing_activities.status = ?", models.MarketingActivityStatusActive)

	// 按任务类型筛选
	if taskType != "" {
		if typeInt, err := strconv.Atoi(taskType); err == nil {
			baseQuery = baseQuery.Where("marketing_activities.type = ?", typeInt)
		}
	}

	// 过滤条件：显示进行中、待开始、或已参与过的已结束活动
	query := baseQuery.Where(`
		(marketing_activities.start_time > ? OR marketing_activities.end_time > ? OR
		 EXISTS (SELECT 1 FROM task_registrations tr WHERE tr.activity_id = marketing_activities.id AND tr.user_id = ?))
	`, now, now, userIDUint64)

	// 查询总数
	var total int64
	if err := query.Count(&total).Error; err != nil {
		response.InternalServerError(c, "查询任务总数失败")
		return
	}

	// 查询活动列表，连表查询参与状态
	type ActivityWithParticipation struct {
		models.MarketingActivity
		HasParticipated     bool                        `json:"has_participated"`
		ParticipationID     *uint64                     `json:"participation_id"`
		ParticipationStatus *models.ParticipationStatus `json:"participation_status"`
		RegistrationTime    *time.Time                  `json:"registration_time"`
		QRCodeURL           *string                     `json:"qr_code_url"`
	}

	var results []ActivityWithParticipation
	offset := (page - 1) * size
	if err := query.
		Select(`marketing_activities.*,
			CASE WHEN tr.id IS NOT NULL THEN true ELSE false END as has_participated,
			tr.id as participation_id,
			tr.status as participation_status,
			tr.created_at as registration_time,
			tr.qr_code_url as qr_code_url`).
		Joins(`LEFT JOIN task_registrations tr ON tr.activity_id = marketing_activities.id AND tr.user_id = ?`, userIDUint64).
		Order("marketing_activities.created_at DESC").
		Offset(offset).
		Limit(size).
		Find(&results).Error; err != nil {
		response.InternalServerError(c, "查询任务列表失败")
		return
	}

	// 构建返回数据
	var taskList []gin.H
	for _, result := range results {
		activity := result.MarketingActivity
		hasParticipated := result.HasParticipated

		// 计算任务状态
		status := h.calculateTaskStatus(&activity, hasParticipated, now)

		// 获取当前完成数和总数
		var currentCount int64
		if hasParticipated && result.ParticipationStatus != nil && *result.ParticipationStatus == models.ParticipationStatusCompleted {
			currentCount = 1
		}

		// 总数根据活动类型设置（这里简化为1，实际可能需要根据具体业务调整）
		totalCount := int64(1)

		// 构建参与信息
		var participationInfo map[string]interface{}
		if hasParticipated && result.ParticipationID != nil {
			participationStatus := "未知"
			if result.ParticipationStatus != nil {
				participationStatus = h.getParticipationStatusText(*result.ParticipationStatus)
			}

			participationInfo = map[string]interface{}{
				"id":          *result.ParticipationID,
				"status":      result.ParticipationStatus,
				"status_text": participationStatus,
			}

			if result.RegistrationTime != nil {
				participationInfo["registration_time"] = result.RegistrationTime.Format("2006-01-02 15:04:05")
			}

			if result.QRCodeURL != nil {
				participationInfo["qr_code_url"] = *result.QRCodeURL
			}
		}

		taskItem := gin.H{
			"id":                  activity.ID,
			"title":               activity.Name,
			"description":         activity.Description,
			"category":            activity.Category,
			"category_text":       h.getCategoryText(activity.Category),
			"type":                activity.Type,
			"type_text":           h.getTypeText(activity.Type),
			"image":               activity.Image,
			"points":              activity.RewardPoints,
			"registration_points": activity.RequiredPoints,
			"daily_limit":         activity.DailyLimit,
			"start_time":          activity.StartTime.Format("2006-01-02 15:04:05"),
			"end_time":            activity.EndTime.Format("2006-01-02 15:04:05"),
			"status":              status,
			"current_count":       currentCount,
			"total_count":         totalCount,
			"has_participated":    hasParticipated,
			"created_at":          activity.CreatedAt.Format("2006-01-02 15:04:05"),
		}

		// 添加参与信息
		if participationInfo != nil {
			taskItem["participation"] = participationInfo
		}

		taskList = append(taskList, taskItem)
	}

	response.Success(c, gin.H{
		"list":  taskList,
		"total": total,
		"page":  page,
		"size":  size,
	})
}

// GetTaskDetail 获取任务详情
func (h *Handler) GetTaskDetail(c *gin.Context) {
	// 获取任务ID
	taskIDStr := c.Param("id")
	taskID, err := strconv.ParseUint(taskIDStr, 10, 64)
	if err != nil {
		response.BadRequest(c, "任务ID格式错误")
		return
	}

	// 获取当前用户ID
	userID, exists := c.Get("user_id")
	if !exists {
		response.Unauthorized(c, "未登录")
		return
	}

	var userIDUint64 uint64
	switch v := userID.(type) {
	case string:
		if id, err := strconv.ParseUint(v, 10, 64); err != nil {
			response.BadRequest(c, "用户ID格式错误")
			return
		} else {
			userIDUint64 = id
		}
	case uint64:
		userIDUint64 = v
	default:
		response.BadRequest(c, "用户ID类型错误")
		return
	}

	// 查询活动详情
	var activity models.MarketingActivity
	if err := h.db.Where("id = ?", taskID).First(&activity).Error; err != nil {
		if err == gorm.ErrRecordNotFound {
			response.NotFound(c, "任务不存在")
			return
		}
		response.InternalServerError(c, "查询任务详情失败")
		return
	}

	// 获取用户报名记录
	var registration models.TaskRegistration
	hasParticipated := false
	if err := h.db.Where("activity_id = ? AND user_id = ?", activity.ID, userIDUint64).
		First(&registration).Error; err == nil {
		hasParticipated = true
	}

	// 计算任务状态
	now := time.Now()
	status := h.calculateTaskStatus(&activity, hasParticipated, now)

	// 检查是否可以报名
	canRegister := h.canRegister(&activity, hasParticipated, userIDUint64, now)

	// 构建返回数据
	result := gin.H{
		"id":                  activity.ID,
		"title":               activity.Name,
		"description":         activity.Description,
		"video_name":          activity.VideoName,
		"video_url":           activity.VideoURL,
		"category":            activity.Category,
		"category_text":       h.getCategoryText(activity.Category),
		"type":                activity.Type,
		"type_text":           h.getTypeText(activity.Type),
		"image":               activity.Image,
		"points":              activity.RewardPoints,
		"registration_points": activity.RequiredPoints,
		"daily_limit":         activity.DailyLimit,
		"start_time":          activity.StartTime.Format("2006-01-02 15:04:05"),
		"end_time":            activity.EndTime.Format("2006-01-02 15:04:05"),
		"status":              status,
		"can_register":        canRegister,
		"has_participated":    hasParticipated,
		"created_at":          activity.CreatedAt.Format("2006-01-02 15:04:05"),
	}

	// 如果已参与，添加参与信息
	if hasParticipated {
		participationInfo := gin.H{
			"id":                registration.ID,
			"registration_time": registration.CreatedAt.Format("2006-01-02 15:04:05"),
			"qr_code":           registration.QRCode,
			"qr_code_url":       registration.QRCodeURL,
			"verify_code":       registration.VerifyCode,
			"status":            registration.Status,
			"status_text":       h.getParticipationStatusText(registration.Status),
		}

		// 添加过期时间
		if registration.ExpiresAt != nil {
			participationInfo["expires_at"] = registration.ExpiresAt.Format("2006-01-02 15:04:05")
		}

		// 添加核销时间
		if registration.VerifiedAt != nil {
			participationInfo["verified_at"] = registration.VerifiedAt.Format("2006-01-02 15:04:05")
		}

		result["participation"] = participationInfo
	}

	response.Success(c, result)
}

// calculateTaskStatus 计算任务状态
func (h *Handler) calculateTaskStatus(activity *models.MarketingActivity, hasParticipated bool, now time.Time) string {
	if activity.StartTime != nil && now.Before(*activity.StartTime) {
		return "待开始"
	} else if activity.EndTime != nil && now.After(*activity.EndTime) {
		if hasParticipated {
			return "已结束"
		}
		return "已结束" // 未参与的已结束活动在列表中会被过滤掉
	} else {
		return "进行中"
	}
}

// canRegister 检查是否可以报名
func (h *Handler) canRegister(activity *models.MarketingActivity, hasParticipated bool, userID uint64, now time.Time) bool {
	// 已参与过不能再报名
	if hasParticipated {
		return false
	}

	// 活动未开始或已结束不能报名
	if (activity.StartTime != nil && now.Before(*activity.StartTime)) ||
		(activity.EndTime != nil && now.After(*activity.EndTime)) {
		return false
	}

	// 活动状态必须是激活状态
	if activity.Status != models.MarketingActivityStatusActive {
		return false
	}

	// 分享类活动不需要报名
	if activity.Category == models.MarketingCategoryShare {
		return false
	}

	// 检查用户积分是否足够（如果需要报名积分）
	if activity.RequiredPoints > 0 {
		var user models.User
		if err := h.db.Select("points").First(&user, userID).Error; err != nil {
			return false
		}
		if user.Points < activity.RequiredPoints {
			return false
		}
	}

	return true
}

// getCategoryText 获取活动类型文本
func (h *Handler) getCategoryText(category models.MarketingCategory) string {
	switch category {
	case models.MarketingCategoryActivity:
		return "活动"
	case models.MarketingCategoryGame:
		return "游戏"
	case models.MarketingCategoryShare:
		return "分享"
	default:
		return "未知"
	}
}

// getTypeText 获取任务类型文本
func (h *Handler) getTypeText(taskType models.MarketingActivityType) string {
	switch taskType {
	case models.MarketingActivityTypeDaily:
		return "日任务"
	case models.MarketingActivityTypeWeekly:
		return "周任务"
	case models.MarketingActivityTypeMonthly:
		return "月任务"
	default:
		return "未知"
	}
}

// getParticipationStatusText 获取参与状态文本
func (h *Handler) getParticipationStatusText(status models.ParticipationStatus) string {
	switch status {
	case models.ParticipationStatusRegistered:
		return "已报名"
	case models.ParticipationStatusCompleted:
		return "已完成"
	case models.ParticipationStatusExpired:
		return "已过期"
	default:
		return "未知"
	}
}

// RegisterTask 报名参与任务
func (h *Handler) RegisterTask(c *gin.Context) {
	// 获取任务ID
	taskIDStr := c.Param("id")
	taskID, err := strconv.ParseUint(taskIDStr, 10, 64)
	if err != nil {
		response.BadRequest(c, "任务ID格式错误")
		return
	}

	// 获取当前用户ID
	userID, exists := c.Get("user_id")
	if !exists {
		response.Unauthorized(c, "未登录")
		return
	}

	var userIDUint64 uint64
	switch v := userID.(type) {
	case string:
		if id, err := strconv.ParseUint(v, 10, 64); err != nil {
			response.BadRequest(c, "用户ID格式错误")
			return
		} else {
			userIDUint64 = id
		}
	case uint64:
		userIDUint64 = v
	default:
		response.BadRequest(c, "用户ID类型错误")
		return
	}

	// 开始事务
	tx := h.db.Begin()
	defer func() {
		if r := recover(); r != nil {
			tx.Rollback()
		}
	}()

	// 查询活动详情
	var activity models.MarketingActivity
	if err := tx.Where("id = ?", taskID).First(&activity).Error; err != nil {
		tx.Rollback()
		if err == gorm.ErrRecordNotFound {
			response.NotFound(c, "任务不存在")
			return
		}
		response.InternalServerError(c, "查询任务详情失败")
		return
	}

	// 检查是否可以报名
	now := time.Now()
	if !h.canRegister(&activity, false, userIDUint64, now) {
		tx.Rollback()
		response.BadRequest(c, "当前无法报名此任务")
		return
	}

	// 检查今日是否已达到参与次数限制
	today := time.Now().Format("2006-01-02")
	var todayRegistrationCount int64
	if err := tx.Model(&models.TaskRegistration{}).
		Where("activity_id = ? AND user_id = ? AND DATE(created_at) = ?", activity.ID, userIDUint64, today).
		Count(&todayRegistrationCount).Error; err != nil {
		tx.Rollback()
		response.InternalServerError(c, "查询今日参与次数失败")
		return
	}

	if todayRegistrationCount >= int64(activity.DailyLimit) {
		tx.Rollback()
		response.BadRequest(c, fmt.Sprintf("今日已达到参与次数限制(%d次)", activity.DailyLimit))
		return
	}

	// 查询用户信息
	var user models.User
	if err := tx.First(&user, userIDUint64).Error; err != nil {
		tx.Rollback()
		response.InternalServerError(c, "查询用户信息失败")
		return
	}

	// 检查用户积分是否足够
	if activity.RequiredPoints > 0 && user.Points < activity.RequiredPoints {
		tx.Rollback()
		response.BadRequest(c, fmt.Sprintf("积分不足，需要%d积分", activity.RequiredPoints))
		return
	}

	// 扣除报名积分（如果需要）
	if activity.RequiredPoints > 0 {
		user.Points -= activity.RequiredPoints
		if err := tx.Save(&user).Error; err != nil {
			tx.Rollback()
			response.InternalServerError(c, "扣除报名积分失败")
			return
		}

		// 创建积分记录
		pointsRecord := models.PointsRecord{
			UserID:      userIDUint64,
			Type:        models.PointsTypeSpend,
			Points:      activity.RequiredPoints,
			Balance:     user.Points,
			Source:      "任务报名",
			RelatedID:   &activity.ID,
			RelatedType: "marketing_activity",
			Description: fmt.Sprintf("报名参与任务: %s", activity.Name),
		}

		if err := tx.Create(&pointsRecord).Error; err != nil {
			tx.Rollback()
			response.InternalServerError(c, "创建积分记录失败")
			return
		}
	}

	// 生成安全的二维码内容和URL
	qrContent, qrCodeURL, verifyCode, err := h.generateSecureTaskQRCode(activity, userIDUint64)
	if err != nil {
		tx.Rollback()
		response.InternalServerError(c, "生成二维码失败: "+err.Error())
		return
	}

	// 创建报名记录
	registration := models.TaskRegistration{
		ActivityID: activity.ID,
		UserID:     userIDUint64,
		QRCode:     qrContent,
		QRCodeURL:  qrCodeURL,
		VerifyCode: verifyCode,
		ExpiresAt:  activity.EndTime, // 设置为活动结束时间
		Status:     models.ParticipationStatusRegistered,
	}

	if err := tx.Create(&registration).Error; err != nil {
		tx.Rollback()
		response.InternalServerError(c, "创建报名记录失败")
		return
	}

	// 提交事务
	if err := tx.Commit().Error; err != nil {
		response.InternalServerError(c, "提交事务失败")
		return
	}

	response.Success(c, gin.H{
		"message": "报名成功",
		"participation": gin.H{
			"id":                registration.ID,
			"qr_code":           registration.QRCode,
			"qr_code_url":       registration.QRCodeURL,
			"verify_code":       registration.VerifyCode,
			"expires_at":        registration.ExpiresAt.Format("2006-01-02 15:04:05"),
			"registration_time": registration.CreatedAt.Format("2006-01-02 15:04:05"),
			"status":            registration.Status,
			"status_text":       h.getParticipationStatusText(registration.Status),
		},
	})
}

// CompleteShareTask 完成分享任务
func (h *Handler) CompleteShareTask(c *gin.Context) {
	// 获取任务ID
	taskIDStr := c.Param("id")
	taskID, err := strconv.ParseUint(taskIDStr, 10, 64)
	if err != nil {
		response.BadRequest(c, "任务ID格式错误")
		return
	}

	// 获取当前用户ID
	userID, exists := c.Get("user_id")
	if !exists {
		response.Unauthorized(c, "未登录")
		return
	}

	var userIDUint64 uint64
	switch v := userID.(type) {
	case string:
		if id, err := strconv.ParseUint(v, 10, 64); err != nil {
			response.BadRequest(c, "用户ID格式错误")
			return
		} else {
			userIDUint64 = id
		}
	case uint64:
		userIDUint64 = v
	default:
		response.BadRequest(c, "用户ID类型错误")
		return
	}

	// 开始事务
	tx := h.db.Begin()
	defer func() {
		if r := recover(); r != nil {
			tx.Rollback()
		}
	}()

	// 查询活动详情
	var activity models.MarketingActivity
	if err := tx.Where("id = ?", taskID).First(&activity).Error; err != nil {
		tx.Rollback()
		if err == gorm.ErrRecordNotFound {
			response.NotFound(c, "任务不存在")
			return
		}
		response.InternalServerError(c, "查询任务详情失败")
		return
	}

	// 检查是否是分享类活动
	if activity.Category != models.MarketingCategoryShare {
		tx.Rollback()
		response.BadRequest(c, "此任务不是分享类任务")
		return
	}

	// 检查活动状态和时间
	now := time.Now()
	if activity.Status != models.MarketingActivityStatusActive {
		tx.Rollback()
		response.BadRequest(c, "活动未激活")
		return
	}

	if now.Before(*activity.StartTime) || now.After(*activity.EndTime) {
		tx.Rollback()
		response.BadRequest(c, "活动未在有效期内")
		return
	}

	// 检查今日是否已经完成过
	today := time.Now().Format("2006-01-02")
	var todayParticipation models.MarketingActivityParticipant
	if err := tx.Where("activity_id = ? AND user_id = ? AND participate_date = ?",
		activity.ID, userIDUint64, today).First(&todayParticipation).Error; err == nil {
		// 检查是否已达到每日限制
		if todayParticipation.ParticipateCount >= activity.DailyLimit {
			tx.Rollback()
			response.BadRequest(c, "今日已达到参与次数限制")
			return
		}
	}

	// 查询用户信息
	var user models.User
	if err := tx.First(&user, userIDUint64).Error; err != nil {
		tx.Rollback()
		response.InternalServerError(c, "查询用户信息失败")
		return
	}

	// 更新用户积分
	user.Points += activity.RewardPoints
	if err := tx.Save(&user).Error; err != nil {
		tx.Rollback()
		response.InternalServerError(c, "更新用户积分失败")
		return
	}

	// 创建积分记录
	pointsRecord := models.PointsRecord{
		UserID:      userIDUint64,
		Type:        models.PointsTypeEarn,
		Points:      activity.RewardPoints,
		Balance:     user.Points,
		Source:      "分享任务",
		RelatedID:   &activity.ID,
		RelatedType: "marketing_activity",
		Description: fmt.Sprintf("完成分享任务: %s", activity.Name),
	}

	if err := tx.Create(&pointsRecord).Error; err != nil {
		tx.Rollback()
		response.InternalServerError(c, "创建积分记录失败")
		return
	}

	// 更新或创建参与记录
	if err == gorm.ErrRecordNotFound {
		// 创建新的参与记录
		participation := models.MarketingActivityParticipant{
			ActivityID:        activity.ID,
			UserID:            userIDUint64,
			ParticipateDate:   today,
			ParticipateCount:  1,
			TotalRewardPoints: activity.RewardPoints,
			LastParticipateAt: &now,
		}
		if err := tx.Create(&participation).Error; err != nil {
			tx.Rollback()
			response.InternalServerError(c, "创建参与记录失败")
			return
		}
	} else {
		// 更新现有记录
		todayParticipation.ParticipateCount++
		todayParticipation.TotalRewardPoints += activity.RewardPoints
		todayParticipation.LastParticipateAt = &now
		if err := tx.Save(&todayParticipation).Error; err != nil {
			tx.Rollback()
			response.InternalServerError(c, "更新参与记录失败")
			return
		}
	}

	// 提交事务
	if err := tx.Commit().Error; err != nil {
		response.InternalServerError(c, "提交事务失败")
		return
	}

	response.Success(c, gin.H{
		"message": "分享任务完成",
		"points":  activity.RewardPoints,
		"balance": user.Points,
	})
}

// generateSecureTaskQRCode 生成安全的任务二维码内容和URL
func (h *Handler) generateSecureTaskQRCode(activity models.MarketingActivity, userID uint64) (string, string, string, error) {
	// 生成唯一的核销码
	verifyCode := uuid.New().String()

	// 当前时间戳
	timestamp := time.Now().Unix()

	// 过期时间设置为活动结束时间
	var expiresAt int64
	if activity.EndTime != nil {
		expiresAt = activity.EndTime.Unix()
	} else {
		expiresAt = timestamp + 7*24*3600 // 默认7天
	}

	// 构建签名字符串 (格式: activityID|userID|verifyCode|timestamp|expiresAt)
	signStr := fmt.Sprintf("%d|%d|%s|%d|%d", activity.ID, userID, verifyCode, timestamp, expiresAt)

	// 生成HMAC签名
	secretKey := "wangfujing_qrcode_secret_key_2024"
	hmacHash := hmac.New(sha256.New, []byte(secretKey))
	hmacHash.Write([]byte(signStr))
	signature := hex.EncodeToString(hmacHash.Sum(nil))

	// 构建二维码内容 (格式: activityID|userID|verifyCode|timestamp|expiresAt|signature)
	qrContent := fmt.Sprintf("%d|%d|%s|%d|%d|%s", activity.ID, userID, verifyCode, timestamp, expiresAt, signature)

	// 生成二维码图片并上传
	qrCodeURL, err := h.generateAndUploadTaskQRCode(qrContent, activity.ID, userID)
	if err != nil {
		return "", "", "", fmt.Errorf("生成二维码图片失败: %w", err)
	}

	return qrContent, qrCodeURL, verifyCode, nil
}

// generateAndUploadTaskQRCode 生成二维码图片并上传到OSS
func (h *Handler) generateAndUploadTaskQRCode(content string, activityID, userID uint64) (string, error) {
	// 生成二维码图片
	qrCode, err := qrcode.New(content, qrcode.Medium)
	if err != nil {
		return "", fmt.Errorf("创建二维码失败: %w", err)
	}

	// 设置二维码大小
	qrCode.DisableBorder = false

	// 将二维码转换为PNG图片
	img := qrCode.Image(256)

	// 将图片编码为字节数组
	var buf bytes.Buffer
	if err := png.Encode(&buf, img); err != nil {
		return "", fmt.Errorf("编码二维码图片失败: %w", err)
	}

	// 生成文件名
	filename := fmt.Sprintf("qrcode_task_%d_%d_%d.png", activityID, userID, time.Now().Unix())

	// 上传到OSS
	qrCodeURL, err := h.ossManager.UploadBytes(buf.Bytes(), filename, "qrcodes")
	if err != nil {
		return "", fmt.Errorf("上传二维码到OSS失败: %w", err)
	}

	return qrCodeURL, nil
}
