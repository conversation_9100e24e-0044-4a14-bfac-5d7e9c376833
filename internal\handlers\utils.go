package handlers

import (
	"fmt"
	"strconv"

	"github.com/gin-gonic/gin"
)

// getUserIDFromContext 从上下文中安全地获取用户ID
func getUserIDFromContext(c *gin.Context) (uint64, error) {
	userIDValue, exists := c.Get("user_id")
	if !exists {
		return 0, fmt.Errorf("用户未授权")
	}

	switch v := userIDValue.(type) {
	case uint64:
		return v, nil
	case int:
		return uint64(v), nil
	case int64:
		return uint64(v), nil
	case float64:
		return uint64(v), nil
	case string:
		if id, err := strconv.ParseUint(v, 10, 64); err == nil {
			return id, nil
		}
		return 0, fmt.Errorf("无效的用户ID格式: %s", v)
	default:
		return 0, fmt.Errorf("用户ID类型错误: %T", v)
	}
}

// getPaginationParams 获取分页参数
func getPaginationParams(c *gin.Context) (int, int) {
	page := 1
	size := 10

	if pageStr := c.Query("page"); pageStr != "" {
		if p, err := strconv.Atoi(pageStr); err == nil && p > 0 {
			page = p
		}
	}

	if sizeStr := c.Query("size"); sizeStr != "" {
		if s, err := strconv.Atoi(sizeStr); err == nil && s > 0 && s <= 100 {
			size = s
		}
	}

	return page, size
}
