package handlers

import (
	"context"
	"strconv"
	"time"
	"wangfujing_admin/internal/middleware"
	"wangfujing_admin/internal/models"
	"wangfujing_admin/pkg/response"

	"github.com/gin-gonic/gin"
	"gorm.io/gorm"
)

// WechatLoginRequest 微信小程序登录请求
type WechatLoginRequest struct {
	LoginCode string `json:"login_code" binding:"required"` // 微信登录凭证code
	PhoneCode string `json:"phone_code" binding:"required"` // 获取手机号的code（必填）
}

// WechatRefreshTokenRequest 刷新令牌请求
type WechatRefreshTokenRequest struct {
	RefreshToken string `json:"refresh_token" binding:"required"`
}

// UpdateUserInfoRequest 更新用户信息请求
type UpdateUserInfoRequest struct {
	Nickname string `json:"nickname"`
	Avatar   string `json:"avatar"`
	Gender   int    `json:"gender"`
}

// GetPhoneRequest 获取手机号请求
type GetPhoneRequest struct {
	Code string `json:"code" binding:"required"` // 获取手机号的code
}

// WechatLogin 微信小程序登录
// 安全要求：此接口只能查询users表，绝对不允许查询admin_users表
func (h *Handler) WechatLogin(c *gin.Context) {
	var req WechatLoginRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		response.ValidateError(c, err)
		return
	}

	// 调用微信接口获取openid和session_key
	sessionResp, err := h.wechatManager.GetUserOpenID(req.LoginCode)
	if err != nil {
		// 记录微信API调用失败日志
		c.Header("X-Request-ID", c.GetString("request_id"))
		response.InternalServerError(c, "微信登录失败: "+err.Error())
		return
	}

	if sessionResp.ErrCode != 0 {
		// 记录微信API业务错误日志
		c.Header("X-Request-ID", c.GetString("request_id"))
		response.BadRequest(c, "微信登录失败: "+sessionResp.ErrMsg)
		return
	}

	openID := sessionResp.OpenID
	unionID := sessionResp.UnionID

	// 查找或创建用户（仅查询users表，确保安全隔离）
	var user models.User
	result := h.db.Table("users").Where("open_id = ?", openID).First(&user)
	isNewUser := false

	if result.Error != nil {
		if result.Error == gorm.ErrRecordNotFound {
			// 新用户，创建普通用户记录
			isNewUser = true
			user = models.User{
				OpenID:       openID,
				UnionID:      unionID,
				Nickname:     "用户" + openID[len(openID)-6:],
				UserType:     models.UserTypeCustomer, // 固定为普通用户
				Status:       models.StatusActive,
				RegisterDate: time.Now(),
			}
			if err := h.db.Create(&user).Error; err != nil {
				// 记录用户创建失败日志
				c.Header("X-Request-ID", c.GetString("request_id"))
				response.InternalServerError(c, "创建用户失败")
				return
			}
		} else {
			// 记录数据库查询失败日志
			c.Header("X-Request-ID", c.GetString("request_id"))
			response.InternalServerError(c, "查询用户失败")
			return
		}
	} else {
		// 检查用户状态
		if user.Status != models.StatusActive {
			// 记录用户被禁用的登录尝试
			middleware.LogBusinessError(c, "UserLoginBlocked", nil, map[string]interface{}{
				"user_id": user.ID,
				"status":  user.Status,
			})
			response.Forbidden(c, "用户已被禁用")
			return
		}

		// 合并更新最后登录时间和UnionID（一次数据库操作）
		now := time.Now()
		updates := map[string]interface{}{
			"last_login_at": &now,
		}

		// 如果有新的UnionID且当前值为空，一起更新
		if unionID != "" && user.UnionID == "" {
			updates["union_id"] = unionID
			user.UnionID = unionID // 更新内存中的值
		}

		// 一次性更新所有字段
		if err := h.db.Model(&user).Updates(updates).Error; err != nil {
			middleware.LogDatabaseError(c, "UPDATE", err, "users", map[string]interface{}{
				"user_id": user.ID,
				"updates": updates,
			})
			response.InternalServerError(c, "更新用户信息失败")
			return
		}

		// 更新内存中的最后登录时间
		user.LastLoginAt = &now
	}

	// 生成JWT Token
	userIDStr := strconv.FormatUint(user.ID, 10)
	token, err := h.JWTService.GenerateToken(userIDStr, user.OpenID, string(user.UserType), "")
	if err != nil {
		// 记录JWT生成失败日志
		c.Header("X-Request-ID", c.GetString("request_id"))
		response.InternalServerError(c, "生成令牌失败")
		return
	}

	// 获取微信手机号（必须）
	// 获取access_token
	accessToken, err := h.wechatManager.GetUserAccessToken()
	if err != nil {
		middleware.LogWeChatAPIError(c, "GetUserAccessToken", err, "", "")
		response.InternalServerError(c, "获取微信访问令牌失败")
		return
	}

	// 获取手机号
	phoneInfo, err := h.wechatManager.GetPhoneNumber(accessToken.AccessToken, req.PhoneCode)
	if err != nil {
		middleware.LogWeChatAPIError(c, "GetPhoneNumber", err, req.PhoneCode[:10]+"...", "")
		response.InternalServerError(c, "获取手机号失败")
		return
	}

	// 更新用户手机号
	if phoneInfo.PhoneNumber != "" && user.Phone != phoneInfo.PhoneNumber {
		if err := h.db.Model(&user).Update("phone", phoneInfo.PhoneNumber).Error; err != nil {
			middleware.LogDatabaseError(c, "UPDATE", err, "users", map[string]interface{}{
				"user_id": user.ID,
				"phone":   phoneInfo.PhoneNumber,
			})
			// 手机号更新失败不影响登录，继续流程
		} else {
			user.Phone = phoneInfo.PhoneNumber // 更新内存中的值
		}
	}

	// 存储Token到Redis（24小时过期）
	ctx := context.Background()
	if err := h.TokenStorageService.StoreUserToken(ctx, userIDStr, token.AccessToken, token.RefreshToken, 24*time.Hour); err != nil {
		middleware.LogBusinessError(c, "StoreUserToken", err, map[string]interface{}{
			"user_id": userIDStr,
		})
		response.InternalServerError(c, "存储令牌失败")
		return
	}

	// 如果是新用户，发放注册积分奖励
	if isNewUser {
		// 调用会员规则服务发放注册积分
		if err := h.memberRuleService.GiveRegisterPoints(ctx, user.ID); err != nil {
			// 积分发放失败不影响登录流程，只记录错误日志
			middleware.LogBusinessError(c, "GiveRegisterPointsFailed", err, map[string]interface{}{
				"user_id": user.ID,
				"phone":   user.Phone,
			})
		} else {
			// 记录成功发放积分的日志
			middleware.LogBusinessError(c, "RegisterPointsGiven", nil, map[string]interface{}{
				"user_id": user.ID,
				"phone":   user.Phone,
			})
		}
	}

	response.Success(c, gin.H{
		"token": gin.H{
			"access_token":  token.AccessToken,
			"refresh_token": token.RefreshToken,
			"expires_at":    token.ExpiresAt,
		},
		"user_info": gin.H{
			"user_id":       user.ID,
			"nickname":      user.Nickname,
			"avatar":        user.Avatar,
			"phone":         user.Phone,
			"register_date": user.RegisterDate,
			// 移除敏感信息：open_id 和 union_id
		},
	})
}

// WechatRefreshToken 刷新微信令牌
func (h *Handler) WechatRefreshToken(c *gin.Context) {
	var req WechatRefreshTokenRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		response.ValidateError(c, err)
		return
	}

	// 解析refresh token获取用户ID
	refreshClaims, err := h.JWTService.ParseRefreshToken(req.RefreshToken)
	if err != nil {
		response.Unauthorized(c, "无效的刷新令牌")
		return
	}

	// 验证refresh token是否存在于Redis中
	ctx := context.Background()
	valid, err := h.TokenStorageService.ValidateUserRefreshToken(ctx, refreshClaims.UserID, req.RefreshToken)
	if err != nil || !valid {
		response.Unauthorized(c, "刷新令牌已过期或不存在")
		return
	}

	// 刷新令牌
	token, err := h.JWTService.RefreshToken(req.RefreshToken)
	if err != nil {
		response.Unauthorized(c, "无效的刷新令牌")
		return
	}

	// 更新Redis中的token
	if err := h.TokenStorageService.StoreUserToken(ctx, refreshClaims.UserID, token.AccessToken, token.RefreshToken, 24*time.Hour); err != nil {
		response.InternalServerError(c, "存储新令牌失败")
		return
	}

	response.Success(c, gin.H{
		"token": gin.H{
			"access_token":  token.AccessToken,
			"refresh_token": token.RefreshToken,
			"expires_at":    token.ExpiresAt,
		},
	})
}

// WechatLogout 微信小程序登出
func (h *Handler) WechatLogout(c *gin.Context) {
	// 获取当前用户ID
	userID, exists := middleware.GetUserIDFromContext(c)
	if !exists {
		response.Unauthorized(c, "用户未认证")
		return
	}

	// 使Token失效
	ctx := context.Background()
	if err := h.TokenStorageService.InvalidateUserToken(ctx, userID); err != nil {
		middleware.LogBusinessError(c, "InvalidateUserToken", err, map[string]interface{}{
			"user_id": userID,
		})
		// 即使删除失败也返回成功，避免用户重复尝试
	}

	response.SuccessWithMessage(c, "登出成功", gin.H{
		"user_id": userID,
	})
}

// UpdateUserInfo 更新用户信息
func (h *Handler) UpdateUserInfo(c *gin.Context) {
	var req UpdateUserInfoRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		response.ValidateError(c, err)
		return
	}

	// 获取当前用户ID
	userID, exists := middleware.GetUserIDFromContext(c)
	if !exists {
		response.Unauthorized(c, "用户未认证")
		return
	}

	// 更新用户信息
	updates := map[string]interface{}{}
	if req.Nickname != "" {
		updates["nickname"] = req.Nickname
	}
	if req.Avatar != "" {
		updates["avatar"] = req.Avatar
	}
	if req.Gender > 0 {
		updates["gender"] = req.Gender
	}

	if len(updates) > 0 {
		if err := h.db.Model(&models.User{}).Where("id = ?", userID).Updates(updates).Error; err != nil {
			response.InternalServerError(c, "更新用户信息失败")
			return
		}
	}

	// 获取更新后的用户信息
	var user models.User
	if err := h.db.First(&user, "id = ?", userID).Error; err != nil {
		response.InternalServerError(c, "获取用户信息失败")
		return
	}

	response.Success(c, gin.H{
		"user_id":       user.ID,
		"nickname":      user.Nickname,
		"avatar":        user.Avatar,
		"gender":        user.Gender,
		"phone":         user.Phone,
		"register_date": user.RegisterDate,
	})
}

// GetPhone 获取手机号
func (h *Handler) GetPhone(c *gin.Context) {
	var req GetPhoneRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		response.ValidateError(c, err)
		return
	}

	// 获取当前用户ID
	userID, exists := middleware.GetUserIDFromContext(c)
	if !exists {
		response.Unauthorized(c, "用户未认证")
		return
	}

	// 调用微信接口获取手机号
	// 获取access_token
	accessToken, err := h.wechatManager.GetUserAccessToken()
	if err != nil {
		// 记录获取微信访问令牌失败日志
		middleware.LogWeChatAPIError(c, "GetUserAccessToken", err, "", "")
		response.InternalServerError(c, "获取微信访问令牌失败")
		return
	}

	// 获取手机号
	phoneInfo, err := h.wechatManager.GetPhoneNumber(accessToken.AccessToken, req.Code)
	if err != nil {
		// 记录获取手机号失败的详细日志
		middleware.LogWeChatAPIError(c, "GetPhoneNumber", err, req.Code[:10]+"...", "")
		response.InternalServerError(c, "获取手机号失败")
		return
	}

	// 更新用户手机号
	if err := h.db.Model(&models.User{}).Where("id = ?", userID).Update("phone", phoneInfo.PhoneNumber).Error; err != nil {
		// 记录数据库更新失败日志
		middleware.LogDatabaseError(c, "UPDATE", err, "users", map[string]interface{}{
			"user_id": userID,
			"phone":   phoneInfo.PhoneNumber,
		})
		response.InternalServerError(c, "更新手机号失败")
		return
	}

	response.Success(c, gin.H{
		"phone": phoneInfo.PhoneNumber,
	})
}

// GetUserInfo 获取用户信息
func (h *Handler) GetUserInfo(c *gin.Context) {
	// 获取当前用户ID
	userID, exists := middleware.GetUserIDFromContext(c)
	if !exists {
		response.Unauthorized(c, "用户未认证")
		return
	}

	// 获取用户信息
	var user models.User
	if err := h.db.First(&user, "id = ?", userID).Error; err != nil {
		if err == gorm.ErrRecordNotFound {
			response.NotFound(c, "用户不存在")
			return
		}
		response.InternalServerError(c, "获取用户信息失败")
		return
	}

	response.Success(c, gin.H{
		"user_id":       user.ID,
		"open_id":       user.OpenID,
		"nickname":      user.Nickname,
		"avatar":        user.Avatar,
		"gender":        user.Gender,
		"phone":         user.Phone,
		"points":        user.Points,
		"register_date": user.RegisterDate,
		"last_login_at": user.LastLoginAt,
	})
}
