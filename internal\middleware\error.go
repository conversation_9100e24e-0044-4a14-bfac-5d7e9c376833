package middleware

import (
	"net/http"
	"runtime/debug"

	"wangfujing_admin/pkg/logger"
	"wangfujing_admin/pkg/response"

	"github.com/gin-gonic/gin"
	"go.uber.org/zap"
)

// ErrorHandler 错误处理中间件
func ErrorHandler() gin.HandlerFunc {
	return func(c *gin.Context) {
		defer func() {
			if err := recover(); err != nil {
				// 记录panic信息
				logger.Error("Panic recovered",
					zap.Any("error", err),
					zap.String("stack", string(debug.Stack())),
					zap.String("method", c.Request.Method),
					zap.String("path", c.Request.URL.Path),
					zap.String("ip", c.ClientIP()),
				)

				// 返回500错误
				if !c.Writer.Written() {
					response.InternalServerError(c, "Internal server error")
				}
				c.Abort()
			}
		}()

		c.Next()

		// 处理错误
		if len(c.Errors) > 0 {
			err := c.Errors.Last()

			// 记录错误
			logger.Error("Request error",
				zap.String("error", err.Error()),
				zap.Any("type", err.Type),
				zap.String("method", c.Request.Method),
				zap.String("path", c.Request.URL.Path),
				zap.String("ip", c.ClientIP()),
			)

			// 根据错误类型返回相应的HTTP状态码
			switch err.Type {
			case gin.ErrorTypeBind:
				if !c.Writer.Written() {
					response.BadRequest(c, "Invalid request parameters")
				}
			case gin.ErrorTypePublic:
				if !c.Writer.Written() {
					response.BadRequest(c, err.Error())
				}
			default:
				if !c.Writer.Written() {
					response.InternalServerError(c, "Internal server error")
				}
			}
		}
	}
}

// NotFoundHandler 404处理器
func NotFoundHandler() gin.HandlerFunc {
	return func(c *gin.Context) {
		response.NotFound(c, "Route not found")
	}
}

// MethodNotAllowedHandler 405处理器
func MethodNotAllowedHandler() gin.HandlerFunc {
	return func(c *gin.Context) {
		response.Error(c, http.StatusMethodNotAllowed, "Method not allowed")
	}
}

// CORSHandler CORS处理中间件
func CORSHandler() gin.HandlerFunc {
	return func(c *gin.Context) {
		origin := c.Request.Header.Get("Origin")

		// 设置CORS头
		c.Header("Access-Control-Allow-Origin", origin)
		c.Header("Access-Control-Allow-Methods", "GET, POST, PUT, DELETE, OPTIONS")
		c.Header("Access-Control-Allow-Headers", "Origin, Content-Type, Content-Length, Accept-Encoding, X-CSRF-Token, Authorization, X-Request-ID")
		c.Header("Access-Control-Expose-Headers", "Content-Length, X-Request-ID")
		c.Header("Access-Control-Allow-Credentials", "true")
		c.Header("Access-Control-Max-Age", "86400")

		// 处理预检请求
		if c.Request.Method == "OPTIONS" {
			c.AbortWithStatus(http.StatusNoContent)
			return
		}

		c.Next()
	}
}

// RateLimitHandler 限流中间件
func RateLimitHandler() gin.HandlerFunc {
	return func(c *gin.Context) {
		// 这里可以实现基于IP或用户的限流逻辑
		// 暂时跳过实现
		c.Next()
	}
}

// SecurityHeaders 安全头中间件
func SecurityHeaders() gin.HandlerFunc {
	return func(c *gin.Context) {
		c.Header("X-Content-Type-Options", "nosniff")
		c.Header("X-Frame-Options", "DENY")
		c.Header("X-XSS-Protection", "1; mode=block")
		c.Header("Referrer-Policy", "strict-origin-when-cross-origin")
		c.Header("Content-Security-Policy", "default-src 'self'")
		c.Next()
	}
}

// RequestSizeLimit 请求大小限制中间件
func RequestSizeLimit(maxSize int64) gin.HandlerFunc {
	return func(c *gin.Context) {
		if c.Request.ContentLength > maxSize {
			response.Error(c, http.StatusRequestEntityTooLarge, "Request entity too large")
			c.Abort()
			return
		}
		c.Next()
	}
}

// TimeoutHandler 超时处理中间件
func TimeoutHandler() gin.HandlerFunc {
	return func(c *gin.Context) {
		// 这里可以实现请求超时处理
		// 暂时跳过实现
		c.Next()
	}
}

// ValidationErrorHandler 参数验证错误处理
func ValidationErrorHandler() gin.HandlerFunc {
	return func(c *gin.Context) {
		c.Next()

		// 检查是否有绑定错误
		if len(c.Errors) > 0 {
			for _, err := range c.Errors {
				if err.Type == gin.ErrorTypeBind {
					if !c.Writer.Written() {
						response.ValidateError(c, err.Err)
					}
					return
				}
			}
		}
	}
}

// DatabaseErrorHandler 数据库错误处理
func DatabaseErrorHandler() gin.HandlerFunc {
	return func(c *gin.Context) {
		c.Next()

		// 检查是否有数据库错误
		if len(c.Errors) > 0 {
			for _, err := range c.Errors {
				// 这里可以根据具体的数据库错误类型进行处理
				logger.Error("Database error",
					zap.String("error", err.Error()),
					zap.String("method", c.Request.Method),
					zap.String("path", c.Request.URL.Path),
				)
			}
		}
	}
}

// CustomErrorHandler 自定义错误处理
type CustomError struct {
	Code    int    `json:"code"`
	Message string `json:"message"`
	Details string `json:"details,omitempty"`
}

func (e *CustomError) Error() string {
	return e.Message
}

// NewCustomError 创建自定义错误
func NewCustomError(code int, message, details string) *CustomError {
	return &CustomError{
		Code:    code,
		Message: message,
		Details: details,
	}
}

// HandleCustomError 处理自定义错误
func HandleCustomError(c *gin.Context, err *CustomError) {
	logger.Error("Custom error",
		zap.Int("code", err.Code),
		zap.String("message", err.Message),
		zap.String("details", err.Details),
		zap.String("method", c.Request.Method),
		zap.String("path", c.Request.URL.Path),
	)

	response.ErrorWithCode(c, http.StatusBadRequest, err.Code, err.Message)
}
