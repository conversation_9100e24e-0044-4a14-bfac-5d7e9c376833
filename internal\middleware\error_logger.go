package middleware

import (
	"bytes"
	"encoding/json"
	"io"
	"time"
	"wangfujing_admin/pkg/logger"

	"github.com/gin-gonic/gin"
	"github.com/google/uuid"
)

// ErrorLoggerMiddleware 错误日志中间件
func ErrorLoggerMiddleware() gin.HandlerFunc {
	return func(c *gin.Context) {
		// 生成请求ID
		requestID := uuid.New().String()
		c.Set("request_id", requestID)
		c.Header("X-Request-ID", requestID)

		// 记录请求开始时间
		startTime := time.Now()

		// 读取请求体（用于错误日志）
		var requestBody []byte
		if c.Request.Body != nil {
			requestBody, _ = io.ReadAll(c.Request.Body)
			c.Request.Body = io.NopCloser(bytes.NewBuffer(requestBody))
		}

		// 创建自定义ResponseWriter来捕获响应
		blw := &bodyLogWriter{body: bytes.NewBufferString(""), ResponseWriter: c.Writer}
		c.Writer = blw

		// 处理请求
		c.Next()

		// 计算处理时间
		duration := time.Since(startTime)

		// 记录请求日志
		logFields := []logger.Field{
			logger.String("request_id", requestID),
			logger.String("method", c.Request.Method),
			logger.String("path", c.Request.URL.Path),
			logger.String("query", c.Request.URL.RawQuery),
			logger.String("user_agent", c.Request.UserAgent()),
			logger.String("client_ip", c.ClientIP()),
			logger.Int("status_code", c.Writer.Status()),
			logger.Duration("duration", duration),
		}

		// 如果是错误响应，记录详细信息
		if c.Writer.Status() >= 400 {
			// 解析响应体
			var responseBody map[string]interface{}
			if err := json.Unmarshal(blw.body.Bytes(), &responseBody); err == nil {
				logFields = append(logFields, logger.Any("response", responseBody))
			} else {
				logFields = append(logFields, logger.String("response_raw", blw.body.String()))
			}

			// 如果有请求体，也记录下来
			if len(requestBody) > 0 {
				var requestBodyMap map[string]interface{}
				if err := json.Unmarshal(requestBody, &requestBodyMap); err == nil {
					// 隐藏敏感信息
					if code, exists := requestBodyMap["code"]; exists {
						if codeStr, ok := code.(string); ok && len(codeStr) > 10 {
							requestBodyMap["code"] = codeStr[:10] + "..."
						}
					}
					logFields = append(logFields, logger.Any("request_body", requestBodyMap))
				}
			}

			// 记录错误详情
			if len(c.Errors) > 0 {
				logFields = append(logFields, logger.String("errors", c.Errors.String()))
			}

			// 根据状态码选择日志级别
			if c.Writer.Status() >= 500 {
				logger.Error("HTTP请求处理失败", logFields...)
			} else {
				logger.Warn("HTTP请求业务错误", logFields...)
			}
		} else {
			// 成功请求只记录基本信息
			logger.Info("HTTP请求处理成功", logFields...)
		}
	}
}

// bodyLogWriter 用于捕获响应体的Writer
type bodyLogWriter struct {
	gin.ResponseWriter
	body *bytes.Buffer
}

func (w bodyLogWriter) Write(b []byte) (int, error) {
	w.body.Write(b)
	return w.ResponseWriter.Write(b)
}

// LogBusinessError 记录业务错误日志
func LogBusinessError(c *gin.Context, operation string, err error, details map[string]interface{}) {
	requestID := c.GetString("request_id")

	logFields := []logger.Field{
		logger.String("request_id", requestID),
		logger.String("operation", operation),
		logger.String("method", c.Request.Method),
		logger.String("path", c.Request.URL.Path),
		logger.String("user_id", c.GetString("user_id")),
		logger.String("client_ip", c.ClientIP()),
	}

	// 只有当err不为nil时才添加错误信息
	if err != nil {
		logFields = append(logFields, logger.String("error", err.Error()))
	}

	if details != nil {
		logFields = append(logFields, logger.Any("details", details))
	}

	logger.Error("业务操作失败", logFields...)
}

// LogWeChatAPIError 记录微信API错误日志
func LogWeChatAPIError(c *gin.Context, apiName string, err error, code string, errMsg string) {
	requestID := c.GetString("request_id")

	logFields := []logger.Field{
		logger.String("request_id", requestID),
		logger.String("api_name", apiName),
		logger.String("error", err.Error()),
		logger.String("wechat_code", code),
		logger.String("wechat_message", errMsg),
		logger.String("user_id", c.GetString("user_id")),
		logger.String("client_ip", c.ClientIP()),
	}

	logger.Error("微信API调用失败", logFields...)
}

// LogDatabaseError 记录数据库错误日志
func LogDatabaseError(c *gin.Context, operation string, err error, table string, condition map[string]interface{}) {
	requestID := c.GetString("request_id")

	logFields := []logger.Field{
		logger.String("request_id", requestID),
		logger.String("operation", operation),
		logger.String("table", table),
		logger.String("error", err.Error()),
		logger.String("user_id", c.GetString("user_id")),
		logger.String("client_ip", c.ClientIP()),
	}

	if condition != nil {
		logFields = append(logFields, logger.Any("condition", condition))
	}

	logger.Error("数据库操作失败", logFields...)
}
