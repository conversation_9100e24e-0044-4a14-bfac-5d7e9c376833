package middleware

import (
	"fmt"
	"log"
	"strings"
	"wangfujing_admin/internal/models"
	"wangfujing_admin/internal/services"
	"wangfujing_admin/pkg/response"

	"github.com/gin-gonic/gin"
)

// min 返回两个整数中的较小值
func min(a, b int) int {
	if a < b {
		return a
	}
	return b
}

// JWTMiddleware JWT认证中间件
func JWTMiddleware(jwtService services.JWTService, tokenService *services.TokenStorageService) gin.HandlerFunc {
	return func(c *gin.Context) {
		log.Printf("=== JWT中间件开始处理 ===")
		log.Printf("请求路径: %s %s", c.Request.Method, c.Request.URL.Path)

		// 从请求头获取Authorization
		authHeader := c.GetHeader("Authorization")
		log.Printf("Authorization头: %s", authHeader)
		if authHeader == "" {
			log.Printf("缺少Authorization头")
			// 记录缺少认证头的请求
			c.Header("X-Debug-Info", "Missing Authorization header")
			response.Unauthorized(c, "请提供认证令牌")
			c.Abort()
			return
		}

		// 检查头部格式
		parts := strings.SplitN(authHeader, " ", 2)
		if len(parts) != 2 || strings.ToLower(parts[0]) != "bearer" {
			// 记录认证格式错误
			c.Header("X-Debug-Info", "Invalid auth format: "+authHeader[:min(50, len(authHeader))])
			response.Unauthorized(c, "认证格式错误，请使用Bearer认证")
			c.Abort()
			return
		}

		tokenString := parts[1]

		// 添加调试信息
		c.Header("X-Debug-Token-Length", fmt.Sprintf("%d", len(tokenString)))
		c.Header("X-Debug-Token-Prefix", tokenString[:min(20, len(tokenString))])

		// 解析JWT获取用户信息
		claims, err := jwtService.ParseToken(tokenString)
		if err != nil {
			c.Header("X-Debug-Info", "Token parse failed: "+err.Error())
			response.Unauthorized(c, "认证失败: "+err.Error())
			c.Abort()
			return
		}

		// 检查Token是否在Redis中存在且匹配
		var isValid bool
		var validationErr error

		// 根据请求路径判断是用户端还是管理端
		if strings.HasPrefix(c.Request.URL.Path, "/admin/") || strings.HasPrefix(c.Request.URL.Path, "/mall/") {
			// 管理端Token验证（包括商场端）
			isValid, validationErr = tokenService.ValidateAdminToken(c.Request.Context(), claims.UserID, tokenString)
		} else {
			// 用户端Token验证
			isValid, validationErr = tokenService.ValidateUserToken(c.Request.Context(), claims.UserID, tokenString)
		}

		if validationErr != nil {
			c.Header("X-Debug-Info", "Token validation failed: "+validationErr.Error())
			response.InternalServerError(c, "认证检查失败")
			c.Abort()
			return
		}

		if !isValid {
			// 添加详细的调试信息
			debugInfo := fmt.Sprintf("Token invalid - UserID: %s, TokenPrefix: %s, Path: %s",
				claims.UserID,
				tokenString[:min(20, len(tokenString))],
				c.Request.URL.Path)
			c.Header("X-Debug-Info", debugInfo)
			response.Unauthorized(c, "令牌已失效")
			c.Abort()
			return
		}

		// Token解析已在上面完成

		// 将用户信息设置到上下文
		c.Set("user_id", claims.UserID)
		c.Set("open_id", claims.OpenID)
		c.Set("user_type", claims.UserType)
		c.Set("roles", claims.Roles)

		c.Next()
	}
}

// OptionalJWTMiddleware 可选的JWT认证中间件（不强制要求认证）
func OptionalJWTMiddleware(jwtService services.JWTService) gin.HandlerFunc {
	return func(c *gin.Context) {
		// 从请求头获取Authorization
		authHeader := c.GetHeader("Authorization")
		if authHeader == "" {
			// 没有认证头，继续执行但不设置用户信息
			c.Next()
			return
		}

		// 检查头部格式
		parts := strings.SplitN(authHeader, " ", 2)
		if len(parts) != 2 || strings.ToLower(parts[0]) != "bearer" {
			// 格式错误，继续执行但不设置用户信息
			c.Next()
			return
		}

		tokenString := parts[1]

		// 解析令牌
		claims, err := jwtService.ParseToken(tokenString)
		if err != nil {
			// 解析失败，继续执行但不设置用户信息
			c.Next()
			return
		}

		// 将用户信息设置到上下文
		c.Set("user_id", claims.UserID)
		c.Set("open_id", claims.OpenID)
		c.Set("user_type", claims.UserType)
		c.Set("roles", claims.Roles)

		c.Next()
	}
}

// GetUserIDFromContext 从上下文中获取用户ID
func GetUserIDFromContext(c *gin.Context) (string, bool) {
	userID, exists := c.Get("user_id")
	if !exists {
		return "", false
	}

	if id, ok := userID.(string); ok {
		return id, true
	}

	return "", false
}

// GetOpenIDFromContext 从上下文中获取OpenID
func GetOpenIDFromContext(c *gin.Context) (string, bool) {
	openID, exists := c.Get("open_id")
	if !exists {
		return "", false
	}

	if id, ok := openID.(string); ok {
		return id, true
	}

	return "", false
}

// GetUserTypeFromContext 从上下文中获取用户类型
func GetUserTypeFromContext(c *gin.Context) (string, bool) {
	userType, exists := c.Get("user_type")
	if !exists {
		return "", false
	}

	if t, ok := userType.(string); ok {
		return t, true
	}

	return "", false
}

// GetRolesFromContext 从上下文中获取用户角色
func GetRolesFromContext(c *gin.Context) ([]string, bool) {
	roles, exists := c.Get("roles")
	if !exists {
		return nil, false
	}

	if r, ok := roles.([]string); ok {
		return r, true
	}

	return nil, false
}

// MerchantMiddleware 商家权限中间件
func MerchantMiddleware() gin.HandlerFunc {
	return func(c *gin.Context) {
		userType, exists := c.Get("user_type")
		if !exists {
			response.Forbidden(c, "用户类型未知")
			c.Abort()
			return
		}

		// 检查是否为商家用户
		if userType.(models.UserType) != models.UserTypeMerchant {
			response.Forbidden(c, "无权限访问")
			c.Abort()
			return
		}

		c.Next()
	}
}
