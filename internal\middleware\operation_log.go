package middleware

import (
	"bytes"
	"context"
	"encoding/json"
	"io"
	"strconv"
	"strings"
	"wangfujing_admin/internal/services"
	"wangfujing_admin/pkg/logger"

	"github.com/gin-gonic/gin"
	"gorm.io/gorm"
)

// OperationLogConfig 操作日志配置
type OperationLogConfig struct {
	// 需要记录日志的路径前缀
	LogPaths []string
	// 排除的路径
	ExcludePaths []string
	// 是否记录请求体
	LogRequestBody bool
	// 是否记录响应体
	LogResponseBody bool
	// 最大请求体大小（字节）
	MaxRequestBodySize int64
	// 最大响应体大小（字节）
	MaxResponseBodySize int64
}

// DefaultOperationLogConfig 默认配置
func DefaultOperationLogConfig() OperationLogConfig {
	return OperationLogConfig{
		LogPaths: []string{
			"/admin/v1/", // 记录所有管理端API操作
		},
		ExcludePaths: []string{
			"/admin/v1/auth/login",     // 登录操作不记录
			"/admin/v1/auth/refresh",   // 刷新token不记录
			"/admin/v1/operation-logs", // 操作日志查询不记录（避免循环）
			"/admin/v1/upload",         // 文件上传不记录
			"/admin/v1/health",         // 健康检查不记录
		},
		LogRequestBody:      true,
		LogResponseBody:     false,     // 响应体可能很大，默认不记录
		MaxRequestBodySize:  10 * 1024, // 10KB
		MaxResponseBodySize: 10 * 1024, // 10KB
	}
}

// operationLogResponseWriter 包装响应写入器以捕获响应体
type operationLogResponseWriter struct {
	gin.ResponseWriter
	body *bytes.Buffer
}

func (w *operationLogResponseWriter) Write(data []byte) (int, error) {
	w.body.Write(data)
	return w.ResponseWriter.Write(data)
}

// OperationLogMiddleware 操作日志中间件
func OperationLogMiddleware(db *gorm.DB, config ...OperationLogConfig) gin.HandlerFunc {
	cfg := DefaultOperationLogConfig()
	if len(config) > 0 {
		cfg = config[0]
	}

	operationLogService := services.NewOperationLogService(db)

	return func(c *gin.Context) {
		// 检查是否需要记录日志
		if !shouldLogOperation(c.Request.RequestURI, cfg) {
			c.Next()
			return
		}

		// 记录开始时间（如果需要的话）
		// startTime := time.Now()

		// 读取请求体
		var requestBody []byte
		if cfg.LogRequestBody && c.Request.Body != nil {
			requestBody, _ = io.ReadAll(io.LimitReader(c.Request.Body, cfg.MaxRequestBodySize))
			c.Request.Body = io.NopCloser(bytes.NewBuffer(requestBody))
		}

		// 包装响应写入器
		responseBuffer := &bytes.Buffer{}
		writer := &operationLogResponseWriter{
			ResponseWriter: c.Writer,
			body:           responseBuffer,
		}
		c.Writer = writer

		// 执行请求
		c.Next()

		// 记录操作日志
		go func() {
			defer func() {
				if r := recover(); r != nil {
					logger.Error("操作日志记录失败", logger.Any("error", r))
				}
			}()

			// 获取操作者信息
			operatorID, operatorName := getOperatorInfo(c)
			if operatorID == 0 {
				return // 未认证用户不记录日志
			}

			// 解析操作信息
			module, action, resource, resourceID, description := parseOperationInfo(c)

			// 准备请求数据
			var requestData interface{}
			if len(requestBody) > 0 {
				json.Unmarshal(requestBody, &requestData)
			}

			// 准备响应数据
			var responseData interface{}
			if cfg.LogResponseBody && responseBuffer.Len() > 0 && responseBuffer.Len() < int(cfg.MaxResponseBodySize) {
				json.Unmarshal(responseBuffer.Bytes(), &responseData)
			}

			// 确定操作状态
			status := 1 // 成功
			errorMessage := ""
			if c.Writer.Status() >= 400 {
				status = 0 // 失败
				if responseData != nil {
					if respMap, ok := responseData.(map[string]interface{}); ok {
						if msg, exists := respMap["message"]; exists {
							errorMessage = msg.(string)
						}
					}
				}
			}

			// 创建新的context，避免context canceled错误
			ctx := context.Background()

			// 记录操作日志
			err := operationLogService.CreateOperationLog(
				ctx,
				operatorID,
				operatorName,
				module,
				action,
				resource,
				resourceID,
				description,
				requestData,
				responseData,
				c.ClientIP(),
				c.Request.UserAgent(),
				status,
				errorMessage,
			)

			if err != nil {
				logger.Error("记录操作日志失败", logger.String("error", err.Error()))
			}
		}()
	}
}

// shouldLogOperation 判断是否应该记录操作日志
func shouldLogOperation(requestURI string, config OperationLogConfig) bool {
	// 检查排除路径
	for _, excludePath := range config.ExcludePaths {
		if strings.Contains(requestURI, excludePath) {
			return false
		}
	}

	// 检查包含路径
	for _, logPath := range config.LogPaths {
		if strings.HasPrefix(requestURI, logPath) {
			return true
		}
	}

	return false
}

// getOperatorInfo 获取操作者信息
func getOperatorInfo(c *gin.Context) (uint64, string) {
	// 从JWT中获取用户ID
	userIDStr, exists := c.Get("user_id")
	if !exists {
		return 0, ""
	}

	userID, err := strconv.ParseUint(userIDStr.(string), 10, 64)
	if err != nil {
		return 0, ""
	}

	// 获取用户昵称（如果有的话）
	nickname, _ := c.Get("nickname")
	if nickname == nil {
		nickname = "管理员"
	}

	return userID, nickname.(string)
}

// parseOperationInfo 解析操作信息
func parseOperationInfo(c *gin.Context) (module, action, resource, resourceID, description string) {
	method := c.Request.Method
	path := c.Request.URL.Path

	// 解析模块和资源
	if strings.Contains(path, "/permissions") {
		module = "权限管理"
		resource = "permission"
	} else if strings.Contains(path, "/roles") {
		module = "角色管理"
		resource = "role"
	} else if strings.Contains(path, "/admin-users") || strings.Contains(path, "/users") {
		module = "用户管理"
		resource = "admin_user"
	} else if strings.Contains(path, "/merchants") {
		module = "商家管理"
		resource = "merchant"
	} else if strings.Contains(path, "/floors") {
		module = "楼层管理"
		resource = "floor"
	} else if strings.Contains(path, "/products") {
		module = "商品管理"
		resource = "product"
	} else if strings.Contains(path, "/orders") {
		module = "订单管理"
		resource = "order"
	} else if strings.Contains(path, "/complaints") {
		module = "客诉管理"
		resource = "complaint"
	} else if strings.Contains(path, "/opinions") {
		module = "意见管理"
		resource = "opinion"
	} else if strings.Contains(path, "/marketing-activities") {
		module = "营销活动管理"
		resource = "marketing_activity"
	} else if strings.Contains(path, "/merchant-activities") {
		module = "商家活动管理"
		resource = "merchant_activity"
	} else if strings.Contains(path, "/points-mall") {
		module = "积分商城管理"
		resource = "points_mall"
	} else if strings.Contains(path, "/membership-rules") {
		module = "会员规则管理"
		resource = "membership_rule"
	} else if strings.Contains(path, "/marketing") || strings.Contains(path, "/activities") {
		module = "营销管理"
		resource = "activity"
	} else if strings.Contains(path, "/operation-logs") {
		module = "系统管理"
		resource = "operation_log"
	} else {
		module = "其他"
		resource = "unknown"
	}

	// 解析操作动作
	switch method {
	case "GET":
		if strings.Contains(path, "/") && len(strings.Split(path, "/")) > 4 {
			action = "查看"
			description = "查看" + module + "详情"
		} else {
			action = "列表"
			description = "获取" + module + "列表"
		}
	case "POST":
		action = "创建"
		description = "创建" + module
	case "PUT", "PATCH":
		action = "更新"
		description = "更新" + module
	case "DELETE":
		action = "删除"
		description = "删除" + module
	}

	// 解析资源ID
	pathParts := strings.Split(path, "/")
	if len(pathParts) > 4 {
		resourceID = pathParts[len(pathParts)-1]
		// 如果最后一部分不是数字，可能是子操作
		if _, err := strconv.Atoi(resourceID); err != nil {
			if len(pathParts) > 5 {
				resourceID = pathParts[len(pathParts)-2]
				// 更新描述以包含子操作
				subAction := pathParts[len(pathParts)-1]
				if subAction == "assign-permissions" {
					action = "分配权限"
					description = "为角色分配权限"
				} else if subAction == "assign-roles" {
					action = "分配角色"
					description = "为用户分配角色"
				}
			}
		}
	}

	return
}
