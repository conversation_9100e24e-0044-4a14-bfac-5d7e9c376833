package middleware

import (
	"fmt"
	"strconv"
	"strings"

	"wangfujing_admin/internal/models"
	"wangfujing_admin/pkg/logger"
	"wangfujing_admin/pkg/response"

	"github.com/gin-gonic/gin"
	"gorm.io/gorm"
)

// PermissionMiddleware 权限检查中间件
func PermissionMiddleware(db *gorm.DB, requiredPermission string) gin.HandlerFunc {
	return func(c *gin.Context) {
		// 获取用户ID
		userIDStr := c.GetString("user_id")
		if userIDStr == "" {
			response.Unauthorized(c, "用户未登录")
			c.Abort()
			return
		}

		userID, err := strconv.ParseUint(userIDStr, 10, 64)
		if err != nil {
			response.Unauthorized(c, "用户ID无效")
			c.Abort()
			return
		}

		// 检查用户权限
		hasPermission, err := CheckUserPermission(db, userID, requiredPermission)
		if err != nil {
			logger.Error("检查用户权限失败",
				logger.String("user_id", userIDStr),
				logger.String("permission", requiredPermission),
				logger.String("error", err.Error()),
			)
			response.InternalServerError(c, "权限检查失败")
			c.Abort()
			return
		}

		if !hasPermission {
			logger.Warn("用户权限不足",
				logger.String("user_id", userIDStr),
				logger.String("permission", requiredPermission),
				logger.String("path", c.Request.URL.Path),
				logger.String("method", c.Request.Method),
			)
			response.Forbidden(c, "权限不足")
			c.Abort()
			return
		}

		c.Next()
	}
}

// CheckUserPermission 检查用户是否有指定权限
func CheckUserPermission(db *gorm.DB, userID uint64, permissionName string) (bool, error) {
	// 首先查询用户类型
	var adminUser models.AdminUser
	if err := db.First(&adminUser, userID).Error; err != nil {
		return false, err
	}

	// 超级管理员拥有所有权限
	if adminUser.UserType == models.UserTypeSuperAdmin {
		return true, nil
	}

	// 商家用户：检查是否为商家端权限
	if adminUser.UserType == models.UserTypeMerchant {
		// 商家用户只能访问merchant:开头的权限
		if strings.HasPrefix(permissionName, "merchant:") {
			return true, nil
		}
		return false, nil
	}

	// 普通管理员：通过角色权限检查
	logger.Info("检查普通管理员权限",
		logger.String("user_id", fmt.Sprintf("%d", userID)),
		logger.String("permission", permissionName),
	)

	var count int64
	err := db.Table("admin_user_roles").
		Joins("JOIN role_permissions ON admin_user_roles.role_id = role_permissions.role_id").
		Joins("JOIN permissions ON role_permissions.permission_id = permissions.id").
		Where("admin_user_roles.admin_user_id = ? AND permissions.name = ?", userID, permissionName).
		Count(&count).Error

	if err != nil {
		logger.Error("权限查询失败",
			logger.String("user_id", fmt.Sprintf("%d", userID)),
			logger.String("permission", permissionName),
			logger.String("error", err.Error()),
		)
		return false, err
	}

	logger.Info("权限查询结果",
		logger.String("user_id", fmt.Sprintf("%d", userID)),
		logger.String("permission", permissionName),
		logger.String("count", fmt.Sprintf("%d", count)),
		logger.Bool("has_permission", count > 0),
	)

	return count > 0, nil
}

// MerchantPermission 商家权限检查中间件工厂函数
func MerchantPermission(db *gorm.DB, permission string) gin.HandlerFunc {
	return PermissionMiddleware(db, permission)
}

// RequirePermissions 要求多个权限中的任意一个
func RequirePermissions(db *gorm.DB, permissions ...string) gin.HandlerFunc {
	return func(c *gin.Context) {
		// 获取用户ID
		userIDStr := c.GetString("user_id")
		if userIDStr == "" {
			response.Unauthorized(c, "用户未登录")
			c.Abort()
			return
		}

		userID, err := strconv.ParseUint(userIDStr, 10, 64)
		if err != nil {
			response.Unauthorized(c, "用户ID无效")
			c.Abort()
			return
		}

		// 检查是否有任意一个权限
		hasAnyPermission := false
		for _, permission := range permissions {
			hasPermission, err := CheckUserPermission(db, userID, permission)
			if err != nil {
				logger.Error("检查用户权限失败",
					logger.String("user_id", userIDStr),
					logger.String("permission", permission),
					logger.String("error", err.Error()),
				)
				continue
			}
			if hasPermission {
				hasAnyPermission = true
				break
			}
		}

		if !hasAnyPermission {
			logger.Warn("用户权限不足",
				logger.String("user_id", userIDStr),
				logger.String("required_permissions", fmt.Sprintf("%v", permissions)),
				logger.String("path", c.Request.URL.Path),
				logger.String("method", c.Request.Method),
			)
			response.Forbidden(c, "权限不足")
			c.Abort()
			return
		}

		c.Next()
	}
}

// GetUserPermissions 获取用户所有权限
func GetUserPermissions(db *gorm.DB, userID uint64) ([]models.Permission, error) {
	var permissions []models.Permission

	err := db.Table("permissions").
		Select("permissions.*").
		Joins("JOIN role_permissions ON permissions.id = role_permissions.permission_id").
		Joins("JOIN admin_user_roles ON role_permissions.role_id = admin_user_roles.role_id").
		Where("admin_user_roles.admin_user_id = ?", userID).
		Distinct().
		Find(&permissions).Error

	return permissions, err
}

// CheckMerchantUser 检查是否为商家用户
func CheckMerchantUser(db *gorm.DB) gin.HandlerFunc {
	return func(c *gin.Context) {
		// 获取用户ID
		userIDStr := c.GetString("user_id")
		if userIDStr == "" {
			response.Unauthorized(c, "用户未登录")
			c.Abort()
			return
		}

		userID, err := strconv.ParseUint(userIDStr, 10, 64)
		if err != nil {
			response.Unauthorized(c, "用户ID无效")
			c.Abort()
			return
		}

		// 查询用户类型
		var adminUser models.AdminUser
		if err := db.First(&adminUser, userID).Error; err != nil {
			if err == gorm.ErrRecordNotFound {
				response.Unauthorized(c, "用户不存在")
			} else {
				response.InternalServerError(c, "查询用户信息失败")
			}
			c.Abort()
			return
		}

		// 检查是否为商家用户
		if adminUser.UserType != models.UserTypeMerchant {
			response.Forbidden(c, "仅限商家用户访问")
			c.Abort()
			return
		}

		// 将商家ID存储到上下文中
		if adminUser.MerchantID != nil {
			c.Set("merchant_id", *adminUser.MerchantID)
		}

		c.Next()
	}
}

// RequireAdminRole 要求管理员角色（普通管理员或超级管理员）
func RequireAdminRole(db *gorm.DB) gin.HandlerFunc {
	return func(c *gin.Context) {
		// 获取用户ID
		userIDStr := c.GetString("user_id")
		if userIDStr == "" {
			response.Unauthorized(c, "用户未登录")
			c.Abort()
			return
		}

		userID, err := strconv.ParseUint(userIDStr, 10, 64)
		if err != nil {
			response.Unauthorized(c, "用户ID无效")
			c.Abort()
			return
		}

		// 查询用户类型
		var adminUser models.AdminUser
		if err := db.First(&adminUser, userID).Error; err != nil {
			if err == gorm.ErrRecordNotFound {
				response.Unauthorized(c, "用户不存在")
			} else {
				response.InternalServerError(c, "查询用户信息失败")
			}
			c.Abort()
			return
		}

		// 检查是否为管理员（普通管理员或超级管理员）
		if !adminUser.IsAdmin() {
			response.Forbidden(c, "仅限管理员访问")
			c.Abort()
			return
		}

		c.Next()
	}
}

// RequireSuperAdmin 要求超级管理员权限
func RequireSuperAdmin(db *gorm.DB) gin.HandlerFunc {
	return func(c *gin.Context) {
		// 获取用户ID
		userIDStr := c.GetString("user_id")
		if userIDStr == "" {
			response.Unauthorized(c, "用户未登录")
			c.Abort()
			return
		}

		userID, err := strconv.ParseUint(userIDStr, 10, 64)
		if err != nil {
			response.Unauthorized(c, "用户ID无效")
			c.Abort()
			return
		}

		// 查询用户类型
		var adminUser models.AdminUser
		if err := db.First(&adminUser, userID).Error; err != nil {
			if err == gorm.ErrRecordNotFound {
				response.Unauthorized(c, "用户不存在")
			} else {
				response.InternalServerError(c, "查询用户信息失败")
			}
			c.Abort()
			return
		}

		// 检查是否为超级管理员
		if !adminUser.IsSuperAdmin() {
			response.Forbidden(c, "仅限超级管理员访问")
			c.Abort()
			return
		}

		c.Next()
	}
}
