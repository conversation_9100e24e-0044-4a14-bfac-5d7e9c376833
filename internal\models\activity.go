package models

import (
	"encoding/json"
	"time"
)

// Activity 活动模型
type Activity struct {
	BaseModel
	Name        string       `json:"name" gorm:"type:varchar(200);not null;comment:活动名称"`
	Description string       `json:"description" gorm:"type:text;comment:活动描述"`
	Type        ActivityType `json:"type" gorm:"type:tinyint;not null;comment:活动类型 1:每日任务 2:周任务 3:月任务 4:特殊活动 5:商家活动"`
	Images      string       `json:"images" gorm:"type:text;comment:活动图片(JSON数组)"`
	Points      int          `json:"points" gorm:"type:int;default:0;comment:奖励积分"`
	MaxTimes    int          `json:"max_times" gorm:"type:int;default:1;comment:最大参与次数"`
	StartTime   time.Time    `json:"start_time" gorm:"not null;comment:开始时间"`
	EndTime     time.Time    `json:"end_time" gorm:"not null;comment:结束时间"`
	Rules       string       `json:"rules" gorm:"type:text;comment:活动规则"`
	MerchantID  *uint64      `json:"merchant_id" gorm:"index;comment:商家ID(商家活动)"`
	Status      Status       `json:"status" gorm:"type:tinyint;default:1;comment:状态 0:禁用 1:启用"`

	// 关联关系（不使用外键约束）
	Merchant       *Merchant               `json:"merchant,omitempty" gorm:"-"`
	Participations []ActivityParticipation `json:"participations,omitempty" gorm:"-"`
}

// TableName 指定表名
func (Activity) TableName() string {
	return "activities"
}

// IsActive 检查活动是否有效
func (a *Activity) IsActive() bool {
	now := time.Now()
	return a.Status == StatusActive &&
		now.After(a.StartTime) &&
		now.Before(a.EndTime)
}

// ActivityParticipation 活动参与记录模型
type ActivityParticipation struct {
	BaseModel
	ActivityID uint64 `json:"activity_id" gorm:"not null;index;comment:活动ID"`
	UserID     uint64 `json:"user_id" gorm:"not null;index;comment:用户ID"`
	Points     int    `json:"points" gorm:"type:int;default:0;comment:获得积分"`
	Times      int    `json:"times" gorm:"type:int;default:1;comment:参与次数"`

	// 关联关系（不使用外键约束）
	Activity Activity `json:"activity" gorm:"-"`
	User     User     `json:"user" gorm:"-"`
}

// TableName 指定表名
func (ActivityParticipation) TableName() string {
	return "activity_participations"
}

// MarshalJSON 自定义JSON序列化，统一时间格式
func (a Activity) MarshalJSON() ([]byte, error) {
	type Alias Activity

	// 创建一个匿名结构体，重新定义时间字段
	return json.Marshal(&struct {
		*Alias
		CreatedAt string `json:"created_at"`
		UpdatedAt string `json:"updated_at"`
		StartTime string `json:"start_time"`
		EndTime   string `json:"end_time"`
	}{
		Alias:     (*Alias)(&a),
		CreatedAt: formatStandardTime(&a.CreatedAt),
		UpdatedAt: formatStandardTime(&a.UpdatedAt),
		StartTime: formatStandardTime(&a.StartTime),
		EndTime:   formatStandardTime(&a.EndTime),
	})
}
