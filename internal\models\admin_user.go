package models

import (
	"encoding/json"
	"time"
)

// AdminUser 管理端用户模型（商家用户和管理员用户）
type AdminUser struct {
	BaseModel
	Phone        string     `json:"phone" gorm:"type:varchar(20);uniqueIndex;comment:手机号"`
	OpenID       string     `json:"-" gorm:"type:varchar(100);comment:微信OpenID"`
	UnionID      string     `json:"-" gorm:"type:varchar(100);comment:微信UnionID"`
	Nickname     string     `json:"nickname" gorm:"type:varchar(50);comment:昵称"`
	Avatar       string     `json:"avatar" gorm:"type:varchar(500);comment:头像"`
	Gender       int        `json:"gender" gorm:"type:tinyint;default:0;comment:性别 0:未知 1:男 2:女"`
	Birthday     *time.Time `json:"birthday" gorm:"comment:生日"`
	UserType     UserType   `json:"user_type" gorm:"type:tinyint;not null;comment:用户类型 1:普通管理员 2:商家用户 3:超级管理员"`
	Status       Status     `json:"status" gorm:"type:tinyint;default:1;comment:状态 0:禁用 1:启用"`
	RegisterDate time.Time  `json:"register_date" gorm:"type:timestamp;not null;default:CURRENT_TIMESTAMP;comment:注册日期"`
	LastLoginAt  *time.Time `json:"last_login_at" gorm:"comment:最后登录时间"`

	// 商家相关字段（仅商家用户使用）
	MerchantID *uint64 `json:"merchant_id" gorm:"index;comment:关联的商家ID"`

	// 关联关系（不使用外键约束）
	Roles    []Role    `json:"roles,omitempty" gorm:"-"`
	Merchant *Merchant `json:"merchant,omitempty" gorm:"-"`
}

// TableName 指定表名
func (AdminUser) TableName() string {
	return "admin_users"
}

// IsActive 检查用户是否激活
func (u *AdminUser) IsActive() bool {
	return u.Status == StatusActive
}

// IsAdmin 检查是否为管理员（包括普通管理员和超级管理员）
func (u *AdminUser) IsAdmin() bool {
	return u.UserType == UserTypeAdmin || u.UserType == UserTypeSuperAdmin
}

// IsSuperAdmin 检查是否为超级管理员
func (u *AdminUser) IsSuperAdmin() bool {
	return u.UserType == UserTypeSuperAdmin
}

// IsMerchant 检查是否为商家用户
func (u *AdminUser) IsMerchant() bool {
	return u.UserType == UserTypeMerchant
}

// AdminUserRole 管理端用户角色关联模型
type AdminUserRole struct {
	BaseModel
	AdminUserID uint64 `json:"admin_user_id" gorm:"not null;index;comment:管理端用户ID"`
	RoleID      uint64 `json:"role_id" gorm:"not null;index;comment:角色ID"`

	// 关联关系（不使用外键约束）
	AdminUser AdminUser `json:"admin_user" gorm:"-"`
	Role      Role      `json:"role" gorm:"-"`
}

// TableName 指定表名
func (AdminUserRole) TableName() string {
	return "admin_user_roles"
}

// MarshalJSON 自定义JSON序列化，统一时间格式
func (u AdminUser) MarshalJSON() ([]byte, error) {
	type Alias AdminUser

	// 创建一个匿名结构体，重新定义时间字段
	return json.Marshal(&struct {
		*Alias
		CreatedAt    string `json:"created_at"`
		UpdatedAt    string `json:"updated_at"`
		Birthday     string `json:"birthday"`
		RegisterDate string `json:"register_date"`
		LastLoginAt  string `json:"last_login_at"`
	}{
		Alias:        (*Alias)(&u),
		CreatedAt:    formatStandardTime(&u.CreatedAt),
		UpdatedAt:    formatStandardTime(&u.UpdatedAt),
		Birthday:     formatDate(u.Birthday),
		RegisterDate: formatStandardTime(&u.RegisterDate),
		LastLoginAt:  formatStandardTime(u.LastLoginAt),
	})
}
