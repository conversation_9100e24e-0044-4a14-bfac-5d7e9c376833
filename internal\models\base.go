package models

import (
	"time"

	"gorm.io/gorm"
)

// BaseModel 基础模型
type BaseModel struct {
	ID        uint64         `json:"id" gorm:"primaryKey;autoIncrement"`
	CreatedAt time.Time      `json:"created_at"`
	UpdatedAt time.Time      `json:"updated_at"`
	DeletedAt gorm.DeletedAt `json:"-" gorm:"index"`
}

// Status 通用状态枚举
type Status int

const (
	StatusInactive Status = 0 // 未激活/禁用
	StatusActive   Status = 1 // 激活/启用
)

func (s Status) String() string {
	switch s {
	case StatusInactive:
		return "inactive"
	case StatusActive:
		return "active"
	default:
		return "unknown"
	}
}

// ApprovalStatus 审核状态枚举
type ApprovalStatus int

const (
	ApprovalStatusPending  ApprovalStatus = 0 // 待审核
	ApprovalStatusApproved ApprovalStatus = 1 // 已通过
	ApprovalStatusRejected ApprovalStatus = 2 // 已拒绝
)

func (s ApprovalStatus) String() string {
	switch s {
	case ApprovalStatusPending:
		return "pending"
	case ApprovalStatusApproved:
		return "approved"
	case ApprovalStatusRejected:
		return "rejected"
	default:
		return "unknown"
	}
}

// OrderStatus 订单状态枚举
type OrderStatus int

const (
	OrderStatusPending    OrderStatus = 0 // 待处理
	OrderStatusProcessing OrderStatus = 1 // 处理中
	OrderStatusCompleted  OrderStatus = 2 // 已完成
	OrderStatusCancelled  OrderStatus = 3 // 已取消
	OrderStatusRefunded   OrderStatus = 4 // 已退款
)

func (s OrderStatus) String() string {
	switch s {
	case OrderStatusPending:
		return "pending"
	case OrderStatusProcessing:
		return "processing"
	case OrderStatusCompleted:
		return "completed"
	case OrderStatusCancelled:
		return "cancelled"
	case OrderStatusRefunded:
		return "refunded"
	default:
		return "unknown"
	}
}

// UserType 用户类型枚举
type UserType int

const (
	UserTypeCustomer   UserType = 0 // 普通用户（用户端小程序）
	UserTypeAdmin      UserType = 1 // 普通管理员（通过角色分配权限）
	UserTypeMerchant   UserType = 2 // 商家用户（固定）
	UserTypeSuperAdmin UserType = 3 // 超级管理员（固定）
)

func (t UserType) String() string {
	switch t {
	case UserTypeCustomer:
		return "customer"
	case UserTypeAdmin:
		return "admin"
	case UserTypeMerchant:
		return "merchant"
	case UserTypeSuperAdmin:
		return "super_admin"
	default:
		return "unknown"
	}
}

// ScoreType 分值类型枚举
type ScoreType int

const (
	ScoreTypeAdd    ScoreType = 1 // 加分
	ScoreTypeDeduct ScoreType = 2 // 扣分
)

func (t ScoreType) String() string {
	switch t {
	case ScoreTypeAdd:
		return "add"
	case ScoreTypeDeduct:
		return "deduct"
	default:
		return "unknown"
	}
}

// PointsType 积分类型枚举
type PointsType int

const (
	PointsTypeEarn  PointsType = 1 // 获得积分
	PointsTypeSpend PointsType = 2 // 消费积分
)

func (t PointsType) String() string {
	switch t {
	case PointsTypeEarn:
		return "earn"
	case PointsTypeSpend:
		return "spend"
	default:
		return "unknown"
	}
}

// ActivityType 活动类型枚举
type ActivityType int

const (
	ActivityTypeDaily    ActivityType = 1 // 每日任务
	ActivityTypeWeekly   ActivityType = 2 // 周任务
	ActivityTypeMonthly  ActivityType = 3 // 月任务
	ActivityTypeSpecial  ActivityType = 4 // 特殊活动
	ActivityTypeMerchant ActivityType = 5 // 商家活动
)

func (t ActivityType) String() string {
	switch t {
	case ActivityTypeDaily:
		return "daily"
	case ActivityTypeWeekly:
		return "weekly"
	case ActivityTypeMonthly:
		return "monthly"
	case ActivityTypeSpecial:
		return "special"
	case ActivityTypeMerchant:
		return "merchant"
	default:
		return "unknown"
	}
}

// ProductType 商品类型枚举
type ProductType int

const (
	ProductTypeMerchant ProductType = 1 // 商家商品
	ProductTypeMall     ProductType = 2 // 积分商城商品
)

func (t ProductType) String() string {
	switch t {
	case ProductTypeMerchant:
		return "merchant"
	case ProductTypeMall:
		return "mall"
	default:
		return "unknown"
	}
}

// ExchangeType 兑换方式枚举
type ExchangeType int

const (
	ExchangeTypePhone  ExchangeType = 1 // 电话兑换
	ExchangeTypeOnsite ExchangeType = 2 // 现场兑换
)

func (t ExchangeType) String() string {
	switch t {
	case ExchangeTypePhone:
		return "phone"
	case ExchangeTypeOnsite:
		return "onsite"
	default:
		return "unknown"
	}
}
