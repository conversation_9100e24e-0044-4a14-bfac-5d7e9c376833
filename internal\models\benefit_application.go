package models

import (
	"encoding/json"
	"time"
)

// BenefitApplication 权益申请记录
type BenefitApplication struct {
	ID             uint64         `json:"id" gorm:"primaryKey;autoIncrement"`
	CreatedAt      time.Time      `json:"created_at"`
	UpdatedAt      time.Time      `json:"updated_at"`
	DeletedAt      *time.Time     `json:"deleted_at,omitempty" gorm:"index"`
	MerchantID     uint64         `json:"merchant_id" gorm:"not null;comment:商家ID"`
	BenefitType    BenefitType    `json:"benefit_type" gorm:"type:tinyint;not null;comment:权益类型"`
	RequiredScore  int            `json:"required_score" gorm:"not null;comment:所需分值"`
	CurrentScore   int            `json:"current_score" gorm:"not null;comment:申请时商家分值"`
	ApplicantID    uint64         `json:"applicant_id" gorm:"not null;comment:申请人ID"`
	ApprovalStatus ApprovalStatus `json:"approval_status" gorm:"type:tinyint;default:0;comment:审核状态"`
	ApproverID     *uint64        `json:"approver_id,omitempty" gorm:"comment:审核人ID"`
	ApprovalNote   string         `json:"approval_note" gorm:"type:text;comment:审核意见"`
	ApprovedAt     *time.Time     `json:"approved_at,omitempty" gorm:"comment:审核时间"`
	Reason         string         `json:"reason" gorm:"type:varchar(500);comment:申请理由"`

	// 延迟闭店权益相关字段
	LatestCloseTime *time.Time `json:"latest_close_time,omitempty" gorm:"comment:最晚闭店时间"`

	// 临时字段：处理数据库中可能存在的type字段
	Type *int `json:"-" gorm:"column:type;default:null"`

	// 关联数据
	Merchant  Merchant   `json:"merchant,omitempty" gorm:"-"`
	Applicant AdminUser  `json:"applicant,omitempty" gorm:"-"`
	Approver  *AdminUser `json:"approver,omitempty" gorm:"-"`
}

// BenefitType 权益类型
type BenefitType int

const (
	BenefitTypeDelayedClosing BenefitType = 1 // 延迟闭店权益
	// 可以扩展其他权益类型
)

// BenefitTypeNames 权益类型名称映射
var BenefitTypeNames = map[BenefitType]string{
	BenefitTypeDelayedClosing: "延迟闭店权益",
}

// GetBenefitTypeName 获取权益类型名称
func (bt BenefitType) GetName() string {
	if name, exists := BenefitTypeNames[bt]; exists {
		return name
	}
	return "未知权益"
}

// TableName 指定表名
func (BenefitApplication) TableName() string {
	return "benefit_applications"
}

// IsEditable 是否可编辑（被拒绝的申请可以重新编辑）
func (ba *BenefitApplication) IsEditable() bool {
	return ba.ApprovalStatus == ApprovalStatusRejected
}

// CanApprove 是否可以审核
func (ba *BenefitApplication) CanApprove() bool {
	return ba.ApprovalStatus == ApprovalStatusPending
}

// CanReapply 是否可以重新申请（已拒绝的可以重新申请）
func (ba *BenefitApplication) CanReapply() bool {
	return ba.ApprovalStatus == ApprovalStatusRejected
}

// IsReadOnly 是否只读（已通过或审核中的不可编辑）
func (ba *BenefitApplication) IsReadOnly() bool {
	return ba.ApprovalStatus == ApprovalStatusApproved || ba.ApprovalStatus == ApprovalStatusPending
}

// IsFinalState 是否为终态（已通过的审核是终态，不可修改）
func (ba *BenefitApplication) IsFinalState() bool {
	return ba.ApprovalStatus == ApprovalStatusApproved
}

// MarshalJSON 自定义JSON序列化，统一时间格式
func (ba BenefitApplication) MarshalJSON() ([]byte, error) {
	type Alias BenefitApplication

	// 创建一个匿名结构体，重新定义时间字段
	return json.Marshal(&struct {
		*Alias
		CreatedAt       string `json:"created_at"`
		UpdatedAt       string `json:"updated_at"`
		DeletedAt       string `json:"deleted_at"`
		ApprovedAt      string `json:"approved_at"`
		LatestCloseTime string `json:"latest_close_time"`
	}{
		Alias:           (*Alias)(&ba),
		CreatedAt:       formatStandardTime(&ba.CreatedAt),
		UpdatedAt:       formatStandardTime(&ba.UpdatedAt),
		DeletedAt:       formatStandardTime(ba.DeletedAt),
		ApprovedAt:      formatStandardTime(ba.ApprovedAt),
		LatestCloseTime: formatStandardTime(ba.LatestCloseTime),
	})
}
