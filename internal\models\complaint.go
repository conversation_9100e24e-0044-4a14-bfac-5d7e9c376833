package models

import (
	"encoding/json"
	"time"
)

// Complaint 客诉模型
type Complaint struct {
	BaseModel
	MerchantID        uint64         `json:"merchant_id" gorm:"not null;index;comment:商家ID"`
	Title             string         `json:"title" gorm:"type:varchar(30);not null;comment:客诉标题"`
	Content           string         `json:"content" gorm:"type:varchar(300);comment:客诉内容"`
	SupplementaryInfo string         `json:"supplementary_info" gorm:"type:text;comment:补充信息(图片/视频URL,JSON数组)"`
	LatestSolveTime   *time.Time     `json:"latest_solve_time" gorm:"comment:最晚解决时间"`
	SubmitterID       uint64         `json:"submitter_id" gorm:"not null;index;comment:提交人ID"`
	ApprovalStatus    ApprovalStatus `json:"approval_status" gorm:"type:tinyint;default:0;comment:审核状态 0:待审核 1:已通过 2:已拒绝"`
	ApproverID        *uint64        `json:"approver_id" gorm:"comment:审核人ID"`
	ApprovalNote      string         `json:"approval_note" gorm:"type:varchar(500);comment:审核意见"`
	ApprovedAt        *time.Time     `json:"approved_at" gorm:"comment:审核时间"`
	DeductScore       int            `json:"deduct_score" gorm:"type:int;default:0;comment:实际扣减分值"`

	// 关联关系（不使用外键约束）
	Merchant  Merchant   `json:"merchant" gorm:"foreignKey:MerchantID;references:ID"`
	Submitter AdminUser  `json:"submitter" gorm:"foreignKey:SubmitterID;references:ID"`
	Approver  *AdminUser `json:"approver,omitempty" gorm:"foreignKey:ApproverID;references:ID"`
}

// TableName 指定表名
func (Complaint) TableName() string {
	return "complaints"
}

// ComplaintCategory 客诉分类模型
type ComplaintCategory struct {
	BaseModel
	Name        string `json:"name" gorm:"type:varchar(50);not null;uniqueIndex;comment:分类名称"`
	Description string `json:"description" gorm:"type:varchar(200);comment:分类描述"`
	DeductScore int    `json:"deduct_score" gorm:"type:int;default:0;comment:默认扣减分值"`
	Sort        int    `json:"sort" gorm:"type:int;default:0;comment:排序"`
	Status      Status `json:"status" gorm:"type:tinyint;default:1;comment:状态 0:禁用 1:启用"`
}

// TableName 指定表名
func (ComplaintCategory) TableName() string {
	return "complaint_categories"
}

// OpinionCategory 意见分类模型
type OpinionCategory struct {
	BaseModel
	Name        string `json:"name" gorm:"type:varchar(50);not null;uniqueIndex;comment:分类名称"`
	Description string `json:"description" gorm:"type:varchar(200);comment:分类描述"`
	Sort        int    `json:"sort" gorm:"type:int;default:0;comment:排序"`
	Status      Status `json:"status" gorm:"type:tinyint;default:1;comment:状态 0:禁用 1:启用"`
}

// TableName 指定表名
func (OpinionCategory) TableName() string {
	return "opinion_categories"
}

// MarshalJSON 自定义JSON序列化，统一时间格式
func (c Complaint) MarshalJSON() ([]byte, error) {
	type Alias Complaint

	// 创建一个匿名结构体，重新定义时间字段
	return json.Marshal(&struct {
		*Alias
		CreatedAt       string `json:"created_at"`
		UpdatedAt       string `json:"updated_at"`
		LatestSolveTime string `json:"latest_solve_time"`
		ApprovedAt      string `json:"approved_at"`
	}{
		Alias:           (*Alias)(&c),
		CreatedAt:       formatStandardTime(&c.CreatedAt),
		UpdatedAt:       formatStandardTime(&c.UpdatedAt),
		LatestSolveTime: formatStandardTime(c.LatestSolveTime),
		ApprovedAt:      formatStandardTime(c.ApprovedAt),
	})
}
