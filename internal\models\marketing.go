package models

import (
	"encoding/json"
	"time"
)

// MarketingActivityType 营销活动类型枚举
type MarketingActivityType int

const (
	MarketingActivityTypeDaily   MarketingActivityType = 1 // 日任务
	MarketingActivityTypeWeekly  MarketingActivityType = 2 // 周任务
	MarketingActivityTypeMonthly MarketingActivityType = 3 // 月任务
)

func (t MarketingActivityType) String() string {
	switch t {
	case MarketingActivityTypeDaily:
		return "日任务"
	case MarketingActivityTypeWeekly:
		return "周任务"
	case MarketingActivityTypeMonthly:
		return "月任务"
	default:
		return "未知"
	}
}

// MarketingCategory 营销活动分类枚举
type MarketingCategory int

const (
	MarketingCategoryActivity MarketingCategory = 1 // 活动
	MarketingCategoryGame     MarketingCategory = 2 // 游戏
	MarketingCategoryShare    MarketingCategory = 3 // 分享
)

func (c MarketingCategory) String() string {
	switch c {
	case MarketingCategoryActivity:
		return "活动"
	case MarketingCategoryGame:
		return "游戏"
	case MarketingCategoryShare:
		return "分享"
	default:
		return "未知"
	}
}

// MarketingActivityStatus 营销活动状态枚举
type MarketingActivityStatus int

const (
	MarketingActivityStatusDraft    MarketingActivityStatus = 0 // 草稿
	MarketingActivityStatusActive   MarketingActivityStatus = 1 // 已上架
	MarketingActivityStatusInactive MarketingActivityStatus = 2 // 已下架
	MarketingActivityStatusExpired  MarketingActivityStatus = 3 // 已过期
)

func (s MarketingActivityStatus) String() string {
	switch s {
	case MarketingActivityStatusDraft:
		return "草稿"
	case MarketingActivityStatusActive:
		return "已上架"
	case MarketingActivityStatusInactive:
		return "已下架"
	case MarketingActivityStatusExpired:
		return "已过期"
	default:
		return "未知"
	}
}

// MarketingActivity 营销活动模型
type MarketingActivity struct {
	BaseModel
	Category       MarketingCategory       `json:"category" gorm:"type:tinyint;not null;comment:活动分类 1:活动 2:游戏 3:分享"`
	Type           MarketingActivityType   `json:"type" gorm:"type:tinyint;not null;comment:活动类型 1:日任务 2:周任务 3:月任务"`
	Name           string                  `json:"name" gorm:"type:varchar(80);comment:活动名称"`
	VideoName      string                  `json:"video_name" gorm:"type:varchar(400);comment:视频名称(分享类型专用)"`
	Image          string                  `json:"image" gorm:"type:varchar(500);comment:活动图片URL"`
	VideoURL       string                  `json:"video_url" gorm:"type:varchar(500);comment:视频URL(分享类型专用)"`
	Description    string                  `json:"description" gorm:"type:varchar(400);comment:活动介绍"`
	DailyLimit     int                     `json:"daily_limit" gorm:"type:int;not null;default:1;comment:每日参与次数限制"`
	StartTime      *time.Time              `json:"start_time" gorm:"not null;comment:活动开始时间"`
	EndTime        *time.Time              `json:"end_time" gorm:"not null;comment:活动结束时间"`
	RewardPoints   int                     `json:"reward_points" gorm:"type:int;not null;comment:奖励积分"`
	RequiredPoints int                     `json:"required_points" gorm:"type:int;default:0;comment:参与所需积分(活动和游戏类型)"`
	Status         MarketingActivityStatus `json:"status" gorm:"type:tinyint;default:1;comment:活动状态 0:草稿 1:已上架 2:已下架 3:已过期"`
	CreatorID      uint64                  `json:"creator_id" gorm:"not null;comment:创建人ID"`
	Sort           int                     `json:"sort" gorm:"type:int;default:0;comment:排序"`

	// 关联关系（不使用外键约束）
	Creator          *AdminUser                     `json:"creator,omitempty" gorm:"foreignKey:CreatorID;references:ID"`
	ParticipantCount int64                          `json:"participant_count" gorm:"-"` // 参与人数统计
	Participants     []MarketingActivityParticipant `json:"participants,omitempty" gorm:"foreignKey:ActivityID;references:ID"`
}

// TableName 指定表名
func (MarketingActivity) TableName() string {
	return "marketing_activities"
}

// IsExpired 检查活动是否已过期
func (m *MarketingActivity) IsExpired() bool {
	if m.EndTime == nil {
		return false
	}
	return time.Now().After(*m.EndTime)
}

// CanEdit 检查活动是否可以编辑
func (m *MarketingActivity) CanEdit() bool {
	// 已上架的活动不能编辑
	if m.Status == MarketingActivityStatusActive {
		return false
	}
	// 已过期的活动不能编辑
	if m.IsExpired() {
		return false
	}
	return true
}

// CanDelete 检查活动是否可以删除
func (m *MarketingActivity) CanDelete() bool {
	// 只有草稿、已下架、已过期的活动可以删除
	return m.Status == MarketingActivityStatusDraft ||
		m.Status == MarketingActivityStatusInactive ||
		m.Status == MarketingActivityStatusExpired
}

// GetStatusText 获取活动状态文本
func (m *MarketingActivity) GetStatusText() string {
	if m.IsExpired() && m.Status == MarketingActivityStatusActive {
		return "已过期"
	}
	return m.Status.String()
}

// MarketingActivityParticipant 营销活动参与记录模型
type MarketingActivityParticipant struct {
	BaseModel
	ActivityID        uint64     `json:"activity_id" gorm:"not null;index;comment:活动ID"`
	UserID            uint64     `json:"user_id" gorm:"not null;index;comment:用户ID"`
	ParticipateDate   string     `json:"participate_date" gorm:"type:varchar(10);not null;index;comment:参与日期(YYYY-MM-DD)"`
	ParticipateCount  int        `json:"participate_count" gorm:"type:int;default:1;comment:当日参与次数"`
	TotalRewardPoints int        `json:"total_reward_points" gorm:"type:int;default:0;comment:当日获得总积分"`
	LastParticipateAt *time.Time `json:"last_participate_at" gorm:"comment:最后参与时间"`

	// 关联关系（不使用外键约束）
	Activity *MarketingActivity `json:"activity,omitempty" gorm:"foreignKey:ActivityID;references:ID"`
	User     *User              `json:"user,omitempty" gorm:"foreignKey:UserID;references:ID"`
}

// TableName 指定表名
func (MarketingActivityParticipant) TableName() string {
	return "marketing_activity_participants"
}

// CanParticipateToday 检查用户今天是否还能参与活动
func (p *MarketingActivityParticipant) CanParticipateToday(dailyLimit int) bool {
	today := time.Now().Format("2006-01-02")
	if p.ParticipateDate != today {
		return true // 不是今天的记录，可以参与
	}
	return p.ParticipateCount < dailyLimit
}

// ParticipationStatus 参与状态枚举
type ParticipationStatus int

const (
	ParticipationStatusRegistered ParticipationStatus = 1 // 已报名
	ParticipationStatusCompleted  ParticipationStatus = 2 // 已完成
	ParticipationStatusExpired    ParticipationStatus = 3 // 已过期
)

func (s ParticipationStatus) String() string {
	switch s {
	case ParticipationStatusRegistered:
		return "已报名"
	case ParticipationStatusCompleted:
		return "已完成"
	case ParticipationStatusExpired:
		return "已过期"
	default:
		return "未知"
	}
}

// TaskRegistration 任务报名记录模型
type TaskRegistration struct {
	BaseModel
	ActivityID uint64              `json:"activity_id" gorm:"not null;index;comment:活动ID"`
	UserID     uint64              `json:"user_id" gorm:"not null;index;comment:用户ID"`
	QRCode     string              `json:"qr_code" gorm:"type:text;comment:二维码内容"`
	QRCodeURL  string              `json:"qr_code_url" gorm:"type:varchar(500);comment:二维码图片URL"`
	VerifyCode string              `json:"verify_code" gorm:"type:varchar(100);comment:核销验证码"`
	ExpiresAt  *time.Time          `json:"expires_at" gorm:"comment:二维码过期时间"`
	Status     ParticipationStatus `json:"status" gorm:"type:tinyint;default:1;comment:参与状态 1:已报名 2:已完成 3:已过期"`
	VerifiedAt *time.Time          `json:"verified_at" gorm:"comment:核销时间"`
	VerifiedBy *uint64             `json:"verified_by" gorm:"comment:核销人员ID"`

	// 关联关系（不使用外键约束）
	Activity *MarketingActivity `json:"activity,omitempty" gorm:"foreignKey:ActivityID;references:ID"`
	User     *User              `json:"user,omitempty" gorm:"foreignKey:UserID;references:ID"`
	Verifier *AdminUser         `json:"verifier,omitempty" gorm:"foreignKey:VerifiedBy;references:ID"`
}

// TableName 指定表名
func (TaskRegistration) TableName() string {
	return "task_registrations"
}

// MarshalJSON 自定义JSON序列化，统一时间格式
func (m MarketingActivity) MarshalJSON() ([]byte, error) {
	type Alias MarketingActivity

	// 创建一个匿名结构体，重新定义时间字段
	return json.Marshal(&struct {
		*Alias
		CreatedAt string `json:"created_at"`
		UpdatedAt string `json:"updated_at"`
		StartTime string `json:"start_time"`
		EndTime   string `json:"end_time"`
	}{
		Alias:     (*Alias)(&m),
		CreatedAt: formatStandardTime(&m.CreatedAt),
		UpdatedAt: formatStandardTime(&m.UpdatedAt),
		StartTime: formatActivityTime(m.StartTime),
		EndTime:   formatActivityTime(m.EndTime),
	})
}
