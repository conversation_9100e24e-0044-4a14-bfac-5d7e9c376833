package models

import (
	"encoding/json"
	"fmt"
	"time"

	"gorm.io/gorm"
)

// MemberRuleType 会员规则类型枚举
type MemberRuleType int

const (
	MemberRuleTypeRegister MemberRuleType = 1 // 注册成功（新用户首次登录）
	MemberRuleTypeConsume  MemberRuleType = 2 // 消费成功（核销成功）
)

func (t MemberRuleType) String() string {
	switch t {
	case MemberRuleTypeRegister:
		return "注册成功"
	case MemberRuleTypeConsume:
		return "消费成功"
	default:
		return "未知"
	}
}

// MembershipRule 会员规则模型
type MembershipRule struct {
	ID             uint64         `json:"id" gorm:"primaryKey;autoIncrement"`
	CreatedAt      time.Time      `json:"created_at"`
	UpdatedAt      time.Time      `json:"updated_at"`
	DeletedAt      gorm.DeletedAt `json:"-" gorm:"index;uniqueIndex:idx_member_rules_type_active"`
	Type           MemberRuleType `json:"type" gorm:"type:tinyint;not null;uniqueIndex:idx_member_rules_type_active;comment:规则类型 1:注册成功 2:消费成功"`
	Name           string         `json:"name" gorm:"type:varchar(40);not null;comment:规则名称"`
	EnablePoints   bool           `json:"enable_points" gorm:"type:tinyint;default:0;comment:是否启用积分奖励"`
	Points         int            `json:"points" gorm:"type:int;default:0;comment:获取的积分数 1-100"`
	EnableDiscount bool           `json:"enable_discount" gorm:"type:tinyint;default:0;comment:是否启用折扣"`
	Discount       float64        `json:"discount" gorm:"type:decimal(3,1);default:10.0;comment:享受的线下折扣 1.0-9.9"`
	CreatorID      uint64         `json:"creator_id" gorm:"not null;comment:创建人ID"`
	Status         Status         `json:"status" gorm:"type:tinyint;default:1;comment:状态 0:禁用 1:启用"`

	// 关联关系（不使用外键约束）
	Creator *AdminUser `json:"creator,omitempty" gorm:"foreignKey:CreatorID;references:ID"`
}

// TableName 指定表名
func (MembershipRule) TableName() string {
	return "member_rules"
}

// GetBenefits 获取权益描述
func (m *MembershipRule) GetBenefits() []string {
	var benefits []string

	if m.EnablePoints && m.Points > 0 {
		benefits = append(benefits, fmt.Sprintf("获得%d积分", m.Points))
	}

	if m.EnableDiscount && m.Discount < 10.0 {
		benefits = append(benefits, fmt.Sprintf("享受%.1f折优惠", m.Discount))
	}

	return benefits
}

// MarshalJSON 自定义JSON序列化，统一时间格式
func (mr MembershipRule) MarshalJSON() ([]byte, error) {
	type Alias MembershipRule

	// 创建一个匿名结构体，重新定义时间字段
	return json.Marshal(&struct {
		*Alias
		CreatedAt string `json:"created_at"`
		UpdatedAt string `json:"updated_at"`
	}{
		Alias:     (*Alias)(&mr),
		CreatedAt: formatStandardTime(&mr.CreatedAt),
		UpdatedAt: formatStandardTime(&mr.UpdatedAt),
	})
}
