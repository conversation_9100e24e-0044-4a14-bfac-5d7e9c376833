package models

import (
	"encoding/json"
	"time"
)

// Merchant 商家模型
type Merchant struct {
	BaseModel
	Name        string  `json:"name" gorm:"type:varchar(100);not null;comment:商家名称"`
	Phone       string  `json:"phone" gorm:"type:varchar(20);not null;comment:联系电话"`
	LoginPhone  string  `json:"login_phone" gorm:"type:varchar(20);uniqueIndex;comment:登录管理端手机号"`
	Contact     string  `json:"contact" gorm:"type:varchar(50);comment:联系人"`
	FloorID     uint64  `json:"floor_id" gorm:"not null;index;comment:楼层ID"`
	Position    string  `json:"position" gorm:"type:varchar(100);comment:具体位置"`
	Cover       string  `json:"cover" gorm:"type:varchar(500);comment:封面图片"`
	Description string  `json:"description" gorm:"type:text;comment:商家描述"`
	Area        float64 `json:"area" gorm:"type:decimal(10,2);not null;comment:商家面积"`
	Score       int     `json:"score" gorm:"type:int;default:0;comment:商家分值"`
	Level       int     `json:"level" gorm:"type:int;default:1;comment:商家等级"`
	Sales       float64 `json:"sales" gorm:"type:decimal(15,2);default:0;comment:当月销售额"`
	Status      Status  `json:"status" gorm:"type:tinyint;default:1;comment:状态 0:禁用 1:启用"`

	// 权益字段
	DelayedClosingBenefit bool `json:"delayed_closing_benefit" gorm:"type:tinyint;default:0;comment:延迟闭店权益"`

	// 关联关系（不使用外键约束）
	Floor        Floor         `json:"floor" gorm:"foreignKey:FloorID;references:ID"`
	Products     []Product     `json:"products,omitempty" gorm:"-"`
	ScoreRecords []ScoreRecord `json:"score_records,omitempty" gorm:"-"`
}

// TableName 指定表名
func (Merchant) TableName() string {
	return "merchants"
}

// IsActive 检查商家是否激活
func (m *Merchant) IsActive() bool {
	return m.Status == StatusActive
}

// Floor 楼层模型
type Floor struct {
	BaseModel
	Name        string `json:"name" gorm:"type:varchar(50);not null;comment:楼层名称"`
	Sort        int    `json:"sort" gorm:"type:int;not null;uniqueIndex;comment:排序序号"`
	MapImage    string `json:"map_image" gorm:"type:varchar(500);comment:楼层平面图"`
	Description string `json:"description" gorm:"type:varchar(200);comment:楼层描述"`
	Status      Status `json:"status" gorm:"type:tinyint;default:1;comment:状态 0:禁用 1:启用"`

	// 关联关系（不使用外键约束）
	Merchants []Merchant `json:"merchants,omitempty" gorm:"-"`
}

// TableName 指定表名
func (Floor) TableName() string {
	return "floors"
}

// ScoreRecord 商家分值记录模型
type ScoreRecord struct {
	BaseModel
	MerchantID     uint64         `json:"merchant_id" gorm:"not null;index;comment:商家ID"`
	ScoreItemID    uint64         `json:"score_item_id" gorm:"not null;index;comment:分值项目ID"`
	Type           ScoreType      `json:"type" gorm:"type:tinyint;not null;comment:类型 1:加分 2:扣分"`
	Score          int            `json:"score" gorm:"type:int;not null;comment:分值"`
	Reason         string         `json:"reason" gorm:"type:varchar(200);not null;comment:原因"`
	Images         string         `json:"images" gorm:"type:text;comment:相关图片/视频(JSON数组)"`
	OperatorID     uint64         `json:"operator_id" gorm:"not null;index;comment:操作人ID"`
	ApprovalStatus ApprovalStatus `json:"approval_status" gorm:"type:tinyint;default:0;comment:审核状态 0:待审核 1:已通过 2:已拒绝"`
	ApproverID     *uint64        `json:"approver_id" gorm:"comment:审核人ID"`
	ApprovalNote   string         `json:"approval_note" gorm:"type:varchar(500);comment:审核意见"`
	ApprovedAt     *time.Time     `json:"approved_at" gorm:"comment:审核时间"`

	// 关联关系（不使用外键约束）
	Merchant  Merchant  `json:"merchant" gorm:"-"`
	ScoreItem ScoreItem `json:"score_item" gorm:"-"`
	Operator  *User     `json:"operator,omitempty" gorm:"-"`
	Approver  *User     `json:"approver,omitempty" gorm:"-"`
}

// TableName 指定表名
func (ScoreRecord) TableName() string {
	return "score_records"
}

// LevelRule 等级规则模型
type LevelRule struct {
	BaseModel
	Level       int    `json:"level" gorm:"type:int;not null;uniqueIndex;comment:等级"`
	Name        string `json:"name" gorm:"type:varchar(50);not null;comment:等级名称"`
	MinScore    int    `json:"min_score" gorm:"type:int;not null;comment:最低分值"`
	MaxScore    int    `json:"max_score" gorm:"type:int;comment:最高分值"`
	Benefits    string `json:"benefits" gorm:"type:text;comment:等级福利(JSON数组)"`
	Color       string `json:"color" gorm:"type:varchar(20);comment:等级颜色"`
	Icon        string `json:"icon" gorm:"type:varchar(200);comment:等级图标"`
	Description string `json:"description" gorm:"type:varchar(200);comment:等级描述"`
	Status      Status `json:"status" gorm:"type:tinyint;default:1;comment:状态 0:禁用 1:启用"`
}

// TableName 指定表名
func (LevelRule) TableName() string {
	return "level_rules"
}

// ScoreRule 分值规则模型
type ScoreRule struct {
	BaseModel
	DefaultScore int `json:"default_score" gorm:"type:int;not null;default:10;comment:默认分值"`
}

// TableName 指定表名
func (ScoreRule) TableName() string {
	return "score_rules"
}

// ScoreItem 增减分项目模型
type ScoreItem struct {
	BaseModel
	Type        ScoreType `json:"type" gorm:"type:tinyint;not null;comment:类型 1:加分 2:扣分"`
	Name        string    `json:"name" gorm:"type:varchar(50);not null;comment:项目名称"`
	Score       int       `json:"score" gorm:"type:int;not null;comment:分值"`
	Description string    `json:"description" gorm:"type:varchar(200);comment:项目描述"`
}

// TableName 指定表名
func (ScoreItem) TableName() string {
	return "score_items"
}

// LevelBenefit 等级权益模型（全局配置，只允许一条记录）
type LevelBenefit struct {
	BaseModel
	RequiredScore  int  `json:"required_score" gorm:"type:int;not null;comment:所需分值"`
	DelayedClosing bool `json:"delayed_closing" gorm:"type:tinyint;default:0;comment:延迟闭店权益"`
}

// TableName 指定表名
func (LevelBenefit) TableName() string {
	return "level_benefits"
}

// MarshalJSON 自定义JSON序列化，统一时间格式
func (m Merchant) MarshalJSON() ([]byte, error) {
	type Alias Merchant

	// 创建一个匿名结构体，重新定义时间字段
	return json.Marshal(&struct {
		*Alias
		CreatedAt string `json:"created_at"`
		UpdatedAt string `json:"updated_at"`
	}{
		Alias:     (*Alias)(&m),
		CreatedAt: formatStandardTime(&m.CreatedAt),
		UpdatedAt: formatStandardTime(&m.UpdatedAt),
	})
}

// MarshalJSON 自定义JSON序列化，统一时间格式
func (f Floor) MarshalJSON() ([]byte, error) {
	type Alias Floor

	// 创建一个匿名结构体，重新定义时间字段
	return json.Marshal(&struct {
		*Alias
		CreatedAt string `json:"created_at"`
		UpdatedAt string `json:"updated_at"`
	}{
		Alias:     (*Alias)(&f),
		CreatedAt: formatStandardTime(&f.CreatedAt),
		UpdatedAt: formatStandardTime(&f.UpdatedAt),
	})
}

// MarshalJSON 自定义JSON序列化，统一时间格式
func (lr LevelRule) MarshalJSON() ([]byte, error) {
	type Alias LevelRule

	// 创建一个匿名结构体，重新定义时间字段
	return json.Marshal(&struct {
		*Alias
		CreatedAt string `json:"created_at"`
		UpdatedAt string `json:"updated_at"`
	}{
		Alias:     (*Alias)(&lr),
		CreatedAt: formatStandardTime(&lr.CreatedAt),
		UpdatedAt: formatStandardTime(&lr.UpdatedAt),
	})
}

// MarshalJSON 自定义JSON序列化，统一时间格式
func (lb LevelBenefit) MarshalJSON() ([]byte, error) {
	type Alias LevelBenefit

	// 创建一个匿名结构体，重新定义时间字段
	return json.Marshal(&struct {
		*Alias
		CreatedAt string `json:"created_at"`
		UpdatedAt string `json:"updated_at"`
	}{
		Alias:     (*Alias)(&lb),
		CreatedAt: formatStandardTime(&lb.CreatedAt),
		UpdatedAt: formatStandardTime(&lb.UpdatedAt),
	})
}

// MarshalJSON 自定义JSON序列化，统一时间格式
func (s ScoreRecord) MarshalJSON() ([]byte, error) {
	type Alias ScoreRecord

	// 创建一个匿名结构体，重新定义时间字段
	return json.Marshal(&struct {
		*Alias
		CreatedAt  string `json:"created_at"`
		UpdatedAt  string `json:"updated_at"`
		ApprovedAt string `json:"approved_at"`
	}{
		Alias:      (*Alias)(&s),
		CreatedAt:  formatStandardTime(&s.CreatedAt),
		UpdatedAt:  formatStandardTime(&s.UpdatedAt),
		ApprovedAt: formatStandardTime(s.ApprovedAt),
	})
}

// MarshalJSON 自定义JSON序列化，统一时间格式
func (si ScoreItem) MarshalJSON() ([]byte, error) {
	type Alias ScoreItem

	// 创建一个匿名结构体，重新定义时间字段
	return json.Marshal(&struct {
		*Alias
		CreatedAt string `json:"created_at"`
		UpdatedAt string `json:"updated_at"`
	}{
		Alias:     (*Alias)(&si),
		CreatedAt: formatStandardTime(&si.CreatedAt),
		UpdatedAt: formatStandardTime(&si.UpdatedAt),
	})
}

// MarshalJSON 自定义JSON序列化，统一时间格式
func (sr ScoreRule) MarshalJSON() ([]byte, error) {
	type Alias ScoreRule

	// 创建一个匿名结构体，重新定义时间字段
	return json.Marshal(&struct {
		*Alias
		CreatedAt string `json:"created_at"`
		UpdatedAt string `json:"updated_at"`
	}{
		Alias:     (*Alias)(&sr),
		CreatedAt: formatStandardTime(&sr.CreatedAt),
		UpdatedAt: formatStandardTime(&sr.UpdatedAt),
	})
}
