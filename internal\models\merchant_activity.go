package models

import (
	"encoding/json"
	"time"
)

// MerchantActivityStatus 商家活动状态枚举
type MerchantActivityStatus int

const (
	MerchantActivityStatusDraft    MerchantActivityStatus = 0 // 草稿
	MerchantActivityStatusActive   MerchantActivityStatus = 1 // 已上架
	MerchantActivityStatusInactive MerchantActivityStatus = 2 // 已下架
	MerchantActivityStatusExpired  MerchantActivityStatus = 3 // 已过期
)

func (s MerchantActivityStatus) String() string {
	switch s {
	case MerchantActivityStatusDraft:
		return "草稿"
	case MerchantActivityStatusActive:
		return "已上架"
	case MerchantActivityStatusInactive:
		return "已下架"
	case MerchantActivityStatusExpired:
		return "已过期"
	default:
		return "未知"
	}
}

// MerchantActivity 商家活动模型
type MerchantActivity struct {
	BaseModel
	Name              string                 `json:"name" gorm:"type:varchar(80);not null;comment:活动名称"`
	Description       string                 `json:"description" gorm:"type:varchar(400);comment:活动介绍"`
	Image             string                 `json:"image" gorm:"type:varchar(500);comment:活动图片URL"`
	LevelLimit        int                    `json:"level_limit" gorm:"type:int;not null;comment:等级限制(前多少名可报名)"`
	StartTime         *time.Time             `json:"start_time" gorm:"not null;comment:活动开始时间"`
	EndTime           *time.Time             `json:"end_time" gorm:"not null;comment:活动结束时间"`
	ContributionScore int                    `json:"contribution_score" gorm:"type:int;not null;comment:贡献度(活动结束后给商家加分)"`
	Status            MerchantActivityStatus `json:"status" gorm:"type:tinyint;default:1;comment:活动状态 0:草稿 1:已上架 2:已下架 3:已过期"`
	CreatorID         uint64                 `json:"creator_id" gorm:"not null;comment:创建人ID"`
	Sort              int                    `json:"sort" gorm:"type:int;default:0;comment:排序"`

	// 关联关系（不使用外键约束）
	Creator      *AdminUser                    `json:"creator,omitempty" gorm:"foreignKey:CreatorID;references:ID"`
	Participants []MerchantActivityParticipant `json:"participants,omitempty" gorm:"foreignKey:ActivityID;references:ID"`

	// 计算字段
	ParticipantCount int64 `json:"participant_count" gorm:"-"` // 参与人数
}

// TableName 指定表名
func (MerchantActivity) TableName() string {
	return "merchant_activities"
}

// IsExpired 检查活动是否已过期
func (a *MerchantActivity) IsExpired() bool {
	if a.EndTime == nil {
		return false
	}
	return time.Now().After(*a.EndTime)
}

// CanEdit 检查活动是否可以编辑
func (a *MerchantActivity) CanEdit() bool {
	// 已上架的活动不能编辑，需要先下架
	// 已过期的活动不能编辑
	return a.Status != MerchantActivityStatusActive && a.Status != MerchantActivityStatusExpired
}

// CanDelete 检查活动是否可以删除
func (a *MerchantActivity) CanDelete() bool {
	// 只有草稿、已下架、已过期的活动可以删除
	return a.Status == MerchantActivityStatusDraft ||
		a.Status == MerchantActivityStatusInactive ||
		a.Status == MerchantActivityStatusExpired
}

// CanRestart 检查活动是否可以重新发起
func (a *MerchantActivity) CanRestart() bool {
	// 已结束、已下架的活动可以重新发起
	return a.Status == MerchantActivityStatusExpired || a.Status == MerchantActivityStatusInactive
}

// IsActive 检查活动是否正在进行中
func (a *MerchantActivity) IsActive() bool {
	if a.Status != MerchantActivityStatusActive {
		return false
	}
	now := time.Now()
	return a.StartTime != nil && a.EndTime != nil &&
		now.After(*a.StartTime) && now.Before(*a.EndTime)
}

// CanParticipate 检查商家是否可以参与活动
func (a *MerchantActivity) CanParticipate(merchantLevel int) bool {
	// 活动必须是已上架状态
	if a.Status != MerchantActivityStatusActive {
		return false
	}

	// 活动必须在报名时间内
	now := time.Now()
	if a.StartTime == nil || now.Before(*a.StartTime) {
		return false
	}
	if a.EndTime != nil && now.After(*a.EndTime) {
		return false
	}

	// 商家等级必须在限制范围内
	return merchantLevel <= a.LevelLimit
}

// MerchantActivityParticipant 商家活动参与记录模型
type MerchantActivityParticipant struct {
	BaseModel
	ActivityID   uint64    `json:"activity_id" gorm:"not null;index;comment:活动ID"`
	MerchantID   uint64    `json:"merchant_id" gorm:"not null;index;comment:商家ID"`
	RegisterTime time.Time `json:"register_time" gorm:"not null;comment:报名时间"`
	Status       int       `json:"status" gorm:"type:tinyint;default:1;comment:参与状态 1:已报名 2:已完成 3:已取消"`

	// 关联关系（不使用外键约束）
	Activity *MerchantActivity `json:"activity,omitempty" gorm:"foreignKey:ActivityID;references:ID"`
	Merchant *Merchant         `json:"merchant,omitempty" gorm:"foreignKey:MerchantID;references:ID"`
}

// TableName 指定表名
func (MerchantActivityParticipant) TableName() string {
	return "merchant_activity_participants"
}

// IsCompleted 检查是否已完成
func (p *MerchantActivityParticipant) IsCompleted() bool {
	return p.Status == 2
}

// IsCancelled 检查是否已取消
func (p *MerchantActivityParticipant) IsCancelled() bool {
	return p.Status == 3
}

// MarshalJSON 自定义JSON序列化，统一时间格式
func (m MerchantActivity) MarshalJSON() ([]byte, error) {
	type Alias MerchantActivity

	// 创建一个匿名结构体，重新定义时间字段
	return json.Marshal(&struct {
		*Alias
		CreatedAt string `json:"created_at"`
		UpdatedAt string `json:"updated_at"`
		StartTime string `json:"start_time"`
		EndTime   string `json:"end_time"`
	}{
		Alias:     (*Alias)(&m),
		CreatedAt: formatStandardTime(&m.CreatedAt),
		UpdatedAt: formatStandardTime(&m.UpdatedAt),
		StartTime: formatActivityTime(m.StartTime),
		EndTime:   formatActivityTime(m.EndTime),
	})
}
