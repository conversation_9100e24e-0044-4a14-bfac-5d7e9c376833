package models

import (
	"encoding/json"
	"time"
)

// Opinion 意见建议模型
type Opinion struct {
	BaseModel
	UserID      *uint64    `json:"user_id" gorm:"index;comment:用户ID"`
	Content     string     `json:"content" gorm:"type:text;not null;comment:意见内容"`
	Contact     string     `json:"contact" gorm:"type:varchar(100);comment:联系方式"`
	Status      Status     `json:"status" gorm:"type:tinyint;default:0;comment:处理状态 0:未读 1:已读 "`
	ProcessorID *uint64    `json:"processor_id" gorm:"comment:已读人ID"`
	ProcessedAt *time.Time `json:"processed_at" gorm:"comment:已读时间"`
}

// TableName 指定表名
func (Opinion) TableName() string {
	return "opinions"
}

// MarshalJSON 自定义JSON序列化，统一时间格式
func (o Opinion) MarshalJSON() ([]byte, error) {
	type Alias Opinion

	// 创建一个匿名结构体，重新定义时间字段
	return json.Marshal(&struct {
		*Alias
		CreatedAt   string `json:"created_at"`
		UpdatedAt   string `json:"updated_at"`
		ProcessedAt string `json:"processed_at"`
	}{
		Alias:       (*Alias)(&o),
		CreatedAt:   formatStandardTime(&o.CreatedAt),
		UpdatedAt:   formatStandardTime(&o.UpdatedAt),
		ProcessedAt: formatStandardTime(o.ProcessedAt),
	})
}
