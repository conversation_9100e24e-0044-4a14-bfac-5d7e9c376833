package models

import (
	"encoding/json"
	"time"
)

// PointsMallItemType 积分商城商品类型枚举
type PointsMallItemType int

const (
	PointsMallItemTypeProduct PointsMallItemType = 1 // 兑换商品
	PointsMallItemTypeAvatar  PointsMallItemType = 2 // VIP形象
)

func (t PointsMallItemType) String() string {
	switch t {
	case PointsMallItemTypeProduct:
		return "兑换商品"
	case PointsMallItemTypeAvatar:
		return "VIP形象"
	default:
		return "未知"
	}
}

// PointsMallItemStatus 积分商城商品状态枚举
type PointsMallItemStatus int

const (
	PointsMallItemStatusDraft    PointsMallItemStatus = 0 // 草稿
	PointsMallItemStatusActive   PointsMallItemStatus = 1 // 已上架
	PointsMallItemStatusInactive PointsMallItemStatus = 2 // 已下架
	PointsMallItemStatusExpired  PointsMallItemStatus = 3 // 已过期
)

func (s PointsMallItemStatus) String() string {
	switch s {
	case PointsMallItemStatusDraft:
		return "草稿"
	case PointsMallItemStatusActive:
		return "已上架"
	case PointsMallItemStatusInactive:
		return "已下架"
	case PointsMallItemStatusExpired:
		return "已过期"
	default:
		return "未知"
	}
}

// PointsMallItem 积分商城商品模型
type PointsMallItem struct {
	BaseModel
	Type        PointsMallItemType   `json:"type" gorm:"type:tinyint;not null;comment:商品类型 1:兑换商品 2:VIP形象"`
	Name        string               `json:"name" gorm:"type:varchar(120);not null;comment:商品名称"`
	Images      string               `json:"images" gorm:"type:text;comment:商品图片(JSON数组)"`
	Description string               `json:"description" gorm:"type:varchar(1200);comment:商品说明"`
	StartTime   *time.Time           `json:"start_time" gorm:"comment:有效期开始时间"`
	EndTime     *time.Time           `json:"end_time" gorm:"comment:有效期结束时间"`
	Stock       int                  `json:"stock" gorm:"type:int;default:1;comment:库存数量"`
	Points      int                  `json:"points" gorm:"type:int;not null;comment:兑换积分"`
	Status      PointsMallItemStatus `json:"status" gorm:"type:tinyint;default:1;comment:商品状态 0:草稿 1:已上架 2:已下架 3:已过期"`
	CreatorID   uint64               `json:"creator_id" gorm:"not null;comment:创建人ID"`
	Sort        int                  `json:"sort" gorm:"type:int;default:0;comment:排序"`

	// VIP形象专用字段
	BackgroundImage string `json:"background_image" gorm:"type:varchar(500);comment:背景图片(VIP形象专用)"`
	AlbumImage      string `json:"album_image" gorm:"type:varchar(500);comment:相册图片(VIP形象专用)"`
	DiscountRate    int    `json:"discount_rate" gorm:"type:int;comment:折扣比例(VIP形象专用)"`
	EnableLottery   bool   `json:"enable_lottery" gorm:"type:tinyint;default:0;comment:是否参与抽卡(VIP形象专用)"`
	LotteryRate     int    `json:"lottery_rate" gorm:"type:int;comment:中奖概率1-100(VIP形象专用)"`
	IsEnabled       bool   `json:"is_enabled" gorm:"type:tinyint;default:1;comment:是否开启(VIP形象专用)"`

	// 关联关系（不使用外键约束）
	Creator *AdminUser `json:"creator,omitempty" gorm:"foreignKey:CreatorID;references:ID"`

	// 计算字段
	ExchangeCount int64 `json:"exchange_count" gorm:"-"` // 兑换次数
	RemainStock   int   `json:"remain_stock" gorm:"-"`   // 剩余库存
}

// LotteryHistory 抽奖历史模型
type LotteryHistory struct {
	BaseModel
	UserID      uint64 `json:"user_id" gorm:"not null;index;comment:用户ID"`
	ItemID      uint64 `json:"item_id" gorm:"not null;index;comment:商品ID"`
	IsWin       bool   `json:"is_win" gorm:"type:tinyint;not null;comment:是否中奖"`
	Probability int    `json:"probability" gorm:"type:int;not null;comment:中奖概率"`

	// 关联关系（不使用外键约束）
	User User           `json:"user,omitempty" gorm:"foreignKey:UserID;references:ID"`
	Item PointsMallItem `json:"item,omitempty" gorm:"foreignKey:ItemID;references:ID"`
}

// TableName 指定表名
func (PointsMallItem) TableName() string {
	return "points_mall_items"
}

// TableName 指定表名
func (LotteryHistory) TableName() string {
	return "lottery_histories"
}

// IsExpired 检查商品是否已过期
func (p *PointsMallItem) IsExpired() bool {
	if p.EndTime == nil {
		return false
	}
	return time.Now().After(*p.EndTime)
}

// CanEdit 检查商品是否可以编辑
func (p *PointsMallItem) CanEdit() bool {
	// 已上架的商品不能编辑，需要先下架
	// 已下架和已过期的商品可以编辑
	return p.Status == PointsMallItemStatusDraft ||
		p.Status == PointsMallItemStatusInactive ||
		p.Status == PointsMallItemStatusExpired
}

// CanDelete 检查商品是否可以删除
func (p *PointsMallItem) CanDelete() bool {
	// 只有草稿、已下架、已过期的商品可以删除
	return p.Status == PointsMallItemStatusDraft ||
		p.Status == PointsMallItemStatusInactive ||
		p.Status == PointsMallItemStatusExpired
}

// IsActive 检查商品是否正在上架中
func (p *PointsMallItem) IsActive() bool {
	if p.Status != PointsMallItemStatusActive {
		return false
	}
	now := time.Now()
	if p.StartTime != nil && now.Before(*p.StartTime) {
		return false
	}
	if p.EndTime != nil && now.After(*p.EndTime) {
		return false
	}
	return true
}

// CanExchange 检查是否可以兑换
func (p *PointsMallItem) CanExchange() bool {
	// 必须是已上架且在有效期内
	if !p.IsActive() {
		return false
	}
	// VIP形象需要检查是否开启
	if p.Type == PointsMallItemTypeAvatar && !p.IsEnabled {
		return false
	}
	// 兑换商品需要检查库存
	if p.Type == PointsMallItemTypeProduct && p.Stock <= 0 {
		return false
	}
	return true
}

// 兑换记录状态常量
const (
	ExchangeStatusRedeemed    = 1 // 已兑换
	ExchangeStatusInUse       = 2 // 正在使用
	ExchangeStatusExpired     = 3 // 已过期
	ExchangeStatusDeactivated = 4 // 已停用
)

// PointsMallExchange 积分商城兑换记录模型
type PointsMallExchange struct {
	BaseModel
	ItemID       uint64     `json:"item_id" gorm:"not null;index;comment:商品ID"`
	UserID       uint64     `json:"user_id" gorm:"not null;index;comment:用户ID"`
	Points       int        `json:"points" gorm:"type:int;not null;comment:消耗积分"`
	ExchangeTime time.Time  `json:"exchange_time" gorm:"not null;comment:兑换时间"`
	Status       int        `json:"status" gorm:"type:tinyint;default:1;comment:兑换状态 1:已兑换 2:正在使用 3:已过期 4:已停用"`
	AppliedAt    *time.Time `json:"applied_at" gorm:"comment:应用时间"`

	// 抽卡相关字段（VIP形象专用）
	IsLottery  bool `json:"is_lottery" gorm:"type:tinyint;default:0;comment:是否抽卡获得"`
	LotteryWin bool `json:"lottery_win" gorm:"type:tinyint;default:0;comment:是否中奖"`

	// 关联关系（不使用外键约束）
	Item *PointsMallItem `json:"item,omitempty" gorm:"foreignKey:ItemID;references:ID"`
	User *User           `json:"user,omitempty" gorm:"foreignKey:UserID;references:ID"`
}

// TableName 指定表名
func (PointsMallExchange) TableName() string {
	return "points_mall_exchanges"
}

// IsExpired 检查兑换记录是否已过期
func (e *PointsMallExchange) IsExpired() bool {
	return e.Status == 3
}

// IsUsed 检查兑换记录是否已使用
func (e *PointsMallExchange) IsUsed() bool {
	return e.Status == 2
}

// MarshalJSON 自定义JSON序列化，统一时间格式
func (p PointsMallItem) MarshalJSON() ([]byte, error) {
	type Alias PointsMallItem

	// 创建一个匿名结构体，重新定义时间字段
	return json.Marshal(&struct {
		*Alias
		CreatedAt string `json:"created_at"`
		UpdatedAt string `json:"updated_at"`
		StartTime string `json:"start_time"`
		EndTime   string `json:"end_time"`
	}{
		Alias:     (*Alias)(&p),
		CreatedAt: formatStandardTime(&p.CreatedAt),
		UpdatedAt: formatStandardTime(&p.UpdatedAt),
		StartTime: formatActivityTime(p.StartTime),
		EndTime:   formatActivityTime(p.EndTime),
	})
}
