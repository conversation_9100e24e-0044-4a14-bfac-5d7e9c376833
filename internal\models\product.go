package models

import (
	"encoding/json"
	"time"
)

// Product 商品模型
type Product struct {
	BaseModel
	Name        string      `json:"name" gorm:"type:varchar(200);not null;comment:商品名称"`
	Description string      `json:"description" gorm:"type:text;comment:商品描述"`
	Images      string      `json:"images" gorm:"type:text;comment:商品图片(JSON数组)"`
	Points      int         `json:"points" gorm:"type:int;default:10;comment:积分兑换值"`
	DailyLimit  int         `json:"daily_limit" gorm:"type:int;default:1;comment:每日活动数量限制"`
	Type        ProductType `json:"type" gorm:"type:tinyint;not null;comment:商品类型 1:商家商品 2:积分商城商品"`
	MerchantID  *uint64     `json:"merchant_id" gorm:"index;comment:商家ID"`
	Category    string      `json:"category" gorm:"type:varchar(50);comment:商品分类"`
	Tags        string      `json:"tags" gorm:"type:varchar(200);comment:商品标签"`

	// 审核相关字段
	ApprovalStatus ApprovalStatus `json:"approval_status" gorm:"type:tinyint;default:0;comment:审核状态 0:待审核 1:已通过 2:已拒绝"`
	ApproverID     *uint64        `json:"approver_id" gorm:"comment:审核人ID"`
	ApprovalNote   string         `json:"approval_note" gorm:"type:varchar(500);comment:审核意见"`
	ApprovedAt     *time.Time     `json:"approved_at" gorm:"comment:审核时间"`
	SubmittedAt    *time.Time     `json:"submitted_at" gorm:"comment:提交审核时间"`

	// 有效期相关字段
	ValidFrom *time.Time `json:"valid_from" gorm:"comment:有效期开始时间"`
	ValidTo   *time.Time `json:"valid_to" gorm:"comment:有效期结束时间"`

	Sort   int    `json:"sort" gorm:"type:int;default:0;comment:排序"`
	Status Status `json:"status" gorm:"type:tinyint;default:1;comment:状态 0:下架 1:上架"`

	// 关联关系（不使用外键约束）
	Merchant *Merchant  `json:"merchant,omitempty" gorm:"foreignKey:MerchantID;references:ID"`
	Approver *AdminUser `json:"approver,omitempty" gorm:"foreignKey:ApproverID;references:ID"`
	Orders   []Order    `json:"orders,omitempty" gorm:"foreignKey:ProductID;references:ID"`
}

// TableName 指定表名
func (Product) TableName() string {
	return "products"
}

// IsAvailable 检查商品是否可用
func (p *Product) IsAvailable() bool {
	return p.Status == StatusActive &&
		p.ApprovalStatus == ApprovalStatusApproved &&
		p.DailyLimit > 0
}

// IsExpired 检查商品是否已过期
func (p *Product) IsExpired() bool {
	if p.ValidTo == nil {
		return false
	}
	return time.Now().After(*p.ValidTo)
}

// CanEdit 检查商品是否可以编辑
func (p *Product) CanEdit() bool {
	// 审核中的不能编辑
	if p.ApprovalStatus == ApprovalStatusPending {
		return false
	}
	// 已拒绝的可以编辑
	if p.ApprovalStatus == ApprovalStatusRejected {
		return true
	}
	// 已通过但已过期的可以编辑
	if p.ApprovalStatus == ApprovalStatusApproved && p.IsExpired() {
		return true
	}
	// 其他情况不能编辑
	return false
}

// CanDelete 检查商品是否可以删除
func (p *Product) CanDelete() bool {
	// 只有下架状态的商品可以删除
	return p.Status == StatusInactive
}

// CanToggleStatus 检查商品是否可以切换上下架状态
func (p *Product) CanToggleStatus() bool {
	// 只有审核通过的商品可以切换状态
	return p.ApprovalStatus == ApprovalStatusApproved
}

// GetStatusText 获取商品状态文本
func (p *Product) GetStatusText() string {
	if p.ApprovalStatus == ApprovalStatusPending {
		return "审核中"
	}
	if p.ApprovalStatus == ApprovalStatusRejected {
		return "已驳回"
	}
	if p.ApprovalStatus == ApprovalStatusApproved {
		if p.IsExpired() {
			return "已过期"
		}
		if p.Status == StatusActive {
			return "已上架"
		}
		return "已下架"
	}
	return "未知状态"
}

// Order 订单模型
type Order struct {
	BaseModel
	OrderNo     string      `json:"order_no" gorm:"type:varchar(50);not null;uniqueIndex;comment:订单号"`
	RequestID   string      `json:"request_id" gorm:"type:varchar(100);index;comment:抢购请求ID"`
	UserID      uint64      `json:"user_id" gorm:"not null;index;comment:用户ID"`
	ProductID   uint64      `json:"product_id" gorm:"not null;index;comment:商品ID"`
	MerchantID  *uint64     `json:"merchant_id" gorm:"index;comment:商家ID"`
	OrderType   ProductType `json:"order_type" gorm:"type:tinyint;default:1;comment:订单类型 1:商家商品 2:积分商城"`
	Quantity    int         `json:"quantity" gorm:"type:int;not null;comment:数量"`
	Price       float64     `json:"price" gorm:"type:decimal(10,2);not null;comment:单价"`
	TotalPrice  float64     `json:"total_price" gorm:"type:decimal(10,2);not null;comment:总价"`
	Points      int         `json:"points" gorm:"type:int;default:0;comment:使用积分"`
	Status      OrderStatus `json:"status" gorm:"type:tinyint;default:0;comment:订单状态 0:待处理 1:处理中 2:已完成 3:已取消 4:已退款"`
	Remark      string      `json:"remark" gorm:"type:varchar(500);comment:备注"`
	CompletedAt *time.Time  `json:"completed_at" gorm:"comment:完成时间"`
	CancelledAt *time.Time  `json:"cancelled_at" gorm:"comment:取消时间"`

	// 核销相关字段
	QRCode       string     `json:"qr_code" gorm:"type:varchar(500);comment:核销二维码"`
	QRCodeURL    string     `json:"qr_code_url" gorm:"type:varchar(500);comment:二维码图片URL"`
	VerifyCode   string     `json:"verify_code" gorm:"type:varchar(36);comment:核销码"`
	VerifiedAt   *time.Time `json:"verified_at" gorm:"comment:核销时间"`
	VerifierID   *uint64    `json:"verifier_id" gorm:"comment:核销人ID"`
	VerifyRemark string     `json:"verify_remark" gorm:"type:varchar(500);comment:核销备注"`

	// 关联关系（不使用外键约束）
	User     User      `json:"user" gorm:"foreignKey:UserID;references:ID"`
	Product  Product   `json:"product" gorm:"foreignKey:ProductID;references:ID"`
	Merchant *Merchant `json:"merchant,omitempty" gorm:"foreignKey:MerchantID;references:ID"`
	Verifier *User     `json:"verifier,omitempty" gorm:"foreignKey:VerifierID;references:ID"`
}

// TableName 指定表名
func (Order) TableName() string {
	return "orders"
}

// IsVerified 检查订单是否已核销
func (o *Order) IsVerified() bool {
	return o.VerifiedAt != nil
}

// CanVerify 检查订单是否可以核销
func (o *Order) CanVerify() bool {
	return o.Status == OrderStatusPending && !o.IsVerified()
}

// GetStatusText 获取订单状态文本
func (o *Order) GetStatusText() string {
	if o.IsVerified() {
		return "已核销"
	}
	switch o.Status {
	case OrderStatusPending:
		return "待核销"
	case OrderStatusProcessing:
		return "处理中"
	case OrderStatusCompleted:
		return "已完成"
	case OrderStatusCancelled:
		return "已取消"
	case OrderStatusRefunded:
		return "已退款"
	default:
		return "未知状态"
	}
}

// PointsRecord 积分记录模型
type PointsRecord struct {
	BaseModel
	UserID         uint64     `json:"user_id" gorm:"not null;index;comment:用户ID"`
	Type           PointsType `json:"type" gorm:"type:tinyint;not null;comment:积分类型 1:获得积分 2:消费积分"`
	Points         int        `json:"points" gorm:"type:int;not null;comment:积分数量"`
	Balance        int        `json:"balance" gorm:"type:int;not null;comment:积分余额"`
	Source         string     `json:"source" gorm:"type:varchar(50);comment:积分来源"`
	SourceID       *uint64    `json:"source_id" gorm:"comment:来源ID"`
	RelatedID      *uint64    `json:"related_id" gorm:"comment:关联ID"`
	RelatedType    string     `json:"related_type" gorm:"type:varchar(50);comment:关联类型"`
	Description    string     `json:"description" gorm:"type:varchar(200);comment:描述"`
	ExpiredAt      *time.Time `json:"expired_at" gorm:"comment:过期时间"`
	VerificationID *uint64    `json:"verification_id" gorm:"index;comment:关联核销记录ID"`

	// 关联关系（不使用外键约束）
	User         User          `json:"user" gorm:"-"`
	Verification *VerifyRecord `json:"verification,omitempty" gorm:"foreignKey:VerificationID;references:ID"`
}

// TableName 指定表名
func (PointsRecord) TableName() string {
	return "points_records"
}

// MarshalJSON 自定义JSON序列化，统一时间格式
func (p Product) MarshalJSON() ([]byte, error) {
	type Alias Product

	// 创建一个匿名结构体，重新定义时间字段
	return json.Marshal(&struct {
		*Alias
		CreatedAt   string `json:"created_at"`
		UpdatedAt   string `json:"updated_at"`
		ApprovedAt  string `json:"approved_at"`
		SubmittedAt string `json:"submitted_at"`
		ValidFrom   string `json:"valid_from"`
		ValidTo     string `json:"valid_to"`
	}{
		Alias:       (*Alias)(&p),
		CreatedAt:   formatStandardTime(&p.CreatedAt),
		UpdatedAt:   formatStandardTime(&p.UpdatedAt),
		ApprovedAt:  formatStandardTime(p.ApprovedAt),
		SubmittedAt: formatStandardTime(p.SubmittedAt),
		ValidFrom:   formatStandardTime(p.ValidFrom),
		ValidTo:     formatStandardTime(p.ValidTo),
	})
}

// MarshalJSON 自定义JSON序列化，统一时间格式
func (pr PointsRecord) MarshalJSON() ([]byte, error) {
	type Alias PointsRecord

	// 创建一个匿名结构体，重新定义时间字段
	return json.Marshal(&struct {
		*Alias
		CreatedAt string `json:"created_at"`
		UpdatedAt string `json:"updated_at"`
		ExpiredAt string `json:"expired_at"`
	}{
		Alias:     (*Alias)(&pr),
		CreatedAt: formatStandardTime(&pr.CreatedAt),
		UpdatedAt: formatStandardTime(&pr.UpdatedAt),
		ExpiredAt: formatStandardTime(pr.ExpiredAt),
	})
}
