package models

// Program 节目模型
type Program struct {
	BaseModel
	ProgramName         string `json:"program_name" gorm:"type:varchar(200);not null;comment:节目名称"`
	ProgramIntroduction string `json:"program_introduction" gorm:"type:text;comment:节目介绍"`
	ShowTimeHour        string `json:"show_time_hour" gorm:"type:text;comment:演出时间段(JSON数组)"`
	ShowTimeStart       string `json:"show_time_start" gorm:"type:varchar(10);not null;comment:演出开始时间"`
	ShowTimeEnd         string `json:"show_time_end" gorm:"type:varchar(10);not null;comment:演出结束时间"`
	FloorId             int    `json:"floor_id" gorm:"type:int(10);comment:演出楼层id"`
	Url                 string `json:"url" gorm:"type:text;comment:节目图片(JSON数组)"`
}

// TableName 指定表名
func (Program) TableName() string {
	return "programs"
}
