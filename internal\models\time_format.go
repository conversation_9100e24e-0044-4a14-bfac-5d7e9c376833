package models

import "time"

// formatActivityTime 格式化活动时间为 "2025-07-10 12:00" 格式
func formatActivityTime(t *time.Time) string {
	if t == nil || t.<PERSON>() {
		return ""
	}
	return t.Format("2006-01-02 15:04")
}

// formatStandardTime 格式化标准时间为 "2025-07-10 12:00:00" 格式
func formatStandardTime(t *time.Time) string {
	if t == nil || t.<PERSON>() {
		return ""
	}
	return t.Format("2006-01-02 15:04:05")
}

// formatDate 格式化日期为 "2025-07-10" 格式
func formatDate(t *time.Time) string {
	if t == nil || t.<PERSON>() {
		return ""
	}
	return t.Format("2006-01-02")
}

// formatTimeValue 格式化时间值
func formatTimeValue(t time.Time) string {
	if t.<PERSON>() {
		return ""
	}
	return t.Format("2006-01-02 15:04:05")
}
