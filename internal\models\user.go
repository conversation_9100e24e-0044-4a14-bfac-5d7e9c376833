package models

import (
	"encoding/json"
	"time"
)

// User 普通用户模型（仅用户端小程序）
type User struct {
	BaseModel
	Phone        string     `json:"phone" gorm:"type:varchar(20);uniqueIndex;comment:手机号"`
	OpenID       string     `json:"-" gorm:"type:varchar(100);comment:微信OpenID"`
	UnionID      string     `json:"-" gorm:"type:varchar(100);comment:微信UnionID"`
	Nickname     string     `json:"nickname" gorm:"type:varchar(50);comment:昵称"`
	Avatar       string     `json:"avatar" gorm:"type:varchar(500);comment:头像"`
	Gender       int        `json:"gender" gorm:"type:tinyint;default:0;comment:性别 0:未知 1:男 2:女"`
	Birthday     *time.Time `json:"birthday" gorm:"comment:生日"`
	UserType     UserType   `json:"user_type" gorm:"type:tinyint;not null;default:1;comment:用户类型 1:普通用户"`
	Status       Status     `json:"status" gorm:"type:tinyint;default:1;comment:状态 0:禁用 1:启用"`
	Points       int        `json:"points" gorm:"type:int;default:0;comment:积分余额"`
	PersonalQR   string     `json:"personal_qr" gorm:"type:varchar(500);comment:个人二维码URL"`
	RegisterDate time.Time  `json:"register_date" gorm:"type:timestamp;not null;default:CURRENT_TIMESTAMP;comment:注册日期"`
	LastLoginAt  *time.Time `json:"last_login_at" gorm:"comment:最后登录时间"`

	// 关联关系（不使用外键约束）
	Roles []Role `json:"roles,omitempty" gorm:"-"`
}

// TableName 指定表名
func (User) TableName() string {
	return "users"
}

// IsActive 检查用户是否激活
func (u *User) IsActive() bool {
	return u.Status == StatusActive
}

// IsAdmin 检查是否为管理员
func (u *User) IsAdmin() bool {
	return u.UserType == UserTypeAdmin
}

// Role 角色模型
type Role struct {
	BaseModel
	Name        string `json:"name" gorm:"type:varchar(50);not null;uniqueIndex;comment:角色名称"`
	DisplayName string `json:"display_name" gorm:"type:varchar(100);comment:显示名称"`
	Description string `json:"description" gorm:"type:varchar(200);comment:角色描述"`

	// 关联关系（不使用外键约束）
	Permissions []Permission `json:"permissions,omitempty" gorm:"-"`
	Users       []User       `json:"users,omitempty" gorm:"-"`
}

// TableName 指定表名
func (Role) TableName() string {
	return "roles"
}

// Permission 权限模型
type Permission struct {
	BaseModel
	Name        string `json:"name" gorm:"type:varchar(100);not null;uniqueIndex;comment:权限名称"`
	DisplayName string `json:"display_name" gorm:"type:varchar(100);comment:显示名称"`
	Module      string `json:"module" gorm:"type:varchar(50);comment:模块"`
	Action      string `json:"action" gorm:"type:varchar(50);comment:操作"`
	Resource    string `json:"resource" gorm:"type:varchar(50);comment:资源"`
	Description string `json:"description" gorm:"type:varchar(200);comment:权限描述"`

	// 关联关系（不使用外键约束）
	Roles []Role `json:"roles,omitempty" gorm:"-"`
}

// TableName 指定表名
func (Permission) TableName() string {
	return "permissions"
}

// RolePermission 角色权限关联模型
type RolePermission struct {
	BaseModel
	RoleID       uint64 `json:"role_id" gorm:"not null;index;comment:角色ID"`
	PermissionID uint64 `json:"permission_id" gorm:"not null;index;comment:权限ID"`

	// 关联关系（不使用外键约束）
	Role       Role       `json:"role" gorm:"-"`
	Permission Permission `json:"permission" gorm:"-"`
}

// TableName 指定表名
func (RolePermission) TableName() string {
	return "role_permissions"
}

// UserRole 用户角色关联模型
type UserRole struct {
	BaseModel
	UserID uint64 `json:"user_id" gorm:"not null;index;comment:用户ID"`
	RoleID uint64 `json:"role_id" gorm:"not null;index;comment:角色ID"`

	// 关联关系（不使用外键约束）
	User User `json:"user" gorm:"-"`
	Role Role `json:"role" gorm:"-"`
}

// TableName 指定表名
func (UserRole) TableName() string {
	return "user_roles"
}

// OperationLog 操作日志
type OperationLog struct {
	BaseModel
	OperatorID   uint64 `json:"operator_id" gorm:"not null;comment:操作者ID"`
	OperatorName string `json:"operator_name" gorm:"size:100;not null;comment:操作者姓名"`
	Module       string `json:"module" gorm:"size:50;not null;comment:操作模块"`
	Action       string `json:"action" gorm:"size:50;not null;comment:操作动作"`
	Resource     string `json:"resource" gorm:"size:50;not null;comment:操作资源"`
	ResourceID   string `json:"resource_id" gorm:"size:100;comment:资源ID"`
	Description  string `json:"description" gorm:"size:500;not null;comment:操作描述"`
	RequestData  string `json:"request_data" gorm:"type:text;comment:请求数据(JSON)"`
	ResponseData string `json:"response_data" gorm:"type:text;comment:响应数据(JSON)"`
	ClientIP     string `json:"client_ip" gorm:"size:45;comment:客户端IP"`
	UserAgent    string `json:"user_agent" gorm:"size:500;comment:用户代理"`
	Status       int    `json:"status" gorm:"not null;comment:操作状态 0:失败 1:成功"`
	ErrorMessage string `json:"error_message" gorm:"type:text;comment:错误信息"`
}

// TableName 指定表名
func (OperationLog) TableName() string {
	return "operation_logs"
}

// MarshalJSON 自定义JSON序列化，统一时间格式
func (ol OperationLog) MarshalJSON() ([]byte, error) {
	type Alias OperationLog

	// 创建一个匿名结构体，重新定义时间字段
	return json.Marshal(&struct {
		*Alias
		CreatedAt string `json:"created_at"`
		UpdatedAt string `json:"updated_at"`
	}{
		Alias:     (*Alias)(&ol),
		CreatedAt: formatStandardTime(&ol.CreatedAt),
		UpdatedAt: formatStandardTime(&ol.UpdatedAt),
	})
}

// MarshalJSON 自定义JSON序列化，统一时间格式
func (u User) MarshalJSON() ([]byte, error) {
	type Alias User

	// 创建一个匿名结构体，重新定义时间字段
	return json.Marshal(&struct {
		*Alias
		CreatedAt    string `json:"created_at"`
		UpdatedAt    string `json:"updated_at"`
		Birthday     string `json:"birthday"`
		RegisterDate string `json:"register_date"`
		LastLoginAt  string `json:"last_login_at"`
	}{
		Alias:        (*Alias)(&u),
		CreatedAt:    formatStandardTime(&u.CreatedAt),
		UpdatedAt:    formatStandardTime(&u.UpdatedAt),
		Birthday:     formatDate(u.Birthday),
		RegisterDate: formatStandardTime(&u.RegisterDate),
		LastLoginAt:  formatStandardTime(u.LastLoginAt),
	})
}
