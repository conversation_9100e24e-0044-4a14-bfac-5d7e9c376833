package models

import "encoding/json"

// VerifyRecordType 核销记录类型枚举
type VerifyRecordType int

const (
	VerifyRecordTypeMerchant   VerifyRecordType = 1 // 商家核销
	VerifyRecordTypePointsMall VerifyRecordType = 2 // 积分商城核销
	VerifyRecordTypeNPC        VerifyRecordType = 3 // NPC送积分
	VerifyRecordTypeActivity   VerifyRecordType = 4 // 活动核销
)

func (t VerifyRecordType) String() string {
	switch t {
	case VerifyRecordTypeMerchant:
		return "商家核销"
	case VerifyRecordTypePointsMall:
		return "积分商城核销"
	case VerifyRecordTypeNPC:
		return "NPC送积分"
	case VerifyRecordTypeActivity:
		return "活动核销"
	default:
		return "未知"
	}
}

// VerifyRecord 核销记录（统一模型，支持自动迁移）
type VerifyRecord struct {
	BaseModel

	// 基础字段（保持与现有表结构兼容）
	OrderID   uint64 `json:"order_id" gorm:"not null;index;comment:订单ID"`
	UserID    uint64 `json:"user_id" gorm:"not null;index;comment:用户ID"`
	ScannerID uint64 `json:"scanner_id" gorm:"not null;index;comment:扫码人ID"`
	Location  string `json:"location" gorm:"type:varchar(200);comment:核销地点"`
	Remark    string `json:"remark" gorm:"type:varchar(500);comment:备注"`

	// 扩展字段（新增，支持自动迁移）
	VerifyType       VerifyRecordType `json:"verify_type" gorm:"type:tinyint;default:1;index;comment:核销类型 1:商家核销 2:积分商城核销 3:NPC送积分 4:活动核销"`
	Points           int              `json:"points" gorm:"type:int;default:0;comment:涉及积分数量"`
	PointsChangeType PointsType       `json:"points_change_type" gorm:"type:tinyint;default:0;comment:积分变动类型 0:无变动 1:获得积分 2:消费积分"`
	Price            float64          `json:"price" gorm:"type:decimal(10,2);default:0;comment:商品价格"`
	ProductID        *uint64          `json:"product_id" gorm:"index;comment:商品ID"`
	MerchantID       *uint64          `json:"merchant_id" gorm:"index;comment:商家ID"`
	ActivityID       *uint64          `json:"activity_id" gorm:"index;comment:活动ID"`
	VerifyCode       string           `json:"verify_code" gorm:"type:varchar(100);comment:核销码"`
	QRCodeData       string           `json:"qr_code_data" gorm:"type:text;comment:二维码原始数据"`
	VerifyMethod     string           `json:"verify_method" gorm:"type:varchar(50);default:'qr_code';comment:核销方式 qr_code:二维码 manual:手动"`

	// 关联关系（不使用外键约束）
	User     User      `json:"user" gorm:"foreignKey:UserID;references:ID"`
	Order    Order     `json:"order" gorm:"foreignKey:OrderID;references:ID"`
	Scanner  User      `json:"scanner" gorm:"foreignKey:ScannerID;references:ID"`
	Product  *Product  `json:"product,omitempty" gorm:"foreignKey:ProductID;references:ID"`
	Merchant *Merchant `json:"merchant,omitempty" gorm:"foreignKey:MerchantID;references:ID"`
}

// TableName 表名
func (VerifyRecord) TableName() string {
	return "verification_records"
}

// MarshalJSON 自定义JSON序列化，统一时间格式
func (vr VerifyRecord) MarshalJSON() ([]byte, error) {
	type Alias VerifyRecord

	// 创建一个匿名结构体，重新定义时间字段
	return json.Marshal(&struct {
		*Alias
		CreatedAt string `json:"created_at"`
		UpdatedAt string `json:"updated_at"`
	}{
		Alias:     (*Alias)(&vr),
		CreatedAt: formatStandardTime(&vr.CreatedAt),
		UpdatedAt: formatStandardTime(&vr.UpdatedAt),
	})
}
