package routes

import (
	"log"
	"wangfujing_admin/internal/handlers"
	"wangfujing_admin/internal/middleware"

	"github.com/gin-gonic/gin"
)

// SetupAdminRoutes 设置管理端路由
func SetupAdminRoutes(r *gin.Engine, h *handlers.Handler) {
	// 健康检查
	r.GET("/health", func(c *gin.Context) {
		c.JSON(200, gin.H{"status": "ok"})
	})

	// 管理端API路由组
	admin := r.Group("/admin/v1")
	{
		// 管理端认证相关
		auth := admin.Group("/auth")
		{
			// 微信小程序登录（主要登录方式）
			auth.POST("/wechat/login", h.AdminWechatLogin)
			auth.POST("/wechat/logout", middleware.JWTMiddleware(h.JWTService, h.TokenStorageService), h.AdminWechatLogout)

			// Token刷新（必需）
			auth.POST("/refresh", h.AdminRefreshToken)
		}

		// 管理端用户信息管理
		user := admin.Group("/user")
		user.Use(middleware.JWTMiddleware(h.JWTService, h.TokenStorageService))
		{
			user.GET("/info", h.AdminGetUserInfo)
			user.PUT("/info", h.AdminUpdateUserInfo)
			user.POST("/phone", h.AdminGetPhone)
		}

		// 需要认证的管理功能路由
		protected := admin.Group("/")
		protected.Use(middleware.JWTMiddleware(h.JWTService, h.TokenStorageService))
		protected.Use(middleware.OperationLogMiddleware(h.GetDB())) // 添加操作日志中间件
		{
			// 文件上传
			upload := protected.Group("/upload")
			{
				upload.POST("/file", h.UploadHandler.UploadFile)           // 单文件上传
				upload.POST("/files", h.UploadHandler.UploadMultipleFiles) // 批量文件上传
				upload.GET("/config", h.UploadHandler.GetUploadConfig)     // 获取上传配置
			}
			// 权限管理 - 仅管理员可访问
			permissions := protected.Group("/permissions")
			permissions.Use(middleware.RequirePermissions(h.GetDB(), "admin:permission:read"))
			{
				permissions.GET("",
					middleware.RequirePermissions(h.GetDB(), "admin:permission:read"),
					h.GetPermissions)
				permissions.GET("/:id",
					middleware.RequirePermissions(h.GetDB(), "admin:permission:read"),
					h.GetPermission)
				permissions.POST("",
					middleware.RequirePermissions(h.GetDB(), "admin:permission:create"),
					h.CreatePermission)
				permissions.PUT("/:id",
					middleware.RequirePermissions(h.GetDB(), "admin:permission:update"),
					h.UpdatePermission)
				permissions.DELETE("/:id",
					middleware.RequirePermissions(h.GetDB(), "admin:permission:delete"),
					h.DeletePermission)
				permissions.GET("/modules",
					middleware.RequirePermissions(h.GetDB(), "admin:permission:read"),
					h.GetPermissionModules)
				permissions.GET("/check", h.CheckPermission)   // 权限检查接口保持开放
				permissions.GET("/user", h.GetUserPermissions) // 获取用户权限保持开放
			}

			// 角色管理 - 仅管理员可访问
			roles := protected.Group("/roles")
			roles.Use(middleware.RequirePermissions(h.GetDB(), "admin:role:read"))
			{
				roles.GET("",
					middleware.RequirePermissions(h.GetDB(), "admin:role:read"),
					h.GetRoles)
				roles.GET("/:id",
					middleware.RequirePermissions(h.GetDB(), "admin:role:read"),
					h.GetRole)
				roles.POST("",
					middleware.RequirePermissions(h.GetDB(), "admin:role:create"),
					h.CreateRole)
				roles.PUT("/:id",
					middleware.RequirePermissions(h.GetDB(), "admin:role:update"),
					h.UpdateRole)
				roles.DELETE("/:id",
					middleware.RequirePermissions(h.GetDB(), "admin:role:delete"),
					h.DeleteRole)
				roles.POST("/:id/permissions",
					middleware.RequirePermissions(h.GetDB(), "admin:role:update"),
					h.AssignPermissionsToRole)
				roles.POST("/assign",
					middleware.RequirePermissions(h.GetDB(), "admin:role:assign"),
					h.AssignRoleToUser)
				roles.DELETE("/remove",
					middleware.RequirePermissions(h.GetDB(), "admin:role:assign"),
					h.RemoveRoleFromUser)
				roles.GET("/user/:user_id",
					middleware.RequirePermissions(h.GetDB(), "admin:role:read"),
					h.GetUserRoles)
			}

			// 用户管理 - 仅管理员可访问
			users := protected.Group("/users")
			users.Use(middleware.RequirePermissions(h.GetDB(), "admin:user:read"))
			{
				users.GET("",
					middleware.RequirePermissions(h.GetDB(), "admin:user:read"),
					h.GetAdminUsers)
				users.GET("/:id",
					middleware.RequirePermissions(h.GetDB(), "admin:user:read"),
					h.GetAdminUser)
				users.POST("",
					middleware.RequirePermissions(h.GetDB(), "admin:user:create"),
					h.CreateAdminUser)
				users.PUT("/:id",
					middleware.RequirePermissions(h.GetDB(), "admin:user:update"),
					h.UpdateAdminUser)
				users.DELETE("/:id",
					middleware.RequirePermissions(h.GetDB(), "admin:user:delete"),
					h.DeleteAdminUser)
				users.POST("/:id/roles",
					middleware.RequirePermissions(h.GetDB(), "admin:user:update"),
					h.AssignUserRoles)
				users.PUT("/:id/status",
					middleware.RequirePermissions(h.GetDB(), "admin:user:update"),
					h.UpdateUserStatus)
			}

			// 商家管理
			merchants := protected.Group("/merchants")
			{
				// 商家排行和列表 - 商家用户可以查看排名
				merchants.GET("/ranking",
					middleware.RequirePermissions(h.GetDB(), "merchant:ranking:sales", "merchant:ranking:efficiency", "merchant:ranking:level_contribution", "admin:merchant:read"),
					h.MerchantManagementHandler.GetMerchantRanking)
				merchants.GET("",
					middleware.RequirePermissions(h.GetDB(), "merchant:ranking:sales", "admin:merchant:read"),
					h.MerchantManagementHandler.GetMerchantRanking) // 兼容原有接口
				merchants.GET("/:id", h.MerchantManagementHandler.GetMerchantDetail)
				merchants.POST("", h.MerchantManagementHandler.CreateMerchant)
				merchants.PUT("/:id", h.MerchantManagementHandler.UpdateMerchant)
				merchants.DELETE("/:id", h.MerchantManagementHandler.DeleteMerchant)

				// 商家分值记录
				merchants.GET("/:id/score-records", h.MerchantManagementHandler.GetMerchantScoreRecords)

				// 分值申请管理
				scoreApps := merchants.Group("/score-applications")
				{
					scoreApps.GET("", h.MerchantManagementHandler.GetScoreApplications)
					scoreApps.POST("", h.MerchantManagementHandler.CreateScoreApplication)
					scoreApps.POST("/:id/approve", h.MerchantManagementHandler.ApproveScoreApplication)
					scoreApps.PUT("/:id/resubmit", h.MerchantManagementHandler.ResubmitScoreApplication)
				}
			}

			// 审核管理
			review := protected.Group("/review")
			{
				// 分值申请审核
				review.GET("/score-applications", h.MerchantManagementHandler.GetScoreApplicationsForReview)
				review.GET("/score-applications/:id", h.MerchantManagementHandler.GetScoreApplicationDetail)
			}

			// 等级管理 - 仅管理员可访问
			levels := protected.Group("/levels")
			levels.Use(middleware.RequirePermissions(h.GetDB(), "admin:level:read"))
			{
				// 分值规则
				levels.GET("/score-rules",
					middleware.RequirePermissions(h.GetDB(), "admin:level:read"),
					h.LevelManagementHandler.GetScoreRules)
				levels.PUT("/score-rules",
					middleware.RequirePermissions(h.GetDB(), "admin:level:update"),
					h.LevelManagementHandler.UpdateScoreRules)

				// 增减分项目
				levels.GET("/score-items",
					middleware.RequirePermissions(h.GetDB(), "admin:level:read"),
					h.LevelManagementHandler.GetScoreItems)
				levels.POST("/score-items",
					middleware.RequirePermissions(h.GetDB(), "admin:level:create"),
					h.LevelManagementHandler.CreateScoreItem)
				levels.PUT("/score-items/:id",
					middleware.RequirePermissions(h.GetDB(), "admin:level:update"),
					h.LevelManagementHandler.UpdateScoreItem)
				levels.DELETE("/score-items/:id",
					middleware.RequirePermissions(h.GetDB(), "admin:level:delete"),
					h.LevelManagementHandler.DeleteScoreItem)

				// 等级权益（全局配置）
				levels.GET("/benefits",
					middleware.RequirePermissions(h.GetDB(), "admin:level:read"),
					h.LevelManagementHandler.GetLevelBenefit)
				levels.POST("/benefits",
					middleware.RequirePermissions(h.GetDB(), "admin:level:create"),
					h.LevelManagementHandler.CreateLevelBenefit)
				levels.PUT("/benefits",
					middleware.RequirePermissions(h.GetDB(), "admin:level:update"),
					h.LevelManagementHandler.UpdateLevelBenefit)
				levels.DELETE("/benefits",
					middleware.RequirePermissions(h.GetDB(), "admin:level:delete"),
					h.LevelManagementHandler.DeleteLevelBenefit)
			}

			// 权益申请管理
			benefits := protected.Group("/benefit-applications")
			{
				log.Println("注册路由: GET /admin/v1/benefit-applications")
				benefits.GET("", h.BenefitApplicationHandler.GetBenefitApplications)
				log.Println("注册路由: POST /admin/v1/benefit-applications")
				// 商家用户申请权益需要权限检查
				benefits.POST("",
					middleware.RequirePermissions(h.GetDB(), "merchant:benefit:apply", "admin:benefit:manage"),
					h.BenefitApplicationHandler.CreateBenefitApplication)
				log.Println("注册路由: GET /admin/v1/benefit-applications/:id")
				benefits.GET("/:id", h.BenefitApplicationHandler.GetBenefitApplicationDetail)
				log.Println("注册路由: PUT /admin/v1/benefit-applications/:id/approve")
				benefits.PUT("/:id/approve", h.BenefitApplicationHandler.ApproveBenefitApplication)
				log.Println("注册路由: PUT /admin/v1/benefit-applications/:id/reapply")
				// 商家用户重新申请权益需要权限检查
				benefits.PUT("/:id/reapply",
					middleware.RequirePermissions(h.GetDB(), "merchant:benefit:apply", "admin:benefit:manage"),
					h.BenefitApplicationHandler.ReapplyBenefitApplication)
			}

			// 楼层管理 - 仅管理员可访问
			floors := protected.Group("/floors")
			floors.Use(middleware.RequirePermissions(h.GetDB(), "admin:floor:read"))
			{
				floors.GET("",
					middleware.RequirePermissions(h.GetDB(), "admin:floor:read"),
					h.GetFloors)
				floors.GET("/:id",
					middleware.RequirePermissions(h.GetDB(), "admin:floor:read"),
					h.GetFloor)
				floors.POST("",
					middleware.RequirePermissions(h.GetDB(), "admin:floor:create"),
					h.CreateFloor)
				floors.PUT("/:id",
					middleware.RequirePermissions(h.GetDB(), "admin:floor:update"),
					h.UpdateFloor)
				floors.DELETE("/:id",
					middleware.RequirePermissions(h.GetDB(), "admin:floor:delete"),
					h.DeleteFloor)
			}

			// 商品管理
			productHandler := handlers.NewProductHandler(h.GetDB(), h.GetRedis())

			// 商家端商品管理
			merchantProducts := protected.Group("/merchant/products")
			{
				// 商家商品列表
				merchantProducts.GET("",
					middleware.RequirePermissions(h.GetDB(), "merchant:product:read"),
					productHandler.GetMerchantProducts)

				// 商家商品详情
				merchantProducts.GET("/:id",
					middleware.RequirePermissions(h.GetDB(), "merchant:product:read"),
					productHandler.GetProductDetail)

				// 创建商品
				merchantProducts.POST("",
					middleware.RequirePermissions(h.GetDB(), "merchant:product:create"),
					productHandler.CreateProduct)

				// 更新商品
				merchantProducts.PUT("/:id",
					middleware.RequirePermissions(h.GetDB(), "merchant:product:update"),
					productHandler.UpdateProduct)

				// 切换商品状态（上架/下架）
				merchantProducts.PUT("/:id/status",
					middleware.RequirePermissions(h.GetDB(), "merchant:product:update"),
					productHandler.ToggleProductStatus)

				// 删除商品
				merchantProducts.DELETE("/:id",
					middleware.RequirePermissions(h.GetDB(), "merchant:product:delete"),
					productHandler.DeleteProduct)
			}

			// 商家端活动报名
			merchantActivities := protected.Group("/merchant/activities")
			{
				// 获取活动列表（包含已报名的活动，不包含已过期的）
				merchantActivities.GET("",
					middleware.RequirePermissions(h.GetDB(), "merchant:activity:read"),
					h.MerchantActivityParticipationHandler.GetActivities)

				// 获取已报名的活动列表
				merchantActivities.GET("/registered",
					middleware.RequirePermissions(h.GetDB(), "merchant:activity:read"),
					h.MerchantActivityParticipationHandler.GetRegisteredActivities)

				// 获取活动详情
				merchantActivities.GET("/:id",
					middleware.RequirePermissions(h.GetDB(), "merchant:activity:read"),
					h.MerchantActivityParticipationHandler.GetActivityDetail)

				// 报名活动
				merchantActivities.POST("/:id/register",
					middleware.RequirePermissions(h.GetDB(), "merchant:activity:register"),
					h.MerchantActivityParticipationHandler.RegisterActivity)
			}

			// 商场端商品审核
			adminProducts := protected.Group("/admin/products")
			{
				// 获取待审核商品列表
				adminProducts.GET("/approval",
					middleware.RequirePermissions(h.GetDB(), "admin:product:read"),
					productHandler.GetProductsForApproval)

				// 审核商品
				adminProducts.POST("/:id/approve",
					middleware.RequirePermissions(h.GetDB(), "admin:product:approve"),
					productHandler.ApproveProduct)
			}

			// 客诉管理 - 仅管理员可访问
			complaints := protected.Group("/complaints")
			complaints.Use(middleware.RequirePermissions(h.GetDB(), "admin:complaint:read"))
			{
				complaints.GET("",
					middleware.RequirePermissions(h.GetDB(), "admin:complaint:read"),
					h.GetComplaintsAdmin)
				complaints.GET("/:id",
					middleware.RequirePermissions(h.GetDB(), "admin:complaint:read"),
					h.GetComplaintAdmin)
				complaints.POST("",
					middleware.RequirePermissions(h.GetDB(), "admin:complaint:create"),
					h.CreateComplaintAdmin)
				complaints.PUT("/:id",
					middleware.RequirePermissions(h.GetDB(), "admin:complaint:update"),
					h.UpdateComplaintAdmin)
				complaints.POST("/:id/approve",
					middleware.RequirePermissions(h.GetDB(), "admin:complaint:approve"),
					h.ApproveComplaintAdmin)
				complaints.POST("/:id/reject",
					middleware.RequirePermissions(h.GetDB(), "admin:complaint:approve"),
					h.RejectComplaintAdmin)
				complaints.GET("/pending",
					middleware.RequirePermissions(h.GetDB(), "admin:complaint:read"),
					h.GetPendingComplaints)
			}

			// 意见管理 - 仅管理员可访问
			opinions := protected.Group("/opinions")
			opinions.Use(middleware.RequirePermissions(h.GetDB(), "admin:opinion:read"))
			{
				opinions.GET("",
					middleware.RequirePermissions(h.GetDB(), "admin:opinion:read"),
					h.GetOpinionsAdmin)
				opinions.GET("/:id",
					middleware.RequirePermissions(h.GetDB(), "admin:opinion:read"),
					h.GetOpinionAdmin)
				opinions.PUT("/:id/status",
					middleware.RequirePermissions(h.GetDB(), "admin:opinion:update"),
					h.UpdateOpinionStatus)
			}

			// 操作日志管理 - 仅管理员可访问
			operationLogs := protected.Group("/operation-logs")
			operationLogs.Use(middleware.RequirePermissions(h.GetDB(), "admin:log:read"))
			{
				operationLogs.GET("",
					middleware.RequirePermissions(h.GetDB(), "admin:log:read"),
					h.GetOperationLogs)
				operationLogs.GET("/:id",
					middleware.RequirePermissions(h.GetDB(), "admin:log:read"),
					h.GetOperationLog)
				operationLogs.GET("/statistics",
					middleware.RequirePermissions(h.GetDB(), "admin:log:read"),
					h.GetOperationStatistics)
				operationLogs.GET("/modules",
					middleware.RequirePermissions(h.GetDB(), "admin:log:read"),
					h.GetOperationModules)
				operationLogs.GET("/actions",
					middleware.RequirePermissions(h.GetDB(), "admin:log:read"),
					h.GetOperationActions)
				operationLogs.GET("/export",
					middleware.RequirePermissions(h.GetDB(), "admin:log:export"),
					h.ExportOperationLogs)
			}

			// 营销管理
			marketing := protected.Group("/marketing")
			{
				// 积分活动 - 商家用户可以查看和报名，管理员可以管理
				activities := marketing.Group("/activities")
				{
					activities.GET("",
						middleware.RequirePermissions(h.GetDB(), "merchant:activity:read", "admin:activity:read"),
						h.GetActivitiesAdmin)
					activities.GET("/:id",
						middleware.RequirePermissions(h.GetDB(), "merchant:activity:read", "admin:activity:read"),
						h.GetActivityAdmin)
					activities.POST("",
						middleware.RequirePermissions(h.GetDB(), "admin:activity:create"),
						h.CreateActivityAdmin)
					activities.PUT("/:id",
						middleware.RequirePermissions(h.GetDB(), "admin:activity:update"),
						h.UpdateActivityAdmin)
					activities.DELETE("/:id",
						middleware.RequirePermissions(h.GetDB(), "admin:activity:delete"),
						h.DeleteActivityAdmin)
					// 活动报名接口 - 商家用户可以访问 (待实现)
					// activities.POST("/:id/register",
					//     middleware.RequirePermissions(h.GetDB(), "merchant:activity:register"),
					//     h.RegisterActivity)
				}

				// 积分商城 - 仅管理员可访问
				mall := marketing.Group("/mall")
				mall.Use(middleware.RequirePermissions(h.GetDB(), "admin:mall:read"))
				{
					mall.GET("/products",
						middleware.RequirePermissions(h.GetDB(), "admin:mall:read"),
						h.GetMallProducts)
					mall.POST("/products",
						middleware.RequirePermissions(h.GetDB(), "admin:mall:create"),
						h.CreateMallProduct)
					mall.PUT("/products/:id",
						middleware.RequirePermissions(h.GetDB(), "admin:mall:update"),
						h.UpdateMallProduct)
					mall.DELETE("/products/:id",
						middleware.RequirePermissions(h.GetDB(), "admin:mall:delete"),
						h.DeleteMallProduct)
					mall.PUT("/products/:id/status",
						middleware.RequirePermissions(h.GetDB(), "admin:mall:update"),
						h.UpdateMallProductStatus)
				}

				// 会员规则 - 仅管理员可访问
				rules := marketing.Group("/rules")
				rules.Use(middleware.RequirePermissions(h.GetDB(), "admin:rule:read"))
				{
					rules.GET("",
						middleware.RequirePermissions(h.GetDB(), "admin:rule:read"),
						h.GetMemberRules)
					rules.POST("",
						middleware.RequirePermissions(h.GetDB(), "admin:rule:create"),
						h.CreateMemberRule)
					rules.PUT("/:id",
						middleware.RequirePermissions(h.GetDB(), "admin:rule:update"),
						h.UpdateMemberRule)
					rules.DELETE("/:id",
						middleware.RequirePermissions(h.GetDB(), "admin:rule:delete"),
						h.DeleteMemberRule)
				}

				// 节目管理 - 仅管理员可访问
				programs := marketing.Group("/programs")
				programs.Use(middleware.RequirePermissions(h.GetDB(), "admin:program:read"))
				{
					programs.GET("",
						middleware.RequirePermissions(h.GetDB(), "admin:program:read"),
						h.ProgramHandler.GetPrograms)
					programs.POST("",
						middleware.RequirePermissions(h.GetDB(), "admin:program:create"),
						h.ProgramHandler.CreateProgram)
					programs.PUT("/:id",
						middleware.RequirePermissions(h.GetDB(), "admin:program:update"),
						h.ProgramHandler.UpdateProgram)
					programs.DELETE("/:id",
						middleware.RequirePermissions(h.GetDB(), "admin:program:delete"),
						h.ProgramHandler.DeleteProgram)
				}
			}

			// 订单管理 - 仅管理员可访问
			orders := protected.Group("/orders")
			orders.Use(middleware.RequirePermissions(h.GetDB(), "admin:order:read"))
			{
				orders.GET("",
					middleware.RequirePermissions(h.GetDB(), "admin:order:read"),
					h.GetOrdersAdmin)
				orders.GET("/:id",
					middleware.RequirePermissions(h.GetDB(), "admin:order:read"),
					h.GetOrderAdmin)
				orders.PUT("/:id/status",
					middleware.RequirePermissions(h.GetDB(), "admin:order:update"),
					h.UpdateOrderStatusAdmin)
				orders.GET("/stats",
					middleware.RequirePermissions(h.GetDB(), "admin:order:read"),
					h.GetOrderStats)
			}

			// 用户管理 - 仅管理员可访问
			customers := protected.Group("/customers")
			customers.Use(middleware.RequirePermissions(h.GetDB(), "admin:customer:read"))
			{
				customers.GET("",
					middleware.RequirePermissions(h.GetDB(), "admin:customer:read"),
					h.GetCustomers)
				customers.GET("/:id",
					middleware.RequirePermissions(h.GetDB(), "admin:customer:read"),
					h.GetCustomer)
				customers.GET("/:id/points",
					middleware.RequirePermissions(h.GetDB(), "admin:customer:read"),
					h.GetCustomerPoints)
			}

			// 用户端用户管理（users表）- 仅管理员可访问
			userManagementHandler := handlers.NewUserManagementHandler(h)
			userManagement := protected.Group("/user-management")
			userManagement.Use(middleware.RequirePermissions(h.GetDB(), "admin:user_management:read"))
			{
				userManagement.GET("/users",
					middleware.RequirePermissions(h.GetDB(), "admin:user_management:read"),
					userManagementHandler.GetUsers)
				userManagement.GET("/users/:id",
					middleware.RequirePermissions(h.GetDB(), "admin:user_management:read"),
					userManagementHandler.GetUserDetail)
				userManagement.GET("/users/:id/points",
					middleware.RequirePermissions(h.GetDB(), "admin:user_management:read"),
					userManagementHandler.GetUserPointsRecords)
			}

			// 商家端核销管理
			merchantVerify := protected.Group("/merchant/verify")
			{
				merchantVerify.GET("/records",
					middleware.RequirePermissions(h.GetDB(), "merchant:verification:read"),
					h.GetVerificationRecords) // 获取核销记录列表
				merchantVerify.GET("/records/:id",
					middleware.RequirePermissions(h.GetDB(), "merchant:verification:read"),
					h.GetVerificationDetail) // 获取核销详情
				merchantVerify.POST("/records/:id",
					middleware.RequirePermissions(h.GetDB(), "merchant:verification:verify"),
					h.VerifyOrder) // 确认核销
				merchantVerify.POST("/scan/detail",
					middleware.RequirePermissions(h.GetDB(), "merchant:verification:read"),
					h.ScanQRCodeForOrderDetail) // 扫描二维码获取订单详情
				merchantVerify.POST("/scan",
					middleware.RequirePermissions(h.GetDB(), "merchant:verification:scan"),
					h.ScanAndVerifyQRCode) // 扫描二维码并直接核销
			}

			// 扫码管理 - 商家用户可以核销，管理员可以管理
			scan := protected.Group("/scan")
			{
				scan.POST("/task",
					middleware.RequirePermissions(h.GetDB(), "merchant:verification:scan", "admin:scan:manage"),
					h.ScanTaskAdmin)
				scan.POST("/user",
					middleware.RequirePermissions(h.GetDB(), "merchant:verification:scan", "admin:scan:manage"),
					h.ScanUserAdmin)
				scan.POST("/product",
					middleware.RequirePermissions(h.GetDB(), "merchant:verification:scan", "admin:scan:manage"),
					h.ScanProductAdmin)
				scan.GET("/records",
					middleware.RequirePermissions(h.GetDB(), "merchant:verification:read", "admin:scan:read"),
					h.GetScanRecords)
			}

			// 营销活动管理（新）
			marketingActivities := protected.Group("/marketing")
			{
				// 用户营销活动管理
				activities := marketingActivities.Group("/marketing-activities")
				{
					activities.POST("",
						middleware.RequirePermissions(h.GetDB(), "admin:user-marketing:create"),
						h.MarketingHandler.CreateActivity)
					activities.GET("",
						middleware.RequirePermissions(h.GetDB(), "admin:user-marketing:read"),
						h.MarketingHandler.GetActivityList)
					// activities.GET("/statistics",
					// 	middleware.RequirePermissions(h.GetDB(), "admin:user-marketing:read"),
					// 	h.MarketingHandler.GetActivityStatistics)
					activities.GET("/:id",
						middleware.RequirePermissions(h.GetDB(), "admin:user-marketing:read"),
						h.MarketingHandler.GetActivity)
					activities.PUT("/:id",
						middleware.RequirePermissions(h.GetDB(), "admin:user-marketing:update"),
						h.MarketingHandler.UpdateActivity)
					activities.PATCH("/:id/status",
						middleware.RequirePermissions(h.GetDB(), "admin:user-marketing:update"),
						h.MarketingHandler.UpdateActivityStatus)
					activities.DELETE("/:id",
						middleware.RequirePermissions(h.GetDB(), "admin:user-marketing:delete"),
						h.MarketingHandler.DeleteActivity)
					activities.POST("/:id/restart",
						middleware.RequirePermissions(h.GetDB(), "admin:user-marketing:create"),
						h.MarketingHandler.RestartActivity)
				}

				// 商家活动管理
				merchantActivities := marketingActivities.Group("/merchant-activities")
				{
					merchantActivities.POST("",
						middleware.RequirePermissions(h.GetDB(), "admin:merchant-activity:create"),
						h.MerchantActivityHandler.CreateMerchantActivity)
					merchantActivities.GET("",
						middleware.RequirePermissions(h.GetDB(), "admin:merchant-activity:read"),
						h.MerchantActivityHandler.GetMerchantActivityList)
					merchantActivities.GET("/:id",
						middleware.RequirePermissions(h.GetDB(), "admin:merchant-activity:read"),
						h.MerchantActivityHandler.GetMerchantActivity)
					merchantActivities.PUT("/:id",
						middleware.RequirePermissions(h.GetDB(), "admin:merchant-activity:update"),
						h.MerchantActivityHandler.UpdateMerchantActivity)
					merchantActivities.PATCH("/:id/status",
						middleware.RequirePermissions(h.GetDB(), "admin:merchant-activity:update"),
						h.MerchantActivityHandler.UpdateMerchantActivityStatus)
					merchantActivities.DELETE("/:id",
						middleware.RequirePermissions(h.GetDB(), "admin:merchant-activity:delete"),
						h.MerchantActivityHandler.DeleteMerchantActivity)
					merchantActivities.POST("/:id/restart",
						middleware.RequirePermissions(h.GetDB(), "admin:merchant-activity:create"),
						h.MerchantActivityHandler.RestartMerchantActivity)

					// 活动奖励管理
					merchantActivities.POST("/process-rewards",
						middleware.RequirePermissions(h.GetDB(), "admin:merchant-activity:manage"),
						h.ActivityRewardHandler.ProcessExpiredActivities)
					merchantActivities.GET("/:id/reward-status",
						middleware.RequirePermissions(h.GetDB(), "admin:merchant-activity:read"),
						h.ActivityRewardHandler.GetActivityRewardStatus)
				}

				// 积分商城管理
				pointsMall := marketingActivities.Group("/points-mall")
				{
					// 兑换商品管理
					pointsMall.POST("/products",
						middleware.RequirePermissions(h.GetDB(), "admin:points-mall:create"),
						h.PointsMallHandler.CreateProduct)
					pointsMall.PUT("/products/:id",
						middleware.RequirePermissions(h.GetDB(), "admin:points-mall:update"),
						h.PointsMallHandler.UpdateProduct)

					// VIP形象管理
					pointsMall.POST("/avatars",
						middleware.RequirePermissions(h.GetDB(), "admin:points-mall:create"),
						h.PointsMallHandler.CreateAvatar)
					pointsMall.PUT("/avatars/:id",
						middleware.RequirePermissions(h.GetDB(), "admin:points-mall:update"),
						h.PointsMallHandler.UpdateAvatar)

					// 通用接口
					pointsMall.GET("",
						middleware.RequirePermissions(h.GetDB(), "admin:points-mall:read"),
						h.PointsMallHandler.GetPointsMallList)
					pointsMall.GET("/:id",
						middleware.RequirePermissions(h.GetDB(), "admin:points-mall:read"),
						h.PointsMallHandler.GetPointsMallItem)
					pointsMall.PATCH("/:id/status",
						middleware.RequirePermissions(h.GetDB(), "admin:points-mall:update"),
						h.PointsMallHandler.UpdatePointsMallItemStatus)
					pointsMall.DELETE("/:id",
						middleware.RequirePermissions(h.GetDB(), "admin:points-mall:delete"),
						h.PointsMallHandler.DeletePointsMallItem)
					pointsMall.POST("/:id/republish",
						middleware.RequirePermissions(h.GetDB(), "admin:points-mall:update"),
						h.PointsMallHandler.RepublishPointsMallItem)
				}

				// 会员规则管理
				memberRules := marketingActivities.Group("/member-rules")
				{
					memberRules.GET("",
						middleware.RequirePermissions(h.GetDB(), "admin:member-rule:read"),
						h.MemberRuleHandler.GetMemberRuleList)
					memberRules.GET("/:id",
						middleware.RequirePermissions(h.GetDB(), "admin:member-rule:read"),
						h.MemberRuleHandler.GetMemberRule)
					memberRules.POST("",
						middleware.RequirePermissions(h.GetDB(), "admin:member-rule:create"),
						h.MemberRuleHandler.CreateMemberRule)
					memberRules.PUT("/:id",
						middleware.RequirePermissions(h.GetDB(), "admin:member-rule:update"),
						h.MemberRuleHandler.UpdateMemberRule)
					memberRules.DELETE("/:id",
						middleware.RequirePermissions(h.GetDB(), "admin:member-rule:delete"),
						h.MemberRuleHandler.DeleteMemberRule)
				}

				// 二维码管理
				qrcode := marketingActivities.Group("/qrcode")
				{
					qrcode.POST("/orders/:order_id/generate",
						middleware.RequirePermissions(h.GetDB(), "admin:qrcode:create"),
						h.AdminGenerateOrderQRCode) // 生成订单二维码
					qrcode.POST("/orders/:order_id/regenerate",
						middleware.RequirePermissions(h.GetDB(), "admin:qrcode:update"),
						h.AdminRegenerateOrderQRCode) // 重新生成订单二维码
					qrcode.POST("/batch-generate",
						middleware.RequirePermissions(h.GetDB(), "admin:qrcode:create"),
						h.AdminBatchGenerateQRCodes) // 批量生成二维码
					qrcode.GET("/stats",
						middleware.RequirePermissions(h.GetDB(), "admin:qrcode:read"),
						h.AdminGetQRCodeStats) // 获取二维码统计
					qrcode.GET("/orders/without-qrcode",
						middleware.RequirePermissions(h.GetDB(), "admin:qrcode:read"),
						h.AdminGetOrdersWithoutQRCode) // 获取没有二维码的订单
					qrcode.GET("/orders/with-qrcode",
						middleware.RequirePermissions(h.GetDB(), "admin:qrcode:read"),
						h.AdminGetOrdersWithQRCode) // 获取已有二维码的订单

					// 调试接口
					qrcode.GET("/debug/orders/:order_id",
						middleware.RequirePermissions(h.GetDB(), "admin:qrcode:read"),
						h.DebugOrderQRCode) // 调试订单二维码状态
					qrcode.POST("/debug/orders/:order_id/force-generate",
						middleware.RequirePermissions(h.GetDB(), "admin:qrcode:create"),
						h.ForceGenerateOrderQRCode) // 强制生成订单二维码
					qrcode.GET("/debug/status",
						middleware.RequirePermissions(h.GetDB(), "admin:qrcode:read"),
						h.CheckAllOrdersQRCodeStatus) // 检查所有订单二维码状态
				}
			}

			// 统计报表
			stats := protected.Group("/stats")
			{
				stats.GET("/dashboard", h.GetAdminDashboardStats)
				stats.GET("/sales", h.GetAdminSalesStats)
				stats.GET("/merchants", h.GetAdminMerchantStats)
				stats.GET("/users", h.GetAdminUserStats)
			}

			// 秒杀系统监控
			seckillMonitor := protected.Group("/seckill/monitor")
			{
				seckillMonitor.GET("/stats", h.GetSeckillStats)                               // 获取秒杀系统统计
				seckillMonitor.GET("/global/ratelimit", h.GetGlobalRateLimit)                 // 获取全局限流状态
				seckillMonitor.GET("/products/:id/ratelimit", h.GetProductRateLimit)          // 获取商品限流状态
				seckillMonitor.POST("/global/ratelimit/reset", h.ResetGlobalRateLimit)        // 重置全局限流
				seckillMonitor.POST("/products/:id/ratelimit/reset", h.ResetProductRateLimit) // 重置商品限流
				seckillMonitor.GET("/workers", h.GetWorkerStats)                              // 获取Worker统计
				seckillMonitor.POST("/workers/restart", h.RestartWorkers)                     // 重启Worker
				seckillMonitor.GET("/config", h.GetSeckillConfig)                             // 获取秒杀配置
				seckillMonitor.PUT("/config", h.UpdateSeckillConfig)                          // 更新秒杀配置
			}

			// 秒杀库存管理
			seckillStock := protected.Group("/seckill/stock")
			{
				seckillStock.POST("/refresh/all", h.ManualRefreshAllStock)              // 手动刷新所有商品库存
				seckillStock.POST("/refresh/products/:id", h.ManualRefreshProductStock) // 手动刷新指定商品库存
				seckillStock.GET("/refresh/status", h.GetDailyRefreshStatus)            // 获取每日刷新状态
				seckillStock.PUT("/products/:id/reset", h.ResetProductStock)            // 重置商品库存
				seckillStock.GET("/products/:id", h.GetProductStockInfo)                // 获取商品库存信息
				seckillStock.POST("/batch/reset", h.BatchResetStock)                    // 批量重置库存
				seckillStock.GET("/summary", h.GetAllProductsStockSummary)              // 获取所有商品库存汇总
			}

			// 秒杀并发测试
			seckillTest := protected.Group("/seckill/test")
			{
				seckillTest.POST("/purchase", h.AdminTestPurchase)                    // 管理端测试抢购
				seckillTest.GET("/result", h.AdminTestPurchaseResult)                 // 查询测试抢购结果
				seckillTest.POST("/concurrent", h.AdminTestConcurrentPurchase)        // 并发测试抢购
				seckillTest.GET("/concurrent/status", h.AdminGetConcurrentTestStatus) // 获取并发测试状态
				seckillTest.POST("/load", h.AdminTestLoadPurchase)                    // 负载测试
				seckillTest.DELETE("/clear", h.AdminClearTestData)                    // 清理测试数据
			}

			// 过期商品清理管理
			cleanup := protected.Group("/cleanup")
			{
				cleanup.GET("/expired/info", h.GetExpiredProductsInfo)          // 获取过期商品信息
				cleanup.POST("/expired/all", h.CleanupExpiredProducts)          // 清理所有过期商品
				cleanup.POST("/expired/products/:id", h.CleanupSpecificProduct) // 清理指定过期商品
			}

			// VIP形象过期管理
			vipAvatarExpiry := protected.Group("/vip-avatar-expiry")
			{
				vipAvatarExpiry.GET("/stats", h.GetVipAvatarStats)             // 获取VIP形象统计信息
				vipAvatarExpiry.POST("/process", h.ProcessVipAvatarExpiry)     // 手动触发过期处理
				vipAvatarExpiry.GET("/schedule", h.GetVipAvatarExpirySchedule) // 获取过期处理计划
				vipAvatarExpiry.GET("/logs", h.GetVipAvatarExpiryLogs)         // 获取过期处理日志
				vipAvatarExpiry.GET("/user-stats", h.GetVipAvatarUserStats)    // 获取用户统计
			}
		}
	}
}
