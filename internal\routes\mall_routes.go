package routes

import (
	"wangfujing_admin/internal/handlers"
	"wangfujing_admin/internal/middleware"

	"github.com/gin-gonic/gin"
)

// SetupMallRoutes 设置商场端路由
func SetupMallRoutes(r *gin.Engine, h *handlers.Handler) {
	// 商场端API组
	mall := r.Group("/mall/v1")
	{
		// 需要认证的核销功能路由
		protected := mall.Group("/")
		protected.Use(middleware.JWTMiddleware(h.JWTService, h.TokenStorageService))
		protected.Use(middleware.OperationLogMiddleware(h.GetDB())) // 添加操作日志中间件
		{
			// 核销相关
			verify := protected.Group("/verify")
			{
				verify.GET("/records", h.GetMallVerifyRecords)       // 获取核销记录列表
				verify.POST("/scan", h.ScanQRCode)                   // 扫描二维码
				verify.POST("/points-mall", h.VerifyPointsMallOrder) // 核销积分商城订单
				verify.POST("/npc-points", h.GiveNPCPoints)          // NPC送积分
				// verify.POST("/activity", h.VerifyActivity)           // 核销活动（手动）
				verify.POST("/task-qr", h.VerifyTaskQRCode) // 核销任务二维码（扫码）
			}
		}
	}
}
