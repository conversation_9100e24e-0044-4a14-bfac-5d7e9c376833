package routes

import (
	"wangfujing_admin/internal/handlers"
	"wangfujing_admin/internal/middleware"

	"github.com/gin-gonic/gin"
)

// SetupUserRoutes 设置用户端路由
func SetupUserRoutes(r *gin.Engine, h *handlers.Handler) {
	// 健康检查
	r.GET("/health", func(c *gin.Context) {
		c.JSON(200, gin.H{"status": "ok"})
	})

	// API路由组
	api := r.Group("/api/v1")
	{
		// 用户端认证相关
		auth := api.Group("/auth")
		{
			// 微信小程序登录（主要登录方式）
			auth.POST("/wechat/login", h.WechatLogin)
			auth.POST("/wechat/logout", middleware.JWTMiddleware(h.JWTService, h.TokenStorageService), h.WechatLogout)

			// Token刷新（必需）
			auth.POST("/refresh", h.RefreshToken)
		}

		// 用户端用户信息管理（需要认证）
		user := api.Group("/user")
		user.Use(middleware.JWTMiddleware(h.JWTService, h.TokenStorageService))
		{
			user.GET("/info", h.GetUserInfo)
			user.PUT("/info", h.UpdateUserInfo)
			user.POST("/phone", h.GetPhone)
			user.GET("/personal-qr", h.GetPersonalQR) // 获取个人二维码
		}

		// 需要认证的路由
		protected := api.Group("/")
		protected.Use(middleware.JWTMiddleware(h.JWTService, h.TokenStorageService))
		{
			// 文件上传
			upload := protected.Group("/upload")
			{
				upload.POST("/file", h.UploadHandler.UploadFile)           // 单文件上传
				upload.POST("/files", h.UploadHandler.UploadMultipleFiles) // 批量文件上传
				upload.GET("/config", h.UploadHandler.GetUploadConfig)     // 获取上传配置
			}

			// 用户管理
			users := protected.Group("/users")
			{
				users.GET("", h.GetUsers)
			}

			// VIP形象相关
			avatars := protected.Group("/avatars")
			{
				avatars.GET("/carousel", h.GetAvatarCarousel)                // 获取形象轮播列表
				avatars.POST("/:id/exchange", h.ExchangeAvatar)              // 兑换形象
				avatars.GET("/my-exchanges", h.GetMyAvatarExchanges)         // 获取我的形象兑换记录
				avatars.POST("/exchanges/:exchange_id/apply", h.ApplyAvatar) // 应用VIP形象
				avatars.GET("/current", h.GetCurrentAvatar)                  // 获取当前正在使用的形象
			}

			// 地图相关
			maps := protected.Group("/maps")
			{
				maps.GET("/floors", h.GetFloorsForUser) // 获取楼层地图信息
			}

			// 薅羊毛相关
			seckill := protected.Group("/seckill")
			{
				seckill.GET("/products", h.GetSeckillProducts)                   // 获取薅羊毛商品列表
				seckill.GET("/products/:id", h.GetSeckillProductDetail)          // 获取商品详情
				seckill.POST("/products/:id/exchange", h.ExchangeSeckillProduct) // 兑换商品（原版本）

				// 高并发版本接口
				seckill.POST("/purchase", h.PurchaseProduct)  // 抢购商品（高并发版本）
				seckill.GET("/result", h.CheckPurchaseResult) // 查询抢购结果
				seckill.GET("/position", h.GetQueuePosition)  // 获取队列位置
			}

			// 积分商城相关
			pointsMall := protected.Group("/points-mall")
			{
				pointsMall.GET("/items", h.GetPointsMallItems)                   // 获取积分商城商品列表
				pointsMall.GET("/items/:id", h.GetPointsMallItemDetail)          // 获取商品详情
				pointsMall.POST("/items/:id/exchange", h.ExchangePointsMallItem) // 兑换商品（普通兑换）
				// pointsMall.POST("/test-exchange", h.TestPointsMallExchange)      // 测试接口
			}

			// 订单相关
			orders := protected.Group("/orders")
			{
				orders.GET("", h.GetUserOrders)          // 获取用户订单列表
				orders.GET("/:id", h.GetUserOrderDetail) // 获取订单详情
			}

			// 任务中心相关
			tasks := protected.Group("/tasks")
			{
				tasks.GET("", h.GetTaskList)                           // 获取任务列表
				tasks.GET("/:id", h.GetTaskDetail)                     // 获取任务详情
				tasks.POST("/:id/register", h.RegisterTask)            // 报名参与任务
				tasks.POST("/:id/complete-share", h.CompleteShareTask) // 完成分享任务
			}

			// 积分相关
			points := protected.Group("/points")
			{
				points.GET("", h.GetMyPoints)                       // 获取用户积分和明细
				points.GET("/records/:id", h.GetPointsRecordDetail) // 获取积分记录详情
			}

			// 发表意见反馈
			opinions := protected.Group("/opinions")
			{
				opinions.GET("", h.GetOpinionByUser) // 获取意见反馈
				opinions.POST("", h.SubmitOpinion)   // 提交意见反馈
			}
		}

		//不需要路由的认证
		public := api.Group("/")
		{
			// 节目单管理
			programs := public.Group("/program")
			{
				programs.GET("", h.GetProgramsForUser) // 获取节目单列表
			}
		}
	}
}
