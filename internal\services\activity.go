package services

import (
	"context"

	"wangfujing_admin/internal/models"

	"gorm.io/gorm"
)

// ActivityService 活动服务
type ActivityService struct {
	db *gorm.DB
}

// NewActivityService 创建活动服务
func NewActivityService(db *gorm.DB) *ActivityService {
	return &ActivityService{
		db: db,
	}
}

// GetActivities 获取活动列表 - 占位符实现
func (s *ActivityService) GetActivities(ctx context.Context, page, size int) ([]*models.Activity, int64, error) {
	// TODO: 实现活动列表获取逻辑
	return []*models.Activity{}, 0, nil
}
