package services

import (
	"context"
	"fmt"
	"log"
	"time"

	"gorm.io/gorm"

	"wangfujing_admin/internal/models"
)

// ActivityRewardService 活动奖励服务
type ActivityRewardService struct {
	db *gorm.DB
}

// NewActivityRewardService 创建活动奖励服务实例
func NewActivityRewardService(db *gorm.DB) *ActivityRewardService {
	return &ActivityRewardService{db: db}
}

// ProcessExpiredActivities 处理已过期的活动，给参与的商家发放贡献度
func (s *ActivityRewardService) ProcessExpiredActivities(ctx context.Context) error {
	// 查找刚刚过期的活动（状态为已上架但已过期）
	var activities []models.MerchantActivity
	now := time.Now()

	if err := s.db.WithContext(ctx).
		Where("status = ? AND end_time < ? AND end_time > ?",
			models.MerchantActivityStatusActive,
			now,
			now.Add(-24*time.Hour)). // 只处理24小时内过期的活动，避免重复处理
		Find(&activities).Error; err != nil {
		return fmt.Errorf("failed to get expired activities: %w", err)
	}

	log.Printf("Found %d expired activities to process", len(activities))

	for _, activity := range activities {
		if err := s.processActivityRewards(ctx, &activity); err != nil {
			log.Printf("Failed to process rewards for activity %d: %v", activity.ID, err)
			continue
		}

		// 更新活动状态为已过期
		if err := s.db.WithContext(ctx).Model(&activity).
			Update("status", models.MerchantActivityStatusExpired).Error; err != nil {
			log.Printf("Failed to update activity %d status: %v", activity.ID, err)
		}
	}

	return nil
}

// processActivityRewards 处理单个活动的奖励发放
func (s *ActivityRewardService) processActivityRewards(ctx context.Context, activity *models.MerchantActivity) error {
	// 获取该活动的所有参与者
	var participants []models.MerchantActivityParticipant
	if err := s.db.WithContext(ctx).
		Where("activity_id = ? AND status = ?", activity.ID, 1). // 1:已报名
		Find(&participants).Error; err != nil {
		return fmt.Errorf("failed to get activity participants: %w", err)
	}

	if len(participants) == 0 {
		log.Printf("No participants found for activity %d", activity.ID)
		return nil
	}

	log.Printf("Processing rewards for %d participants in activity %d", len(participants), activity.ID)

	// 获取或创建活动奖励分值项目
	scoreItemID, err := s.getOrCreateActivityRewardScoreItem(ctx)
	if err != nil {
		return fmt.Errorf("failed to get activity reward score item: %w", err)
	}

	// 开始事务
	return s.db.WithContext(ctx).Transaction(func(tx *gorm.DB) error {
		for _, participant := range participants {
			// 更新商家积分
			if err := tx.Model(&models.Merchant{}).
				Where("id = ?", participant.MerchantID).
				Update("score", gorm.Expr("score + ?", activity.ContributionScore)).Error; err != nil {
				return fmt.Errorf("failed to update merchant %d score: %w", participant.MerchantID, err)
			}

			// 创建分值记录
			scoreRecord := &models.ScoreRecord{
				MerchantID:     participant.MerchantID,
				ScoreItemID:    scoreItemID,
				Type:           models.ScoreTypeAdd,
				Score:          activity.ContributionScore,
				Reason:         fmt.Sprintf("活动奖励：%s", activity.Name),
				OperatorID:     1,                             // 系统操作，使用系统用户ID
				ApprovalStatus: models.ApprovalStatusApproved, // 自动审核通过
			}

			// 设置审核时间为当前时间
			now := time.Now()
			scoreRecord.ApprovedAt = &now

			if err := tx.Create(scoreRecord).Error; err != nil {
				return fmt.Errorf("failed to create score record for merchant %d: %w", participant.MerchantID, err)
			}

			// 更新参与记录状态为已完成
			if err := tx.Model(&participant).
				Update("status", 2).Error; err != nil { // 2:已完成
				return fmt.Errorf("failed to update participant %d status: %w", participant.ID, err)
			}

			log.Printf("Awarded %d points to merchant %d for activity %d",
				activity.ContributionScore, participant.MerchantID, activity.ID)
		}

		return nil
	})
}

// getOrCreateActivityRewardScoreItem 获取或创建活动奖励分值项目
func (s *ActivityRewardService) getOrCreateActivityRewardScoreItem(ctx context.Context) (uint64, error) {
	var scoreItem models.ScoreItem

	// 先尝试查找现有的活动奖励项目
	err := s.db.WithContext(ctx).Where("name = ? AND type = ?", "活动奖励", models.ScoreTypeAdd).First(&scoreItem).Error
	if err == nil {
		return scoreItem.ID, nil
	}

	if err != gorm.ErrRecordNotFound {
		return 0, fmt.Errorf("failed to query score item: %w", err)
	}

	// 如果不存在，创建新的活动奖励项目
	scoreItem = models.ScoreItem{
		Type:        models.ScoreTypeAdd,
		Name:        "活动奖励",
		Score:       0, // 分值由具体活动决定
		Description: "商家活动结束后的贡献度奖励",
	}

	if err := s.db.WithContext(ctx).Create(&scoreItem).Error; err != nil {
		return 0, fmt.Errorf("failed to create activity reward score item: %w", err)
	}

	log.Printf("Created activity reward score item with ID: %d", scoreItem.ID)
	return scoreItem.ID, nil
}

// StartRewardProcessor 启动奖励处理器（定时任务）
func (s *ActivityRewardService) StartRewardProcessor(ctx context.Context) {
	ticker := time.NewTicker(10 * time.Minute) // 每10分钟检查一次
	defer ticker.Stop()

	log.Println("Activity reward processor started (every 10 minutes)")

	for {
		select {
		case <-ctx.Done():
			log.Println("Activity reward processor stopped")
			return
		case <-ticker.C:
			if err := s.ProcessExpiredActivities(ctx); err != nil {
				log.Printf("Error processing expired activities: %v", err)
			}
		}
	}
}

// ManualProcessExpiredActivities 手动处理过期活动（用于管理接口）
func (s *ActivityRewardService) ManualProcessExpiredActivities(ctx context.Context) (int, error) {
	// 查找所有已过期但状态仍为已上架的活动
	var activities []models.MerchantActivity
	now := time.Now()

	if err := s.db.WithContext(ctx).
		Where("status = ? AND end_time < ?",
			models.MerchantActivityStatusActive,
			now).
		Find(&activities).Error; err != nil {
		return 0, fmt.Errorf("failed to get expired activities: %w", err)
	}

	processedCount := 0
	for _, activity := range activities {
		if err := s.processActivityRewards(ctx, &activity); err != nil {
			log.Printf("Failed to process rewards for activity %d: %v", activity.ID, err)
			continue
		}

		// 更新活动状态为已过期
		if err := s.db.WithContext(ctx).Model(&activity).
			Update("status", models.MerchantActivityStatusExpired).Error; err != nil {
			log.Printf("Failed to update activity %d status: %v", activity.ID, err)
			continue
		}

		processedCount++
	}

	return processedCount, nil
}

// GetActivityRewardStatus 获取活动奖励发放状态
func (s *ActivityRewardService) GetActivityRewardStatus(ctx context.Context, activityID uint64) (*ActivityRewardStatus, error) {
	var activity models.MerchantActivity
	if err := s.db.WithContext(ctx).First(&activity, activityID).Error; err != nil {
		return nil, fmt.Errorf("failed to get activity: %w", err)
	}

	// 统计参与人数
	var totalParticipants int64
	if err := s.db.WithContext(ctx).Model(&models.MerchantActivityParticipant{}).
		Where("activity_id = ? AND status IN (?)", activityID, []int{1, 2}). // 1:已报名, 2:已完成
		Count(&totalParticipants).Error; err != nil {
		return nil, fmt.Errorf("failed to count participants: %w", err)
	}

	// 统计已发放奖励的人数
	var rewardedParticipants int64
	if err := s.db.WithContext(ctx).Model(&models.MerchantActivityParticipant{}).
		Where("activity_id = ? AND status = ?", activityID, 2). // 2:已完成
		Count(&rewardedParticipants).Error; err != nil {
		return nil, fmt.Errorf("failed to count rewarded participants: %w", err)
	}

	status := &ActivityRewardStatus{
		ActivityID:           activityID,
		ActivityName:         activity.Name,
		ContributionScore:    activity.ContributionScore,
		TotalParticipants:    totalParticipants,
		RewardedParticipants: rewardedParticipants,
		IsExpired:            activity.IsExpired(),
		Status:               activity.Status,
		RewardProcessed:      rewardedParticipants > 0,
	}

	return status, nil
}

// ActivityRewardStatus 活动奖励状态
type ActivityRewardStatus struct {
	ActivityID           uint64                        `json:"activity_id"`
	ActivityName         string                        `json:"activity_name"`
	ContributionScore    int                           `json:"contribution_score"`
	TotalParticipants    int64                         `json:"total_participants"`
	RewardedParticipants int64                         `json:"rewarded_participants"`
	IsExpired            bool                          `json:"is_expired"`
	Status               models.MerchantActivityStatus `json:"status"`
	RewardProcessed      bool                          `json:"reward_processed"`
}
