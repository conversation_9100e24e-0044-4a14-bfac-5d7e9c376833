package services

import (
	"context"
	"fmt"
	"time"
	"wangfujing_admin/internal/models"

	"gorm.io/gorm"
)

// BenefitApplicationService 权益申请服务
type BenefitApplicationService struct {
	db *gorm.DB
}

// parseCustomDateTime 解析自定义时间格式 "2024-01-15 12:00"，明确使用中国时区
func parseCustomDateTime(timeStr string) (*time.Time, error) {
	if timeStr == "" {
		return nil, nil
	}

	// 确保使用中国时区解析时间
	loc, err := time.LoadLocation("Asia/Shanghai")
	if err != nil {
		return nil, fmt.Errorf("时区设置错误: %w", err)
	}

	// 解析时间格式 "2024-01-15 12:00"，明确指定时区
	t, err := time.ParseInLocation("2006-01-02 15:04", timeStr, loc)
	if err != nil {
		return nil, fmt.Errorf("时间格式错误，请使用格式：2024-01-15 12:00")
	}

	return &t, nil
}

// formatCustomDateTime 格式化时间为自定义格式 "2024-01-15 12:00"，确保使用中国时区
func formatCustomDateTime(t *time.Time) string {
	if t == nil {
		return ""
	}
	// 确保使用中国时区
	loc, err := time.LoadLocation("Asia/Shanghai")
	if err != nil {
		// 如果加载时区失败，使用原始时间
		return t.Format("2006-01-02 15:04")
	}
	return t.In(loc).Format("2006-01-02 15:04")
}

// formatStandardDateTime 格式化时间为标准格式 "2024-01-15 12:00:00"，确保使用中国时区
func formatStandardDateTime(t *time.Time) string {
	if t == nil {
		return ""
	}
	// 确保使用中国时区
	loc, err := time.LoadLocation("Asia/Shanghai")
	if err != nil {
		// 如果加载时区失败，使用原始时间
		return t.Format("2006-01-02 15:04:05")
	}
	return t.In(loc).Format("2006-01-02 15:04:05")
}

// NewBenefitApplicationService 创建权益申请服务
func NewBenefitApplicationService(db *gorm.DB) *BenefitApplicationService {
	return &BenefitApplicationService{
		db: db,
	}
}

// CreateBenefitApplicationRequest 创建权益申请请求
type CreateBenefitApplicationRequest struct {
	MerchantID      uint64             `json:"merchant_id" binding:"required"`
	BenefitType     models.BenefitType `json:"benefit_type" binding:"required"`
	Reason          string             `json:"reason" binding:"required,max=500"`
	LatestCloseTime string             `json:"latest_close_time,omitempty" binding:"omitempty"`
}

// ApproveBenefitApplicationRequest 审核权益申请请求
type ApproveBenefitApplicationRequest struct {
	Status models.ApprovalStatus `json:"status" binding:"required,oneof=1 2"`
	Note   string                `json:"note" binding:"required,max=500"`
}

// ReapplyBenefitApplicationRequest 重新申请权益请求
type ReapplyBenefitApplicationRequest struct {
	Reason          string `json:"reason" binding:"required,max=500"`
	LatestCloseTime string `json:"latest_close_time,omitempty" binding:"omitempty"`
}

// CreateBenefitApplication 创建权益申请
func (s *BenefitApplicationService) CreateBenefitApplication(ctx context.Context, req *CreateBenefitApplicationRequest, applicantID uint64) (*CreateBenefitApplicationResponse, error) {
	// 检查申请人信息
	var applicant models.AdminUser
	if err := s.db.WithContext(ctx).First(&applicant, applicantID).Error; err != nil {
		return nil, fmt.Errorf("申请人不存在")
	}

	// 如果是商家用户，验证只能为自己关联的商家申请权益
	if applicant.UserType == models.UserTypeMerchant {
		if applicant.MerchantID == nil {
			return nil, fmt.Errorf("商家用户未关联商家信息")
		}
		if *applicant.MerchantID != req.MerchantID {
			return nil, fmt.Errorf("商家用户只能为自己的商家申请权益")
		}
	}

	// 检查商家是否存在
	var merchant models.Merchant
	if err := s.db.WithContext(ctx).First(&merchant, req.MerchantID).Error; err != nil {
		if err == gorm.ErrRecordNotFound {
			return nil, fmt.Errorf("商家不存在")
		}
		return nil, fmt.Errorf("failed to get merchant: %w", err)
	}

	// 检查是否已有同类型的待审核申请
	var existingApp models.BenefitApplication
	err := s.db.WithContext(ctx).Where("merchant_id = ? AND benefit_type = ? AND approval_status = ?",
		req.MerchantID, req.BenefitType, models.ApprovalStatusPending).First(&existingApp).Error
	if err == nil {
		return nil, fmt.Errorf("该商家已有待审核的同类型权益申请")
	} else if err != gorm.ErrRecordNotFound {
		return nil, fmt.Errorf("failed to check existing application: %w", err)
	}

	// 根据权益类型获取所需分值
	var requiredScore int
	switch req.BenefitType {
	case models.BenefitTypeDelayedClosing:
		// 查询延迟闭店权益的分值要求（全局配置）
		var levelBenefit models.LevelBenefit
		if err := s.db.WithContext(ctx).Where("delayed_closing = ?", true).First(&levelBenefit).Error; err != nil {
			if err == gorm.ErrRecordNotFound {
				return nil, fmt.Errorf("系统未配置延迟闭店权益，请联系管理员配置")
			} else {
				return nil, fmt.Errorf("failed to get level benefit: %w", err)
			}
		} else {
			requiredScore = int(levelBenefit.RequiredScore)
		}
	default:
		return nil, fmt.Errorf("不支持的权益类型")
	}

	// 验证延迟闭店权益的特殊字段
	var latestCloseTime *time.Time
	if req.BenefitType == models.BenefitTypeDelayedClosing {
		if req.LatestCloseTime == "" {
			return nil, fmt.Errorf("申请延迟闭店权益必须填写最晚闭店时间")
		}

		// 解析时间
		parsedTime, err := parseCustomDateTime(req.LatestCloseTime)
		if err != nil {
			return nil, err
		}
		latestCloseTime = parsedTime

		// 验证闭店时间不能是过去的时间
		if parsedTime.Before(time.Now()) {
			return nil, fmt.Errorf("最晚闭店时间不能早于当前时间")
		}
	}

	// 检查商家当前分值是否满足权益要求
	if int(merchant.Score) < requiredScore {
		return nil, fmt.Errorf("商家当前分值(%d)不满足权益要求(%d)，无法申请该权益", int(merchant.Score), requiredScore)
	}

	// 创建权益申请记录
	application := &models.BenefitApplication{
		MerchantID:      req.MerchantID,
		BenefitType:     req.BenefitType,
		RequiredScore:   requiredScore,
		CurrentScore:    int(merchant.Score),
		ApplicantID:     applicantID,
		ApprovalStatus:  models.ApprovalStatusPending,
		Reason:          req.Reason,
		LatestCloseTime: latestCloseTime,
	}

	if err := s.db.WithContext(ctx).Create(application).Error; err != nil {
		return nil, fmt.Errorf("failed to create benefit application: %w", err)
	}

	// 返回简化的响应
	response := &CreateBenefitApplicationResponse{
		ID:              application.ID,
		MerchantID:      application.MerchantID,
		BenefitType:     application.BenefitType,
		RequiredScore:   application.RequiredScore,
		CurrentScore:    application.CurrentScore,
		ApplicantID:     application.ApplicantID,
		ApprovalStatus:  application.ApprovalStatus,
		Reason:          application.Reason,
		LatestCloseTime: formatCustomDateTime(application.LatestCloseTime),
		CreatedAt:       application.CreatedAt,
	}

	return response, nil
}

// ReapplyBenefitApplication 重新申请权益（覆盖已拒绝的申请）
func (s *BenefitApplicationService) ReapplyBenefitApplication(ctx context.Context, id uint64, req *ReapplyBenefitApplicationRequest, applicantID uint64) (*CreateBenefitApplicationResponse, error) {
	// 获取原申请记录
	var application models.BenefitApplication
	if err := s.db.WithContext(ctx).First(&application, id).Error; err != nil {
		if err == gorm.ErrRecordNotFound {
			return nil, fmt.Errorf("申请记录不存在")
		}
		return nil, fmt.Errorf("failed to get benefit application: %w", err)
	}

	// 检查是否可以重新申请
	if !application.CanReapply() {
		return nil, fmt.Errorf("该申请当前状态不允许重新申请")
	}

	// 验证申请人
	if application.ApplicantID != applicantID {
		return nil, fmt.Errorf("只能重新申请自己提交的申请")
	}

	// 验证延迟闭店权益的特殊字段
	var latestCloseTime *time.Time
	if application.BenefitType == models.BenefitTypeDelayedClosing {
		if req.LatestCloseTime == "" {
			return nil, fmt.Errorf("申请延迟闭店权益必须填写最晚闭店时间")
		}

		// 解析时间
		parsedTime, err := parseCustomDateTime(req.LatestCloseTime)
		if err != nil {
			return nil, err
		}
		latestCloseTime = parsedTime

		// 验证闭店时间不能是过去的时间
		if parsedTime.Before(time.Now()) {
			return nil, fmt.Errorf("最晚闭店时间不能早于当前时间")
		}
	}

	// 更新申请记录（覆盖原记录）
	application.Reason = req.Reason
	application.LatestCloseTime = latestCloseTime
	application.ApprovalStatus = models.ApprovalStatusPending
	application.ApproverID = nil
	application.ApprovalNote = ""
	application.ApprovedAt = nil

	if err := s.db.WithContext(ctx).Save(&application).Error; err != nil {
		return nil, fmt.Errorf("failed to update benefit application: %w", err)
	}

	// 返回响应
	response := &CreateBenefitApplicationResponse{
		ID:              application.ID,
		MerchantID:      application.MerchantID,
		BenefitType:     application.BenefitType,
		RequiredScore:   application.RequiredScore,
		CurrentScore:    application.CurrentScore,
		ApplicantID:     application.ApplicantID,
		ApprovalStatus:  application.ApprovalStatus,
		Reason:          application.Reason,
		LatestCloseTime: formatCustomDateTime(application.LatestCloseTime),
		CreatedAt:       application.CreatedAt,
	}

	return response, nil
}

// CreateBenefitApplicationResponse 创建权益申请响应
type CreateBenefitApplicationResponse struct {
	ID              uint64                `json:"id"`
	MerchantID      uint64                `json:"merchant_id"`
	BenefitType     models.BenefitType    `json:"benefit_type"`
	RequiredScore   int                   `json:"required_score"`
	CurrentScore    int                   `json:"current_score"`
	ApplicantID     uint64                `json:"applicant_id"`
	ApprovalStatus  models.ApprovalStatus `json:"approval_status"`
	Reason          string                `json:"reason"`
	LatestCloseTime string                `json:"latest_close_time,omitempty"`
	CreatedAt       time.Time             `json:"created_at"`
}

// MerchantInfo 商家信息
type MerchantInfo struct {
	ID    uint64 `json:"id"`
	Name  string `json:"name"`
	Phone string `json:"phone"`
	Score int64  `json:"score"`
}

// ApplicantInfo 申请人信息
type ApplicantInfo struct {
	ID       uint64 `json:"id"`
	Nickname string `json:"nickname"`
	Phone    string `json:"phone"`
}

// ApproverInfo 审核人信息
type ApproverInfo struct {
	ID       uint64 `json:"id"`
	Nickname string `json:"nickname"`
	Phone    string `json:"phone"`
}

// BenefitApplicationItem 权益申请列表项
type BenefitApplicationItem struct {
	ID              uint64                `json:"id"`
	MerchantID      uint64                `json:"merchant_id"`
	MerchantName    string                `json:"merchant_name"`
	BenefitType     string                `json:"benefit_type"`
	RequiredScore   int                   `json:"required_score"`
	CurrentScore    int                   `json:"current_score"`
	ApprovalStatus  models.ApprovalStatus `json:"approval_status"`
	ApprovalNote    string                `json:"approval_note"`
	Reason          string                `json:"reason"`
	LatestCloseTime string                `json:"latest_close_time,omitempty"`
	CreatedAt       string                `json:"created_at"`
	UpdatedAt       string                `json:"updated_at"`
	ApprovedAt      string                `json:"approved_at"`
	CanReapply      bool                  `json:"can_reapply"`
	IsReadOnly      bool                  `json:"is_read_only"`

	// 关联信息
	Merchant  *MerchantInfo  `json:"merchant,omitempty"`
	Applicant *ApplicantInfo `json:"applicant,omitempty"`
	Approver  *ApproverInfo  `json:"approver,omitempty"`
}

// GetBenefitApplications 获取权益申请列表
func (s *BenefitApplicationService) GetBenefitApplications(ctx context.Context, status models.ApprovalStatus, page, size int) ([]*BenefitApplicationItem, int64, error) {
	var applications []models.BenefitApplication
	var total int64

	// 构建查询条件
	query := s.db.WithContext(ctx).Model(&models.BenefitApplication{}).Where("approval_status = ?", status)

	// 统计总数
	if err := query.Count(&total).Error; err != nil {
		return nil, 0, fmt.Errorf("failed to count benefit applications: %w", err)
	}

	// 分页查询，按最新状态更新时间倒序
	offset := (page - 1) * size
	if err := query.Offset(offset).Limit(size).
		Order("updated_at DESC").
		Find(&applications).Error; err != nil {
		return nil, 0, fmt.Errorf("failed to get benefit applications: %w", err)
	}

	// 收集需要查询的ID
	merchantMap := make(map[uint64]*models.Merchant)
	applicantMap := make(map[uint64]*models.AdminUser)
	approverMap := make(map[uint64]*models.AdminUser)

	var merchantIDs []uint64
	var applicantIDs []uint64
	var approverIDs []uint64

	for _, app := range applications {
		merchantIDs = append(merchantIDs, app.MerchantID)
		applicantIDs = append(applicantIDs, app.ApplicantID)
		if app.ApproverID != nil {
			approverIDs = append(approverIDs, *app.ApproverID)
		}
	}

	// 查询商家信息
	if len(merchantIDs) > 0 {
		var merchants []models.Merchant
		if err := s.db.WithContext(ctx).Where("id IN ?", merchantIDs).Find(&merchants).Error; err == nil {
			for i := range merchants {
				merchantMap[merchants[i].ID] = &merchants[i]
			}
		}
	}

	// 查询申请人信息
	if len(applicantIDs) > 0 {
		var applicants []models.AdminUser
		if err := s.db.WithContext(ctx).Where("id IN ?", applicantIDs).Find(&applicants).Error; err == nil {
			for i := range applicants {
				applicantMap[applicants[i].ID] = &applicants[i]
			}
		}
	}

	// 查询审核人信息
	if len(approverIDs) > 0 {
		var approvers []models.AdminUser
		if err := s.db.WithContext(ctx).Where("id IN ?", approverIDs).Find(&approvers).Error; err == nil {
			for i := range approvers {
				approverMap[approvers[i].ID] = &approvers[i]
			}
		}
	}

	// 构建返回数据
	items := make([]*BenefitApplicationItem, 0, len(applications))
	for _, app := range applications {
		merchant := merchantMap[app.MerchantID]
		applicant := applicantMap[app.ApplicantID]

		merchantName := ""
		var merchantInfo *MerchantInfo
		if merchant != nil {
			merchantName = merchant.Name
			merchantInfo = &MerchantInfo{
				ID:    merchant.ID,
				Name:  merchant.Name,
				Phone: merchant.LoginPhone,
				Score: int64(merchant.Score),
			}
		}

		var applicantInfo *ApplicantInfo
		if applicant != nil {
			applicantInfo = &ApplicantInfo{
				ID:       applicant.ID,
				Nickname: applicant.Nickname,
				Phone:    applicant.Phone,
			}
		}

		var approverInfo *ApproverInfo
		if app.ApproverID != nil {
			if approver := approverMap[*app.ApproverID]; approver != nil {
				approverInfo = &ApproverInfo{
					ID:       approver.ID,
					Nickname: approver.Nickname,
					Phone:    approver.Phone,
				}
			}
		}

		item := &BenefitApplicationItem{
			ID:              app.ID,
			MerchantID:      app.MerchantID,
			MerchantName:    merchantName,
			BenefitType:     app.BenefitType.GetName(),
			RequiredScore:   app.RequiredScore,
			CurrentScore:    app.CurrentScore,
			ApprovalStatus:  app.ApprovalStatus,
			ApprovalNote:    app.ApprovalNote,
			Reason:          app.Reason,
			LatestCloseTime: formatCustomDateTime(app.LatestCloseTime),
			CreatedAt:       formatStandardDateTime(&app.CreatedAt),
			UpdatedAt:       formatStandardDateTime(&app.UpdatedAt),
			ApprovedAt:      formatStandardDateTime(app.ApprovedAt),
			CanReapply:      app.CanReapply(),
			IsReadOnly:      app.IsReadOnly(),
			Merchant:        merchantInfo,
			Applicant:       applicantInfo,
			Approver:        approverInfo,
		}
		items = append(items, item)
	}

	return items, total, nil
}

// GetBenefitApplicationDetail 获取权益申请详情
func (s *BenefitApplicationService) GetBenefitApplicationDetail(ctx context.Context, id uint64) (*BenefitApplicationItem, error) {
	var application models.BenefitApplication
	if err := s.db.WithContext(ctx).First(&application, id).Error; err != nil {
		if err == gorm.ErrRecordNotFound {
			return nil, fmt.Errorf("申请记录不存在")
		}
		return nil, fmt.Errorf("failed to get benefit application: %w", err)
	}

	// 获取商家信息
	var merchant models.Merchant
	merchantName := ""
	var merchantInfo *MerchantInfo
	if err := s.db.WithContext(ctx).First(&merchant, application.MerchantID).Error; err == nil {
		merchantName = merchant.Name
		merchantInfo = &MerchantInfo{
			ID:    merchant.ID,
			Name:  merchant.Name,
			Phone: merchant.LoginPhone,
			Score: int64(merchant.Score),
		}
	}

	// 获取申请人信息
	var applicant models.AdminUser
	var applicantInfo *ApplicantInfo
	if err := s.db.WithContext(ctx).First(&applicant, application.ApplicantID).Error; err == nil {
		applicantInfo = &ApplicantInfo{
			ID:       applicant.ID,
			Nickname: applicant.Nickname,
			Phone:    applicant.Phone,
		}
	}

	// 获取审核人信息
	var approverInfo *ApproverInfo
	if application.ApproverID != nil {
		var approver models.AdminUser
		if err := s.db.WithContext(ctx).First(&approver, *application.ApproverID).Error; err == nil {
			approverInfo = &ApproverInfo{
				ID:       approver.ID,
				Nickname: approver.Nickname,
				Phone:    approver.Phone,
			}
		}
	}

	item := &BenefitApplicationItem{
		ID:              application.ID,
		MerchantID:      application.MerchantID,
		MerchantName:    merchantName,
		BenefitType:     application.BenefitType.GetName(),
		RequiredScore:   application.RequiredScore,
		CurrentScore:    application.CurrentScore,
		ApprovalStatus:  application.ApprovalStatus,
		ApprovalNote:    application.ApprovalNote,
		Reason:          application.Reason,
		LatestCloseTime: formatCustomDateTime(application.LatestCloseTime),
		CreatedAt:       formatStandardDateTime(&application.CreatedAt),
		UpdatedAt:       formatStandardDateTime(&application.UpdatedAt),
		ApprovedAt:      formatStandardDateTime(application.ApprovedAt),
		CanReapply:      application.CanReapply(),
		IsReadOnly:      application.IsReadOnly(),
		Merchant:        merchantInfo,
		Applicant:       applicantInfo,
		Approver:        approverInfo,
	}

	return item, nil
}

// ApproveBenefitApplication 审核权益申请
func (s *BenefitApplicationService) ApproveBenefitApplication(ctx context.Context, id uint64, req *ApproveBenefitApplicationRequest, approverID uint64) error {
	return s.db.WithContext(ctx).Transaction(func(tx *gorm.DB) error {
		// 获取申请记录
		var application models.BenefitApplication
		if err := tx.First(&application, id).Error; err != nil {
			if err == gorm.ErrRecordNotFound {
				return fmt.Errorf("申请记录不存在")
			}
			return fmt.Errorf("failed to get benefit application: %w", err)
		}

		// 检查状态
		if application.ApprovalStatus != models.ApprovalStatusPending {
			return fmt.Errorf("该申请已被审核，无法重复审核")
		}

		// 如果是通过，检查商家当前分值是否满足要求
		if req.Status == models.ApprovalStatusApproved {
			var merchant models.Merchant
			if err := tx.First(&merchant, application.MerchantID).Error; err != nil {
				return fmt.Errorf("failed to get merchant: %w", err)
			}

			// 获取当前最新的权益要求分值
			var levelBenefit models.LevelBenefit
			currentRequiredScore := application.RequiredScore // 默认使用申请时的分值
			if err := tx.First(&levelBenefit).Error; err == nil {
				switch application.BenefitType {
				case models.BenefitTypeDelayedClosing:
					// 检查是否启用延迟闭店权益，如果启用则使用当前配置的分值要求
					if levelBenefit.DelayedClosing {
						currentRequiredScore = levelBenefit.RequiredScore
					}
				}
			}

			if int(merchant.Score) < currentRequiredScore {
				return fmt.Errorf("商家当前分值(%d)不满足权益要求(%d)", int(merchant.Score), currentRequiredScore)
			}

			// 更新商家权益状态
			switch application.BenefitType {
			case models.BenefitTypeDelayedClosing:
				if err := tx.Model(&merchant).Update("delayed_closing_benefit", true).Error; err != nil {
					return fmt.Errorf("failed to update merchant benefit: %w", err)
				}
			}
		}

		// 更新申请记录
		now := time.Now()
		updates := map[string]interface{}{
			"approval_status": req.Status,
			"approver_id":     approverID,
			"approval_note":   req.Note,
			"approved_at":     &now,
		}

		if err := tx.Model(&application).Updates(updates).Error; err != nil {
			return fmt.Errorf("failed to update benefit application: %w", err)
		}

		return nil
	})
}

// GetBenefitApplicationsWithOptionalStatus 获取权益申请列表（可选状态筛选）
func (s *BenefitApplicationService) GetBenefitApplicationsWithOptionalStatus(ctx context.Context, status *models.ApprovalStatus, page, size int) ([]*BenefitApplicationItem, int64, error) {
	var applications []models.BenefitApplication
	var total int64

	// 构建查询条件
	query := s.db.WithContext(ctx).Model(&models.BenefitApplication{})

	// 如果指定了状态，则按状态筛选
	if status != nil {
		query = query.Where("approval_status = ?", *status)
	}

	// 统计总数
	if err := query.Count(&total).Error; err != nil {
		return nil, 0, fmt.Errorf("failed to count benefit applications: %w", err)
	}

	// 分页查询，按最新状态更新时间倒序
	offset := (page - 1) * size
	if err := query.Offset(offset).Limit(size).
		Order("updated_at DESC").
		Find(&applications).Error; err != nil {
		return nil, 0, fmt.Errorf("failed to get benefit applications: %w", err)
	}

	// 收集需要查询的ID
	merchantMap := make(map[uint64]*models.Merchant)
	applicantMap := make(map[uint64]*models.AdminUser)
	approverMap := make(map[uint64]*models.AdminUser)

	var merchantIDs []uint64
	var applicantIDs []uint64
	var approverIDs []uint64

	for _, app := range applications {
		merchantIDs = append(merchantIDs, app.MerchantID)
		applicantIDs = append(applicantIDs, app.ApplicantID)
		if app.ApproverID != nil {
			approverIDs = append(approverIDs, *app.ApproverID)
		}
	}

	// 查询商家信息
	if len(merchantIDs) > 0 {
		var merchants []models.Merchant
		if err := s.db.WithContext(ctx).Where("id IN ?", merchantIDs).Find(&merchants).Error; err == nil {
			for i := range merchants {
				merchantMap[merchants[i].ID] = &merchants[i]
			}
		}
	}

	// 查询申请人信息
	if len(applicantIDs) > 0 {
		var applicants []models.AdminUser
		if err := s.db.WithContext(ctx).Where("id IN ?", applicantIDs).Find(&applicants).Error; err == nil {
			for i := range applicants {
				applicantMap[applicants[i].ID] = &applicants[i]
			}
		}
	}

	// 查询审核人信息
	if len(approverIDs) > 0 {
		var approvers []models.AdminUser
		if err := s.db.WithContext(ctx).Where("id IN ?", approverIDs).Find(&approvers).Error; err == nil {
			for i := range approvers {
				approverMap[approvers[i].ID] = &approvers[i]
			}
		}
	}

	// 构建返回数据
	items := make([]*BenefitApplicationItem, 0, len(applications))
	for _, app := range applications {
		merchant := merchantMap[app.MerchantID]
		applicant := applicantMap[app.ApplicantID]

		merchantName := ""
		var merchantInfo *MerchantInfo
		if merchant != nil {
			merchantName = merchant.Name
			merchantInfo = &MerchantInfo{
				ID:    merchant.ID,
				Name:  merchant.Name,
				Phone: merchant.LoginPhone,
				Score: int64(merchant.Score),
			}
		}

		var applicantInfo *ApplicantInfo
		if applicant != nil {
			applicantInfo = &ApplicantInfo{
				ID:       applicant.ID,
				Nickname: applicant.Nickname,
				Phone:    applicant.Phone,
			}
		}

		var approverInfo *ApproverInfo
		if app.ApproverID != nil {
			if approver := approverMap[*app.ApproverID]; approver != nil {
				approverInfo = &ApproverInfo{
					ID:       approver.ID,
					Nickname: approver.Nickname,
					Phone:    approver.Phone,
				}
			}
		}

		item := &BenefitApplicationItem{
			ID:              app.ID,
			MerchantID:      app.MerchantID,
			MerchantName:    merchantName,
			BenefitType:     app.BenefitType.GetName(),
			RequiredScore:   app.RequiredScore,
			CurrentScore:    app.CurrentScore,
			ApprovalStatus:  app.ApprovalStatus,
			ApprovalNote:    app.ApprovalNote,
			Reason:          app.Reason,
			LatestCloseTime: formatCustomDateTime(app.LatestCloseTime),
			CreatedAt:       formatStandardDateTime(&app.CreatedAt),
			UpdatedAt:       formatStandardDateTime(&app.UpdatedAt),
			ApprovedAt:      formatStandardDateTime(app.ApprovedAt),
			CanReapply:      app.CanReapply(),
			IsReadOnly:      app.IsReadOnly(),
			Merchant:        merchantInfo,
			Applicant:       applicantInfo,
			Approver:        approverInfo,
		}
		items = append(items, item)
	}

	return items, total, nil
}
