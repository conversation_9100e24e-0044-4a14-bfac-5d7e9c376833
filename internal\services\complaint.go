package services

import (
	"context"

	"time"
	"wangfujing_admin/internal/models"

	"gorm.io/gorm"
)

// ComplaintService 客诉服务
type ComplaintService struct {
	db *gorm.DB
}

// NewComplaintService 创建客诉服务
func NewComplaintService(db *gorm.DB) *ComplaintService {
	return &ComplaintService{
		db: db,
	}
}

// GetComplaints 获取客诉列表
func (s *ComplaintService) GetComplaints(ctx context.Context, page, size int, merchantID string, status *models.ApprovalStatus) ([]*models.Complaint, int64, error) {
	var complaints []*models.Complaint
	var total int64

	query := s.db.WithContext(ctx).Model(&models.Complaint{})

	// 添加筛选条件
	if merchantID != "" {
		query = query.Where("merchant_id = ?", merchantID)
	}
	if status != nil {
		query = query.Where("approval_status = ?", *status)
	}

	// 获取总数
	if err := query.Count(&total).Error; err != nil {
		return nil, 0, err
	}

	// 分页查询
	offset := (page - 1) * size
	if err := query.Preload("Merchant.Floor").Preload("Merchant").Preload("Submitter").Preload("Approver").
		Offset(offset).Limit(size).Order("created_at DESC").Find(&complaints).Error; err != nil {
		return nil, 0, err
	}

	return complaints, total, nil
}

// GetComplaintByID 根据ID获取客诉
func (s *ComplaintService) GetComplaintByID(ctx context.Context, id string) (*models.Complaint, error) {
	var complaint models.Complaint
	if err := s.db.WithContext(ctx).Preload("Merchant.Floor").Preload("Merchant").Preload("Submitter").Preload("Approver").
		First(&complaint, "id = ?", id).Error; err != nil {
		if err == gorm.ErrRecordNotFound {
			return nil, err
		}
		return nil, err
	}
	return &complaint, nil
}

// CreateComplaint 创建客诉
func (s *ComplaintService) CreateComplaint(ctx context.Context, complaint *models.Complaint) error {
	return s.db.WithContext(ctx).Create(complaint).Error
}

// UpdateComplaint 更新客诉
func (s *ComplaintService) UpdateComplaint(ctx context.Context, id string, userID string, updates map[string]interface{}) error {
	// 检查是否只有已拒绝的客诉可以编辑
	var complaint models.Complaint
	if err := s.db.WithContext(ctx).First(&complaint, "id = ? AND submitter_id = ?", id, userID).Error; err != nil {
		return err
	}

	// 只有已拒绝的客诉可以编辑
	if complaint.ApprovalStatus != models.ApprovalStatusRejected {
		return gorm.ErrRecordNotFound
	}

	// 重新提交时重置审核状态
	updates["approval_status"] = models.ApprovalStatusPending
	updates["approver_id"] = nil
	updates["approval_note"] = ""
	updates["approved_at"] = nil

	return s.db.WithContext(ctx).Model(&models.Complaint{}).Where("id = ?", id).Updates(updates).Error
}

// ApproveComplaint 审核通过客诉
func (s *ComplaintService) ApproveComplaint(ctx context.Context, id string, approverID string, note string) error {
	// 获取客诉信息
	var complaint models.Complaint
	if err := s.db.WithContext(ctx).Preload("Merchant").First(&complaint, "id = ?", id).Error; err != nil {
		return err
	}

	// 获取默认扣分分值
	var scoreRule models.ScoreRule
	if err := s.db.WithContext(ctx).First(&scoreRule).Error; err != nil {
		return err
	}

	// 开始事务
	return s.db.WithContext(ctx).Transaction(func(tx *gorm.DB) error {
		// 更新客诉状态，记录实际扣减分值
		now := time.Now()
		updates := map[string]interface{}{
			"approval_status": models.ApprovalStatusApproved,
			"approver_id":     approverID,
			"approval_note":   note,
			"approved_at":     now,
			"deduct_score":    scoreRule.DefaultScore, // 记录实际扣减分值
		}

		if err := tx.Model(&models.Complaint{}).Where("id = ?", id).Updates(updates).Error; err != nil {
			return err
		}

		// 扣减商家分数
		if scoreRule.DefaultScore > 0 {
			if err := tx.Model(&models.Merchant{}).Where("id = ?", complaint.MerchantID).
				Update("score", gorm.Expr("score - ?", scoreRule.DefaultScore)).Error; err != nil {
				return err
			}
		}

		return nil
	})
}

// RejectComplaint 审核拒绝客诉
func (s *ComplaintService) RejectComplaint(ctx context.Context, id string, approverID string, note string) error {
	now := time.Now()
	updates := map[string]interface{}{
		"approval_status": models.ApprovalStatusRejected,
		"approver_id":     approverID,
		"approval_note":   note,
		"approved_at":     now,
	}

	return s.db.WithContext(ctx).Model(&models.Complaint{}).Where("id = ?", id).Updates(updates).Error
}
