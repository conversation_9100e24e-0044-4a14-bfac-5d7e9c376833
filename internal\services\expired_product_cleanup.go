package services

import (
	"context"
	"fmt"
	"log"
	"time"
	"wangfujing_admin/internal/models"

	"github.com/redis/go-redis/v9"
	"gorm.io/gorm"
)

// ExpiredProductCleanupService 过期商品清理服务
type ExpiredProductCleanupService struct {
	db    *gorm.DB
	redis *redis.Client
}

// NewExpiredProductCleanupService 创建过期商品清理服务
func NewExpiredProductCleanupService(db *gorm.DB, redis *redis.Client) *ExpiredProductCleanupService {
	return &ExpiredProductCleanupService{
		db:    db,
		redis: redis,
	}
}

// StartCleanupScheduler 启动清理调度器
func (s *ExpiredProductCleanupService) StartCleanupScheduler(ctx context.Context) {
	log.Printf("Starting expired product cleanup scheduler...")

	// 立即执行一次清理
	go func() {
		if err := s.CleanupExpiredProducts(ctx); err != nil {
			log.Printf("Failed to run initial cleanup: %v", err)
		}
	}()

	// 设置定时器，每小时执行一次清理
	ticker := time.NewTicker(1 * time.Hour)
	go func() {
		defer ticker.Stop()
		for {
			select {
			case <-ctx.Done():
				log.Printf("Expired product cleanup scheduler stopped")
				return
			case <-ticker.C:
				if err := s.CleanupExpiredProducts(ctx); err != nil {
					log.Printf("Failed to run scheduled cleanup: %v", err)
				}
			}
		}
	}()
}

// CleanupExpiredProducts 清理过期商品的Redis数据
func (s *ExpiredProductCleanupService) CleanupExpiredProducts(ctx context.Context) error {
	log.Printf("Starting expired product cleanup at %s", time.Now().Format("2006-01-02 15:04:05"))

	// 获取所有已过期的商品
	now := time.Now()
	var expiredProducts []models.Product
	if err := s.db.WithContext(ctx).Where("valid_to IS NOT NULL AND valid_to < ?", now).
		Find(&expiredProducts).Error; err != nil {
		return fmt.Errorf("failed to get expired products: %w", err)
	}

	if len(expiredProducts) == 0 {
		log.Printf("No expired products found")
		return nil
	}

	successCount := 0
	errorCount := 0

	// 清理每个过期商品的Redis数据
	for _, product := range expiredProducts {
		if err := s.cleanupProductRedisData(ctx, product.ID); err != nil {
			log.Printf("Failed to cleanup Redis data for product %d: %v", product.ID, err)
			errorCount++
		} else {
			log.Printf("Successfully cleaned up Redis data for expired product %d (%s)", 
				product.ID, product.Name)
			successCount++
		}
	}

	log.Printf("Expired product cleanup completed: %d success, %d errors", successCount, errorCount)
	return nil
}

// cleanupProductRedisData 清理单个商品的Redis数据
func (s *ExpiredProductCleanupService) cleanupProductRedisData(ctx context.Context, productID uint64) error {
	// 定义需要清理的Redis键
	keysToDelete := []string{
		fmt.Sprintf("seckill:product:%d:stock", productID),      // 库存信息
		fmt.Sprintf("seckill:product:%d:count", productID),      // 库存计数
		fmt.Sprintf("seckill:product:%d:queue", productID),      // 抢购队列
		fmt.Sprintf("seckill:product:%d:processing", productID), // 处理中队列
		fmt.Sprintf("seckill:product:%d:results:*", productID),  // 处理结果（通配符）
	}

	// 删除基础键
	for _, key := range keysToDelete[:4] { // 前4个是精确键名
		if err := s.redis.Del(ctx, key).Err(); err != nil {
			log.Printf("Warning: failed to delete key %s: %v", key, err)
		}
	}

	// 删除结果键（使用模式匹配）
	resultPattern := fmt.Sprintf("seckill:product:%d:results:*", productID)
	resultKeys, err := s.redis.Keys(ctx, resultPattern).Result()
	if err != nil {
		log.Printf("Warning: failed to get result keys for product %d: %v", productID, err)
	} else if len(resultKeys) > 0 {
		if err := s.redis.Del(ctx, resultKeys...).Err(); err != nil {
			log.Printf("Warning: failed to delete result keys for product %d: %v", productID, err)
		}
	}

	return nil
}

// CleanupSpecificProduct 清理指定商品的Redis数据（手动调用）
func (s *ExpiredProductCleanupService) CleanupSpecificProduct(ctx context.Context, productID uint64) error {
	// 检查商品是否确实已过期
	var product models.Product
	if err := s.db.WithContext(ctx).First(&product, productID).Error; err != nil {
		return fmt.Errorf("failed to get product: %w", err)
	}

	now := time.Now()
	if product.ValidTo == nil || now.Before(*product.ValidTo) {
		return fmt.Errorf("product %d is not expired yet", productID)
	}

	return s.cleanupProductRedisData(ctx, productID)
}

// GetExpiredProductsInfo 获取过期商品信息
func (s *ExpiredProductCleanupService) GetExpiredProductsInfo(ctx context.Context) (map[string]interface{}, error) {
	now := time.Now()
	
	// 获取过期商品数量
	var expiredCount int64
	if err := s.db.WithContext(ctx).Model(&models.Product{}).
		Where("valid_to IS NOT NULL AND valid_to < ?", now).
		Count(&expiredCount).Error; err != nil {
		return nil, fmt.Errorf("failed to count expired products: %w", err)
	}

	// 获取即将过期的商品数量（24小时内）
	tomorrow := now.Add(24 * time.Hour)
	var soonExpiredCount int64
	if err := s.db.WithContext(ctx).Model(&models.Product{}).
		Where("valid_to IS NOT NULL AND valid_to > ? AND valid_to <= ?", now, tomorrow).
		Count(&soonExpiredCount).Error; err != nil {
		return nil, fmt.Errorf("failed to count soon expired products: %w", err)
	}

	// 获取有效商品数量
	var activeCount int64
	if err := s.db.WithContext(ctx).Model(&models.Product{}).
		Where("status = ? AND approval_status = ? AND (valid_to IS NULL OR valid_to > ?)", 
			models.StatusActive, models.ApprovalStatusApproved, now).
		Count(&activeCount).Error; err != nil {
		return nil, fmt.Errorf("failed to count active products: %w", err)
	}

	return map[string]interface{}{
		"expired_count":      expiredCount,
		"soon_expired_count": soonExpiredCount,
		"active_count":       activeCount,
		"check_time":         now.Format("2006-01-02 15:04:05"),
	}, nil
}

// ForceCleanupAllExpired 强制清理所有过期商品（管理员手动触发）
func (s *ExpiredProductCleanupService) ForceCleanupAllExpired(ctx context.Context) error {
	log.Printf("Force cleanup all expired products triggered")
	return s.CleanupExpiredProducts(ctx)
}
