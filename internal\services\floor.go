package services

import (
	"context"
	"errors"
	"fmt"

	"wangfujing_admin/internal/models"

	"gorm.io/gorm"
)

// FloorService 楼层服务
type FloorService struct {
	db *gorm.DB
}

// NewFloorService 创建楼层服务
func NewFloorService(db *gorm.DB) *FloorService {
	return &FloorService{
		db: db,
	}
}

// CreateFloor 创建楼层
func (s *FloorService) CreateFloor(ctx context.Context, floor *models.Floor) error {
	// 检查序号是否重复
	var count int64
	if err := s.db.WithContext(ctx).Model(&models.Floor{}).Where("sort = ?", floor.Sort).Count(&count).Error; err != nil {
		return fmt.Errorf("failed to check sort uniqueness: %w", err)
	}
	if count > 0 {
		return errors.New("sort number already exists")
	}

	if err := s.db.WithContext(ctx).Create(floor).Error; err != nil {
		return fmt.Errorf("failed to create floor: %w", err)
	}
	return nil
}

// GetFloorByID 根据ID获取楼层
func (s *FloorService) GetFloorByID(ctx context.Context, id string) (*models.Floor, error) {
	var floor models.Floor
	if err := s.db.WithContext(ctx).First(&floor, "id = ?", id).Error; err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return nil, errors.New("floor not found")
		}
		return nil, fmt.Errorf("failed to get floor: %w", err)
	}
	return &floor, nil
}

// GetFloors 获取楼层列表
func (s *FloorService) GetFloors(ctx context.Context, page, size int, status *models.Status) ([]*models.Floor, int64, error) {
	var floors []*models.Floor
	var total int64

	query := s.db.WithContext(ctx).Model(&models.Floor{})

	if status != nil {
		query = query.Where("status = ?", *status)
	}

	if err := query.Count(&total).Error; err != nil {
		return nil, 0, fmt.Errorf("failed to count floors: %w", err)
	}

	offset := (page - 1) * size
	if err := query.Offset(offset).Limit(size).Order("sort ASC, created_at DESC").Find(&floors).Error; err != nil {
		return nil, 0, fmt.Errorf("failed to get floors: %w", err)
	}

	return floors, total, nil
}

// UpdateFloor 更新楼层
func (s *FloorService) UpdateFloor(ctx context.Context, id string, updates map[string]interface{}) error {
	// 如果更新序号，检查是否重复
	if sort, exists := updates["sort"]; exists {
		var count int64
		if err := s.db.WithContext(ctx).Model(&models.Floor{}).Where("sort = ? AND id != ?", sort, id).Count(&count).Error; err != nil {
			return fmt.Errorf("failed to check sort uniqueness: %w", err)
		}
		if count > 0 {
			return errors.New("sort number already exists")
		}
	}

	result := s.db.WithContext(ctx).Model(&models.Floor{}).Where("id = ?", id).Updates(updates)
	if result.Error != nil {
		return fmt.Errorf("failed to update floor: %w", result.Error)
	}
	if result.RowsAffected == 0 {
		return errors.New("floor not found")
	}
	return nil
}

// DeleteFloor 删除楼层
func (s *FloorService) DeleteFloor(ctx context.Context, id string) error {
	// 检查是否有商家在该楼层
	var count int64
	if err := s.db.WithContext(ctx).Model(&models.Merchant{}).Where("floor_id = ?", id).Count(&count).Error; err != nil {
		return fmt.Errorf("failed to check floor usage: %w", err)
	}
	if count > 0 {
		return errors.New("floor has merchants, cannot delete")
	}

	result := s.db.WithContext(ctx).Delete(&models.Floor{}, "id = ?", id)
	if result.Error != nil {
		return fmt.Errorf("failed to delete floor: %w", result.Error)
	}
	if result.RowsAffected == 0 {
		return errors.New("floor not found")
	}
	return nil
}
