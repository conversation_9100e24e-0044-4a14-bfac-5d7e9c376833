package services

import (
	"fmt"
	"math/rand"
	"time"

	"wangfujing_admin/internal/models"
	"wangfujing_admin/pkg/logger"

	"gorm.io/gorm"
)

// GameService 游戏服务
type GameService struct {
	db *gorm.DB
}

// NewGameService 创建游戏服务实例
func NewGameService(db *gorm.DB) *GameService {
	return &GameService{db: db}
}

// GameOption 游戏选项
type GameOption struct {
	Position int    `json:"position"` // 位置 1-6
	Text     string `json:"text"`     // 显示文本
	IsWin    bool   `json:"is_win"`   // 是否为中奖选项
}

// GameOptionsResponse 游戏选项响应
type GameOptionsResponse struct {
	GameType models.GameType `json:"game_type"` // 游戏类型
	Options  []GameOption    `json:"options"`   // 选项列表
}

// LotteryRequest 抽奖请求
type LotteryRequest struct {
	ActivityID uint64 `json:"activity_id" binding:"required"`
}

// LotteryResponse 抽奖响应
type LotteryResponse struct {
	Position     int  `json:"position"`      // 抽中位置
	IsWin        bool `json:"is_win"`        // 是否中奖
	RewardPoints int  `json:"reward_points"` // 奖励积分
}

// GetGameOptions 获取游戏选项
func (s *GameService) GetGameOptions(activityID uint64) (*GameOptionsResponse, error) {
	// 获取活动信息
	var activity models.MarketingActivity
	if err := s.db.First(&activity, activityID).Error; err != nil {
		if err == gorm.ErrRecordNotFound {
			return nil, fmt.Errorf("activity not found")
		}
		return nil, fmt.Errorf("failed to get activity: %w", err)
	}

	// 检查是否为游戏类型
	if activity.Category != models.MarketingCategoryGame {
		return nil, fmt.Errorf("activity is not a game type")
	}

	// 生成固定的游戏选项
	options := []GameOption{
		{Position: 1, Text: "积分大放送", IsWin: true},
		{Position: 2, Text: "谢谢参与", IsWin: false},
		{Position: 3, Text: "积分大放送", IsWin: true},
		{Position: 4, Text: "积分大放送", IsWin: true},
		{Position: 5, Text: "谢谢参与", IsWin: false},
		{Position: 6, Text: "积分大放送", IsWin: true},
	}

	return &GameOptionsResponse{
		GameType: activity.GameType,
		Options:  options,
	}, nil
}

// PlayLottery 进行抽奖
func (s *GameService) PlayLottery(userID uint64, req *LotteryRequest) (*LotteryResponse, error) {
	// 获取活动信息
	var activity models.MarketingActivity
	if err := s.db.First(&activity, req.ActivityID).Error; err != nil {
		if err == gorm.ErrRecordNotFound {
			return nil, fmt.Errorf("activity not found")
		}
		return nil, fmt.Errorf("failed to get activity: %w", err)
	}

	// 检查是否为游戏类型
	if activity.Category != models.MarketingCategoryGame {
		return nil, fmt.Errorf("activity is not a game type")
	}

	// 验证参与条件
	if err := s.validateParticipation(userID, &activity); err != nil {
		return nil, err
	}

	// 根据概率决定是否中奖
	rand.Seed(time.Now().UnixNano())
	randomNum := rand.Intn(100) + 1 // 1-100
	isWin := randomNum <= activity.WinProbability

	// 确定抽中位置
	var position int
	if isWin {
		// 中奖时随机选择中奖位置 (1, 3, 4, 6)
		winPositions := []int{1, 3, 4, 6}
		position = winPositions[rand.Intn(len(winPositions))]
	} else {
		// 未中奖时随机选择未中奖位置 (2, 5)
		losePositions := []int{2, 5}
		position = losePositions[rand.Intn(len(losePositions))]
	}

	// 计算奖励积分
	rewardPoints := 0
	if isWin {
		rewardPoints = activity.RewardPoints
	}

	// 开启事务处理抽奖结果
	err := s.db.Transaction(func(tx *gorm.DB) error {
		// 记录游戏参与记录
		gameRecord := &models.GameParticipationRecord{
			ActivityID:   req.ActivityID,
			UserID:       userID,
			GameType:     activity.GameType,
			Position:     position,
			IsWin:        isWin,
			RewardPoints: rewardPoints,
		}
		if err := tx.Create(gameRecord).Error; err != nil {
			return fmt.Errorf("failed to create game record: %w", err)
		}

		// 更新营销活动参与记录
		if err := s.updateMarketingParticipation(tx, userID, &activity); err != nil {
			return err
		}

		// 如果中奖，发放积分奖励
		if isWin && rewardPoints > 0 {
			if err := s.rewardPoints(tx, userID, rewardPoints, &activity); err != nil {
				return err
			}
		}

		return nil
	})

	if err != nil {
		return nil, err
	}

	return &LotteryResponse{
		Position:     position,
		IsWin:        isWin,
		RewardPoints: rewardPoints,
	}, nil
}

// validateParticipation 验证参与条件
func (s *GameService) validateParticipation(userID uint64, activity *models.MarketingActivity) error {
	// 检查活动状态
	if activity.Status != models.MarketingActivityStatusActive {
		return fmt.Errorf("activity is not active")
	}

	// 检查活动时间
	now := time.Now()
	if activity.StartTime != nil && now.Before(*activity.StartTime) {
		return fmt.Errorf("activity has not started yet")
	}
	if activity.EndTime != nil && now.After(*activity.EndTime) {
		return fmt.Errorf("activity has ended")
	}

	// 获取用户信息
	var user models.User
	if err := s.db.First(&user, userID).Error; err != nil {
		return fmt.Errorf("user not found")
	}

	// 检查用户积分是否满足参与条件
	logger.Infof("user.Points", user.Points)
	logger.Infof("activity.RequiredPoints", activity.RequiredPoints)
	if user.Points < activity.RequiredPoints {
		return fmt.Errorf("insufficient points, required: %d, current: %d",
			activity.RequiredPoints, user.Points)
	}

	// 检查每日参与次数限制
	today := time.Now().Format("2006-01-02")
	var participant models.MarketingActivityParticipant
	err := s.db.Where("activity_id = ? AND user_id = ? AND participate_date = ?",
		activity.ID, userID, today).First(&participant).Error

	if err == nil {
		// 找到今日参与记录，检查次数
		if participant.ParticipateCount >= activity.DailyLimit {
			return fmt.Errorf("daily participation limit exceeded")
		}
	} else if err != gorm.ErrRecordNotFound {
		return fmt.Errorf("failed to check participation: %w", err)
	}

	return nil
}

// updateMarketingParticipation 更新营销活动参与记录
func (s *GameService) updateMarketingParticipation(tx *gorm.DB, userID uint64, activity *models.MarketingActivity) error {
	today := time.Now().Format("2006-01-02")
	now := time.Now()

	var participant models.MarketingActivityParticipant
	err := tx.Where("activity_id = ? AND user_id = ? AND participate_date = ?",
		activity.ID, userID, today).First(&participant).Error

	if err == gorm.ErrRecordNotFound {
		// 创建新的参与记录
		participant = models.MarketingActivityParticipant{
			ActivityID:        activity.ID,
			UserID:            userID,
			ParticipateDate:   today,
			ParticipateCount:  1,
			TotalRewardPoints: activity.RewardPoints,
			LastParticipateAt: &now,
		}
		return tx.Create(&participant).Error
	} else if err != nil {
		return fmt.Errorf("failed to get participation record: %w", err)
	} else {
		// 更新现有记录
		updates := map[string]interface{}{
			"participate_count":   participant.ParticipateCount + 1,
			"total_reward_points": participant.TotalRewardPoints + activity.RewardPoints,
			"last_participate_at": &now,
		}
		return tx.Model(&participant).Updates(updates).Error
	}
}

// rewardPoints 发放积分奖励
func (s *GameService) rewardPoints(tx *gorm.DB, userID uint64, points int, activity *models.MarketingActivity) error {
	// 更新用户积分
	if err := tx.Model(&models.User{}).Where("id = ?", userID).
		UpdateColumn("points", gorm.Expr("points + ?", points)).Error; err != nil {
		return fmt.Errorf("failed to update user points: %w", err)
	}

	// 获取更新后的用户积分余额
	var user models.User
	if err := tx.Select("points").First(&user, userID).Error; err != nil {
		return fmt.Errorf("failed to get user points: %w", err)
	}

	// 创建积分记录
	activityID := activity.ID
	pointsRecord := &models.PointsRecord{
		UserID:      userID,
		Type:        models.PointsTypeEarn,
		Points:      points,
		Balance:     user.Points,
		Source:      "游戏奖励",
		SourceID:    &activityID,
		RelatedID:   &activityID,
		RelatedType: "game_activity",
		Description: fmt.Sprintf("参与游戏活动「%s」获得积分", activity.Name),
	}

	return tx.Create(pointsRecord).Error
}
