package services

import (
	"context"
	"fmt"
	"sync"
	"time"

	"wangfujing_admin/internal/config"

	"github.com/redis/go-redis/v9"
)

// GlobalRateLimiter 全局限流器
type GlobalRateLimiter struct {
	redis  *redis.Client
	config *config.SeckillConfig
	mu     sync.RWMutex
}

// NewGlobalRateLimiter 创建全局限流器
func NewGlobalRateLimiter(redis *redis.Client, config *config.SeckillConfig) *GlobalRateLimiter {
	return &GlobalRateLimiter{
		redis:  redis,
		config: config,
	}
}

// AllowGlobal 全局限流检查
func (g *GlobalRateLimiter) AllowGlobal(ctx context.Context) (bool, error) {
	key := "seckill:global:ratelimit"
	return g.allowWithConfig(ctx, key, g.config.GlobalRateLimit, g.config.GlobalBucketSize)
}

// AllowProduct 商品级别限流检查
func (g *GlobalRateLimiter) AllowProduct(ctx context.Context, productID uint64) (bool, error) {
	key := fmt.Sprintf("seckill:product:%d:ratelimit", productID)
	return g.allowWithConfig(ctx, key, g.config.ProductRateLimit, g.config.ProductBucketSize)
}

// allowWithConfig 使用指定配置进行限流检查
func (g *GlobalRateLimiter) allowWithConfig(ctx context.Context, key string, rate, capacity int) (bool, error) {
	luaScript := `
		local key = KEYS[1]
		local rate = tonumber(ARGV[1])
		local capacity = tonumber(ARGV[2])
		local now = tonumber(ARGV[3])
		local window = tonumber(ARGV[4])
		
		-- 获取当前令牌数和上次更新时间
		local bucket = redis.call('HMGET', key, 'tokens', 'last_time')
		local tokens = tonumber(bucket[1]) or capacity
		local last_time = tonumber(bucket[2]) or now
		
		-- 计算时间差(毫秒)
		local elapsed = now - last_time
		
		-- 计算新增令牌数
		local new_tokens = math.min(capacity, tokens + (elapsed * rate / 1000))
		
		-- 检查是否有足够令牌
		if new_tokens < 1 then
			-- 更新最后访问时间但不扣减令牌
			redis.call('HSET', key, 'last_time', now)
			redis.call('EXPIRE', key, window)
			return 0
		end
		
		-- 扣减一个令牌
		new_tokens = new_tokens - 1
		
		-- 更新令牌桶状态
		redis.call('HMSET', key, 'tokens', new_tokens, 'last_time', now)
		redis.call('EXPIRE', key, window)
		
		return 1
	`

	now := time.Now().UnixMilli()
	window := 3600 // 1小时过期时间

	result, err := g.redis.Eval(ctx, luaScript, []string{key}, rate, capacity, now, window).Int()
	if err != nil {
		return false, fmt.Errorf("rate limit check failed: %w", err)
	}

	return result == 1, nil
}

// GetGlobalStats 获取全局限流统计
func (g *GlobalRateLimiter) GetGlobalStats(ctx context.Context) (map[string]interface{}, error) {
	key := "seckill:global:ratelimit"
	return g.getStats(ctx, key, "global")
}

// GetProductStats 获取商品限流统计
func (g *GlobalRateLimiter) GetProductStats(ctx context.Context, productID uint64) (map[string]interface{}, error) {
	key := fmt.Sprintf("seckill:product:%d:ratelimit", productID)
	return g.getStats(ctx, key, fmt.Sprintf("product_%d", productID))
}

// getStats 获取限流统计信息
func (g *GlobalRateLimiter) getStats(ctx context.Context, key, name string) (map[string]interface{}, error) {
	bucket, err := g.redis.HMGet(ctx, key, "tokens", "last_time").Result()
	if err != nil {
		return nil, err
	}

	stats := map[string]interface{}{
		"name":         name,
		"key":          key,
		"tokens":       "0",
		"last_time":    "0",
		"rate_limit":   g.config.GlobalRateLimit,
		"bucket_size":  g.config.GlobalBucketSize,
	}

	if len(bucket) >= 2 {
		if bucket[0] != nil {
			stats["tokens"] = bucket[0]
		}
		if bucket[1] != nil {
			stats["last_time"] = bucket[1]
		}
	}

	return stats, nil
}

// ResetGlobalLimit 重置全局限流
func (g *GlobalRateLimiter) ResetGlobalLimit(ctx context.Context) error {
	key := "seckill:global:ratelimit"
	return g.redis.Del(ctx, key).Err()
}

// ResetProductLimit 重置商品限流
func (g *GlobalRateLimiter) ResetProductLimit(ctx context.Context, productID uint64) error {
	key := fmt.Sprintf("seckill:product:%d:ratelimit", productID)
	return g.redis.Del(ctx, key).Err()
}

// UpdateConfig 更新配置（热更新）
func (g *GlobalRateLimiter) UpdateConfig(config *config.SeckillConfig) {
	g.mu.Lock()
	defer g.mu.Unlock()
	g.config = config
}

// GetConfig 获取当前配置
func (g *GlobalRateLimiter) GetConfig() *config.SeckillConfig {
	g.mu.RLock()
	defer g.mu.RUnlock()
	return g.config
}

// BatchCheck 批量检查限流（用于批处理场景）
func (g *GlobalRateLimiter) BatchCheck(ctx context.Context, productIDs []uint64, count int) (map[uint64]bool, error) {
	results := make(map[uint64]bool)
	
	// 先检查全局限流
	for i := 0; i < count; i++ {
		allowed, err := g.AllowGlobal(ctx)
		if err != nil {
			return nil, err
		}
		if !allowed {
			// 全局限流触发，所有请求都拒绝
			for _, productID := range productIDs {
				results[productID] = false
			}
			return results, nil
		}
	}
	
	// 检查商品级别限流
	for _, productID := range productIDs {
		allowed, err := g.AllowProduct(ctx, productID)
		if err != nil {
			return nil, err
		}
		results[productID] = allowed
	}
	
	return results, nil
}

// GetAllStats 获取所有限流统计
func (g *GlobalRateLimiter) GetAllStats(ctx context.Context) (map[string]interface{}, error) {
	stats := make(map[string]interface{})
	
	// 获取全局统计
	globalStats, err := g.GetGlobalStats(ctx)
	if err == nil {
		stats["global"] = globalStats
	}
	
	// 获取所有商品限流键
	keys, err := g.redis.Keys(ctx, "seckill:product:*:ratelimit").Result()
	if err == nil {
		productStats := make(map[string]interface{})
		for _, key := range keys {
			bucket, err := g.redis.HMGet(ctx, key, "tokens", "last_time").Result()
			if err == nil && len(bucket) >= 2 {
				productStats[key] = map[string]interface{}{
					"tokens":    bucket[0],
					"last_time": bucket[1],
				}
			}
		}
		stats["products"] = productStats
	}
	
	// 添加配置信息
	stats["config"] = map[string]interface{}{
		"global_rate_limit":    g.config.GlobalRateLimit,
		"global_bucket_size":   g.config.GlobalBucketSize,
		"product_rate_limit":   g.config.ProductRateLimit,
		"product_bucket_size":  g.config.ProductBucketSize,
	}
	
	return stats, nil
}
