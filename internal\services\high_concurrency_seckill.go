package services

import (
	"context"
	"encoding/json"
	"errors"
	"fmt"
	"math/rand"
	"strconv"
	"sync"
	"time"
	"wangfujing_admin/internal/config"
	"wangfujing_admin/internal/models"

	"github.com/redis/go-redis/v9"
	"gorm.io/gorm"
)

const (
	// 高并发秒杀专用常量
	HCTokenRate      = 800  // 令牌生成速率/秒
	HCBucketCapacity = 2000 // 令牌桶容量
)

// RateLimiter 令牌桶限流器
type RateLimiter struct {
	redis *redis.Client
}

// NewRateLimiter 创建限流器
func NewRateLimiter(redis *redis.Client) *RateLimiter {
	return &RateLimiter{redis: redis}
}

// Allow 检查是否允许请求
func (rl *RateLimiter) Allow(ctx context.Context, key string) (bool, error) {
	luaScript := `
		local key = KEYS[1]
		local rate = tonumber(ARGV[1])
		local capacity = tonumber(ARGV[2])
		local now = tonumber(ARGV[3])
		
		local last_time = redis.call('HGET', key, 'time') or now
		local tokens = tonumber(redis.call('HGET', key, 'tokens') or capacity)
		
		-- 计算新令牌
		local elapsed = now - last_time
		local new_tokens = math.min(capacity, tokens + elapsed*rate/1000)
		
		if new_tokens < 1 then
			return 0
		end
		
		redis.call('HSET', key, 'time', now)
		redis.call('HSET', key, 'tokens', new_tokens-1)
		redis.call('EXPIRE', key, 3600)
		return 1
	`

	now := time.Now().UnixMilli()
	result, err := rl.redis.Eval(ctx, luaScript, []string{key},
		HCTokenRate, HCBucketCapacity, now).Int()

	return result == 1, err
}

// HighConcurrencySeckillService 高并发秒杀服务
type HighConcurrencySeckillService struct {
	db            *gorm.DB
	redis         *redis.Client
	globalLimiter *GlobalRateLimiter
	config        *config.SeckillConfig
	qrCodeService *QRCodeService
}

// NewHighConcurrencySeckillService 创建高并发秒杀服务
func NewHighConcurrencySeckillService(db *gorm.DB, redis *redis.Client, cfg *config.SeckillConfig) *HighConcurrencySeckillService {
	return &HighConcurrencySeckillService{
		db:            db,
		redis:         redis,
		globalLimiter: NewGlobalRateLimiter(redis, cfg),
		config:        cfg,
		qrCodeService: nil, // 暂时设为nil，后续通过SetQRCodeService设置
	}
}

// SetQRCodeService 设置二维码服务
func (s *HighConcurrencySeckillService) SetQRCodeService(qrCodeService *QRCodeService) {
	s.qrCodeService = qrCodeService
}

// PurchaseRequest 抢购请求
type PurchaseRequest struct {
	UserID    uint64 `json:"user_id"`
	ProductID uint64 `json:"product_id"`
	RequestID string `json:"request_id"`
	CreatedAt int64  `json:"created_at"`
}

// ToJSON 转换为JSON字符串
func (pr *PurchaseRequest) ToJSON() string {
	data, _ := json.Marshal(pr)
	return string(data)
}

// PurchaseResponse 抢购响应
type PurchaseResponse struct {
	Status    string `json:"status"`     // queued, success, failed
	Position  int64  `json:"position"`   // 在队列中的位置
	RequestID string `json:"request_id"` // 请求ID
	ResultKey string `json:"result_key"` // 结果查询key
	Message   string `json:"message"`    // 提示信息
}

// Purchase 用户抢购入口
func (s *HighConcurrencySeckillService) Purchase(ctx context.Context, productID, userID uint64) (*PurchaseResponse, error) {
	// 1. 全局限流检查
	globalAllowed, err := s.globalLimiter.AllowGlobal(ctx)
	if err != nil {
		return nil, fmt.Errorf("全局限流检查失败: %w", err)
	}

	if !globalAllowed {
		return &PurchaseResponse{
			Status:  "failed",
			Message: "系统繁忙，请稍后再试",
		}, nil
	}

	// 2. 商品级别限流检查
	productAllowed, err := s.globalLimiter.AllowProduct(ctx, productID)
	if err != nil {
		return nil, fmt.Errorf("商品限流检查失败: %w", err)
	}

	if !productAllowed {
		return &PurchaseResponse{
			Status:  "failed",
			Message: "该商品请求过于频繁，请稍后再试",
		}, nil
	}

	// 2. 检查商品是否已售完
	stockCountKey := fmt.Sprintf("seckill:product:%d:count", productID)
	stockCount, err := s.redis.Get(ctx, stockCountKey).Result()
	if err != nil {
		if err == redis.Nil {
			return &PurchaseResponse{
				Status:  "failed",
				Message: "商品不存在或已下架",
			}, nil
		}
		return nil, fmt.Errorf("获取商品库存失败: %w", err)
	}

	// 转换库存数量
	stock, err := strconv.Atoi(stockCount)
	if err != nil {
		return nil, fmt.Errorf("库存数据格式错误: %w", err)
	}

	// 商品已售完
	if stock <= 0 {
		return &PurchaseResponse{
			Status:  "failed",
			Message: "商品已售完",
		}, nil
	}

	// 3. 获取商品每日限量作为队列最大长度
	maxQueueSize, err := s.getProductDailyLimit(ctx, productID)
	if err != nil {
		return nil, fmt.Errorf("获取商品限量失败: %w", err)
	}

	// 4. 检查队列长度
	queueKey := fmt.Sprintf("seckill:queue:%d", productID)
	queueLen, err := s.redis.LLen(ctx, queueKey).Result()
	if err != nil {
		return nil, err
	}

	// 队列已满（队列长度不能超过商品每日限量）
	if queueLen >= int64(maxQueueSize) {
		return &PurchaseResponse{
			Status:  "failed",
			Message: "请求过多，请稍后再试",
		}, nil
	}

	// 5. 生成唯一请求ID（独立于数据库主键）
	requestID := fmt.Sprintf("%d_%d_%d_%d",
		productID,
		userID,
		time.Now().UnixNano(),
		rand.Intn(1000),
	)
	resultKey := fmt.Sprintf("seckill:result:%s", requestID)

	// 6. 构造请求
	req := PurchaseRequest{
		UserID:    userID,
		ProductID: productID,
		RequestID: requestID,
		CreatedAt: time.Now().UnixMilli(),
	}

	// 7. 请求入队
	newLen, err := s.redis.RPush(ctx, queueKey, req.ToJSON()).Result()
	if err != nil {
		return nil, err
	}

	// 8. 设置结果占位符（防止用户立即查询时无结果）
	s.redis.Set(ctx, resultKey, "queued", 5*time.Minute)

	return &PurchaseResponse{
		Status:    "queued",
		Position:  newLen,
		RequestID: requestID,
		ResultKey: resultKey,
		Message:   fmt.Sprintf("排队中，当前位置：%d", newLen),
	}, nil
}

// CheckResult 检查结果
func (s *HighConcurrencySeckillService) CheckResult(ctx context.Context, requestID string) (string, error) {
	resultKey := fmt.Sprintf("seckill:result:%s", requestID)
	status, err := s.redis.Get(ctx, resultKey).Result()

	if err == redis.Nil {
		return "not_found", nil
	}

	return status, err
}

// GetQueuePosition 获取队列位置
func (s *HighConcurrencySeckillService) GetQueuePosition(ctx context.Context, productID uint64, requestID string) (int64, error) {
	queueKey := fmt.Sprintf("seckill:queue:%d", productID)

	// 获取队列所有元素
	items, err := s.redis.LRange(ctx, queueKey, 0, -1).Result()
	if err != nil {
		return -1, err
	}

	// 查找请求位置
	for i, item := range items {
		var req PurchaseRequest
		if err := json.Unmarshal([]byte(item), &req); err != nil {
			continue
		}

		if req.RequestID == requestID {
			return int64(i + 1), nil
		}
	}

	return -1, errors.New("request not found in queue")
}

// StartWorkers 启动队列处理worker
func (s *HighConcurrencySeckillService) StartWorkers(ctx context.Context, numWorkers int) {
	var wg sync.WaitGroup

	for i := 0; i < numWorkers; i++ {
		wg.Add(1)
		go func(workerID int) {
			defer wg.Done()
			s.processQueues(ctx, workerID)
		}(i)
	}

	wg.Wait()
}

// processQueues 处理队列请求
func (s *HighConcurrencySeckillService) processQueues(ctx context.Context, workerID int) {
	for {
		select {
		case <-ctx.Done():
			return
		default:
			// 1. 获取所有商品队列
			keys, _ := s.redis.Keys(ctx, "seckill:queue:*").Result()
			if len(keys) == 0 {
				time.Sleep(1 * time.Second)
				continue
			}

			// 2. 从队列获取请求（阻塞式）
			result, err := s.redis.BLPop(ctx, 30*time.Second, keys...).Result()
			if err != nil {
				time.Sleep(100 * time.Millisecond)
				continue
			}

			data := result[1]

			// 3. 解析请求
			var req PurchaseRequest
			if err := json.Unmarshal([]byte(data), &req); err != nil {
				continue
			}

			// 4. 处理请求
			resultKey := fmt.Sprintf("seckill:result:%s", req.RequestID)
			success, err := s.processPurchase(ctx, req.ProductID, req.UserID)

			// 5. 存储结果
			if err != nil {
				s.redis.Set(ctx, resultKey, "error:"+err.Error(), 10*time.Minute)
			} else if success {
				s.redis.Set(ctx, resultKey, "success", 24*time.Hour)
			} else {
				s.redis.Set(ctx, resultKey, "sold_out", 10*time.Minute)
			}
		}
	}
}

// processPurchase 实际库存扣减和订单创建逻辑
func (s *HighConcurrencySeckillService) processPurchase(ctx context.Context, productID, userID uint64) (bool, error) {
	// 使用Lua脚本原子性扣减库存
	luaScript := `
		local stock_key = KEYS[1]

		-- 检查库存
		local stock = redis.call('GET', stock_key)
		if not stock or tonumber(stock) <= 0 then
			return 0
		end

		-- 扣减库存
		local new_stock = redis.call('DECR', stock_key)
		if new_stock < 0 then
			redis.call('INCR', stock_key)
			return 0
		end

		return 1
	`

	stockCountKey := fmt.Sprintf("seckill:product:%d:count", productID)
	result, err := s.redis.Eval(ctx, luaScript, []string{stockCountKey}).Result()
	if err != nil {
		return false, err
	}

	stockSuccess := result.(int64) == 1
	if !stockSuccess {
		return false, nil // 库存不足
	}

	// 库存扣减成功，立即创建订单
	err = s.createSeckillOrder(ctx, productID, userID)
	if err != nil {
		// 订单创建失败，回滚库存
		s.redis.Incr(ctx, stockCountKey)
		return false, err
	}

	return true, nil
}

// createSeckillOrder 创建秒杀订单
func (s *HighConcurrencySeckillService) createSeckillOrder(ctx context.Context, productID, userID uint64) error {
	// 获取商品信息
	var product models.Product
	if err := s.db.WithContext(ctx).Preload("Merchant").First(&product, productID).Error; err != nil {
		return fmt.Errorf("获取商品信息失败: %w", err)
	}

	// 获取用户信息
	var user models.User
	if err := s.db.WithContext(ctx).First(&user, userID).Error; err != nil {
		return fmt.Errorf("获取用户信息失败: %w", err)
	}

	// 检查用户积分是否足够
	if user.Points < product.Points {
		return fmt.Errorf("用户积分不足")
	}

	// 开始数据库事务
	tx := s.db.Begin()
	defer func() {
		if r := recover(); r != nil {
			tx.Rollback()
		}
	}()

	// 扣减用户积分
	if err := tx.Model(&user).Update("points", gorm.Expr("points - ?", product.Points)).Error; err != nil {
		tx.Rollback()
		return fmt.Errorf("扣减积分失败: %w", err)
	}

	// 生成订单号
	orderNo := fmt.Sprintf("SK%d%d", time.Now().Unix(), userID%1000)

	// 生成二维码内容
	qrCode := fmt.Sprintf("SECKILL_ORDER:%s", orderNo)

	// 生成请求ID（用于关联）
	requestID := fmt.Sprintf("%d_%d_%d_%d",
		productID,
		userID,
		time.Now().UnixNano(),
		rand.Intn(1000),
	)

	// 创建订单
	order := &models.Order{
		OrderNo:    orderNo,
		RequestID:  requestID,
		UserID:     userID,
		ProductID:  productID,
		MerchantID: product.MerchantID,
		Quantity:   1,
		Price:      0, // 积分兑换，价格为0
		TotalPrice: 0,
		Points:     product.Points,
		Status:     models.OrderStatusPending,
		QRCode:     qrCode,
	}

	if err := tx.Create(order).Error; err != nil {
		tx.Rollback()
		return fmt.Errorf("创建订单失败: %w", err)
	}

	// 记录订单创建成功的日志
	fmt.Printf("秒杀订单创建成功: orderID=%d, orderNo=%s, userID=%d, productID=%d, requestID=%s\n",
		order.ID, order.OrderNo, order.UserID, order.ProductID, order.RequestID)

	// 创建积分记录
	pointsRecord := &models.PointsRecord{
		UserID:      userID,
		Type:        models.PointsTypeSpend,
		Points:      product.Points,
		Balance:     user.Points - product.Points,
		Source:      "秒杀商品",
		SourceID:    &productID,
		RelatedID:   &order.ID,
		RelatedType: "seckill_order",
		Description: fmt.Sprintf("秒杀兑换商品：%s", product.Name),
	}

	if err := tx.Create(pointsRecord).Error; err != nil {
		tx.Rollback()
		return fmt.Errorf("创建积分记录失败: %w", err)
	}

	// 提交事务
	if err := tx.Commit().Error; err != nil {
		return fmt.Errorf("提交事务失败: %w", err)
	}

	// 事务提交成功后，生成二维码
	fmt.Printf("事务提交成功，开始生成秒杀订单二维码: orderID=%d, orderNo=%s\n", order.ID, order.OrderNo)

	if s.qrCodeService != nil {
		fmt.Printf("二维码服务已初始化，开始生成秒杀订单二维码: orderID=%d\n", order.ID)
		qrCodeURL, err := s.qrCodeService.GenerateOrderQRCode(ctx, order.ID)
		if err != nil {
			// 记录错误日志，但不影响订单创建
			fmt.Printf("生成秒杀订单二维码失败: orderID=%d, orderNo=%s, error=%v\n", order.ID, order.OrderNo, err)
		} else {
			fmt.Printf("成功生成秒杀订单二维码: orderID=%d, orderNo=%s, url=%s\n", order.ID, order.OrderNo, qrCodeURL)
		}
	} else {
		fmt.Printf("二维码服务未初始化，无法生成秒杀订单二维码: orderID=%d, orderNo=%s\n", order.ID, order.OrderNo)
	}

	return nil
}

// getProductDailyLimit 获取商品每日限量
func (s *HighConcurrencySeckillService) getProductDailyLimit(ctx context.Context, productID uint64) (int, error) {
	// 先从Redis获取库存信息
	stockKey := fmt.Sprintf("seckill:product:%d:stock", productID)
	stockData, err := s.redis.Get(ctx, stockKey).Result()
	if err == nil {
		var stockInfo ProductStock
		if err := json.Unmarshal([]byte(stockData), &stockInfo); err == nil {
			return stockInfo.DailyLimit, nil
		}
	}

	// Redis中没有，从数据库获取
	var product models.Product
	if err := s.db.WithContext(ctx).Select("daily_limit").
		Where("id = ? AND type = ?", productID, models.ProductTypeMerchant).
		First(&product).Error; err != nil {
		return 0, fmt.Errorf("商品不存在: %w", err)
	}

	return product.DailyLimit, nil
}
