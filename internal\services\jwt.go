package services

import (
	"context"
	"errors"
	"fmt"
	"strconv"
	"time"
	"wangfujing_admin/pkg/cache"

	"github.com/golang-jwt/jwt/v5"
)

// JWTService JWT服务接口
type JWTService interface {
	GenerateToken(userID, openID, userType, sessionID string) (*TokenInfo, error)
	RefreshToken(refreshToken string) (*TokenInfo, error)
	ParseToken(tokenString string) (*Claims, error)
	ParseRefreshToken(tokenString string) (*RefreshClaims, error)
	InvalidateToken(tokenString string) error
}

// TokenInfo 令牌信息
type TokenInfo struct {
	AccessToken  string `json:"access_token"`
	RefreshToken string `json:"refresh_token"`
	ExpiresAt    int64  `json:"expires_at"`
}

// Claims JWT声明
type Claims struct {
	UserID    string   `json:"user_id"`
	OpenID    string   `json:"open_id"`
	UserType  string   `json:"user_type"`
	SessionID string   `json:"session_id"` // 新增会话ID
	Roles     []string `json:"roles"`
	jwt.RegisteredClaims
}

// RefreshClaims 刷新令牌声明
type RefreshClaims struct {
	UserID    string `json:"user_id"`
	OpenID    string `json:"open_id"`
	UserType  string `json:"user_type"`  // 添加用户类型
	SessionID string `json:"session_id"` // 添加会话ID
	jwt.RegisteredClaims
}

// jwtService JWT服务实现
type jwtService struct {
	secretKey       []byte
	accessTokenTTL  time.Duration
	refreshTokenTTL time.Duration
	issuer          string
}

// NewJWTService 创建JWT服务
func NewJWTService(secretKey string, accessTokenTTL, refreshTokenTTL time.Duration, issuer string) JWTService {
	return &jwtService{
		secretKey:       []byte(secretKey),
		accessTokenTTL:  accessTokenTTL,
		refreshTokenTTL: refreshTokenTTL,
		issuer:          issuer,
	}
}

// GenerateToken 生成访问令牌和刷新令牌
func (s *jwtService) GenerateToken(userID, openID, userType, sessionID string) (*TokenInfo, error) {
	now := time.Now()

	// 生成访问令牌
	accessClaims := &Claims{
		UserID:    userID,
		OpenID:    openID,
		UserType:  userType,
		SessionID: sessionID,        // 添加会话ID
		Roles:     []string{"user"}, // 默认角色
		RegisteredClaims: jwt.RegisteredClaims{
			ExpiresAt: jwt.NewNumericDate(now.Add(s.accessTokenTTL)),
			IssuedAt:  jwt.NewNumericDate(now),
			NotBefore: jwt.NewNumericDate(now),
			Issuer:    s.issuer,
			Subject:   userID,
		},
	}

	accessToken := jwt.NewWithClaims(jwt.SigningMethodHS256, accessClaims)
	accessTokenString, err := accessToken.SignedString(s.secretKey)
	if err != nil {
		return nil, fmt.Errorf("生成访问令牌失败: %w", err)
	}

	// 生成刷新令牌
	refreshClaims := &RefreshClaims{
		UserID:    userID,
		OpenID:    openID,
		UserType:  userType,  // 添加用户类型
		SessionID: sessionID, // 添加会话ID
		RegisteredClaims: jwt.RegisteredClaims{
			ExpiresAt: jwt.NewNumericDate(now.Add(s.refreshTokenTTL)),
			IssuedAt:  jwt.NewNumericDate(now),
			NotBefore: jwt.NewNumericDate(now),
			Issuer:    s.issuer,
			Subject:   userID,
		},
	}

	refreshToken := jwt.NewWithClaims(jwt.SigningMethodHS256, refreshClaims)
	refreshTokenString, err := refreshToken.SignedString(s.secretKey)
	if err != nil {
		return nil, fmt.Errorf("生成刷新令牌失败: %w", err)
	}

	// 注意：刷新令牌的存储由TokenStorageService统一管理，这里不再重复存储

	return &TokenInfo{
		AccessToken:  accessTokenString,
		RefreshToken: refreshTokenString,
		ExpiresAt:    accessClaims.ExpiresAt.Unix(),
	}, nil
}

// RefreshToken 刷新令牌
func (s *jwtService) RefreshToken(refreshTokenString string) (*TokenInfo, error) {
	// 解析刷新令牌
	refreshClaims := &RefreshClaims{}
	token, err := jwt.ParseWithClaims(refreshTokenString, refreshClaims, func(token *jwt.Token) (interface{}, error) {
		if _, ok := token.Method.(*jwt.SigningMethodHMAC); !ok {
			return nil, fmt.Errorf("意外的签名方法: %v", token.Header["alg"])
		}
		return s.secretKey, nil
	})

	if err != nil {
		return nil, fmt.Errorf("解析刷新令牌失败: %w", err)
	}

	if !token.Valid {
		return nil, errors.New("无效的刷新令牌")
	}

	// 注意：刷新令牌的验证现在由TokenStorageService统一管理
	// 这里只验证JWT本身的有效性，具体的存储验证在调用方处理

	// 生成新的令牌对（刷新时保持原有信息）
	return s.GenerateToken(refreshClaims.UserID, refreshClaims.OpenID, refreshClaims.UserType, refreshClaims.SessionID)
}

// ParseToken 解析访问令牌
func (s *jwtService) ParseToken(tokenString string) (*Claims, error) {
	claims := &Claims{}
	token, err := jwt.ParseWithClaims(tokenString, claims, func(token *jwt.Token) (interface{}, error) {
		if _, ok := token.Method.(*jwt.SigningMethodHMAC); !ok {
			return nil, fmt.Errorf("意外的签名方法: %v", token.Header["alg"])
		}
		return s.secretKey, nil
	})

	if err != nil {
		return nil, fmt.Errorf("解析令牌失败: %w", err)
	}

	if !token.Valid {
		return nil, errors.New("无效的令牌")
	}

	// 检查令牌是否在黑名单中
	blacklistKey := fmt.Sprintf("blacklist_token:%s", tokenString)
	var blacklisted string
	if err := cache.Get(context.Background(), blacklistKey, &blacklisted); err == nil {
		return nil, errors.New("令牌已被撤销")
	}

	return claims, nil
}

// ParseRefreshToken 解析刷新令牌
func (s *jwtService) ParseRefreshToken(tokenString string) (*RefreshClaims, error) {
	claims := &RefreshClaims{}
	token, err := jwt.ParseWithClaims(tokenString, claims, func(token *jwt.Token) (interface{}, error) {
		if _, ok := token.Method.(*jwt.SigningMethodHMAC); !ok {
			return nil, fmt.Errorf("意外的签名方法: %v", token.Header["alg"])
		}
		return s.secretKey, nil
	})

	if err != nil {
		return nil, fmt.Errorf("解析刷新令牌失败: %w", err)
	}

	if !token.Valid {
		return nil, errors.New("无效的刷新令牌")
	}

	return claims, nil
}

// InvalidateToken 撤销令牌（加入黑名单）
func (s *jwtService) InvalidateToken(tokenString string) error {
	// 解析令牌获取过期时间
	claims := &Claims{}
	_, err := jwt.ParseWithClaims(tokenString, claims, func(token *jwt.Token) (interface{}, error) {
		return s.secretKey, nil
	})

	if err != nil {
		return fmt.Errorf("解析令牌失败: %w", err)
	}

	// 计算令牌剩余有效时间
	now := time.Now()
	expiresAt := claims.ExpiresAt.Time
	if expiresAt.Before(now) {
		// 令牌已过期，无需加入黑名单
		return nil
	}

	ttl := expiresAt.Sub(now)
	blacklistKey := fmt.Sprintf("blacklist_token:%s", tokenString)

	// 将令牌加入黑名单，TTL设置为令牌剩余有效时间
	return cache.Set(context.Background(), blacklistKey, "1", ttl)
}

// GetUserIDFromToken 从令牌中获取用户ID
func GetUserIDFromToken(tokenString string, jwtService JWTService) (string, error) {
	claims, err := jwtService.ParseToken(tokenString)
	if err != nil {
		return "", err
	}
	return claims.UserID, nil
}

// GetUserIDAsUint 将字符串用户ID转换为uint
func GetUserIDAsUint(userID string) (uint, error) {
	id, err := strconv.ParseUint(userID, 10, 32)
	if err != nil {
		return 0, fmt.Errorf("无效的用户ID: %w", err)
	}
	return uint(id), nil
}
