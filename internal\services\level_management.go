package services

import (
	"context"
	"fmt"
	"wangfujing_admin/internal/models"

	"gorm.io/gorm"
)

// LevelManagementService 等级管理服务
type LevelManagementService struct {
	db *gorm.DB
}

// NewLevelManagementService 创建等级管理服务
func NewLevelManagementService(db *gorm.DB) *LevelManagementService {
	return &LevelManagementService{
		db: db,
	}
}

// GetScoreRules 获取分值规则
func (s *LevelManagementService) GetScoreRules(ctx context.Context) (*models.ScoreRule, error) {
	var rule models.ScoreRule
	if err := s.db.WithContext(ctx).First(&rule).Error; err != nil {
		if err == gorm.ErrRecordNotFound {
			// 如果没有记录，创建默认规则
			rule = models.ScoreRule{
				DefaultScore: 10,
			}
			if err := s.db.WithContext(ctx).Create(&rule).Error; err != nil {
				return nil, fmt.Errorf("failed to create default score rule: %w", err)
			}
		} else {
			return nil, fmt.Errorf("failed to get score rule: %w", err)
		}
	}
	return &rule, nil
}

// UpdateScoreRules 更新分值规则
func (s *LevelManagementService) UpdateScoreRules(ctx context.Context, req *UpdateScoreRulesRequest) (*models.ScoreRule, error) {
	var rule models.ScoreRule
	if err := s.db.WithContext(ctx).First(&rule).Error; err != nil {
		if err == gorm.ErrRecordNotFound {
			// 如果没有记录，创建新的
			rule = models.ScoreRule{
				DefaultScore: req.DefaultScore,
			}
			if err := s.db.WithContext(ctx).Create(&rule).Error; err != nil {
				return nil, fmt.Errorf("failed to create score rule: %w", err)
			}
		} else {
			return nil, fmt.Errorf("failed to get score rule: %w", err)
		}
	} else {
		// 更新现有记录
		rule.DefaultScore = req.DefaultScore
		if err := s.db.WithContext(ctx).Save(&rule).Error; err != nil {
			return nil, fmt.Errorf("failed to update score rule: %w", err)
		}
	}
	return &rule, nil
}

// GetScoreItems 获取增减分项目
func (s *LevelManagementService) GetScoreItems(ctx context.Context, scoreType *models.ScoreType) ([]*models.ScoreItem, error) {
	var items []*models.ScoreItem

	query := s.db.WithContext(ctx)

	// 如果指定了类型，则按类型筛选
	if scoreType != nil {
		query = query.Where("type = ?", *scoreType)
	}

	if err := query.Order("created_at ASC").Find(&items).Error; err != nil {
		return nil, fmt.Errorf("failed to get score items: %w", err)
	}
	return items, nil
}

// CreateScoreItem 创建增减分项目
func (s *LevelManagementService) CreateScoreItem(ctx context.Context, req *CreateScoreItemRequest) (*models.ScoreItem, error) {
	item := &models.ScoreItem{
		Type:        req.Type,
		Name:        req.Name,
		Score:       req.Score,
		Description: req.Description,
	}

	if err := s.db.WithContext(ctx).Create(item).Error; err != nil {
		return nil, fmt.Errorf("failed to create score item: %w", err)
	}

	return item, nil
}

// UpdateScoreItem 更新增减分项目
func (s *LevelManagementService) UpdateScoreItem(ctx context.Context, id uint64, req *UpdateScoreItemRequest) (*models.ScoreItem, error) {
	var item models.ScoreItem
	if err := s.db.WithContext(ctx).First(&item, id).Error; err != nil {
		if err == gorm.ErrRecordNotFound {
			return nil, fmt.Errorf("增减分项目不存在")
		}
		return nil, fmt.Errorf("failed to get score item: %w", err)
	}

	// 更新字段
	updates := make(map[string]interface{})
	if req.Name != "" {
		updates["name"] = req.Name
	}
	if req.Score > 0 {
		updates["score"] = req.Score
	}
	if req.Description != "" {
		updates["description"] = req.Description
	}

	if err := s.db.WithContext(ctx).Model(&item).Updates(updates).Error; err != nil {
		return nil, fmt.Errorf("failed to update score item: %w", err)
	}

	return &item, nil
}

// DeleteScoreItem 删除增减分项目
func (s *LevelManagementService) DeleteScoreItem(ctx context.Context, id uint64) error {
	var item models.ScoreItem
	if err := s.db.WithContext(ctx).First(&item, id).Error; err != nil {
		if err == gorm.ErrRecordNotFound {
			return fmt.Errorf("增减分项目不存在")
		}
		return fmt.Errorf("failed to get score item: %w", err)
	}

	if err := s.db.WithContext(ctx).Delete(&item).Error; err != nil {
		return fmt.Errorf("failed to delete score item: %w", err)
	}

	return nil
}

// GetLevelBenefit 获取等级权益（全局配置）
func (s *LevelManagementService) GetLevelBenefit(ctx context.Context) (*models.LevelBenefit, error) {
	var benefit models.LevelBenefit
	if err := s.db.WithContext(ctx).First(&benefit).Error; err != nil {
		if err == gorm.ErrRecordNotFound {
			// 如果没有记录，创建默认配置
			benefit = models.LevelBenefit{
				RequiredScore:  50, // 默认需要50分
				DelayedClosing: true,
			}
			if err := s.db.WithContext(ctx).Create(&benefit).Error; err != nil {
				return nil, fmt.Errorf("failed to create default benefit config: %w", err)
			}
		} else {
			return nil, fmt.Errorf("failed to get level benefit: %w", err)
		}
	}
	return &benefit, nil
}

// UpdateLevelBenefit 更新等级权益（全局配置）
func (s *LevelManagementService) UpdateLevelBenefit(ctx context.Context, req *UpdateLevelBenefitRequest) (*models.LevelBenefit, error) {
	var benefit models.LevelBenefit
	if err := s.db.WithContext(ctx).First(&benefit).Error; err != nil {
		if err == gorm.ErrRecordNotFound {
			// 创建新的等级权益
			benefit = models.LevelBenefit{
				RequiredScore:  req.RequiredScore,
				DelayedClosing: req.DelayedClosing,
			}
			if err := s.db.WithContext(ctx).Create(&benefit).Error; err != nil {
				return nil, fmt.Errorf("failed to create level benefit: %w", err)
			}
		} else {
			return nil, fmt.Errorf("failed to get level benefit: %w", err)
		}
	} else {
		// 更新现有记录
		benefit.RequiredScore = req.RequiredScore
		benefit.DelayedClosing = req.DelayedClosing
		if err := s.db.WithContext(ctx).Save(&benefit).Error; err != nil {
			return nil, fmt.Errorf("failed to update level benefit: %w", err)
		}
	}
	return &benefit, nil
}

// CreateLevelBenefit 创建等级权益（全局配置，如果已存在则更新）
func (s *LevelManagementService) CreateLevelBenefit(ctx context.Context, req *CreateLevelBenefitRequest) (*models.LevelBenefit, error) {
	// 检查是否已存在配置
	var existingBenefit models.LevelBenefit
	err := s.db.WithContext(ctx).First(&existingBenefit).Error
	if err == nil {
		// 如果已存在，则更新
		existingBenefit.RequiredScore = req.RequiredScore
		existingBenefit.DelayedClosing = req.DelayedClosing
		if err := s.db.WithContext(ctx).Save(&existingBenefit).Error; err != nil {
			return nil, fmt.Errorf("failed to update existing benefit: %w", err)
		}
		return &existingBenefit, nil
	} else if err != gorm.ErrRecordNotFound {
		return nil, fmt.Errorf("failed to check existing benefit: %w", err)
	}

	// 创建新的等级权益
	benefit := models.LevelBenefit{
		RequiredScore:  req.RequiredScore,
		DelayedClosing: req.DelayedClosing,
	}

	if err := s.db.WithContext(ctx).Create(&benefit).Error; err != nil {
		return nil, fmt.Errorf("failed to create level benefit: %w", err)
	}

	return &benefit, nil
}

// DeleteLevelBenefit 删除等级权益（全局配置）
func (s *LevelManagementService) DeleteLevelBenefit(ctx context.Context) error {
	// 先检查是否存在记录
	var count int64
	if err := s.db.WithContext(ctx).Model(&models.LevelBenefit{}).Count(&count).Error; err != nil {
		return fmt.Errorf("failed to check level benefit existence: %w", err)
	}

	if count == 0 {
		return fmt.Errorf("权益配置不存在")
	}

	// 删除所有记录（因为是全局配置）
	result := s.db.WithContext(ctx).Unscoped().Delete(&models.LevelBenefit{}, "1=1")
	if result.Error != nil {
		return fmt.Errorf("failed to delete level benefit: %w", result.Error)
	}

	return nil
}

// UpdateScoreRulesRequest 更新分值规则请求
type UpdateScoreRulesRequest struct {
	DefaultScore int `json:"default_score" binding:"required,min=1,max=10000"`
}

// CreateScoreItemRequest 创建增减分项目请求
type CreateScoreItemRequest struct {
	Type        models.ScoreType `json:"type" binding:"required"`
	Name        string           `json:"name" binding:"required,min=1,max=20"`
	Score       int              `json:"score" binding:"required,min=1,max=10000"`
	Description string           `json:"description" binding:"max=200"`
}

// UpdateScoreItemRequest 更新增减分项目请求
type UpdateScoreItemRequest struct {
	Name        string `json:"name" binding:"omitempty,min=1,max=20"`
	Score       int    `json:"score" binding:"omitempty,min=1,max=10000"`
	Description string `json:"description" binding:"omitempty,max=200"`
}

// UpdateLevelBenefitRequest 更新等级权益请求
type UpdateLevelBenefitRequest struct {
	RequiredScore  int  `json:"required_score" binding:"required,min=1,max=10000"`
	DelayedClosing bool `json:"delayed_closing"`
}

// CreateLevelBenefitRequest 创建等级权益请求
type CreateLevelBenefitRequest struct {
	RequiredScore  int  `json:"required_score" binding:"required,min=1,max=10000"`
	DelayedClosing bool `json:"delayed_closing"`
}
