package services

import (
	"context"
	"crypto/rand"
	"fmt"
	"math/big"
	"time"
	"wangfujing_admin/internal/models"

	"gorm.io/gorm"
)

// LotteryService 抽奖服务
type LotteryService struct {
	db *gorm.DB
}

// NewLotteryService 创建抽奖服务
func NewLotteryService(db *gorm.DB) *LotteryService {
	return &LotteryService{
		db: db,
	}
}

// LotteryResult 抽奖结果
type LotteryResult struct {
	Success   bool   `json:"success"`   // 是否中奖
	Message   string `json:"message"`   // 结果消息
	ItemID    uint64 `json:"item_id"`   // 商品ID
	ItemName  string `json:"item_name"` // 商品名称
	Probability int  `json:"probability"` // 中奖概率
}

// DrawLottery 执行抽奖
func (s *LotteryService) DrawLottery(ctx context.Context, userID uint64, itemID uint64) (*LotteryResult, error) {
	// 获取VIP形象商品信息
	var item models.PointsMallItem
	if err := s.db.WithContext(ctx).Where("id = ? AND type = ? AND status = ?", 
		itemID, models.PointsMallItemTypeAvatar, models.PointsMallItemStatusActive).
		First(&item).Error; err != nil {
		if err == gorm.ErrRecordNotFound {
			return nil, fmt.Errorf("VIP形象商品不存在")
		}
		return nil, fmt.Errorf("查询VIP形象商品失败: %w", err)
	}

	// 检查是否启用抽奖
	if !item.EnableLottery {
		return &LotteryResult{
			Success:     true, // 不启用抽奖则直接成功
			Message:     "兑换成功",
			ItemID:      item.ID,
			ItemName:    item.Name,
			Probability: 100,
		}, nil
	}

	// 检查用户今日抽奖次数限制（可选）
	if err := s.checkDailyLimit(ctx, userID, itemID); err != nil {
		return nil, err
	}

	// 执行概率计算
	isWin, err := s.calculateProbability(item.LotteryRate)
	if err != nil {
		return nil, fmt.Errorf("概率计算失败: %w", err)
	}

	// 记录抽奖历史
	if err := s.recordLotteryHistory(ctx, userID, itemID, isWin, item.LotteryRate); err != nil {
		// 记录失败不影响抽奖结果，只记录日志
		fmt.Printf("记录抽奖历史失败: %v\n", err)
	}

	result := &LotteryResult{
		Success:     isWin,
		ItemID:      item.ID,
		ItemName:    item.Name,
		Probability: item.LotteryRate,
	}

	if isWin {
		result.Message = "恭喜您，抽奖成功！"
	} else {
		result.Message = "很遗憾，本次抽奖未中奖"
	}

	return result, nil
}

// calculateProbability 计算概率
func (s *LotteryService) calculateProbability(probability int) (bool, error) {
	if probability <= 0 {
		return false, nil
	}
	if probability >= 100 {
		return true, nil
	}

	// 使用加密安全的随机数生成器
	randomNum, err := rand.Int(rand.Reader, big.NewInt(100))
	if err != nil {
		return false, fmt.Errorf("生成随机数失败: %w", err)
	}

	// 随机数范围是0-99，概率是1-100
	// 例如：概率30%，则随机数0-29为中奖
	return randomNum.Int64() < int64(probability), nil
}

// checkDailyLimit 检查每日抽奖次数限制
func (s *LotteryService) checkDailyLimit(ctx context.Context, userID uint64, itemID uint64) error {
	// 获取今日开始时间
	today := time.Now().Truncate(24 * time.Hour)
	tomorrow := today.Add(24 * time.Hour)

	// 查询今日抽奖次数
	var count int64
	if err := s.db.WithContext(ctx).Model(&models.LotteryHistory{}).
		Where("user_id = ? AND item_id = ? AND created_at >= ? AND created_at < ?", 
			userID, itemID, today, tomorrow).
		Count(&count).Error; err != nil {
		return fmt.Errorf("查询今日抽奖次数失败: %w", err)
	}

	// 设置每日限制次数（可以配置化）
	const dailyLimit = 10
	if count >= dailyLimit {
		return fmt.Errorf("今日抽奖次数已达上限")
	}

	return nil
}

// recordLotteryHistory 记录抽奖历史
func (s *LotteryService) recordLotteryHistory(ctx context.Context, userID uint64, itemID uint64, isWin bool, probability int) error {
	history := models.LotteryHistory{
		UserID:      userID,
		ItemID:      itemID,
		IsWin:       isWin,
		Probability: probability,
	}

	if err := s.db.WithContext(ctx).Create(&history).Error; err != nil {
		return fmt.Errorf("创建抽奖历史记录失败: %w", err)
	}

	return nil
}

// GetLotteryHistory 获取用户抽奖历史
func (s *LotteryService) GetLotteryHistory(ctx context.Context, userID uint64, page, size int) ([]*models.LotteryHistory, int64, error) {
	var histories []*models.LotteryHistory
	var total int64

	query := s.db.WithContext(ctx).Model(&models.LotteryHistory{}).Where("user_id = ?", userID)

	// 获取总数
	if err := query.Count(&total).Error; err != nil {
		return nil, 0, fmt.Errorf("统计抽奖历史总数失败: %w", err)
	}

	// 分页查询
	offset := (page - 1) * size
	if err := query.Preload("Item").
		Offset(offset).Limit(size).
		Order("created_at DESC").
		Find(&histories).Error; err != nil {
		return nil, 0, fmt.Errorf("查询抽奖历史失败: %w", err)
	}

	return histories, total, nil
}

// GetLotteryStats 获取抽奖统计
func (s *LotteryService) GetLotteryStats(ctx context.Context, itemID uint64) (map[string]interface{}, error) {
	var totalCount int64
	var winCount int64

	// 统计总抽奖次数
	if err := s.db.WithContext(ctx).Model(&models.LotteryHistory{}).
		Where("item_id = ?", itemID).
		Count(&totalCount).Error; err != nil {
		return nil, fmt.Errorf("统计总抽奖次数失败: %w", err)
	}

	// 统计中奖次数
	if err := s.db.WithContext(ctx).Model(&models.LotteryHistory{}).
		Where("item_id = ? AND is_win = ?", itemID, true).
		Count(&winCount).Error; err != nil {
		return nil, fmt.Errorf("统计中奖次数失败: %w", err)
	}

	// 计算实际中奖率
	var actualWinRate float64
	if totalCount > 0 {
		actualWinRate = float64(winCount) / float64(totalCount) * 100
	}

	stats := map[string]interface{}{
		"total_count":     totalCount,
		"win_count":       winCount,
		"lose_count":      totalCount - winCount,
		"actual_win_rate": actualWinRate,
	}

	return stats, nil
}

// GetTodayLotteryCount 获取用户今日抽奖次数
func (s *LotteryService) GetTodayLotteryCount(ctx context.Context, userID uint64, itemID uint64) (int64, error) {
	// 获取今日开始时间
	today := time.Now().Truncate(24 * time.Hour)
	tomorrow := today.Add(24 * time.Hour)

	var count int64
	if err := s.db.WithContext(ctx).Model(&models.LotteryHistory{}).
		Where("user_id = ? AND item_id = ? AND created_at >= ? AND created_at < ?", 
			userID, itemID, today, tomorrow).
		Count(&count).Error; err != nil {
		return 0, fmt.Errorf("查询今日抽奖次数失败: %w", err)
	}

	return count, nil
}

// BatchDrawLottery 批量抽奖（用于测试概率准确性）
func (s *LotteryService) BatchDrawLottery(ctx context.Context, itemID uint64, count int) (map[string]interface{}, error) {
	// 获取VIP形象商品信息
	var item models.PointsMallItem
	if err := s.db.WithContext(ctx).Where("id = ? AND type = ?", 
		itemID, models.PointsMallItemTypeAvatar).
		First(&item).Error; err != nil {
		return nil, fmt.Errorf("查询VIP形象商品失败: %w", err)
	}

	var winCount int
	for i := 0; i < count; i++ {
		isWin, err := s.calculateProbability(item.LotteryRate)
		if err != nil {
			return nil, fmt.Errorf("概率计算失败: %w", err)
		}
		if isWin {
			winCount++
		}
	}

	actualWinRate := float64(winCount) / float64(count) * 100
	expectedWinRate := float64(item.LotteryRate)

	result := map[string]interface{}{
		"total_count":       count,
		"win_count":         winCount,
		"lose_count":        count - winCount,
		"expected_win_rate": expectedWinRate,
		"actual_win_rate":   actualWinRate,
		"deviation":         actualWinRate - expectedWinRate,
	}

	return result, nil
}
