package services

import (
	"fmt"
	"strconv"
	"time"

	"wangfujing_admin/internal/models"

	"gorm.io/gorm"
)

// MarketingService 营销服务
type MarketingService struct {
	db *gorm.DB
}

// NewMarketingService 创建营销服务实例
func NewMarketingService(db *gorm.DB) *MarketingService {
	return &MarketingService{db: db}
}

// CreateActivityRequest 创建活动请求
type CreateActivityRequest struct {
	Category       models.MarketingCategory     `json:"category" binding:"required,oneof=1 2 3"`
	Type           models.MarketingActivityType `json:"type" binding:"required,oneof=1 2 3"`
	Name           string                       `json:"name"`
	VideoName      string                       `json:"video_name" binding:"max=100"`
	Image          string                       `json:"image"`
	VideoURL       string                       `json:"video_url"`
	Description    string                       `json:"description" binding:"max=100"`
	DailyLimit     int                          `json:"daily_limit" binding:"required,min=1,max=99"`
	StartTime      string                       `json:"start_time" binding:"required"`
	RewardPoints   int                          `json:"reward_points" binding:"required,min=1,max=99"`
	RequiredPoints int                          `json:"required_points" binding:"min=0"`

	// 游戏相关字段（仅游戏类型使用）
	GameType       models.GameType `json:"game_type" binding:"oneof=1 2"`
	WinProbability int             `json:"win_probability" binding:"min=1,max=100"`
}

// UpdateActivityRequest 更新活动请求
type UpdateActivityRequest struct {
	Name           string `json:"name"`
	VideoName      string `json:"video_name" binding:"max=100"`
	Image          string `json:"image"`
	VideoURL       string `json:"video_url"`
	Description    string `json:"description" binding:"max=100"`
	DailyLimit     int    `json:"daily_limit" binding:"required,min=1,max=99"`
	StartTime      string `json:"start_time" binding:"required"`
	RewardPoints   int    `json:"reward_points" binding:"required,min=1,max=99"`
	RequiredPoints int    `json:"required_points" binding:"min=0"`

	// 游戏相关字段（仅游戏类型使用）
	GameType       models.GameType `json:"game_type" binding:"oneof=1 2"`
	WinProbability int             `json:"win_probability" binding:"min=1,max=100"`
}

// ActivityListRequest 活动列表请求
type ActivityListRequest struct {
	Page     int                             `form:"page,default=1"`
	Size     int                             `form:"size,default=10"`
	Category *models.MarketingCategory       `form:"category"`
	Type     *models.MarketingActivityType   `form:"type"`
	Status   *models.MarketingActivityStatus `form:"status"`
	Keyword  string                          `form:"keyword"`
}

// CreateActivity 创建营销活动
func (s *MarketingService) CreateActivity(req *CreateActivityRequest, creatorID string) error {
	// 解析创建人ID
	creatorIDUint, err := strconv.ParseUint(creatorID, 10, 64)
	if err != nil {
		return fmt.Errorf("invalid creator ID: %w", err)
	}

	// 解析开始时间，明确使用中国时区
	loc, err := time.LoadLocation("Asia/Shanghai")
	if err != nil {
		return fmt.Errorf("failed to load timezone: %w", err)
	}
	startTime, err := time.ParseInLocation("2006-01-02 15:04", req.StartTime, loc)
	if err != nil {
		return fmt.Errorf("invalid start time format: %w", err)
	}

	// 计算结束时间
	endTime := s.calculateEndTime(startTime, req.Type)

	// 分享类型不需要报名条件
	if req.Category == models.MarketingCategoryShare {
		req.RequiredPoints = 0
	}

	// 设置游戏类型默认值
	gameType := models.GameTypeWheel // 默认大转盘
	winProbability := 50             // 默认50%概率
	if req.Category == models.MarketingCategoryGame {
		if req.GameType > 0 {
			gameType = req.GameType
		}
		if req.WinProbability > 0 {
			winProbability = req.WinProbability
		}
	}

	activity := &models.MarketingActivity{
		Category:       req.Category,
		Type:           req.Type,
		Name:           req.Name,
		VideoName:      req.VideoName,
		Image:          req.Image,
		VideoURL:       req.VideoURL,
		Description:    req.Description,
		DailyLimit:     req.DailyLimit,
		StartTime:      &startTime,
		EndTime:        &endTime,
		RewardPoints:   req.RewardPoints,
		RequiredPoints: req.RequiredPoints,
		Status:         models.MarketingActivityStatusActive, // 默认上架
		CreatorID:      creatorIDUint,
		GameType:       gameType,
		WinProbability: winProbability,
	}

	if err := s.db.Create(activity).Error; err != nil {
		return fmt.Errorf("failed to create activity: %w", err)
	}

	return nil
}

// GetActivityList 获取活动列表
func (s *MarketingService) GetActivityList(req *ActivityListRequest) ([]models.MarketingActivity, int64, error) {
	var activities []models.MarketingActivity
	var total int64

	query := s.db.Model(&models.MarketingActivity{})

	// 筛选条件
	if req.Category != nil && *req.Category > 0 {
		query = query.Where("category = ?", *req.Category)
	}
	if req.Type != nil && *req.Type > 0 {
		query = query.Where("type = ?", *req.Type)
	}
	if req.Status != nil {
		query = query.Where("status = ?", *req.Status)
	}
	if req.Keyword != "" {
		query = query.Where("name LIKE ? OR video_name LIKE ? OR description LIKE ?",
			"%"+req.Keyword+"%", "%"+req.Keyword+"%", "%"+req.Keyword+"%")
	}

	// 获取总数
	if err := query.Count(&total).Error; err != nil {
		return nil, 0, fmt.Errorf("failed to count activities: %w", err)
	}

	// 分页查询
	offset := (req.Page - 1) * req.Size
	if err := query.Preload("Creator").
		Order("created_at DESC").
		Offset(offset).
		Limit(req.Size).
		Find(&activities).Error; err != nil {
		return nil, 0, fmt.Errorf("failed to get activities: %w", err)
	}

	// 更新过期状态
	s.updateExpiredActivities(&activities)

	// 统计参与人数
	for i := range activities {
		count, _ := s.getParticipantCount(activities[i].ID)
		activities[i].ParticipantCount = count
	}

	return activities, total, nil
}

// GetActivity 获取活动详情
func (s *MarketingService) GetActivity(id uint64) (*models.MarketingActivity, error) {
	var activity models.MarketingActivity
	if err := s.db.Preload("Creator").First(&activity, id).Error; err != nil {
		if err == gorm.ErrRecordNotFound {
			return nil, fmt.Errorf("activity not found")
		}
		return nil, fmt.Errorf("failed to get activity: %w", err)
	}

	// 检查并更新过期状态
	if activity.IsExpired() && activity.Status == models.MarketingActivityStatusActive {
		activity.Status = models.MarketingActivityStatusExpired
		s.db.Model(&activity).Update("status", models.MarketingActivityStatusExpired)
	}

	// 统计参与人数
	count, _ := s.getParticipantCount(activity.ID)
	activity.ParticipantCount = count

	return &activity, nil
}

// UpdateActivity 更新活动
func (s *MarketingService) UpdateActivity(id uint64, req *UpdateActivityRequest) error {
	var activity models.MarketingActivity
	if err := s.db.First(&activity, id).Error; err != nil {
		if err == gorm.ErrRecordNotFound {
			return fmt.Errorf("activity not found")
		}
		return fmt.Errorf("failed to get activity: %w", err)
	}

	// 检查是否可以编辑
	if !activity.CanEdit() {
		return fmt.Errorf("activity cannot be edited")
	}

	// 解析开始时间，明确使用中国时区
	loc, err := time.LoadLocation("Asia/Shanghai")
	if err != nil {
		return fmt.Errorf("failed to load timezone: %w", err)
	}
	startTime, err := time.ParseInLocation("2006-01-02 15:04", req.StartTime, loc)
	if err != nil {
		return fmt.Errorf("invalid start time format: %w", err)
	}

	// 计算结束时间
	endTime := s.calculateEndTime(startTime, activity.Type)

	// 分享类型不需要报名条件
	if activity.Category == models.MarketingCategoryShare {
		req.RequiredPoints = 0
	}

	// 更新字段
	updates := map[string]interface{}{
		"name":            req.Name,
		"video_name":      req.VideoName,
		"image":           req.Image,
		"video_url":       req.VideoURL,
		"description":     req.Description,
		"daily_limit":     req.DailyLimit,
		"start_time":      &startTime,
		"end_time":        &endTime,
		"reward_points":   req.RewardPoints,
		"required_points": req.RequiredPoints,
	}

	// 如果是游戏类型，更新游戏相关字段
	if activity.Category == models.MarketingCategoryGame {
		if req.GameType > 0 {
			updates["game_type"] = req.GameType
		}
		if req.WinProbability > 0 {
			updates["win_probability"] = req.WinProbability
		}
	}

	if err := s.db.Model(&activity).Updates(updates).Error; err != nil {
		return fmt.Errorf("failed to update activity: %w", err)
	}

	return nil
}

// calculateEndTime 计算活动结束时间
func (s *MarketingService) calculateEndTime(startTime time.Time, activityType models.MarketingActivityType) time.Time {
	// 确保使用中国时区
	loc, _ := time.LoadLocation("Asia/Shanghai")
	startTimeCST := startTime.In(loc)

	switch activityType {
	case models.MarketingActivityTypeDaily:
		// 日任务：当天23:59结束（中国时区）
		return time.Date(startTimeCST.Year(), startTimeCST.Month(), startTimeCST.Day(), 23, 59, 0, 0, loc)
	case models.MarketingActivityTypeWeekly:
		// 周任务：开始时间+6天23:59结束
		endDate := startTimeCST.AddDate(0, 0, 6)
		return time.Date(endDate.Year(), endDate.Month(), endDate.Day(), 23, 59, 0, 0, loc)
	case models.MarketingActivityTypeMonthly:
		// 月任务：开始时间+29天23:59结束
		endDate := startTimeCST.AddDate(0, 0, 29)
		return time.Date(endDate.Year(), endDate.Month(), endDate.Day(), 23, 59, 0, 0, loc)
	default:
		// 默认1天
		endDate := startTimeCST.AddDate(0, 0, 1)
		return time.Date(endDate.Year(), endDate.Month(), endDate.Day(), 23, 59, 0, 0, loc)
	}
}

// updateExpiredActivities 更新过期活动状态
func (s *MarketingService) updateExpiredActivities(activities *[]models.MarketingActivity) {
	var expiredIDs []uint64
	for i := range *activities {
		activity := &(*activities)[i]
		if activity.IsExpired() && activity.Status == models.MarketingActivityStatusActive {
			activity.Status = models.MarketingActivityStatusExpired
			expiredIDs = append(expiredIDs, activity.ID)
		}
	}

	// 批量更新过期状态
	if len(expiredIDs) > 0 {
		s.db.Model(&models.MarketingActivity{}).
			Where("id IN ?", expiredIDs).
			Update("status", models.MarketingActivityStatusExpired)
	}
}

// getParticipantCount 获取活动参与人数
func (s *MarketingService) getParticipantCount(activityID uint64) (int64, error) {
	var count int64
	err := s.db.Model(&models.MarketingActivityParticipant{}).
		Where("activity_id = ?", activityID).
		Distinct("user_id").
		Count(&count).Error
	return count, err
}

// UpdateActivityStatus 更新活动状态
func (s *MarketingService) UpdateActivityStatus(id uint64, status models.MarketingActivityStatus) error {
	var activity models.MarketingActivity
	if err := s.db.First(&activity, id).Error; err != nil {
		if err == gorm.ErrRecordNotFound {
			return fmt.Errorf("activity not found")
		}
		return fmt.Errorf("failed to get activity: %w", err)
	}

	// 检查状态转换是否合法
	if !s.isValidStatusTransition(activity.Status, status) {
		return fmt.Errorf("invalid status transition from %s to %s",
			activity.Status.String(), status.String())
	}

	if err := s.db.Model(&activity).Update("status", status).Error; err != nil {
		return fmt.Errorf("failed to update activity status: %w", err)
	}

	return nil
}

// DeleteActivity 删除活动
func (s *MarketingService) DeleteActivity(id uint64) error {
	var activity models.MarketingActivity
	if err := s.db.First(&activity, id).Error; err != nil {
		if err == gorm.ErrRecordNotFound {
			return fmt.Errorf("activity not found")
		}
		return fmt.Errorf("failed to get activity: %w", err)
	}

	// 检查是否可以删除
	if !activity.CanDelete() {
		return fmt.Errorf("activity cannot be deleted")
	}

	// 开启事务删除活动及相关数据
	return s.db.Transaction(func(tx *gorm.DB) error {
		// 删除参与记录
		if err := tx.Where("activity_id = ?", id).Delete(&models.MarketingActivityParticipant{}).Error; err != nil {
			return fmt.Errorf("failed to delete participants: %w", err)
		}

		// 删除活动
		if err := tx.Delete(&activity).Error; err != nil {
			return fmt.Errorf("failed to delete activity: %w", err)
		}

		return nil
	})
}

// RestartActivity 重新发起活动（复制已结束的活动）
func (s *MarketingService) RestartActivity(id uint64, req *CreateActivityRequest, creatorID string) error {
	var originalActivity models.MarketingActivity
	if err := s.db.First(&originalActivity, id).Error; err != nil {
		if err == gorm.ErrRecordNotFound {
			return fmt.Errorf("activity not found")
		}
		return fmt.Errorf("failed to get activity: %w", err)
	}

	// 检查原活动是否已结束
	if originalActivity.Status != models.MarketingActivityStatusExpired &&
		originalActivity.Status != models.MarketingActivityStatusInactive {
		return fmt.Errorf("only expired or inactive activities can be restarted")
	}

	// 创建新活动
	return s.CreateActivity(req, creatorID)
}

// isValidStatusTransition 检查状态转换是否合法
func (s *MarketingService) isValidStatusTransition(from, to models.MarketingActivityStatus) bool {
	switch from {
	case models.MarketingActivityStatusDraft:
		// 草稿可以转换为上架或删除
		return to == models.MarketingActivityStatusActive
	case models.MarketingActivityStatusActive:
		// 已上架可以转换为下架
		return to == models.MarketingActivityStatusInactive
	case models.MarketingActivityStatusInactive:
		// 已下架可以转换为上架
		return to == models.MarketingActivityStatusActive
	case models.MarketingActivityStatusExpired:
		// 已过期不能转换状态
		return false
	default:
		return false
	}
}

// GetActivityStatistics 获取活动统计信息
func (s *MarketingService) GetActivityStatistics() (map[string]interface{}, error) {
	stats := make(map[string]interface{})

	// 统计各状态活动数量
	var statusStats []struct {
		Status models.MarketingActivityStatus `json:"status"`
		Count  int64                          `json:"count"`
	}

	if err := s.db.Model(&models.MarketingActivity{}).
		Select("status, COUNT(*) as count").
		Group("status").
		Find(&statusStats).Error; err != nil {
		return nil, fmt.Errorf("failed to get status statistics: %w", err)
	}

	stats["status_stats"] = statusStats

	// 统计各分类活动数量
	var categoryStats []struct {
		Category models.MarketingCategory `json:"category"`
		Count    int64                    `json:"count"`
	}

	if err := s.db.Model(&models.MarketingActivity{}).
		Select("category, COUNT(*) as count").
		Group("category").
		Find(&categoryStats).Error; err != nil {
		return nil, fmt.Errorf("failed to get category statistics: %w", err)
	}

	stats["category_stats"] = categoryStats

	// 统计总参与人次
	var totalParticipants int64
	if err := s.db.Model(&models.MarketingActivityParticipant{}).
		Count(&totalParticipants).Error; err != nil {
		return nil, fmt.Errorf("failed to get total participants: %w", err)
	}

	stats["total_participants"] = totalParticipants

	return stats, nil
}
