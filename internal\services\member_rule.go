package services

import (
	"context"
	"fmt"
	"strconv"
	"unicode/utf8"

	"gorm.io/gorm"

	"wangfujing_admin/internal/models"
)

// MemberRuleService 会员规则服务
type MemberRuleService struct {
	db *gorm.DB
}

// NewMemberRuleService 创建会员规则服务实例
func NewMemberRuleService(db *gorm.DB) *MemberRuleService {
	return &MemberRuleService{db: db}
}

// CreateMemberRuleRequest 创建会员规则请求
type CreateMemberRuleRequest struct {
	Type           int     `json:"type" binding:"required,oneof=1 2"`
	Name           string  `json:"name" binding:"required"`
	EnablePoints   bool    `json:"enable_points"`
	Points         int     `json:"points"`
	EnableDiscount bool    `json:"enable_discount"`
	Discount       float64 `json:"discount"`
}

// UpdateMemberRuleRequest 更新会员规则请求
type UpdateMemberRuleRequest struct {
	Name           string  `json:"name" binding:"required"`
	EnablePoints   bool    `json:"enable_points"`
	Points         int     `json:"points"`
	EnableDiscount bool    `json:"enable_discount"`
	Discount       float64 `json:"discount"`
}

// CreateMemberRule 创建会员规则
func (s *MemberRuleService) CreateMemberRule(ctx context.Context, req *CreateMemberRuleRequest, creatorID string) error {
	// 验证请求参数
	if err := s.validateCreateRequest(req); err != nil {
		return err
	}

	// 检查该类型是否已存在
	var existingRule models.MembershipRule
	if err := s.db.WithContext(ctx).Where("type = ?", req.Type).First(&existingRule).Error; err == nil {
		return fmt.Errorf("该类型的规则已存在，每种类型只能创建一个规则")
	} else if err != gorm.ErrRecordNotFound {
		return fmt.Errorf("检查规则类型失败: %w", err)
	}

	// 转换创建人ID
	creatorIDUint, err := strconv.ParseUint(creatorID, 10, 64)
	if err != nil {
		return fmt.Errorf("invalid creator ID: %w", err)
	}

	// 创建规则
	rule := &models.MembershipRule{
		Type:           models.MemberRuleType(req.Type),
		Name:           req.Name,
		EnablePoints:   req.EnablePoints,
		Points:         req.Points,
		EnableDiscount: req.EnableDiscount,
		Discount:       req.Discount,
		CreatorID:      creatorIDUint,
		Status:         models.StatusActive,
	}

	if err := s.db.WithContext(ctx).Create(rule).Error; err != nil {
		return fmt.Errorf("创建会员规则失败: %w", err)
	}

	return nil
}

// GetMemberRuleList 获取会员规则列表
func (s *MemberRuleService) GetMemberRuleList(ctx context.Context) ([]*models.MembershipRule, error) {
	var rules []*models.MembershipRule

	if err := s.db.WithContext(ctx).
		Preload("Creator").
		Order("created_at DESC").
		Find(&rules).Error; err != nil {
		return nil, fmt.Errorf("获取会员规则列表失败: %w", err)
	}

	return rules, nil
}

// GetMemberRule 获取会员规则详情
func (s *MemberRuleService) GetMemberRule(ctx context.Context, id uint64) (*models.MembershipRule, error) {
	var rule models.MembershipRule

	if err := s.db.WithContext(ctx).
		Preload("Creator").
		First(&rule, id).Error; err != nil {
		if err == gorm.ErrRecordNotFound {
			return nil, fmt.Errorf("会员规则不存在")
		}
		return nil, fmt.Errorf("获取会员规则失败: %w", err)
	}

	return &rule, nil
}

// UpdateMemberRule 更新会员规则
func (s *MemberRuleService) UpdateMemberRule(ctx context.Context, id uint64, req *UpdateMemberRuleRequest) error {
	// 验证请求参数
	if err := s.validateUpdateRequest(req); err != nil {
		return err
	}

	// 检查规则是否存在
	var rule models.MembershipRule
	if err := s.db.WithContext(ctx).First(&rule, id).Error; err != nil {
		if err == gorm.ErrRecordNotFound {
			return fmt.Errorf("会员规则不存在")
		}
		return fmt.Errorf("获取会员规则失败: %w", err)
	}

	// 更新字段
	updates := map[string]interface{}{
		"name":            req.Name,
		"enable_points":   req.EnablePoints,
		"points":          req.Points,
		"enable_discount": req.EnableDiscount,
		"discount":        req.Discount,
	}

	if err := s.db.WithContext(ctx).Model(&rule).Updates(updates).Error; err != nil {
		return fmt.Errorf("更新会员规则失败: %w", err)
	}

	return nil
}

// DeleteMemberRule 删除会员规则
func (s *MemberRuleService) DeleteMemberRule(ctx context.Context, id uint64) error {
	// 检查规则是否存在
	var rule models.MembershipRule
	if err := s.db.WithContext(ctx).First(&rule, id).Error; err != nil {
		if err == gorm.ErrRecordNotFound {
			return fmt.Errorf("会员规则不存在")
		}
		return fmt.Errorf("获取会员规则失败: %w", err)
	}

	if err := s.db.WithContext(ctx).Delete(&rule).Error; err != nil {
		return fmt.Errorf("删除会员规则失败: %w", err)
	}

	return nil
}

// GiveRegisterPoints 给用户发放注册积分
func (s *MemberRuleService) GiveRegisterPoints(ctx context.Context, userID uint64) error {
	// 获取注册规则
	var rule models.MembershipRule
	if err := s.db.WithContext(ctx).Where("type = ? AND status = ?",
		models.MemberRuleTypeRegister, models.StatusActive).First(&rule).Error; err != nil {
		if err == gorm.ErrRecordNotFound {
			// 没有注册规则，不处理
			return nil
		}
		return fmt.Errorf("获取注册规则失败: %w", err)
	}

	// 如果没有启用积分奖励或积分为0，不处理
	if !rule.EnablePoints || rule.Points <= 0 {
		return nil
	}

	// 检查用户是否已经获得过注册积分
	var existingRecord models.PointsRecord
	if err := s.db.WithContext(ctx).Where("user_id = ? AND source = ? AND related_type = ?",
		userID, "注册奖励", "membership_rule").First(&existingRecord).Error; err == nil {
		// 已经获得过注册积分，不重复发放
		return nil
	} else if err != gorm.ErrRecordNotFound {
		return fmt.Errorf("检查用户积分记录失败: %w", err)
	}

	// 开始事务
	return s.db.WithContext(ctx).Transaction(func(tx *gorm.DB) error {
		// 更新用户积分
		if err := tx.Model(&models.User{}).Where("id = ?", userID).
			Update("points", gorm.Expr("points + ?", rule.Points)).Error; err != nil {
			return fmt.Errorf("更新用户积分失败: %w", err)
		}

		// 获取更新后的用户积分余额
		var user models.User
		if err := tx.Select("points").First(&user, userID).Error; err != nil {
			return fmt.Errorf("获取用户积分余额失败: %w", err)
		}

		// 创建积分记录
		record := &models.PointsRecord{
			UserID:      userID,
			Type:        models.PointsTypeEarn,
			Points:      rule.Points,
			Balance:     user.Points,
			Source:      "注册奖励",
			SourceID:    &rule.ID,
			RelatedID:   &rule.ID,
			RelatedType: "membership_rule",
			Description: fmt.Sprintf("注册奖励：%s", rule.Name),
		}

		if err := tx.Create(record).Error; err != nil {
			return fmt.Errorf("创建积分记录失败: %w", err)
		}

		return nil
	})
}

// validateCreateRequest 验证创建请求
func (s *MemberRuleService) validateCreateRequest(req *CreateMemberRuleRequest) error {
	// 验证规则名称
	if req.Name == "" {
		return fmt.Errorf("规则名称不能为空")
	}
	nameLen := utf8.RuneCountInString(req.Name)
	if nameLen > 10 {
		return fmt.Errorf("规则名称长度不能超过10字符")
	}

	// 验证积分设置
	if req.EnablePoints {
		if req.Points < 1 || req.Points > 100 {
			return fmt.Errorf("积分数量必须在1-100之间")
		}
	}

	// 验证折扣设置
	if req.EnableDiscount {
		if req.Discount < 1.0 || req.Discount > 9.9 {
			return fmt.Errorf("折扣必须在1.0-9.9之间")
		}
	}

	// 至少要启用一种权益
	if !req.EnablePoints && !req.EnableDiscount {
		return fmt.Errorf("至少要启用一种权益（积分或折扣）")
	}

	return nil
}

// validateUpdateRequest 验证更新请求
func (s *MemberRuleService) validateUpdateRequest(req *UpdateMemberRuleRequest) error {
	// 验证规则名称
	if req.Name == "" {
		return fmt.Errorf("规则名称不能为空")
	}
	nameLen := utf8.RuneCountInString(req.Name)
	if nameLen > 10 {
		return fmt.Errorf("规则名称长度不能超过10字符")
	}

	// 验证积分设置
	if req.EnablePoints {
		if req.Points < 1 || req.Points > 100 {
			return fmt.Errorf("积分数量必须在1-100之间")
		}
	}

	// 验证折扣设置
	if req.EnableDiscount {
		if req.Discount < 1.0 || req.Discount > 9.9 {
			return fmt.Errorf("折扣必须在1.0-9.9之间")
		}
	}

	// 至少要启用一种权益
	if !req.EnablePoints && !req.EnableDiscount {
		return fmt.Errorf("至少要启用一种权益（积分或折扣）")
	}

	return nil
}

// GiveConsumePoints 给用户发放消费积分（核销成功后）
func (s *MemberRuleService) GiveConsumePoints(ctx context.Context, userID uint64, orderID uint64) error {
	// 获取消费规则
	var rule models.MembershipRule
	if err := s.db.WithContext(ctx).Where("type = ? AND status = ?",
		models.MemberRuleTypeConsume, models.StatusActive).First(&rule).Error; err != nil {
		if err == gorm.ErrRecordNotFound {
			// 没有消费规则，不处理
			return nil
		}
		return fmt.Errorf("获取消费规则失败: %w", err)
	}

	// 如果没有启用积分奖励或积分为0，不处理
	if !rule.EnablePoints || rule.Points <= 0 {
		return nil
	}

	// 检查该订单是否已经获得过消费积分
	var existingRecord models.PointsRecord
	if err := s.db.WithContext(ctx).Where("user_id = ? AND source = ? AND related_id = ? AND related_type = ?",
		userID, "消费奖励", orderID, "order_verification").First(&existingRecord).Error; err == nil {
		// 该订单已经获得过消费积分，不重复发放
		return nil
	} else if err != gorm.ErrRecordNotFound {
		return fmt.Errorf("检查用户消费积分记录失败: %w", err)
	}

	// 开始事务
	return s.db.WithContext(ctx).Transaction(func(tx *gorm.DB) error {
		// 更新用户积分
		if err := tx.Model(&models.User{}).Where("id = ?", userID).
			Update("points", gorm.Expr("points + ?", rule.Points)).Error; err != nil {
			return fmt.Errorf("更新用户积分失败: %w", err)
		}

		// 获取更新后的用户积分余额
		var user models.User
		if err := tx.Select("points").First(&user, userID).Error; err != nil {
			return fmt.Errorf("获取用户积分余额失败: %w", err)
		}

		// 创建积分记录
		record := &models.PointsRecord{
			UserID:      userID,
			Type:        models.PointsTypeEarn,
			Points:      rule.Points,
			Balance:     user.Points,
			Source:      "消费奖励",
			SourceID:    &rule.ID,
			RelatedID:   &orderID,
			RelatedType: "order_verification",
			Description: fmt.Sprintf("消费奖励：%s", rule.Name),
		}

		if err := tx.Create(record).Error; err != nil {
			return fmt.Errorf("创建积分记录失败: %w", err)
		}

		return nil
	})
}
