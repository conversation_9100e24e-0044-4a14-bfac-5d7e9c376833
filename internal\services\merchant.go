package services

import (
	"context"
	"errors"
	"fmt"

	"wangfujing_admin/internal/models"

	"gorm.io/gorm"
)

// MerchantService 商家服务
type MerchantService struct {
	db *gorm.DB
}

// NewMerchantService 创建商家服务
func NewMerchantService(db *gorm.DB) *MerchantService {
	return &MerchantService{
		db: db,
	}
}

// CreateMerchant 创建商家
func (s *MerchantService) CreateMerchant(ctx context.Context, merchant *models.Merchant) error {
	if err := s.db.WithContext(ctx).Create(merchant).Error; err != nil {
		return fmt.Errorf("failed to create merchant: %w", err)
	}
	return nil
}

// GetMerchantByID 根据ID获取商家
func (s *MerchantService) GetMerchantByID(ctx context.Context, id string) (*models.Merchant, error) {
	var merchant models.Merchant
	if err := s.db.WithContext(ctx).Preload("Floor").First(&merchant, "id = ?", id).Error; err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return nil, errors.New("merchant not found")
		}
		return nil, fmt.Errorf("failed to get merchant: %w", err)
	}
	return &merchant, nil
}

// GetMerchants 获取商家列表
func (s *MerchantService) GetMerchants(ctx context.Context, page, size int, floorID string, status *models.Status) ([]*models.Merchant, int64, error) {
	var merchants []*models.Merchant
	var total int64

	query := s.db.WithContext(ctx).Model(&models.Merchant{})
	
	if floorID != "" {
		query = query.Where("floor_id = ?", floorID)
	}
	if status != nil {
		query = query.Where("status = ?", *status)
	}

	if err := query.Count(&total).Error; err != nil {
		return nil, 0, fmt.Errorf("failed to count merchants: %w", err)
	}

	offset := (page - 1) * size
	if err := query.Preload("Floor").Offset(offset).Limit(size).Order("created_at DESC").Find(&merchants).Error; err != nil {
		return nil, 0, fmt.Errorf("failed to get merchants: %w", err)
	}

	return merchants, total, nil
}

// UpdateMerchant 更新商家
func (s *MerchantService) UpdateMerchant(ctx context.Context, id string, updates map[string]interface{}) error {
	result := s.db.WithContext(ctx).Model(&models.Merchant{}).Where("id = ?", id).Updates(updates)
	if result.Error != nil {
		return fmt.Errorf("failed to update merchant: %w", result.Error)
	}
	if result.RowsAffected == 0 {
		return errors.New("merchant not found")
	}
	return nil
}

// DeleteMerchant 删除商家
func (s *MerchantService) DeleteMerchant(ctx context.Context, id string) error {
	result := s.db.WithContext(ctx).Delete(&models.Merchant{}, "id = ?", id)
	if result.Error != nil {
		return fmt.Errorf("failed to delete merchant: %w", result.Error)
	}
	if result.RowsAffected == 0 {
		return errors.New("merchant not found")
	}
	return nil
}
