package services

import (
	"fmt"
	"strconv"
	"time"

	"wangfujing_admin/internal/models"

	"gorm.io/gorm"
)

// MerchantActivityService 商家活动服务
type MerchantActivityService struct {
	db *gorm.DB
}

// NewMerchantActivityService 创建商家活动服务实例
func NewMerchantActivityService(db *gorm.DB) *MerchantActivityService {
	return &MerchantActivityService{db: db}
}

// CreateMerchantActivityRequest 创建商家活动请求
type CreateMerchantActivityRequest struct {
	Name              string `json:"name"`
	Description       string `json:"description"`
	Image             string `json:"image"`
	LevelLimit        int    `json:"level_limit" binding:"required,min=1,max=99"`
	StartTime         string `json:"start_time" binding:"required"`
	EndTime           string `json:"end_time" binding:"required"`
	ContributionScore int    `json:"contribution_score" binding:"required,min=1,max=1000"`
}

// UpdateMerchantActivityRequest 更新商家活动请求
type UpdateMerchantActivityRequest struct {
	Name              string `json:"name"`
	Description       string `json:"description"`
	Image             string `json:"image"`
	LevelLimit        int    `json:"level_limit" binding:"required,min=1,max=99"`
	StartTime         string `json:"start_time" binding:"required"`
	EndTime           string `json:"end_time" binding:"required"`
	ContributionScore int    `json:"contribution_score" binding:"required,min=1,max=1000"`
}

// MerchantActivityListRequest 商家活动列表请求
type MerchantActivityListRequest struct {
	Page    int                            `form:"page,default=1"`
	Size    int                            `form:"size,default=10"`
	Status  *models.MerchantActivityStatus `form:"status"`
	Keyword string                         `form:"keyword"`
}

// CreateMerchantActivity 创建商家活动
func (s *MerchantActivityService) CreateMerchantActivity(req *CreateMerchantActivityRequest, creatorID string) error {
	// 解析创建人ID
	creatorIDUint, err := strconv.ParseUint(creatorID, 10, 64)
	if err != nil {
		return fmt.Errorf("invalid creator ID: %w", err)
	}

	// 解析开始时间，明确使用中国时区
	loc, err := time.LoadLocation("Asia/Shanghai")
	if err != nil {
		return fmt.Errorf("failed to load timezone: %w", err)
	}
	startTime, err := time.ParseInLocation("2006-01-02 15:04", req.StartTime, loc)
	if err != nil {
		return fmt.Errorf("invalid start time format: %w", err)
	}

	// 解析结束时间，明确使用中国时区
	endTime, err := time.ParseInLocation("2006-01-02 15:04", req.EndTime, loc)
	if err != nil {
		return fmt.Errorf("invalid end time format: %w", err)
	}

	// 验证时间逻辑
	if endTime.Before(startTime) {
		return fmt.Errorf("end time must be after start time")
	}

	activity := &models.MerchantActivity{
		Name:              req.Name,
		Description:       req.Description,
		Image:             req.Image,
		LevelLimit:        req.LevelLimit,
		StartTime:         &startTime,
		EndTime:           &endTime,
		ContributionScore: req.ContributionScore,
		Status:            models.MerchantActivityStatusActive, // 默认上架
		CreatorID:         creatorIDUint,
	}

	if err := s.db.Create(activity).Error; err != nil {
		return fmt.Errorf("failed to create merchant activity: %w", err)
	}

	return nil
}

// GetMerchantActivityList 获取商家活动列表
func (s *MerchantActivityService) GetMerchantActivityList(req *MerchantActivityListRequest) ([]models.MerchantActivity, int64, error) {
	var activities []models.MerchantActivity
	var total int64

	query := s.db.Model(&models.MerchantActivity{})

	// 筛选条件
	if req.Status != nil {
		query = query.Where("status = ?", *req.Status)
	}
	if req.Keyword != "" {
		query = query.Where("name LIKE ? OR description LIKE ?",
			"%"+req.Keyword+"%", "%"+req.Keyword+"%")
	}

	// 获取总数
	if err := query.Count(&total).Error; err != nil {
		return nil, 0, fmt.Errorf("failed to count merchant activities: %w", err)
	}

	// 分页查询
	offset := (req.Page - 1) * req.Size
	if err := query.Preload("Creator").
		Order("created_at DESC").
		Offset(offset).
		Limit(req.Size).
		Find(&activities).Error; err != nil {
		return nil, 0, fmt.Errorf("failed to get merchant activities: %w", err)
	}

	// 更新过期状态
	s.updateExpiredActivities(&activities)

	// 统计参与人数
	for i := range activities {
		count, _ := s.getParticipantCount(activities[i].ID)
		activities[i].ParticipantCount = count
	}

	return activities, total, nil
}

// GetMerchantActivity 获取商家活动详情
func (s *MerchantActivityService) GetMerchantActivity(id uint64) (*models.MerchantActivity, error) {
	var activity models.MerchantActivity
	if err := s.db.Preload("Creator").First(&activity, id).Error; err != nil {
		if err == gorm.ErrRecordNotFound {
			return nil, fmt.Errorf("merchant activity not found")
		}
		return nil, fmt.Errorf("failed to get merchant activity: %w", err)
	}

	// 检查并更新过期状态
	if activity.IsExpired() && activity.Status == models.MerchantActivityStatusActive {
		activity.Status = models.MerchantActivityStatusExpired
		s.db.Model(&activity).Update("status", models.MerchantActivityStatusExpired)
	}

	// 统计参与人数
	count, _ := s.getParticipantCount(activity.ID)
	activity.ParticipantCount = count

	return &activity, nil
}

// UpdateMerchantActivity 更新商家活动
func (s *MerchantActivityService) UpdateMerchantActivity(id uint64, req *UpdateMerchantActivityRequest) error {
	var activity models.MerchantActivity
	if err := s.db.First(&activity, id).Error; err != nil {
		if err == gorm.ErrRecordNotFound {
			return fmt.Errorf("merchant activity not found")
		}
		return fmt.Errorf("failed to get merchant activity: %w", err)
	}

	// 检查是否可以编辑
	if !activity.CanEdit() {
		return fmt.Errorf("merchant activity cannot be edited")
	}

	// 解析开始时间，明确使用中国时区
	loc, err := time.LoadLocation("Asia/Shanghai")
	if err != nil {
		return fmt.Errorf("failed to load timezone: %w", err)
	}
	startTime, err := time.ParseInLocation("2006-01-02 15:04", req.StartTime, loc)
	if err != nil {
		return fmt.Errorf("invalid start time format: %w", err)
	}

	// 解析结束时间，明确使用中国时区
	endTime, err := time.ParseInLocation("2006-01-02 15:04", req.EndTime, loc)
	if err != nil {
		return fmt.Errorf("invalid end time format: %w", err)
	}

	// 验证时间逻辑
	if endTime.Before(startTime) {
		return fmt.Errorf("end time must be after start time")
	}

	// 更新字段
	updates := map[string]interface{}{
		"name":               req.Name,
		"description":        req.Description,
		"image":              req.Image,
		"level_limit":        req.LevelLimit,
		"start_time":         &startTime,
		"end_time":           &endTime,
		"contribution_score": req.ContributionScore,
	}

	if err := s.db.Model(&activity).Updates(updates).Error; err != nil {
		return fmt.Errorf("failed to update merchant activity: %w", err)
	}

	return nil
}

// UpdateMerchantActivityStatus 更新商家活动状态
func (s *MerchantActivityService) UpdateMerchantActivityStatus(id uint64, status models.MerchantActivityStatus) error {
	var activity models.MerchantActivity
	if err := s.db.First(&activity, id).Error; err != nil {
		if err == gorm.ErrRecordNotFound {
			return fmt.Errorf("merchant activity not found")
		}
		return fmt.Errorf("failed to get merchant activity: %w", err)
	}

	// 检查状态转换是否合法
	if !s.isValidStatusTransition(activity.Status, status) {
		return fmt.Errorf("invalid status transition from %s to %s",
			activity.Status.String(), status.String())
	}

	if err := s.db.Model(&activity).Update("status", status).Error; err != nil {
		return fmt.Errorf("failed to update merchant activity status: %w", err)
	}

	return nil
}

// DeleteMerchantActivity 删除商家活动
func (s *MerchantActivityService) DeleteMerchantActivity(id uint64) error {
	var activity models.MerchantActivity
	if err := s.db.First(&activity, id).Error; err != nil {
		if err == gorm.ErrRecordNotFound {
			return fmt.Errorf("merchant activity not found")
		}
		return fmt.Errorf("failed to get merchant activity: %w", err)
	}

	// 检查是否可以删除
	if !activity.CanDelete() {
		return fmt.Errorf("merchant activity cannot be deleted")
	}

	// 开启事务删除活动及相关数据
	return s.db.Transaction(func(tx *gorm.DB) error {
		// 删除参与记录
		if err := tx.Where("activity_id = ?", id).Delete(&models.MerchantActivityParticipant{}).Error; err != nil {
			return fmt.Errorf("failed to delete participants: %w", err)
		}

		// 删除活动
		if err := tx.Delete(&activity).Error; err != nil {
			return fmt.Errorf("failed to delete merchant activity: %w", err)
		}

		return nil
	})
}

// RestartMerchantActivity 重新发起商家活动
func (s *MerchantActivityService) RestartMerchantActivity(id uint64, req *CreateMerchantActivityRequest, creatorID string) error {
	var originalActivity models.MerchantActivity
	if err := s.db.First(&originalActivity, id).Error; err != nil {
		if err == gorm.ErrRecordNotFound {
			return fmt.Errorf("merchant activity not found")
		}
		return fmt.Errorf("failed to get merchant activity: %w", err)
	}

	// 检查原活动是否可以重新发起
	if !originalActivity.CanRestart() {
		return fmt.Errorf("only expired or inactive activities can be restarted")
	}

	// 创建新活动
	return s.CreateMerchantActivity(req, creatorID)
}

// updateExpiredActivities 更新过期活动状态
func (s *MerchantActivityService) updateExpiredActivities(activities *[]models.MerchantActivity) {
	var expiredIDs []uint64
	for i := range *activities {
		activity := &(*activities)[i]
		if activity.IsExpired() && activity.Status == models.MerchantActivityStatusActive {
			activity.Status = models.MerchantActivityStatusExpired
			expiredIDs = append(expiredIDs, activity.ID)
		}
	}

	// 批量更新过期状态
	if len(expiredIDs) > 0 {
		s.db.Model(&models.MerchantActivity{}).
			Where("id IN ?", expiredIDs).
			Update("status", models.MerchantActivityStatusExpired)
	}
}

// getParticipantCount 获取活动参与人数
func (s *MerchantActivityService) getParticipantCount(activityID uint64) (int64, error) {
	var count int64
	err := s.db.Model(&models.MerchantActivityParticipant{}).
		Where("activity_id = ?", activityID).
		Count(&count).Error
	return count, err
}

// isValidStatusTransition 检查状态转换是否合法
func (s *MerchantActivityService) isValidStatusTransition(from, to models.MerchantActivityStatus) bool {
	switch from {
	case models.MerchantActivityStatusDraft:
		// 草稿可以转换为上架
		return to == models.MerchantActivityStatusActive
	case models.MerchantActivityStatusActive:
		// 已上架可以转换为下架
		return to == models.MerchantActivityStatusInactive
	case models.MerchantActivityStatusInactive:
		// 已下架可以转换为上架
		return to == models.MerchantActivityStatusActive
	case models.MerchantActivityStatusExpired:
		// 已过期不能转换状态
		return false
	default:
		return false
	}
}
