package services

import (
	"context"
	"fmt"
	"time"

	"gorm.io/gorm"

	"wangfujing_admin/internal/models"
)

// MerchantActivityParticipationService 商家活动参与服务
type MerchantActivityParticipationService struct {
	db *gorm.DB
}

// NewMerchantActivityParticipationService 创建商家活动参与服务实例
func NewMerchantActivityParticipationService(db *gorm.DB) *MerchantActivityParticipationService {
	return &MerchantActivityParticipationService{db: db}
}

// GetActivities 获取活动列表（商家端）- 包含已报名的活动，不包含已过期的
func (s *MerchantActivityParticipationService) GetActivities(ctx context.Context, merchantID uint64, page, size int, status string) ([]*ActivityListItem, int64, error) {
	// 获取商家信息以确定等级
	var merchant models.Merchant
	if err := s.db.WithContext(ctx).First(&merchant, merchantID).Error; err != nil {
		return nil, 0, fmt.Errorf("failed to get merchant info: %w", err)
	}

	var activities []models.MerchantActivity
	var total int64

	// 构建查询条件：已上架且未过期的活动
	now := time.Now()
	query := s.db.WithContext(ctx).Model(&models.MerchantActivity{}).
		Where("status = ? AND end_time > ?", models.MerchantActivityStatusActive, now)

	// 根据状态筛选
	switch status {
	case "ongoing":
		// 进行中：开始时间 <= 现在 <= 结束时间
		query = query.Where("start_time <= ? AND end_time >= ?", now, now)
	case "upcoming":
		// 待开始：开始时间 > 现在
		query = query.Where("start_time > ?", now)
	}

	// 统计总数
	if err := query.Count(&total).Error; err != nil {
		return nil, 0, fmt.Errorf("failed to count activities: %w", err)
	}

	// 分页查询
	offset := (page - 1) * size
	if err := query.Order("start_time DESC").Offset(offset).Limit(size).Find(&activities).Error; err != nil {
		return nil, 0, fmt.Errorf("failed to get activities: %w", err)
	}

	// 转换为响应格式
	var result []*ActivityListItem
	for _, activity := range activities {
		// 检查是否已报名
		isRegistered, err := s.isActivityRegistered(ctx, merchantID, activity.ID)
		if err != nil {
			continue // 跳过错误的记录
		}

		// 检查是否可以报名（基于积分排名）- 只影响报名按钮，不影响可见性
		canRegister, err := s.canMerchantRegister(ctx, merchantID, activity.LevelLimit)
		if err != nil {
			// 如果排名检查失败，设置为不可报名但仍显示活动
			canRegister = false
		}

		item := &ActivityListItem{
			ID:                activity.ID,
			Name:              activity.Name,
			Description:       activity.Description,
			Image:             activity.Image,
			LevelLimit:        activity.LevelLimit,
			StartTime:         formatActivityTime(activity.StartTime),
			EndTime:           formatActivityTime(activity.EndTime),
			ContributionScore: activity.ContributionScore,
			Status:            activity.Status,
			IsRegistered:      isRegistered,
			CanRegister:       canRegister && !isRegistered && !activity.IsExpired(),
		}

		result = append(result, item)
	}

	return result, total, nil
}

// GetRegisteredActivities 获取已报名的活动列表（商家端）
func (s *MerchantActivityParticipationService) GetRegisteredActivities(ctx context.Context, merchantID uint64, status string, page, size int) ([]*ActivityListItem, int64, error) {
	var participants []models.MerchantActivityParticipant
	var total int64

	// 构建查询条件
	query := s.db.WithContext(ctx).Model(&models.MerchantActivityParticipant{}).
		Where("merchant_activity_participants.merchant_id = ? AND merchant_activity_participants.status = ?", merchantID, 1) // 1:已报名

	// 根据状态筛选
	now := time.Now()
	switch status {
	case "ongoing":
		// 进行中：开始时间 <= 现在 <= 结束时间
		query = query.Joins("JOIN merchant_activities ON merchant_activity_participants.activity_id = merchant_activities.id").
			Where("merchant_activities.start_time <= ? AND merchant_activities.end_time >= ?", now, now)
	case "upcoming":
		// 待开始：开始时间 > 现在
		query = query.Joins("JOIN merchant_activities ON merchant_activity_participants.activity_id = merchant_activities.id").
			Where("merchant_activities.start_time > ?", now)
	case "finished":
		// 已结束：结束时间 < 现在
		query = query.Joins("JOIN merchant_activities ON merchant_activity_participants.activity_id = merchant_activities.id").
			Where("merchant_activities.end_time < ?", now)
	}

	// 统计总数
	if err := query.Count(&total).Error; err != nil {
		return nil, 0, fmt.Errorf("failed to count registered activities: %w", err)
	}

	// 分页查询
	offset := (page - 1) * size
	if err := query.Preload("Activity").Order("register_time DESC").Offset(offset).Limit(size).Find(&participants).Error; err != nil {
		return nil, 0, fmt.Errorf("failed to get registered activities: %w", err)
	}

	// 转换为响应格式
	var result []*ActivityListItem
	for _, participant := range participants {
		if participant.Activity == nil {
			continue
		}

		activity := participant.Activity
		item := &ActivityListItem{
			ID:                activity.ID,
			Name:              activity.Name,
			Description:       activity.Description,
			Image:             activity.Image,
			LevelLimit:        activity.LevelLimit,
			StartTime:         formatActivityTime(activity.StartTime),
			EndTime:           formatActivityTime(activity.EndTime),
			ContributionScore: activity.ContributionScore,
			Status:            activity.Status,
			IsRegistered:      true,
			CanRegister:       false,
			RegisterTime:      formatTimeValue(participant.RegisterTime),
		}

		result = append(result, item)
	}

	return result, total, nil
}

// GetActivityDetail 获取活动详情（商家端）
func (s *MerchantActivityParticipationService) GetActivityDetail(ctx context.Context, merchantID, activityID uint64) (*ActivityDetailResponse, error) {
	// 获取活动信息
	var activity models.MerchantActivity
	if err := s.db.WithContext(ctx).First(&activity, activityID).Error; err != nil {
		if err == gorm.ErrRecordNotFound {
			return nil, fmt.Errorf("activity not found")
		}
		return nil, fmt.Errorf("failed to get activity: %w", err)
	}

	// 获取商家信息
	var merchant models.Merchant
	if err := s.db.WithContext(ctx).First(&merchant, merchantID).Error; err != nil {
		return nil, fmt.Errorf("failed to get merchant info: %w", err)
	}

	// 检查是否已报名
	isRegistered, err := s.isActivityRegistered(ctx, merchantID, activityID)
	if err != nil {
		return nil, fmt.Errorf("failed to check registration status: %w", err)
	}

	// 检查是否可以报名（基于积分排名）- 只影响报名按钮，不影响详情查看
	canRegister, err := s.canMerchantRegister(ctx, merchantID, activity.LevelLimit)
	if err != nil {
		// 如果排名检查失败，设置为不可报名但仍可查看详情
		canRegister = false
	}

	// 检查活动是否已结束并且已报名
	var earnedContribution int
	if isRegistered && activity.IsExpired() {
		earnedContribution = activity.ContributionScore
	}

	response := &ActivityDetailResponse{
		ID:                 activity.ID,
		Name:               activity.Name,
		Description:        activity.Description,
		Image:              activity.Image,
		LevelLimit:         activity.LevelLimit,
		StartTime:          formatActivityTime(activity.StartTime),
		EndTime:            formatActivityTime(activity.EndTime),
		ContributionScore:  activity.ContributionScore,
		Status:             activity.Status,
		IsRegistered:       isRegistered,
		CanRegister:        canRegister && !isRegistered && !activity.IsExpired(),
		EarnedContribution: earnedContribution,
	}

	return response, nil
}

// RegisterActivity 报名活动（商家端）
func (s *MerchantActivityParticipationService) RegisterActivity(ctx context.Context, merchantID, activityID uint64) error {
	// 获取活动信息
	var activity models.MerchantActivity
	if err := s.db.WithContext(ctx).First(&activity, activityID).Error; err != nil {
		if err == gorm.ErrRecordNotFound {
			return fmt.Errorf("activity not found")
		}
		return fmt.Errorf("failed to get activity: %w", err)
	}

	// 获取商家信息
	var merchant models.Merchant
	if err := s.db.WithContext(ctx).First(&merchant, merchantID).Error; err != nil {
		return fmt.Errorf("failed to get merchant info: %w", err)
	}

	// 检查活动是否已过期
	if activity.IsExpired() {
		return fmt.Errorf("activity has expired")
	}

	// 检查是否可以报名（基于积分排名）
	canRegister, err := s.canMerchantRegister(ctx, merchantID, activity.LevelLimit)
	if err != nil {
		return fmt.Errorf("failed to check registration eligibility: %w", err)
	}
	if !canRegister {
		return fmt.Errorf("merchant ranking does not meet activity requirements")
	}

	// 检查是否已经报名
	isRegistered, err := s.isActivityRegistered(ctx, merchantID, activityID)
	if err != nil {
		return fmt.Errorf("failed to check registration status: %w", err)
	}
	if isRegistered {
		return fmt.Errorf("already registered for this activity")
	}

	// 创建报名记录
	participant := &models.MerchantActivityParticipant{
		ActivityID:   activityID,
		MerchantID:   merchantID,
		RegisterTime: time.Now(),
		Status:       1, // 1:已报名
	}

	if err := s.db.WithContext(ctx).Create(participant).Error; err != nil {
		return fmt.Errorf("failed to register activity: %w", err)
	}

	return nil
}

// isActivityRegistered 检查商家是否已报名活动
func (s *MerchantActivityParticipationService) isActivityRegistered(ctx context.Context, merchantID, activityID uint64) (bool, error) {
	var count int64
	if err := s.db.WithContext(ctx).Model(&models.MerchantActivityParticipant{}).
		Where("merchant_id = ? AND activity_id = ? AND status = ?", merchantID, activityID, 1).
		Count(&count).Error; err != nil {
		return false, err
	}
	return count > 0, nil
}

// ActivityListItem 活动列表项
type ActivityListItem struct {
	ID                uint64                        `json:"id"`
	Name              string                        `json:"name"`
	Description       string                        `json:"description"`
	Image             string                        `json:"image"`
	LevelLimit        int                           `json:"level_limit"`
	StartTime         string                        `json:"start_time"`
	EndTime           string                        `json:"end_time"`
	ContributionScore int                           `json:"contribution_score"`
	Status            models.MerchantActivityStatus `json:"status"`
	IsRegistered      bool                          `json:"is_registered"`
	CanRegister       bool                          `json:"can_register"`
	RegisterTime      string                        `json:"register_time,omitempty"`
}

// ActivityDetailResponse 活动详情响应
type ActivityDetailResponse struct {
	ID                 uint64                        `json:"id"`
	Name               string                        `json:"name"`
	Description        string                        `json:"description"`
	Image              string                        `json:"image"`
	LevelLimit         int                           `json:"level_limit"`
	StartTime          string                        `json:"start_time"`
	EndTime            string                        `json:"end_time"`
	ContributionScore  int                           `json:"contribution_score"`
	Status             models.MerchantActivityStatus `json:"status"`
	IsRegistered       bool                          `json:"is_registered"`
	CanRegister        bool                          `json:"can_register"`
	EarnedContribution int                           `json:"earned_contribution"` // 已获得的贡献度（活动结束后显示）
}

// canMerchantRegister 检查商家是否可以报名（基于积分排名）
func (s *MerchantActivityParticipationService) canMerchantRegister(ctx context.Context, merchantID uint64, levelLimit int) (bool, error) {
	// 获取商家信息
	var merchant models.Merchant
	if err := s.db.WithContext(ctx).First(&merchant, merchantID).Error; err != nil {
		return false, fmt.Errorf("failed to get merchant info: %w", err)
	}

	// 计算商家在积分排名中的位置
	var rank int64
	if err := s.db.WithContext(ctx).Model(&models.Merchant{}).
		Where("score > ? AND status = ?", merchant.Score, models.StatusActive).
		Count(&rank).Error; err != nil {
		return false, fmt.Errorf("failed to calculate merchant rank: %w", err)
	}

	// 排名从1开始，所以当前商家的排名是 rank + 1
	currentRank := rank + 1

	// 检查是否在前 levelLimit 名内（包含第 levelLimit 名）
	return currentRank <= int64(levelLimit), nil
}

// getMerchantRank 获取商家的积分排名
func (s *MerchantActivityParticipationService) getMerchantRank(ctx context.Context, merchantID uint64) (int64, error) {
	// 获取商家信息
	var merchant models.Merchant
	if err := s.db.WithContext(ctx).First(&merchant, merchantID).Error; err != nil {
		return 0, fmt.Errorf("failed to get merchant info: %w", err)
	}

	// 计算排名：比当前商家积分高的商家数量 + 1
	var rank int64
	if err := s.db.WithContext(ctx).Model(&models.Merchant{}).
		Where("score > ? AND status = ?", merchant.Score, models.StatusActive).
		Count(&rank).Error; err != nil {
		return 0, fmt.Errorf("failed to calculate merchant rank: %w", err)
	}

	return rank + 1, nil
}

// formatActivityTime 格式化活动时间为 "2025-07-10 12:00" 格式
func formatActivityTime(t *time.Time) string {
	if t == nil || t.IsZero() {
		return ""
	}
	return t.Format("2006-01-02 15:04")
}

// formatStandardTime 格式化标准时间为 "2025-07-10 12:00:00" 格式
func formatStandardTime(t *time.Time) string {
	if t == nil || t.IsZero() {
		return ""
	}
	return t.Format("2006-01-02 15:04:05")
}

// formatTimeValue 格式化时间值
func formatTimeValue(t time.Time) string {
	if t.IsZero() {
		return ""
	}
	return t.Format("2006-01-02 15:04:05")
}
