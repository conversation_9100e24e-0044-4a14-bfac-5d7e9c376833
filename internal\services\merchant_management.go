package services

import (
	"context"
	"encoding/json"
	"fmt"
	"time"
	"wangfujing_admin/internal/models"

	"gorm.io/gorm"
)

// MerchantManagementService 商家管理服务
type MerchantManagementService struct {
	db *gorm.DB
}

// NewMerchantManagementService 创建商家管理服务
func NewMerchantManagementService(db *gorm.DB) *MerchantManagementService {
	return &MerchantManagementService{
		db: db,
	}
}

// RankingType 排行类型
type RankingType string

const (
	RankingTypeSales      RankingType = "sales"      // 销售排行
	RankingTypeScore      RankingType = "score"      // 等级贡献
	RankingTypeEfficiency RankingType = "efficiency" // 评效排行
)

// SortOrder 排序方式
type SortOrder string

const (
	SortOrderDesc SortOrder = "desc" // 降序（高到低）
	SortOrderAsc  SortOrder = "asc"  // 升序（低到高）
)

// MerchantRankingItem 商家排行项
type MerchantRankingItem struct {
	ID          uint64  `json:"id"`
	Name        string  `json:"name"`
	Cover       string  `json:"cover"`
	Phone       string  `json:"phone"`
	FloorName   string  `json:"floor_name"`
	Position    string  `json:"position"`
	Score       int     `json:"score"`
	Sales       float64 `json:"sales"`
	Area        float64 `json:"area"`
	Efficiency  float64 `json:"efficiency"`
	Rank        int     `json:"rank"`
	IsTop3      bool    `json:"is_top3"`
	Description string  `json:"description"`
}

// GetMerchantRanking 获取商家排行
func (s *MerchantManagementService) GetMerchantRanking(ctx context.Context, rankingType RankingType, sortOrder SortOrder, page, size int) ([]*MerchantRankingItem, int64, error) {
	var merchants []models.Merchant
	var total int64

	// 构建查询
	query := s.db.WithContext(ctx).Model(&models.Merchant{}).Where("status = ?", models.StatusActive)

	// 统计总数
	if err := query.Count(&total).Error; err != nil {
		return nil, 0, fmt.Errorf("failed to count merchants: %w", err)
	}

	// 根据排行类型设置排序
	var orderBy string
	switch rankingType {
	case RankingTypeSales:
		if sortOrder == SortOrderDesc {
			orderBy = "sales DESC"
		} else {
			orderBy = "sales ASC"
		}
	case RankingTypeScore:
		if sortOrder == SortOrderDesc {
			orderBy = "score DESC"
		} else {
			orderBy = "score ASC"
		}
	case RankingTypeEfficiency:
		// 评效 = 销售额 / 面积
		if sortOrder == SortOrderDesc {
			orderBy = "(sales / NULLIF(area, 0)) DESC"
		} else {
			orderBy = "(sales / NULLIF(area, 0)) ASC"
		}
	default:
		orderBy = "sales DESC"
	}

	// 分页查询
	offset := (page - 1) * size
	if err := query.Offset(offset).Limit(size).Order(orderBy).Find(&merchants).Error; err != nil {
		return nil, 0, fmt.Errorf("failed to get merchants: %w", err)
	}

	// 获取楼层信息
	floorMap := make(map[uint64]string)
	var floorIDs []uint64
	for _, merchant := range merchants {
		floorIDs = append(floorIDs, merchant.FloorID)
	}

	if len(floorIDs) > 0 {
		var floors []models.Floor
		if err := s.db.WithContext(ctx).Where("id IN ?", floorIDs).Find(&floors).Error; err == nil {
			for _, floor := range floors {
				floorMap[floor.ID] = floor.Name
			}
		}
	}

	// 构建排行数据
	items := make([]*MerchantRankingItem, 0, len(merchants))
	for i, merchant := range merchants {
		efficiency := float64(0)
		if merchant.Area > 0 {
			efficiency = merchant.Sales / merchant.Area
		}

		item := &MerchantRankingItem{
			ID:          merchant.ID,
			Name:        merchant.Name,
			Cover:       merchant.Cover,
			Phone:       merchant.Phone,
			FloorName:   floorMap[merchant.FloorID],
			Position:    merchant.Position,
			Score:       merchant.Score,
			Sales:       merchant.Sales,
			Area:        merchant.Area,
			Efficiency:  efficiency,
			Rank:        offset + i + 1,
			IsTop3:      (offset + i + 1) <= 3,
			Description: merchant.Description,
		}
		items = append(items, item)
	}

	return items, total, nil
}

// CreateMerchant 创建商家
func (s *MerchantManagementService) CreateMerchant(ctx context.Context, req *CreateMerchantRequest) (*models.Merchant, error) {
	var merchant *models.Merchant
	var floor models.Floor

	// 使用事务确保数据一致性
	err := s.db.WithContext(ctx).Transaction(func(tx *gorm.DB) error {
		// 检查楼层是否存在
		if err := tx.First(&floor, req.FloorID).Error; err != nil {
			if err == gorm.ErrRecordNotFound {
				return fmt.Errorf("楼层不存在")
			}
			return fmt.Errorf("failed to get floor: %w", err)
		}

		// 检查联系电话是否已存在
		var existingMerchant models.Merchant
		if err := tx.Where("phone = ?", req.Phone).First(&existingMerchant).Error; err == nil {
			return fmt.Errorf("联系电话已存在")
		}

		// 检查登录手机号是否已存在（商家表）
		if err := tx.Where("login_phone = ?", req.LoginPhone).First(&existingMerchant).Error; err == nil {
			return fmt.Errorf("登录手机号已存在")
		}

		// 检查登录手机号是否已在admin_users表中存在
		var existingAdminUser models.AdminUser
		if err := tx.Where("phone = ?", req.LoginPhone).First(&existingAdminUser).Error; err == nil {
			return fmt.Errorf("登录手机号已被其他管理员使用")
		}

		// 检查位置是否重复
		var existingPosition models.Merchant
		if err := tx.Where("floor_id = ? AND position = ?", req.FloorID, req.Position).First(&existingPosition).Error; err == nil {
			return fmt.Errorf("该楼层位置已被占用")
		}

		// 创建商家
		merchant = &models.Merchant{
			Name:        req.Name,
			Phone:       req.Phone,
			LoginPhone:  req.LoginPhone,
			Contact:     req.Contact,
			FloorID:     req.FloorID,
			Position:    req.Position,
			Cover:       req.Cover,
			Description: req.Description,
			Area:        req.Area,
			Score:       0,
			Level:       1,
			Sales:       0,
			Status:      models.StatusActive,
		}

		if err := tx.Create(merchant).Error; err != nil {
			return fmt.Errorf("failed to create merchant: %w", err)
		}

		// 自动创建对应的admin_users记录
		adminUser := &models.AdminUser{
			Phone:      req.LoginPhone,
			MerchantID: &merchant.ID, // 设置商家ID
			UserType:   2,            // 商家类型
			Status:     models.StatusActive,
		}

		if err := tx.Create(adminUser).Error; err != nil {
			return fmt.Errorf("failed to create admin user: %w", err)
		}

		// 为商家用户分配商家角色
		var merchantRole models.Role
		if err := tx.Where("name = ?", "merchant").First(&merchantRole).Error; err != nil {
			return fmt.Errorf("查找商家角色失败: %w", err)
		}

		// 创建用户角色关联
		userRole := &models.AdminUserRole{
			AdminUserID: adminUser.ID,
			RoleID:      merchantRole.ID,
		}
		if err := tx.Create(userRole).Error; err != nil {
			return fmt.Errorf("为商家用户分配角色失败: %w", err)
		}

		return nil
	})

	if err != nil {
		return nil, err
	}

	// 填充楼层信息
	merchant.Floor = floor

	return merchant, nil
}

// UpdateMerchant 更新商家
func (s *MerchantManagementService) UpdateMerchant(ctx context.Context, id uint64, req *UpdateMerchantRequest) (*models.Merchant, error) {
	var merchant models.Merchant
	if err := s.db.WithContext(ctx).First(&merchant, id).Error; err != nil {
		if err == gorm.ErrRecordNotFound {
			return nil, fmt.Errorf("商家不存在")
		}
		return nil, fmt.Errorf("failed to get merchant: %w", err)
	}

	// 使用事务确保数据一致性
	err := s.db.WithContext(ctx).Transaction(func(tx *gorm.DB) error {
		// 检查楼层是否存在（如果要更新楼层）
		if req.FloorID > 0 {
			var floor models.Floor
			if err := tx.First(&floor, req.FloorID).Error; err != nil {
				if err == gorm.ErrRecordNotFound {
					return fmt.Errorf("楼层不存在")
				}
				return fmt.Errorf("failed to get floor: %w", err)
			}
		}

		// 检查登录手机号是否重复（如果要更新登录手机号）
		if req.LoginPhone != "" && req.LoginPhone != merchant.LoginPhone {
			// 检查商家表中是否已存在
			var existingMerchant models.Merchant
			if err := tx.Where("login_phone = ? AND id != ?", req.LoginPhone, id).First(&existingMerchant).Error; err == nil {
				return fmt.Errorf("登录手机号已存在")
			}

			// 检查admin_users表中是否已存在
			var existingAdminUser models.AdminUser
			if err := tx.Where("phone = ? AND merchant_id != ?", req.LoginPhone, id).First(&existingAdminUser).Error; err == nil {
				return fmt.Errorf("登录手机号已被其他管理员使用")
			}
		}

		// 检查位置是否重复（排除自己）
		if req.Position != "" && req.FloorID > 0 {
			var existingPosition models.Merchant
			if err := tx.Where("floor_id = ? AND position = ? AND id != ?", req.FloorID, req.Position, id).First(&existingPosition).Error; err == nil {
				return fmt.Errorf("该楼层位置已被占用")
			}
		}

		// 更新字段
		updates := make(map[string]interface{})
		if req.Name != "" {
			updates["name"] = req.Name
		}
		if req.LoginPhone != "" {
			updates["login_phone"] = req.LoginPhone
		}
		if req.Contact != "" {
			updates["contact"] = req.Contact
		}
		if req.FloorID > 0 {
			updates["floor_id"] = req.FloorID
		}
		if req.Position != "" {
			updates["position"] = req.Position
		}
		if req.Cover != "" {
			updates["cover"] = req.Cover
		}
		if req.Description != "" {
			updates["description"] = req.Description
		}
		if req.Area > 0 {
			updates["area"] = req.Area
		}

		if err := tx.Model(&merchant).Updates(updates).Error; err != nil {
			return fmt.Errorf("failed to update merchant: %w", err)
		}

		// 如果更新了登录手机号，同时更新admin_users表
		if req.LoginPhone != "" && req.LoginPhone != merchant.LoginPhone {
			if err := tx.Model(&models.AdminUser{}).Where("merchant_id = ?", id).Update("phone", req.LoginPhone).Error; err != nil {
				return fmt.Errorf("failed to update admin user phone: %w", err)
			}
		}

		return nil
	})

	if err != nil {
		return nil, err
	}

	// 重新查询以获取最新数据并填充楼层信息
	if err := s.db.WithContext(ctx).First(&merchant, id).Error; err != nil {
		return nil, fmt.Errorf("failed to get updated merchant: %w", err)
	}

	// 填充楼层信息
	var floor models.Floor
	if err := s.db.WithContext(ctx).First(&floor, merchant.FloorID).Error; err == nil {
		merchant.Floor = floor
	}

	return &merchant, nil
}

// DeleteMerchant 删除商家
func (s *MerchantManagementService) DeleteMerchant(ctx context.Context, id uint64) error {
	var merchant models.Merchant
	if err := s.db.WithContext(ctx).First(&merchant, id).Error; err != nil {
		if err == gorm.ErrRecordNotFound {
			return fmt.Errorf("商家不存在")
		}
		return fmt.Errorf("failed to get merchant: %w", err)
	}

	// 软删除
	if err := s.db.WithContext(ctx).Delete(&merchant).Error; err != nil {
		return fmt.Errorf("failed to delete merchant: %w", err)
	}

	return nil
}

// GetMerchantByID 根据ID获取商家详情
func (s *MerchantManagementService) GetMerchantByID(ctx context.Context, id uint64) (*models.Merchant, error) {
	var merchant models.Merchant
	if err := s.db.WithContext(ctx).First(&merchant, id).Error; err != nil {
		if err == gorm.ErrRecordNotFound {
			return nil, fmt.Errorf("商家不存在")
		}
		return nil, fmt.Errorf("failed to get merchant: %w", err)
	}

	// 填充楼层信息
	var floor models.Floor
	if err := s.db.WithContext(ctx).First(&floor, merchant.FloorID).Error; err == nil {
		merchant.Floor = floor
	}

	return &merchant, nil
}

// CreateMerchantRequest 创建商家请求
type CreateMerchantRequest struct {
	Name        string  `json:"name" binding:"required,min=1,max=30"`
	Phone       string  `json:"phone" binding:"required,len=11"`
	LoginPhone  string  `json:"login_phone" binding:"required,len=11"`
	Contact     string  `json:"contact" binding:"required,min=1,max=10"`
	FloorID     uint64  `json:"floor_id" binding:"required"`
	Position    string  `json:"position" binding:"required,min=1,max=30"`
	Cover       string  `json:"cover"`
	Description string  `json:"description" binding:"max=300"`
	Area        float64 `json:"area" binding:"required,gt=0"`
}

// UpdateMerchantRequest 更新商家请求
type UpdateMerchantRequest struct {
	Name        string  `json:"name" binding:"omitempty,min=1,max=30"`
	LoginPhone  string  `json:"login_phone" binding:"omitempty,len=11"`
	Contact     string  `json:"contact" binding:"omitempty,min=1,max=10"`
	FloorID     uint64  `json:"floor_id"`
	Position    string  `json:"position" binding:"omitempty,min=1,max=30"`
	Cover       string  `json:"cover"`
	Description string  `json:"description" binding:"omitempty,max=300"`
	Area        float64 `json:"area" binding:"omitempty,gt=0"`
}

// GetMerchantScoreRecords 获取商家分值记录
func (s *MerchantManagementService) GetMerchantScoreRecords(ctx context.Context, merchantID uint64, page, size int) ([]*models.ScoreRecord, int64, error) {
	var records []*models.ScoreRecord
	var total int64

	// 构建查询条件 - 显示所有记录（包括待审核、已通过、已拒绝）
	query := s.db.WithContext(ctx).Model(&models.ScoreRecord{}).
		Where("merchant_id = ?", merchantID)

	// 统计总数
	if err := query.Count(&total).Error; err != nil {
		return nil, 0, fmt.Errorf("failed to count score records: %w", err)
	}

	// 分页查询，按时间倒序
	offset := (page - 1) * size
	if err := query.Offset(offset).Limit(size).
		Order("created_at DESC").
		Find(&records).Error; err != nil {
		return nil, 0, fmt.Errorf("failed to get score records: %w", err)
	}

	// 填充商家、楼层和分值项目信息
	for _, record := range records {
		var merchant models.Merchant
		if err := s.db.WithContext(ctx).First(&merchant, record.MerchantID).Error; err == nil {
			record.Merchant = merchant

			// 填充楼层信息
			var floor models.Floor
			if err := s.db.WithContext(ctx).First(&floor, merchant.FloorID).Error; err == nil {
				record.Merchant.Floor = floor
			}
		}

		// 填充分值项目信息
		var scoreItem models.ScoreItem
		if err := s.db.WithContext(ctx).First(&scoreItem, record.ScoreItemID).Error; err == nil {
			record.ScoreItem = scoreItem
		}
	}

	return records, total, nil
}

// GetScoreApplications 获取分值申请列表
func (s *MerchantManagementService) GetScoreApplications(ctx context.Context, status models.ApprovalStatus, page, size int) ([]*models.ScoreRecord, int64, error) {
	var records []*models.ScoreRecord
	var total int64

	// 构建查询条件 - 始终按状态筛选
	query := s.db.WithContext(ctx).Model(&models.ScoreRecord{}).
		Where("approval_status = ?", status)

	// 统计总数
	if err := query.Count(&total).Error; err != nil {
		return nil, 0, fmt.Errorf("failed to count score applications: %w", err)
	}

	// 分页查询，按提交时间倒序
	offset := (page - 1) * size
	if err := query.Offset(offset).Limit(size).
		Order("created_at DESC").
		Find(&records).Error; err != nil {
		return nil, 0, fmt.Errorf("failed to get score applications: %w", err)
	}

	// 填充商家、楼层和分值项目信息
	for _, record := range records {
		var merchant models.Merchant
		if err := s.db.WithContext(ctx).First(&merchant, record.MerchantID).Error; err == nil {
			record.Merchant = merchant

			// 填充楼层信息
			var floor models.Floor
			if err := s.db.WithContext(ctx).First(&floor, merchant.FloorID).Error; err == nil {
				record.Merchant.Floor = floor
			}
		}

		// 填充分值项目信息
		var scoreItem models.ScoreItem
		if err := s.db.WithContext(ctx).First(&scoreItem, record.ScoreItemID).Error; err == nil {
			record.ScoreItem = scoreItem
		}
	}

	return records, total, nil
}

// GetScoreApplicationsWithOptionalStatus 获取分值申请列表（可选状态筛选）
func (s *MerchantManagementService) GetScoreApplicationsWithOptionalStatus(ctx context.Context, status *models.ApprovalStatus, page, size int) ([]*models.ScoreRecord, int64, error) {
	var records []*models.ScoreRecord
	var total int64

	// 构建查询条件
	query := s.db.WithContext(ctx).Model(&models.ScoreRecord{})

	// 如果指定了状态，则按状态筛选
	if status != nil {
		query = query.Where("approval_status = ?", *status)
	}

	// 统计总数
	if err := query.Count(&total).Error; err != nil {
		return nil, 0, fmt.Errorf("failed to count score applications: %w", err)
	}

	// 分页查询，按提交时间倒序
	offset := (page - 1) * size
	if err := query.Offset(offset).Limit(size).
		Order("created_at DESC").
		Find(&records).Error; err != nil {
		return nil, 0, fmt.Errorf("failed to get score applications: %w", err)
	}

	// 填充商家、楼层和分值项目信息
	for _, record := range records {
		var merchant models.Merchant
		if err := s.db.WithContext(ctx).First(&merchant, record.MerchantID).Error; err == nil {
			record.Merchant = merchant

			// 填充楼层信息
			var floor models.Floor
			if err := s.db.WithContext(ctx).First(&floor, merchant.FloorID).Error; err == nil {
				record.Merchant.Floor = floor
			}
		}

		// 填充分值项目信息
		var scoreItem models.ScoreItem
		if err := s.db.WithContext(ctx).First(&scoreItem, record.ScoreItemID).Error; err == nil {
			record.ScoreItem = scoreItem
		}
	}

	return records, total, nil
}

// GetScoreApplicationDetail 获取分值申请详情
func (s *MerchantManagementService) GetScoreApplicationDetail(ctx context.Context, id uint64) (*models.ScoreRecord, error) {
	var record models.ScoreRecord
	if err := s.db.WithContext(ctx).First(&record, id).Error; err != nil {
		if err == gorm.ErrRecordNotFound {
			return nil, fmt.Errorf("申请记录不存在")
		}
		return nil, fmt.Errorf("failed to get score application: %w", err)
	}

	// 填充商家信息
	var merchant models.Merchant
	if err := s.db.WithContext(ctx).First(&merchant, record.MerchantID).Error; err == nil {
		record.Merchant = merchant

		// 填充楼层信息
		var floor models.Floor
		if err := s.db.WithContext(ctx).First(&floor, merchant.FloorID).Error; err == nil {
			record.Merchant.Floor = floor
		}
	}

	// 填充分值项目信息
	var scoreItem models.ScoreItem
	if err := s.db.WithContext(ctx).First(&scoreItem, record.ScoreItemID).Error; err == nil {
		record.ScoreItem = scoreItem
	}

	return &record, nil
}

// CreateScoreApplication 创建分值申请
func (s *MerchantManagementService) CreateScoreApplication(ctx context.Context, req *CreateScoreApplicationRequest, operatorID uint64) (*models.ScoreRecord, error) {
	// 获取商家信息
	var merchant models.Merchant
	if err := s.db.WithContext(ctx).First(&merchant, req.MerchantID).Error; err != nil {
		if err == gorm.ErrRecordNotFound {
			return nil, fmt.Errorf("商家不存在")
		}
		return nil, fmt.Errorf("failed to get merchant: %w", err)
	}

	// 获取分值项目信息
	var scoreItem models.ScoreItem
	if err := s.db.WithContext(ctx).First(&scoreItem, req.ScoreItemID).Error; err != nil {
		if err == gorm.ErrRecordNotFound {
			return nil, fmt.Errorf("分值项目不存在")
		}
		return nil, fmt.Errorf("failed to get score item: %w", err)
	}

	// 将图片数组转换为JSON字符串
	imagesJSON := ""
	if len(req.Images) > 0 {
		if jsonBytes, err := json.Marshal(req.Images); err == nil {
			imagesJSON = string(jsonBytes)
		}
	}

	// 创建分值申请记录，使用分值项目的信息
	record := &models.ScoreRecord{
		MerchantID:     req.MerchantID,
		ScoreItemID:    req.ScoreItemID,
		Type:           scoreItem.Type,
		Score:          scoreItem.Score,
		Reason:         scoreItem.Name, // 使用分值项目的名称作为原因
		Images:         imagesJSON,
		OperatorID:     operatorID,
		ApprovalStatus: models.ApprovalStatusPending,
	}

	if err := s.db.WithContext(ctx).Create(record).Error; err != nil {
		return nil, fmt.Errorf("failed to create score application: %w", err)
	}

	// 填充商家信息
	record.Merchant = merchant

	// 填充楼层信息
	var floor models.Floor
	if err := s.db.WithContext(ctx).First(&floor, merchant.FloorID).Error; err == nil {
		record.Merchant.Floor = floor
	}

	// 填充分值项目信息
	record.ScoreItem = scoreItem

	return record, nil
}

// ResubmitScoreApplication 重新提交分值申请（用于被拒绝的申请）
func (s *MerchantManagementService) ResubmitScoreApplication(ctx context.Context, id uint64, req *ResubmitScoreApplicationRequest, operatorID uint64) (*models.ScoreRecord, error) {
	var record models.ScoreRecord
	if err := s.db.WithContext(ctx).First(&record, id).Error; err != nil {
		if err == gorm.ErrRecordNotFound {
			return nil, fmt.Errorf("申请记录不存在")
		}
		return nil, fmt.Errorf("failed to get score application: %w", err)
	}

	// 只有被拒绝的申请才能重新提交
	if record.ApprovalStatus != models.ApprovalStatusRejected {
		return nil, fmt.Errorf("只有被拒绝的申请才能重新提交")
	}

	// 获取新的分值项目信息
	var scoreItem models.ScoreItem
	if err := s.db.WithContext(ctx).First(&scoreItem, req.ScoreItemID).Error; err != nil {
		if err == gorm.ErrRecordNotFound {
			return nil, fmt.Errorf("分值项目不存在")
		}
		return nil, fmt.Errorf("failed to get score item: %w", err)
	}

	// 将图片数组转换为JSON字符串
	imagesJSON := ""
	if len(req.Images) > 0 {
		if jsonBytes, err := json.Marshal(req.Images); err == nil {
			imagesJSON = string(jsonBytes)
		}
	}

	// 更新申请记录
	updates := map[string]interface{}{
		"score_item_id":   req.ScoreItemID,
		"type":            scoreItem.Type,
		"score":           scoreItem.Score,
		"reason":          scoreItem.Name,
		"images":          imagesJSON,
		"operator_id":     operatorID,
		"approval_status": models.ApprovalStatusPending,
		"approver_id":     nil,
		"approval_note":   "",
		"approved_at":     nil,
		"updated_at":      time.Now(),
	}

	if err := s.db.WithContext(ctx).Model(&record).Updates(updates).Error; err != nil {
		return nil, fmt.Errorf("failed to resubmit score application: %w", err)
	}

	// 重新查询更新后的记录
	if err := s.db.WithContext(ctx).First(&record, id).Error; err != nil {
		return nil, fmt.Errorf("failed to get updated record: %w", err)
	}

	// 填充商家信息
	var merchant models.Merchant
	if err := s.db.WithContext(ctx).First(&merchant, record.MerchantID).Error; err == nil {
		record.Merchant = merchant

		// 填充楼层信息
		var floor models.Floor
		if err := s.db.WithContext(ctx).First(&floor, merchant.FloorID).Error; err == nil {
			record.Merchant.Floor = floor
		}
	}

	// 填充分值项目信息
	record.ScoreItem = scoreItem

	return &record, nil
}

// ApproveScoreApplication 审核分值申请
func (s *MerchantManagementService) ApproveScoreApplication(ctx context.Context, id uint64, req *ApproveScoreApplicationRequest, approverID uint64) error {
	return s.db.WithContext(ctx).Transaction(func(tx *gorm.DB) error {
		// 获取申请记录
		var record models.ScoreRecord
		if err := tx.First(&record, id).Error; err != nil {
			if err == gorm.ErrRecordNotFound {
				return fmt.Errorf("申请记录不存在")
			}
			return fmt.Errorf("failed to get score record: %w", err)
		}

		// 检查状态
		if record.ApprovalStatus != models.ApprovalStatusPending {
			return fmt.Errorf("该申请已处理")
		}

		// 更新申请记录
		updates := map[string]interface{}{
			"approval_status": req.Status,
			"approver_id":     approverID,
			"approval_note":   req.Note,
			"approved_at":     time.Now(),
		}

		if err := tx.Model(&record).Updates(updates).Error; err != nil {
			return fmt.Errorf("failed to update score record: %w", err)
		}

		// 如果审核通过，更新商家分值
		if req.Status == models.ApprovalStatusApproved {
			// 直接使用数据库表达式更新分值，支持负数
			var updateExpr string
			if record.Type == models.ScoreTypeAdd {
				updateExpr = "score + ?"
			} else {
				updateExpr = "score - ?"
			}

			if err := tx.Model(&models.Merchant{}).Where("id = ?", record.MerchantID).
				Update("score", gorm.Expr(updateExpr, record.Score)).Error; err != nil {
				return fmt.Errorf("failed to update merchant score: %w", err)
			}
		}

		return nil
	})
}

// CreateScoreApplicationRequest 创建分值申请请求
type CreateScoreApplicationRequest struct {
	MerchantID  uint64   `json:"merchant_id" binding:"required"`
	ScoreItemID uint64   `json:"score_item_id" binding:"required"`
	Images      []string `json:"images" binding:"omitempty,max=5"` // 可选，最多5张图片
}

// ResubmitScoreApplicationRequest 重新提交分值申请请求
type ResubmitScoreApplicationRequest struct {
	ScoreItemID uint64   `json:"score_item_id" binding:"required"`
	Images      []string `json:"images" binding:"omitempty,max=5"` // 可选，最多5张图片
}

// ApproveScoreApplicationRequest 审核分值申请请求
type ApproveScoreApplicationRequest struct {
	Status models.ApprovalStatus `json:"status" binding:"required,oneof=1 2"`
	Note   string                `json:"note" binding:"required,max=500"`
}
