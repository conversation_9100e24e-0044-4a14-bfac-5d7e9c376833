package services

import (
	"context"
	"encoding/json"
	"fmt"
	"wangfujing_admin/internal/models"

	"gorm.io/gorm"
)

// OperationLogService 操作日志服务
type OperationLogService struct {
	db *gorm.DB
}

// NewOperationLogService 创建操作日志服务实例
func NewOperationLogService(db *gorm.DB) *OperationLogService {
	return &OperationLogService{db: db}
}

// LogOperation 记录操作日志
func (s *OperationLogService) LogOperation(ctx context.Context, log *models.OperationLog) error {
	if err := s.db.WithContext(ctx).Create(log).Error; err != nil {
		return fmt.Errorf("failed to create operation log: %w", err)
	}
	return nil
}

// GetOperationLogs 获取操作日志列表（分页）
func (s *OperationLogService) GetOperationLogs(ctx context.Context, page, size int, filters map[string]interface{}) ([]*models.OperationLog, int64, error) {
	var logs []*models.OperationLog
	var total int64

	query := s.db.WithContext(ctx).Model(&models.OperationLog{})

	// 添加过滤条件
	if operatorID, ok := filters["operator_id"]; ok && operatorID != "" {
		query = query.Where("operator_id = ?", operatorID)
	}
	if module, ok := filters["module"]; ok && module != "" {
		query = query.Where("module = ?", module)
	}
	if action, ok := filters["action"]; ok && action != "" {
		query = query.Where("action = ?", action)
	}
	if resource, ok := filters["resource"]; ok && resource != "" {
		query = query.Where("resource = ?", resource)
	}
	if status, ok := filters["status"]; ok && status != "" {
		query = query.Where("status = ?", status)
	}
	if startTime, ok := filters["start_time"]; ok && startTime != "" {
		query = query.Where("created_at >= ?", startTime)
	}
	if endTime, ok := filters["end_time"]; ok && endTime != "" {
		query = query.Where("created_at <= ?", endTime)
	}

	// 获取总数
	if err := query.Count(&total).Error; err != nil {
		return nil, 0, fmt.Errorf("failed to count operation logs: %w", err)
	}

	// 分页查询
	offset := (page - 1) * size
	if err := query.Order("created_at DESC").Offset(offset).Limit(size).Find(&logs).Error; err != nil {
		return nil, 0, fmt.Errorf("failed to get operation logs: %w", err)
	}

	return logs, total, nil
}

// GetOperationLogByID 根据ID获取操作日志
func (s *OperationLogService) GetOperationLogByID(ctx context.Context, id string) (*models.OperationLog, error) {
	var log models.OperationLog
	if err := s.db.WithContext(ctx).First(&log, "id = ?", id).Error; err != nil {
		if err == gorm.ErrRecordNotFound {
			return nil, fmt.Errorf("operation log not found")
		}
		return nil, fmt.Errorf("failed to get operation log: %w", err)
	}
	return &log, nil
}

// CreateOperationLog 创建操作日志的便捷方法
func (s *OperationLogService) CreateOperationLog(ctx context.Context, operatorID uint64, operatorName, module, action, resource, resourceID, description string, requestData, responseData interface{}, clientIP, userAgent string, status int, errorMessage string) error {
	// 序列化请求和响应数据
	var requestJSON, responseJSON string

	if requestData != nil {
		if data, err := json.Marshal(requestData); err == nil {
			requestJSON = string(data)
		}
	}

	if responseData != nil {
		if data, err := json.Marshal(responseData); err == nil {
			responseJSON = string(data)
		}
	}

	log := &models.OperationLog{
		OperatorID:   operatorID,
		OperatorName: operatorName,
		Module:       module,
		Action:       action,
		Resource:     resource,
		ResourceID:   resourceID,
		Description:  description,
		RequestData:  requestJSON,
		ResponseData: responseJSON,
		ClientIP:     clientIP,
		UserAgent:    userAgent,
		Status:       status,
		ErrorMessage: errorMessage,
	}

	return s.LogOperation(ctx, log)
}

// GetOperationStatistics 获取操作统计信息
func (s *OperationLogService) GetOperationStatistics(ctx context.Context, filters map[string]interface{}) (map[string]interface{}, error) {
	query := s.db.WithContext(ctx).Model(&models.OperationLog{})

	// 添加时间过滤
	if startTime, ok := filters["start_time"]; ok && startTime != "" {
		query = query.Where("created_at >= ?", startTime)
	}
	if endTime, ok := filters["end_time"]; ok && endTime != "" {
		query = query.Where("created_at <= ?", endTime)
	}

	// 总操作数
	var totalCount int64
	if err := query.Count(&totalCount).Error; err != nil {
		return nil, fmt.Errorf("failed to count total operations: %w", err)
	}

	// 成功操作数
	var successCount int64
	if err := query.Where("status = 1").Count(&successCount).Error; err != nil {
		return nil, fmt.Errorf("failed to count success operations: %w", err)
	}

	// 失败操作数
	var failureCount int64
	if err := query.Where("status = 0").Count(&failureCount).Error; err != nil {
		return nil, fmt.Errorf("failed to count failure operations: %w", err)
	}

	// 按模块统计
	var moduleStats []map[string]interface{}
	if err := s.db.WithContext(ctx).Model(&models.OperationLog{}).
		Select("module, COUNT(*) as count").
		Group("module").
		Order("count DESC").
		Find(&moduleStats).Error; err != nil {
		return nil, fmt.Errorf("failed to get module statistics: %w", err)
	}

	// 按操作统计
	var actionStats []map[string]interface{}
	if err := s.db.WithContext(ctx).Model(&models.OperationLog{}).
		Select("action, COUNT(*) as count").
		Group("action").
		Order("count DESC").
		Find(&actionStats).Error; err != nil {
		return nil, fmt.Errorf("failed to get action statistics: %w", err)
	}

	// 计算成功率，避免除零错误
	var successRate float64
	if totalCount > 0 {
		successRate = float64(successCount) / float64(totalCount) * 100
	}

	return map[string]interface{}{
		"total_count":   totalCount,
		"success_count": successCount,
		"failure_count": failureCount,
		"success_rate":  successRate,
		"module_stats":  moduleStats,
		"action_stats":  actionStats,
	}, nil
}
