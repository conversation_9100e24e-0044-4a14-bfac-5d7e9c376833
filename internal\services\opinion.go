package services

import (
	"context"
	"fmt"
	"time"

	"wangfujing_admin/internal/models"

	"gorm.io/gorm"
)

// OpinionService 意见服务
type OpinionService struct {
	db *gorm.DB
}

// NewOpinionService 创建意见服务
func NewOpinionService(db *gorm.DB) *OpinionService {
	return &OpinionService{
		db: db,
	}
}

// 获取意见列表
func (s *OpinionService) GetOpinions(ctx context.Context, page int, size int, startTime string, endTime string) ([]*models.Opinion, int64, error) {
	var opinions []*models.Opinion
	var total int64

	query := s.db.WithContext(ctx).Model(&models.Opinion{})
	// 处理 startTime
	if startTime != "" {
		if _, err := time.Parse("2006-01-02 15:04:05", startTime); err != nil {
			return nil, 0, fmt.Errorf("invalid start_time: %v", err)
		}
		query = query.Where("created_at >= ?", startTime)
	}

	// 处理 endTime
	if endTime != "" {
		end, err := time.Parse("2006-01-02 15:04:05", endTime)
		if err != nil {
			return nil, 0, fmt.Errorf("invalid end_time: %v", err)
		}
		query = query.Where("created_at < ?", end.AddDate(0, 0, 1)) // 包含全天
	}

	// 分页参数校验
	if page < 1 {
		page = 1
	}
	if size < 1 {
		size = 10 // 默认分页大小
	}

	// 获取总数
	if err := query.Count(&total).Error; err != nil {
		return nil, 0, err
	}
	// 获取分页数据
	offset := (page - 1) * size
	if err := query.Offset(offset).Limit(size).Order("created_at DESC").Find(&opinions).Error; err != nil {
		return nil, 0, fmt.Errorf("failed to get merchants: %w", err)
	}

	return opinions, total, nil
}
