package services

import (
	"fmt"
	"wangfujing_admin/pkg/oss"
)

// OSSService OSS服务
type OSSService struct {
	ossManager *oss.OSSManager
}

// NewOSSService 创建OSS服务实例
func NewOSSService(ossManager *oss.OSSManager) *OSSService {
	return &OSSService{
		ossManager: ossManager,
	}
}

// UploadFile 上传文件到OSS
func (s *OSSService) UploadFile(fileName string, data []byte, contentType string) (string, error) {
	if s.ossManager == nil {
		return "", fmt.Errorf("OSS管理器未初始化")
	}

	// 上传文件到OSS
	url, err := s.ossManager.UploadBytes(data, fileName, "qr-codes")
	if err != nil {
		return "", fmt.Errorf("上传文件失败: %w", err)
	}

	return url, nil
}

// DeleteFile 删除OSS文件
func (s *OSSService) DeleteFile(fileName string) error {
	if s.ossManager == nil {
		return fmt.Errorf("OSS管理器未初始化")
	}

	return s.ossManager.DeleteFile(fileName)
}
