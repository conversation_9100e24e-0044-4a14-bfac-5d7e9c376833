package services

import (
	"context"
	"errors"
	"fmt"
	"strconv"

	"wangfujing_admin/internal/models"
	"wangfujing_admin/pkg/cache"

	"gorm.io/gorm"
)

// PermissionService 权限服务
type PermissionService struct {
	db *gorm.DB
}

// NewPermissionService 创建权限服务
func NewPermissionService(db *gorm.DB) *PermissionService {
	return &PermissionService{
		db: db,
	}
}

// CreatePermission 创建权限
func (s *PermissionService) CreatePermission(ctx context.Context, permission *models.Permission) error {
	// 检查权限名是否已存在
	var existingPermission models.Permission
	if err := s.db.WithContext(ctx).Where("name = ?", permission.Name).First(&existingPermission).Error; err == nil {
		return errors.New("permission name already exists")
	} else if !errors.Is(err, gorm.ErrRecordNotFound) {
		return fmt.Errorf("failed to check existing permission: %w", err)
	}

	// 创建权限
	if err := s.db.WithContext(ctx).Create(permission).Error; err != nil {
		return fmt.Errorf("failed to create permission: %w", err)
	}

	return nil
}

// GetPermissionByID 根据ID获取权限
func (s *PermissionService) GetPermissionByID(ctx context.Context, id string) (*models.Permission, error) {
	var permission models.Permission
	if err := s.db.WithContext(ctx).First(&permission, "id = ?", id).Error; err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return nil, errors.New("permission not found")
		}
		return nil, fmt.Errorf("failed to get permission: %w", err)
	}
	return &permission, nil
}

// GetPermissionByName 根据名称获取权限
func (s *PermissionService) GetPermissionByName(ctx context.Context, name string) (*models.Permission, error) {
	var permission models.Permission
	if err := s.db.WithContext(ctx).First(&permission, "name = ?", name).Error; err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return nil, errors.New("permission not found")
		}
		return nil, fmt.Errorf("failed to get permission: %w", err)
	}
	return &permission, nil
}

// UpdatePermission 更新权限
func (s *PermissionService) UpdatePermission(ctx context.Context, id string, updates map[string]interface{}) error {
	result := s.db.WithContext(ctx).Model(&models.Permission{}).Where("id = ?", id).Updates(updates)
	if result.Error != nil {
		return fmt.Errorf("failed to update permission: %w", result.Error)
	}
	if result.RowsAffected == 0 {
		return errors.New("permission not found")
	}

	// 清除缓存
	cache.Delete(ctx, fmt.Sprintf("permission:%s", id))

	return nil
}

// DeletePermission 删除权限
func (s *PermissionService) DeletePermission(ctx context.Context, id string) error {
	// 检查是否有角色使用该权限
	var count int64
	if err := s.db.WithContext(ctx).Model(&models.RolePermission{}).Where("permission_id = ?", id).Count(&count).Error; err != nil {
		return fmt.Errorf("failed to check permission usage: %w", err)
	}
	if count > 0 {
		return errors.New("permission is in use, cannot delete")
	}

	result := s.db.WithContext(ctx).Delete(&models.Permission{}, "id = ?", id)
	if result.Error != nil {
		return fmt.Errorf("failed to delete permission: %w", result.Error)
	}
	if result.RowsAffected == 0 {
		return errors.New("permission not found")
	}

	// 清除缓存
	cache.Delete(ctx, fmt.Sprintf("permission:%s", id))

	return nil
}

// GetPermissions 获取权限列表
func (s *PermissionService) GetPermissions(ctx context.Context, page, size int, module string, status *models.Status) ([]*models.Permission, int64, error) {
	var permissions []*models.Permission
	var total int64

	query := s.db.WithContext(ctx).Model(&models.Permission{})

	// 添加过滤条件
	if module != "" {
		query = query.Where("module = ?", module)
	}
	if status != nil {
		query = query.Where("status = ?", *status)
	}

	// 获取总数
	if err := query.Count(&total).Error; err != nil {
		return nil, 0, fmt.Errorf("failed to count permissions: %w", err)
	}

	// 获取分页数据
	offset := (page - 1) * size
	if err := query.Offset(offset).Limit(size).Order("module ASC, name ASC").Find(&permissions).Error; err != nil {
		return nil, 0, fmt.Errorf("failed to get permissions: %w", err)
	}

	return permissions, total, nil
}

// UpdatePermissionStatus 更新权限状态
func (s *PermissionService) UpdatePermissionStatus(ctx context.Context, id string, status models.Status) error {
	return s.UpdatePermission(ctx, id, map[string]interface{}{
		"status": status,
	})
}

// GetAllPermissions 获取所有权限（不分页）
func (s *PermissionService) GetAllPermissions(ctx context.Context) ([]*models.Permission, error) {
	var permissions []*models.Permission

	if err := s.db.WithContext(ctx).Order("module ASC, name ASC").Find(&permissions).Error; err != nil {
		return nil, fmt.Errorf("failed to get all permissions: %w", err)
	}

	return permissions, nil
}

// GetPermissionsByModule 根据模块获取权限
func (s *PermissionService) GetPermissionsByModule(ctx context.Context, module string) ([]*models.Permission, error) {
	var permissions []*models.Permission

	if err := s.db.WithContext(ctx).Where("module = ?", module).Order("name ASC").Find(&permissions).Error; err != nil {
		return nil, fmt.Errorf("failed to get permissions by module: %w", err)
	}

	return permissions, nil
}

// GetModules 获取所有模块
func (s *PermissionService) GetModules(ctx context.Context) ([]string, error) {
	var modules []string

	if err := s.db.WithContext(ctx).Model(&models.Permission{}).
		Distinct("module").
		Pluck("module", &modules).Error; err != nil {
		return nil, fmt.Errorf("failed to get modules: %w", err)
	}

	return modules, nil
}

// CheckPermissionExists 检查权限是否存在
func (s *PermissionService) CheckPermissionExists(ctx context.Context, id string) (bool, error) {
	// 转换字符串ID为uint64
	idUint, err := strconv.ParseUint(id, 10, 64)
	if err != nil {
		return false, fmt.Errorf("invalid permission ID: %w", err)
	}

	var count int64
	if err := s.db.WithContext(ctx).Model(&models.Permission{}).Where("id = ?", idUint).Count(&count).Error; err != nil {
		return false, fmt.Errorf("failed to check permission existence: %w", err)
	}
	return count > 0, nil
}

// GetPermissionsByIDs 根据ID列表获取权限
func (s *PermissionService) GetPermissionsByIDs(ctx context.Context, ids []string) ([]*models.Permission, error) {
	var permissions []*models.Permission

	// 转换字符串ID列表为uint64列表
	var idUints []uint64
	for _, id := range ids {
		idUint, err := strconv.ParseUint(id, 10, 64)
		if err != nil {
			return nil, fmt.Errorf("invalid permission ID %s: %w", id, err)
		}
		idUints = append(idUints, idUint)
	}

	if err := s.db.WithContext(ctx).Where("id IN ?", idUints).Find(&permissions).Error; err != nil {
		return nil, fmt.Errorf("failed to get permissions by ids: %w", err)
	}

	return permissions, nil
}

// GetPermissionTree 获取权限树结构
func (s *PermissionService) GetPermissionTree(ctx context.Context) (map[string][]*models.Permission, error) {
	permissions, err := s.GetAllPermissions(ctx)
	if err != nil {
		return nil, err
	}

	tree := make(map[string][]*models.Permission)
	for _, permission := range permissions {
		tree[permission.Module] = append(tree[permission.Module], permission)
	}

	return tree, nil
}

// BatchCreatePermissions 批量创建权限
func (s *PermissionService) BatchCreatePermissions(ctx context.Context, permissions []*models.Permission) error {
	// 开始事务
	tx := s.db.WithContext(ctx).Begin()
	defer func() {
		if r := recover(); r != nil {
			tx.Rollback()
		}
	}()

	for _, permission := range permissions {
		// 检查权限名是否已存在
		var existingPermission models.Permission
		if err := tx.Where("name = ?", permission.Name).First(&existingPermission).Error; err == nil {
			continue // 跳过已存在的权限
		} else if !errors.Is(err, gorm.ErrRecordNotFound) {
			tx.Rollback()
			return fmt.Errorf("failed to check existing permission: %w", err)
		}

		// 创建权限
		if err := tx.Create(permission).Error; err != nil {
			tx.Rollback()
			return fmt.Errorf("failed to create permission: %w", err)
		}
	}

	if err := tx.Commit().Error; err != nil {
		return fmt.Errorf("failed to commit transaction: %w", err)
	}

	return nil
}

// InitDefaultPermissions 初始化默认权限
func (s *PermissionService) InitDefaultPermissions(ctx context.Context) error {
	defaultPermissions := []*models.Permission{
		// 用户管理
		{Name: "user:read", DisplayName: "查看用户", Module: "user", Action: "read", Resource: "user"},
		{Name: "user:create", DisplayName: "创建用户", Module: "user", Action: "create", Resource: "user"},
		{Name: "user:update", DisplayName: "更新用户", Module: "user", Action: "update", Resource: "user"},
		{Name: "user:delete", DisplayName: "删除用户", Module: "user", Action: "delete", Resource: "user"},

		// 角色管理
		{Name: "role:read", DisplayName: "查看角色", Module: "role", Action: "read", Resource: "role"},
		{Name: "role:create", DisplayName: "创建角色", Module: "role", Action: "create", Resource: "role"},
		{Name: "role:update", DisplayName: "更新角色", Module: "role", Action: "update", Resource: "role"},
		{Name: "role:delete", DisplayName: "删除角色", Module: "role", Action: "delete", Resource: "role"},

		// 权限管理
		{Name: "permission:read", DisplayName: "查看权限", Module: "permission", Action: "read", Resource: "permission"},
		{Name: "permission:create", DisplayName: "创建权限", Module: "permission", Action: "create", Resource: "permission"},
		{Name: "permission:update", DisplayName: "更新权限", Module: "permission", Action: "update", Resource: "permission"},
		{Name: "permission:delete", DisplayName: "删除权限", Module: "permission", Action: "delete", Resource: "permission"},

		// 商家管理
		{Name: "merchant:read", DisplayName: "查看商家", Module: "merchant", Action: "read", Resource: "merchant"},
		{Name: "merchant:create", DisplayName: "创建商家", Module: "merchant", Action: "create", Resource: "merchant"},
		{Name: "merchant:update", DisplayName: "更新商家", Module: "merchant", Action: "update", Resource: "merchant"},
		{Name: "merchant:delete", DisplayName: "删除商家", Module: "merchant", Action: "delete", Resource: "merchant"},

		// 商品管理
		{Name: "product:read", DisplayName: "查看商品", Module: "product", Action: "read", Resource: "product"},
		{Name: "product:approve", DisplayName: "审核商品", Module: "product", Action: "approve", Resource: "product"},

		// 订单管理
		{Name: "order:read", DisplayName: "查看订单", Module: "order", Action: "read", Resource: "order"},
		{Name: "order:update", DisplayName: "更新订单", Module: "order", Action: "update", Resource: "order"},

		// 客诉管理
		{Name: "complaint:read", DisplayName: "查看客诉", Module: "complaint", Action: "read", Resource: "complaint"},
		{Name: "complaint:create", DisplayName: "创建客诉", Module: "complaint", Action: "create", Resource: "complaint"},
		{Name: "complaint:approve", DisplayName: "审核客诉", Module: "complaint", Action: "approve", Resource: "complaint"},

		// 统计报表
		{Name: "stats:read", DisplayName: "查看统计", Module: "stats", Action: "read", Resource: "stats"},
	}

	return s.BatchCreatePermissions(ctx, defaultPermissions)
}
