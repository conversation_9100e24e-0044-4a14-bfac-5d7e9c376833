package services

import (
	"crypto/hmac"
	"crypto/sha256"
	"encoding/hex"
	"fmt"
	"strconv"
	"time"

	"wangfujing_admin/internal/config"
	"wangfujing_admin/internal/models"

	"github.com/skip2/go-qrcode"
	"gorm.io/gorm"
)

// PersonalQRService 个人二维码服务
type PersonalQRService struct {
	db         *gorm.DB
	ossService *OSSService
	secretKey  string
}

// NewPersonalQRService 创建个人二维码服务实例
func NewPersonalQRService(db *gorm.DB, ossService *OSSService, cfg *config.Config) *PersonalQRService {
	secretKey := "personal_qr_secret_key_2025"
	if cfg != nil && cfg.JWT.APISecret != "" {
		secretKey = cfg.JWT.APISecret + "_personal_qr"
	}

	return &PersonalQRService{
		db:         db,
		ossService: ossService,
		secretKey:  secretKey,
	}
}

// GeneratePersonalQR 生成用户个人二维码
func (s *PersonalQRService) GeneratePersonalQR(userID uint64) (string, error) {
	// 检查用户是否已有二维码
	var user models.User
	if err := s.db.First(&user, userID).Error; err != nil {
		return "", fmt.Errorf("用户不存在: %w", err)
	}

	// 如果已有二维码URL，直接返回
	if user.PersonalQR != "" {
		return user.PersonalQR, nil
	}

	// 生成二维码数据
	qrData := s.generateQRData(userID)

	// 生成二维码图片
	qrCode, err := qrcode.New(qrData, qrcode.Medium)
	if err != nil {
		return "", fmt.Errorf("生成二维码失败: %w", err)
	}

	// 设置二维码大小
	qrCode.DisableBorder = false

	// 生成PNG图片
	pngBytes, err := qrCode.PNG(256)
	if err != nil {
		return "", fmt.Errorf("生成二维码图片失败: %w", err)
	}

	// 上传到OSS
	fileName := fmt.Sprintf("personal-qr/user_%d_%d.png", userID, time.Now().Unix())
	qrURL, err := s.ossService.UploadFile(fileName, pngBytes, "image/png")
	if err != nil {
		return "", fmt.Errorf("上传二维码失败: %w", err)
	}

	// 更新用户记录
	if err := s.db.Model(&user).Update("personal_qr", qrURL).Error; err != nil {
		return "", fmt.Errorf("更新用户二维码URL失败: %w", err)
	}

	fmt.Printf("用户个人二维码生成成功 - 用户ID: %d, URL: %s\n", userID, qrURL)

	return qrURL, nil
}

// generateQRData 生成二维码数据
func (s *PersonalQRService) generateQRData(userID uint64) string {
	// 二维码数据格式：USER|{userID}|{signature}（永久有效，不包含时间戳）

	// 生成签名
	data := fmt.Sprintf("USER|%d", userID)
	signature := s.generateSignature(data)

	return fmt.Sprintf("%s|%s", data, signature)
}

// generateSignature 生成HMAC签名
func (s *PersonalQRService) generateSignature(data string) string {
	h := hmac.New(sha256.New, []byte(s.secretKey))
	h.Write([]byte(data))
	return hex.EncodeToString(h.Sum(nil))
}

// ParsePersonalQR 解析个人二维码数据
func (s *PersonalQRService) ParsePersonalQR(qrData string) (*PersonalQRInfo, error) {
	// 解析二维码数据格式：USER|{userID}|{signature}
	parts := splitQRData(qrData)
	if len(parts) != 3 {
		return nil, fmt.Errorf("二维码格式错误")
	}

	if parts[0] != "USER" {
		return nil, fmt.Errorf("不是用户个人二维码")
	}

	// 解析用户ID
	userID, err := strconv.ParseUint(parts[1], 10, 64)
	if err != nil {
		return nil, fmt.Errorf("用户ID格式错误: %w", err)
	}

	signature := parts[2]

	// 验证签名
	expectedData := fmt.Sprintf("USER|%d", userID)
	expectedSignature := s.generateSignature(expectedData)
	if signature != expectedSignature {
		return nil, fmt.Errorf("二维码签名验证失败")
	}

	// 检查用户是否存在
	var user models.User
	if err := s.db.First(&user, userID).Error; err != nil {
		if err == gorm.ErrRecordNotFound {
			return nil, fmt.Errorf("用户不存在")
		}
		return nil, fmt.Errorf("查询用户失败: %w", err)
	}

	return &PersonalQRInfo{
		UserID: userID,
		User:   &user,
		QRData: qrData,
	}, nil
}

// PersonalQRInfo 个人二维码信息
type PersonalQRInfo struct {
	UserID uint64       `json:"user_id"`
	User   *models.User `json:"user"`
	QRData string       `json:"qr_data"`
}

// splitQRData 分割二维码数据
func splitQRData(data string) []string {
	var parts []string
	var current string

	for _, char := range data {
		if char == '|' {
			parts = append(parts, current)
			current = ""
		} else {
			current += string(char)
		}
	}

	if current != "" {
		parts = append(parts, current)
	}

	return parts
}

// RegeneratePersonalQR 重新生成用户个人二维码
func (s *PersonalQRService) RegeneratePersonalQR(userID uint64) (string, error) {
	// 先清空现有的二维码URL
	if err := s.db.Model(&models.User{}).Where("id = ?", userID).Update("personal_qr", "").Error; err != nil {
		return "", fmt.Errorf("清空现有二维码失败: %w", err)
	}

	// 重新生成
	return s.GeneratePersonalQR(userID)
}

// GetPersonalQR 获取用户个人二维码URL
func (s *PersonalQRService) GetPersonalQR(userID uint64) (string, error) {
	var user models.User
	if err := s.db.Select("personal_qr").First(&user, userID).Error; err != nil {
		return "", fmt.Errorf("用户不存在: %w", err)
	}

	// 如果没有二维码，自动生成
	if user.PersonalQR == "" {
		return s.GeneratePersonalQR(userID)
	}

	return user.PersonalQR, nil
}
