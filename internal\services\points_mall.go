package services

import (
	"encoding/json"
	"fmt"
	"strconv"
	"time"

	"wangfujing_admin/internal/models"

	"gorm.io/gorm"
)

// PointsMallService 积分商城服务
type PointsMallService struct {
	db *gorm.DB
}

// NewPointsMallService 创建积分商城服务实例
func NewPointsMallService(db *gorm.DB) *PointsMallService {
	return &PointsMallService{db: db}
}

// CreateProductRequest 创建兑换商品请求
type CreateProductRequest struct {
	Name        string   `json:"name"`
	Images      []string `json:"images"`
	Description string   `json:"description"`
	StartTime   string   `json:"start_time" binding:"required"`
	EndTime     string   `json:"end_time" binding:"required"`
	Stock       int      `json:"stock" binding:"required,min=1,max=1000"`
	Points      int      `json:"points" binding:"required,min=1,max=1000"`
}

// CreateAvatarRequest 创建VIP形象请求
type CreateAvatarRequest struct {
	Name            string `json:"name"`
	BackgroundImage string `json:"background_image" binding:"required"`
	AlbumImage      string `json:"album_image" binding:"required"`
	DiscountRate    int    `json:"discount_rate" binding:"required"`
	EnableLottery   bool   `json:"enable_lottery"`
	LotteryRate     int    `json:"lottery_rate"`
	IsEnabled       bool   `json:"is_enabled"`
}

// UpdateProductRequest 更新兑换商品请求
type UpdateProductRequest struct {
	Name        string   `json:"name"`
	Images      []string `json:"images"`
	Description string   `json:"description"`
	StartTime   string   `json:"start_time" binding:"required"`
	EndTime     string   `json:"end_time" binding:"required"`
	Stock       int      `json:"stock" binding:"required,min=1,max=1000"`
	Points      int      `json:"points" binding:"required,min=1,max=1000"`
}

// UpdateAvatarRequest 更新VIP形象请求
type UpdateAvatarRequest struct {
	Name            string `json:"name"`
	BackgroundImage string `json:"background_image" binding:"required"`
	AlbumImage      string `json:"album_image" binding:"required"`
	DiscountRate    int    `json:"discount_rate" binding:"required"`
	EnableLottery   bool   `json:"enable_lottery"`
	LotteryRate     int    `json:"lottery_rate"`
	IsEnabled       bool   `json:"is_enabled"`
}

// PointsMallListRequest 积分商城列表请求
type PointsMallListRequest struct {
	Page    int                          `form:"page,default=1"`
	Size    int                          `form:"size,default=10"`
	Type    *models.PointsMallItemType   `form:"type"`
	Status  *models.PointsMallItemStatus `form:"status"`
	Keyword string                       `form:"keyword"`
}

// CreateProduct 创建兑换商品
func (s *PointsMallService) CreateProduct(req *CreateProductRequest, creatorID string) error {
	// 解析创建人ID
	creatorIDUint, err := strconv.ParseUint(creatorID, 10, 64)
	if err != nil {
		return fmt.Errorf("invalid creator ID: %w", err)
	}

	// 解析开始时间，明确使用中国时区
	loc, err := time.LoadLocation("Asia/Shanghai")
	if err != nil {
		return fmt.Errorf("failed to load timezone: %w", err)
	}
	startTime, err := time.ParseInLocation("2006-01-02 15:04", req.StartTime, loc)
	if err != nil {
		return fmt.Errorf("invalid start time format: %w", err)
	}

	// 解析结束时间，明确使用中国时区
	endTime, err := time.ParseInLocation("2006-01-02 15:04", req.EndTime, loc)
	if err != nil {
		return fmt.Errorf("invalid end time format: %w", err)
	}

	// 验证时间逻辑
	if endTime.Before(startTime) {
		return fmt.Errorf("end time must be after start time")
	}

	// 序列化图片数组
	imagesJSON, err := json.Marshal(req.Images)
	if err != nil {
		return fmt.Errorf("failed to marshal images: %w", err)
	}

	item := &models.PointsMallItem{
		Type:        models.PointsMallItemTypeProduct,
		Name:        req.Name,
		Images:      string(imagesJSON),
		Description: req.Description,
		StartTime:   &startTime,
		EndTime:     &endTime,
		Stock:       req.Stock,
		Points:      req.Points,
		Status:      models.PointsMallItemStatusActive, // 保存即上架
		CreatorID:   creatorIDUint,
	}

	if err := s.db.Create(item).Error; err != nil {
		return fmt.Errorf("failed to create product: %w", err)
	}

	return nil
}

// CreateAvatar 创建VIP形象
func (s *PointsMallService) CreateAvatar(req *CreateAvatarRequest, creatorID string) error {
	// 解析创建人ID
	creatorIDUint, err := strconv.ParseUint(creatorID, 10, 64)
	if err != nil {
		return fmt.Errorf("invalid creator ID: %w", err)
	}

	// 验证抽卡设置
	if req.EnableLottery && (req.LotteryRate < 1 || req.LotteryRate > 100) {
		return fmt.Errorf("lottery rate must be between 1 and 100 when lottery is enabled")
	}

	item := &models.PointsMallItem{
		Type:            models.PointsMallItemTypeAvatar,
		Name:            req.Name,
		Points:          1, // VIP形象固定1积分
		BackgroundImage: req.BackgroundImage,
		AlbumImage:      req.AlbumImage,
		DiscountRate:    req.DiscountRate,
		EnableLottery:   req.EnableLottery,
		LotteryRate:     req.LotteryRate,
		IsEnabled:       req.IsEnabled,
		Status:          models.PointsMallItemStatusActive, // 保存即上架
		CreatorID:       creatorIDUint,
	}

	if err := s.db.Create(item).Error; err != nil {
		return fmt.Errorf("failed to create avatar: %w", err)
	}

	return nil
}

// GetPointsMallList 获取积分商城列表
func (s *PointsMallService) GetPointsMallList(req *PointsMallListRequest) ([]models.PointsMallItem, int64, error) {
	var items []models.PointsMallItem
	var total int64

	query := s.db.Model(&models.PointsMallItem{})

	// 筛选条件
	if req.Type != nil {
		query = query.Where("type = ?", *req.Type)
	}
	if req.Status != nil {
		query = query.Where("status = ?", *req.Status)
	}
	if req.Keyword != "" {
		query = query.Where("name LIKE ? OR description LIKE ?",
			"%"+req.Keyword+"%", "%"+req.Keyword+"%")
	}

	// 获取总数
	if err := query.Count(&total).Error; err != nil {
		return nil, 0, fmt.Errorf("failed to count points mall items: %w", err)
	}

	// 分页查询
	offset := (req.Page - 1) * req.Size
	if err := query.Preload("Creator").
		Order("created_at DESC").
		Offset(offset).
		Limit(req.Size).
		Find(&items).Error; err != nil {
		return nil, 0, fmt.Errorf("failed to get points mall items: %w", err)
	}

	// 更新过期状态
	s.updateExpiredItems(&items)

	// 统计兑换次数和剩余库存
	for i := range items {
		count, _ := s.getExchangeCount(items[i].ID)
		items[i].ExchangeCount = count

		if items[i].Type == models.PointsMallItemTypeProduct {
			items[i].RemainStock = items[i].Stock - int(count)
			if items[i].RemainStock < 0 {
				items[i].RemainStock = 0
			}
		}
	}

	return items, total, nil
}

// GetPointsMallItem 获取积分商城商品详情
func (s *PointsMallService) GetPointsMallItem(id uint64) (*models.PointsMallItem, error) {
	var item models.PointsMallItem
	if err := s.db.Preload("Creator").First(&item, id).Error; err != nil {
		if err == gorm.ErrRecordNotFound {
			return nil, fmt.Errorf("points mall item not found")
		}
		return nil, fmt.Errorf("failed to get points mall item: %w", err)
	}

	// 检查并更新过期状态
	if item.IsExpired() && item.Status == models.PointsMallItemStatusActive {
		item.Status = models.PointsMallItemStatusExpired
		s.db.Model(&item).Update("status", models.PointsMallItemStatusExpired)
	}

	// 统计兑换次数和剩余库存
	count, _ := s.getExchangeCount(item.ID)
	item.ExchangeCount = count

	if item.Type == models.PointsMallItemTypeProduct {
		item.RemainStock = item.Stock - int(count)
		if item.RemainStock < 0 {
			item.RemainStock = 0
		}
	}

	return &item, nil
}

// UpdateProduct 更新兑换商品
func (s *PointsMallService) UpdateProduct(id uint64, req *UpdateProductRequest) error {
	var item models.PointsMallItem
	if err := s.db.First(&item, id).Error; err != nil {
		if err == gorm.ErrRecordNotFound {
			return fmt.Errorf("points mall item not found")
		}
		return fmt.Errorf("failed to get points mall item: %w", err)
	}

	// 检查是否是兑换商品
	if item.Type != models.PointsMallItemTypeProduct {
		return fmt.Errorf("item is not a product")
	}

	// 检查是否可以编辑
	if !item.CanEdit() {
		return fmt.Errorf("points mall item cannot be edited")
	}

	// 解析开始时间，明确使用中国时区
	loc, err := time.LoadLocation("Asia/Shanghai")
	if err != nil {
		return fmt.Errorf("failed to load timezone: %w", err)
	}
	startTime, err := time.ParseInLocation("2006-01-02 15:04", req.StartTime, loc)
	if err != nil {
		return fmt.Errorf("invalid start time format: %w", err)
	}

	// 解析结束时间，明确使用中国时区
	endTime, err := time.ParseInLocation("2006-01-02 15:04", req.EndTime, loc)
	if err != nil {
		return fmt.Errorf("invalid end time format: %w", err)
	}

	// 验证时间逻辑
	if endTime.Before(startTime) {
		return fmt.Errorf("end time must be after start time")
	}

	// 序列化图片数组
	imagesJSON, err := json.Marshal(req.Images)
	if err != nil {
		return fmt.Errorf("failed to marshal images: %w", err)
	}

	// 更新字段
	updates := map[string]interface{}{
		"name":        req.Name,
		"images":      string(imagesJSON),
		"description": req.Description,
		"start_time":  &startTime,
		"end_time":    &endTime,
		"stock":       req.Stock,
		"points":      req.Points,
		"status":      models.PointsMallItemStatusActive, // 编辑后重新发布
	}

	if err := s.db.Model(&item).Updates(updates).Error; err != nil {
		return fmt.Errorf("failed to update product: %w", err)
	}

	return nil
}

// UpdateAvatar 更新VIP形象
func (s *PointsMallService) UpdateAvatar(id uint64, req *UpdateAvatarRequest) error {
	var item models.PointsMallItem
	if err := s.db.First(&item, id).Error; err != nil {
		if err == gorm.ErrRecordNotFound {
			return fmt.Errorf("points mall item not found")
		}
		return fmt.Errorf("failed to get points mall item: %w", err)
	}

	// 检查是否是VIP形象
	if item.Type != models.PointsMallItemTypeAvatar {
		return fmt.Errorf("item is not an avatar")
	}

	// 检查是否可以编辑
	if !item.CanEdit() {
		return fmt.Errorf("points mall item cannot be edited")
	}

	// 验证抽卡设置
	if req.EnableLottery && (req.LotteryRate < 1 || req.LotteryRate > 100) {
		return fmt.Errorf("lottery rate must be between 1 and 100 when lottery is enabled")
	}

	// 更新字段
	updates := map[string]interface{}{
		"name":             req.Name,
		"background_image": req.BackgroundImage,
		"album_image":      req.AlbumImage,
		"discount_rate":    req.DiscountRate,
		"enable_lottery":   req.EnableLottery,
		"lottery_rate":     req.LotteryRate,
		"is_enabled":       req.IsEnabled,
		"status":           models.PointsMallItemStatusActive, // 编辑后重新发布
	}

	if err := s.db.Model(&item).Updates(updates).Error; err != nil {
		return fmt.Errorf("failed to update avatar: %w", err)
	}

	return nil
}

// UpdatePointsMallItemStatus 更新积分商城商品状态
func (s *PointsMallService) UpdatePointsMallItemStatus(id uint64, status models.PointsMallItemStatus) error {
	var item models.PointsMallItem
	if err := s.db.First(&item, id).Error; err != nil {
		if err == gorm.ErrRecordNotFound {
			return fmt.Errorf("points mall item not found")
		}
		return fmt.Errorf("failed to get points mall item: %w", err)
	}

	// 检查状态转换是否合法
	if !s.isValidStatusTransition(item.Status, status) {
		return fmt.Errorf("invalid status transition from %s to %s",
			item.Status.String(), status.String())
	}

	if err := s.db.Model(&item).Update("status", status).Error; err != nil {
		return fmt.Errorf("failed to update points mall item status: %w", err)
	}

	return nil
}

// DeletePointsMallItem 删除积分商城商品
func (s *PointsMallService) DeletePointsMallItem(id uint64) error {
	var item models.PointsMallItem
	if err := s.db.First(&item, id).Error; err != nil {
		if err == gorm.ErrRecordNotFound {
			return fmt.Errorf("points mall item not found")
		}
		return fmt.Errorf("failed to get points mall item: %w", err)
	}

	// 检查是否可以删除
	if !item.CanDelete() {
		return fmt.Errorf("points mall item cannot be deleted")
	}

	// 开启事务删除商品及相关数据
	return s.db.Transaction(func(tx *gorm.DB) error {
		// 删除兑换记录
		if err := tx.Where("item_id = ?", id).Delete(&models.PointsMallExchange{}).Error; err != nil {
			return fmt.Errorf("failed to delete exchange records: %w", err)
		}

		// 删除商品
		if err := tx.Delete(&item).Error; err != nil {
			return fmt.Errorf("failed to delete points mall item: %w", err)
		}

		return nil
	})
}

// RepublishPointsMallItem 重新发布积分商城商品（已过期商品重新编辑）
func (s *PointsMallService) RepublishPointsMallItem(id uint64, productReq *UpdateProductRequest, avatarReq *UpdateAvatarRequest) error {
	var item models.PointsMallItem
	if err := s.db.First(&item, id).Error; err != nil {
		if err == gorm.ErrRecordNotFound {
			return fmt.Errorf("points mall item not found")
		}
		return fmt.Errorf("failed to get points mall item: %w", err)
	}

	// 检查是否是已过期状态
	if item.Status != models.PointsMallItemStatusExpired {
		return fmt.Errorf("only expired items can be republished")
	}

	// 根据商品类型进行更新
	if item.Type == models.PointsMallItemTypeProduct {
		if productReq == nil {
			return fmt.Errorf("product request is required for product type")
		}
		return s.UpdateProduct(id, productReq)
	} else if item.Type == models.PointsMallItemTypeAvatar {
		if avatarReq == nil {
			return fmt.Errorf("avatar request is required for avatar type")
		}
		return s.UpdateAvatar(id, avatarReq)
	}

	return fmt.Errorf("unknown item type")
}

// updateExpiredItems 更新过期商品状态
func (s *PointsMallService) updateExpiredItems(items *[]models.PointsMallItem) {
	var expiredIDs []uint64
	for i := range *items {
		item := &(*items)[i]
		if item.IsExpired() && item.Status == models.PointsMallItemStatusActive {
			item.Status = models.PointsMallItemStatusExpired
			expiredIDs = append(expiredIDs, item.ID)
		}
	}

	// 批量更新过期状态
	if len(expiredIDs) > 0 {
		s.db.Model(&models.PointsMallItem{}).
			Where("id IN ?", expiredIDs).
			Update("status", models.PointsMallItemStatusExpired)
	}
}

// getExchangeCount 获取商品兑换次数
func (s *PointsMallService) getExchangeCount(itemID uint64) (int64, error) {
	var count int64
	err := s.db.Model(&models.PointsMallExchange{}).
		Where("item_id = ?", itemID).
		Count(&count).Error
	return count, err
}

// isValidStatusTransition 检查状态转换是否合法
func (s *PointsMallService) isValidStatusTransition(from, to models.PointsMallItemStatus) bool {
	switch from {
	case models.PointsMallItemStatusDraft:
		// 草稿可以转换为上架
		return to == models.PointsMallItemStatusActive
	case models.PointsMallItemStatusActive:
		// 已上架可以转换为下架
		return to == models.PointsMallItemStatusInactive
	case models.PointsMallItemStatusInactive:
		// 已下架可以转换为上架
		return to == models.PointsMallItemStatusActive
	case models.PointsMallItemStatusExpired:
		// 已过期可以重新上架
		return to == models.PointsMallItemStatusActive
	default:
		return false
	}
}
