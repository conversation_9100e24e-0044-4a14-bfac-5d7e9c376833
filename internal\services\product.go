package services

import (
	"context"
	"fmt"
	"time"

	"wangfujing_admin/internal/models"

	"gorm.io/gorm"
)

// ProductService 商品服务
type ProductService struct {
	db *gorm.DB
}

// NewProductService 创建商品服务
func NewProductService(db *gorm.DB) *ProductService {
	return &ProductService{
		db: db,
	}
}

// CreateProduct 创建商品
func (s *ProductService) CreateProduct(ctx context.Context, product *models.Product) error {
	now := time.Now()
	product.SubmittedAt = &now
	product.ApprovalStatus = models.ApprovalStatusPending

	return s.db.WithContext(ctx).Create(product).Error
}

// UpdateProduct 更新商品
func (s *ProductService) UpdateProduct(ctx context.Context, id uint64, updates map[string]interface{}) error {
	// 添加提交审核时间
	updates["submitted_at"] = time.Now()
	updates["approval_status"] = models.ApprovalStatusPending

	return s.db.WithContext(ctx).Model(&models.Product{}).Where("id = ?", id).Updates(updates).Error
}

// GetProductByID 根据ID获取商品
func (s *ProductService) GetProductByID(ctx context.Context, id uint64) (*models.Product, error) {
	var product models.Product
	err := s.db.WithContext(ctx).First(&product, id).Error
	if err != nil {
		return nil, err
	}
	return &product, nil
}

// GetMerchantProducts 获取商家商品列表
func (s *ProductService) GetMerchantProducts(ctx context.Context, merchantID uint64, page, size int, status string) ([]*models.Product, int64, error) {
	var products []*models.Product
	var total int64

	query := s.db.WithContext(ctx).Where("merchant_id = ?", merchantID)

	// 根据状态筛选
	switch status {
	case "pending":
		query = query.Where("approval_status = ?", models.ApprovalStatusPending)
	case "approved":
		// 已上架：审核通过且状态为启用且未过期
		query = query.Where("approval_status = ? AND status = ? AND (valid_to IS NULL OR valid_to > NOW())",
			models.ApprovalStatusApproved, models.StatusActive)
	case "rejected":
		query = query.Where("approval_status = ?", models.ApprovalStatusRejected)
	case "offline":
		// 已下架：审核通过但状态为禁用
		query = query.Where("approval_status = ? AND status = ?", models.ApprovalStatusApproved, models.StatusInactive)
	case "expired":
		// 已过期：审核通过且有效期已过
		query = query.Where("approval_status = ? AND valid_to IS NOT NULL AND valid_to <= NOW()",
			models.ApprovalStatusApproved)
	}

	// 获取总数
	if err := query.Model(&models.Product{}).Count(&total).Error; err != nil {
		return nil, 0, err
	}

	// 获取分页数据
	offset := (page - 1) * size
	if err := query.Order("updated_at DESC").Offset(offset).Limit(size).Find(&products).Error; err != nil {
		return nil, 0, err
	}

	return products, total, nil
}

// GetProductsForApproval 获取待审核商品列表（商场端）
func (s *ProductService) GetProductsForApproval(ctx context.Context, page, size int, status string) ([]*models.Product, int64, error) {
	var products []*models.Product
	var total int64

	query := s.db.WithContext(ctx)

	// 根据状态筛选
	switch status {
	case "pending":
		query = query.Where("approval_status = ?", models.ApprovalStatusPending)
	case "approved":
		query = query.Where("approval_status = ?", models.ApprovalStatusApproved)
	case "rejected":
		query = query.Where("approval_status = ?", models.ApprovalStatusRejected)
	default:
		// 默认显示待审核
		query = query.Where("approval_status = ?", models.ApprovalStatusPending)
	}

	// 获取总数
	if err := query.Model(&models.Product{}).Count(&total).Error; err != nil {
		return nil, 0, err
	}

	// 获取分页数据，预加载商家信息
	offset := (page - 1) * size
	if err := query.Preload("Merchant").Order("submitted_at DESC").Offset(offset).Limit(size).Find(&products).Error; err != nil {
		return nil, 0, err
	}

	return products, total, nil
}

// ApproveProduct 审核商品
func (s *ProductService) ApproveProduct(ctx context.Context, id uint64, approverID uint64, approved bool, note string) error {
	now := time.Now()
	updates := map[string]interface{}{
		"approver_id":   approverID,
		"approval_note": note,
		"approved_at":   now,
	}

	if approved {
		updates["approval_status"] = models.ApprovalStatusApproved
		updates["status"] = models.StatusActive // 审核通过自动上架
	} else {
		updates["approval_status"] = models.ApprovalStatusRejected
	}

	// 更新数据库
	if err := s.db.WithContext(ctx).Model(&models.Product{}).Where("id = ?", id).Updates(updates).Error; err != nil {
		return err
	}

	// 如果审核通过，记录需要初始化库存的标记
	// 实际的库存初始化将在Handler层处理
	if approved {
		fmt.Printf("Product %d approved, stock initialization will be handled by handler\n", id)
	}

	return nil
}

// ToggleProductStatus 切换商品上下架状态
func (s *ProductService) ToggleProductStatus(ctx context.Context, id uint64) error {
	var product models.Product
	if err := s.db.WithContext(ctx).First(&product, id).Error; err != nil {
		return err
	}

	// 检查是否可以切换状态
	if !product.CanToggleStatus() {
		return fmt.Errorf("商品状态不允许切换")
	}

	newStatus := models.StatusInactive
	if product.Status == models.StatusInactive {
		newStatus = models.StatusActive
	}

	return s.db.WithContext(ctx).Model(&product).Update("status", newStatus).Error
}

// DeleteProduct 删除商品
func (s *ProductService) DeleteProduct(ctx context.Context, id uint64) error {
	var product models.Product
	if err := s.db.WithContext(ctx).First(&product, id).Error; err != nil {
		return err
	}

	// 检查是否可以删除
	if !product.CanDelete() {
		return fmt.Errorf("商品状态不允许删除")
	}

	return s.db.WithContext(ctx).Delete(&product).Error
}
