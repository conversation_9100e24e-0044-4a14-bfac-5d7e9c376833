package services

import (
	"context"
	"fmt"
	"time"

	"wangfujing_admin/internal/models"

	"github.com/redis/go-redis/v9"
	"gorm.io/gorm"
)

// ProductStockAutoInitService 商品库存自动初始化服务
type ProductStockAutoInitService struct {
	db            *gorm.DB
	redis         *redis.Client
	seckillService *SeckillService
}

// NewProductStockAutoInitService 创建商品库存自动初始化服务
func NewProductStockAutoInitService(db *gorm.DB, redis *redis.Client) *ProductStockAutoInitService {
	return &ProductStockAutoInitService{
		db:            db,
		redis:         redis,
		seckillService: NewSeckillService(db, redis),
	}
}

// AutoInitStockOnApproval 商品审核通过时自动初始化库存
func (s *ProductStockAutoInitService) AutoInitStockOnApproval(ctx context.Context, productID uint64) error {
	// 获取商品信息
	var product models.Product
	if err := s.db.WithContext(ctx).Where("id = ? AND type = ? AND status = ? AND approval_status = ?", 
		productID, models.ProductTypeMerchant, models.StatusActive, models.ApprovalStatusApproved).
		First(&product).Error; err != nil {
		return fmt.Errorf("failed to get approved product: %w", err)
	}

	// 检查商品是否在有效期内
	now := time.Now()
	if product.ValidFrom != nil && now.Before(*product.ValidFrom) {
		// 商品还未开始，不初始化库存
		return nil
	}
	if product.ValidTo != nil && now.After(*product.ValidTo) {
		// 商品已过期，不初始化库存
		return nil
	}

	// 初始化库存到Redis
	return s.seckillService.InitProductStock(ctx, productID)
}

// AutoInitStockOnSchedule 定时初始化库存（每日凌晨执行）
func (s *ProductStockAutoInitService) AutoInitStockOnSchedule(ctx context.Context) error {
	now := time.Now()
	today := time.Date(now.Year(), now.Month(), now.Day(), 0, 0, 0, 0, now.Location())
	
	// 获取今日生效的商品
	var products []models.Product
	if err := s.db.WithContext(ctx).Where(
		"type = ? AND status = ? AND approval_status = ? AND (valid_from IS NULL OR valid_from <= ?) AND (valid_to IS NULL OR valid_to >= ?)",
		models.ProductTypeMerchant, 
		models.StatusActive, 
		models.ApprovalStatusApproved,
		today,
		today,
	).Find(&products).Error; err != nil {
		return fmt.Errorf("failed to get products for schedule init: %w", err)
	}

	// 批量初始化库存
	for _, product := range products {
		if err := s.seckillService.InitProductStock(ctx, product.ID); err != nil {
			// 记录错误但继续处理其他商品
			fmt.Printf("Failed to init stock for product %d: %v\n", product.ID, err)
		}
	}

	return nil
}

// RefreshStockOnProductUpdate 商品信息更新时刷新库存
func (s *ProductStockAutoInitService) RefreshStockOnProductUpdate(ctx context.Context, productID uint64) error {
	// 获取商品信息
	var product models.Product
	if err := s.db.WithContext(ctx).First(&product, productID).Error; err != nil {
		return fmt.Errorf("failed to get product: %w", err)
	}

	// 如果商品已下架或审核不通过，清除Redis库存
	if product.Status != models.StatusActive || product.ApprovalStatus != models.ApprovalStatusApproved {
		return s.clearProductStock(ctx, productID)
	}

	// 如果商品已过期，清除Redis库存
	now := time.Now()
	if product.ValidTo != nil && now.After(*product.ValidTo) {
		return s.clearProductStock(ctx, productID)
	}

	// 刷新库存信息
	return s.seckillService.RefreshProductStock(ctx, productID)
}

// clearProductStock 清除商品库存
func (s *ProductStockAutoInitService) clearProductStock(ctx context.Context, productID uint64) error {
	stockKey := fmt.Sprintf("seckill:product:%d:stock", productID)
	stockCountKey := fmt.Sprintf("seckill:product:%d:count", productID)
	
	// 删除Redis中的库存信息
	s.redis.Del(ctx, stockKey)
	s.redis.Del(ctx, stockCountKey)
	
	return nil
}

// GetStockInitializationRules 获取库存初始化规则说明
func (s *ProductStockAutoInitService) GetStockInitializationRules() map[string]string {
	return map[string]string{
		"auto_init_on_approval": "商品审核通过且上架时自动初始化库存",
		"auto_init_on_schedule": "每日凌晨自动初始化当日生效商品的库存",
		"refresh_on_update":     "商品信息更新时刷新库存（下架/过期时清除库存）",
		"manual_repair":         "保留手动修复脚本用于数据修复",
		"valid_period_check":    "只有在有效期内的商品才会初始化库存",
		"daily_reset":           "每日库存独立计算，不累计",
	}
}

// BatchRepairStock 批量修复库存（用于数据修复）
func (s *ProductStockAutoInitService) BatchRepairStock(ctx context.Context) error {
	// 获取所有应该有库存的商品
	now := time.Now()
	var products []models.Product
	if err := s.db.WithContext(ctx).Where(
		"type = ? AND status = ? AND approval_status = ? AND (valid_from IS NULL OR valid_from <= ?) AND (valid_to IS NULL OR valid_to >= ?)",
		models.ProductTypeMerchant,
		models.StatusActive,
		models.ApprovalStatusApproved,
		now,
		now,
	).Find(&products).Error; err != nil {
		return fmt.Errorf("failed to get products for repair: %w", err)
	}

	repairCount := 0
	for _, product := range products {
		// 检查Redis中是否有库存记录
		stockCountKey := fmt.Sprintf("seckill:product:%d:count", product.ID)
		exists, err := s.redis.Exists(ctx, stockCountKey).Result()
		if err != nil {
			continue
		}

		// 如果没有库存记录，则初始化
		if exists == 0 {
			if err := s.seckillService.InitProductStock(ctx, product.ID); err != nil {
				fmt.Printf("Failed to repair stock for product %d: %v\n", product.ID, err)
			} else {
				repairCount++
				fmt.Printf("Repaired stock for product %d\n", product.ID)
			}
		}
	}

	fmt.Printf("Stock repair completed, repaired %d products\n", repairCount)
	return nil
}

// ScheduledStockMaintenance 定时库存维护任务
func (s *ProductStockAutoInitService) ScheduledStockMaintenance(ctx context.Context) error {
	// 1. 清理过期商品的库存
	if err := s.cleanupExpiredProductStock(ctx); err != nil {
		fmt.Printf("Failed to cleanup expired product stock: %v\n", err)
	}

	// 2. 初始化新生效商品的库存
	if err := s.AutoInitStockOnSchedule(ctx); err != nil {
		fmt.Printf("Failed to auto init stock on schedule: %v\n", err)
	}

	// 3. 修复缺失的库存记录
	if err := s.BatchRepairStock(ctx); err != nil {
		fmt.Printf("Failed to batch repair stock: %v\n", err)
	}

	return nil
}

// cleanupExpiredProductStock 清理过期商品库存
func (s *ProductStockAutoInitService) cleanupExpiredProductStock(ctx context.Context) error {
	// 获取所有已过期的商品
	now := time.Now()
	var expiredProducts []models.Product
	if err := s.db.WithContext(ctx).Where("valid_to IS NOT NULL AND valid_to < ?", now).
		Find(&expiredProducts).Error; err != nil {
		return fmt.Errorf("failed to get expired products: %w", err)
	}

	// 清理过期商品的Redis库存
	for _, product := range expiredProducts {
		s.clearProductStock(ctx, product.ID)
	}

	fmt.Printf("Cleaned up stock for %d expired products\n", len(expiredProducts))
	return nil
}
