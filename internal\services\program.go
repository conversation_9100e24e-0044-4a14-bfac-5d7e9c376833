package services

import (
	"context"
	"encoding/json"
	"fmt"
	"strconv"
	"time"
	"wangfujing_admin/internal/models"

	"gorm.io/gorm"
)

// PaogramService 节目单服务
type ProgramService struct {
	db *gorm.DB
}

type TimeRange struct {
	StartHour string `json:"start_hour" binding:"required,gte=0,lte=24"`
	EndHour   string `json:"end_hour" binding:"required,gte=0,lte=24"`
}

// NewProgramService 创建节目单服务实例
func NewProgramService(db *gorm.DB) *ProgramService {
	return &ProgramService{db: db}
}

// CreateActivityRequest 创建节目单请求
type CreateProgramRequest struct {
	ProgramName         string   `json:"program_name" binding:"required,max=200"`
	Url                 []string `json:"url" binding:"required"`
	ProgramIntroduction string   `json:"program_introduction" bunding:"required"`
	ShowTimeHour        []struct {
		StartHour string `json:"start_hour" validate:"required"`
		EndHour   string `json:"end_hour" validate:"required"`
	} `json:"show_time_hour" budding:"required,dive"`
	ShowTimeStart string `json:"show_time_start" binding:"required,max=10"`
	ShowTimeEnd   string `json:"show_time_end" binding:"required,max=10"`
	FloorId       int    `json:"floor_id" binding:"max=14"`
}

// UpdateActivityRequest 更新节目单请求
type UpdateProgramRequest struct {
	ProgramName         string   `json:"program_name" binding:"required,max=200"`
	Url                 []string `json:"url" binding:"required"`
	ProgramIntroduction string   `json:"program_introduction" bunding:"required"`
	ShowTimeHour        []struct {
		StartHour string `json:"start_hour" validate:"required"`
		EndHour   string `json:"end_hour" validate:"required"`
	} `json:"show_time_hour" budding:"required,dive"`
	ShowTimeStart string `json:"show_time_start" binding:"required,max=10"`
	ShowTimeEnd   string `json:"show_time_end" binding:"required,max=10"`
	FloorId       int    `json:"floor_id" binding:"max=14"`
}

// ActivityListRequest 活动列表请求
type ProgramListRequest struct {
	Page     int                             `form:"page,default=1"`
	Size     int                             `form:"size,default=10"`
	Category *models.MarketingCategory       `form:"category"`
	Type     *models.MarketingActivityType   `form:"type"`
	Status   *models.MarketingActivityStatus `form:"status"`
	Keyword  string                          `form:"keyword"`
}

type ProgramWithFloor struct {
	models.Program
	FloorName string `json:"floor_name" gorm:"column:floor_name"`
}

// CreateProgram 创建节目单
func (s *ProgramService) CreateProgram(req *CreateProgramRequest, creatorID string) error {
	// 解析创建人ID
	_, err := strconv.ParseUint(creatorID, 10, 64)
	if err != nil {
		return fmt.Errorf("invalid creator ID: %w", err)
	}
	// 序列化数组字段
	urlJSON, err := json.Marshal(req.Url)
	if err != nil {
		return fmt.Errorf("failed to marshal URLs: %v", err)
	}

	hoursJSON, err := json.Marshal(req.ShowTimeHour)
	if err != nil {
		return fmt.Errorf("failed to marshal time ranges: %v", err)
	}
	program := &models.Program{
		ProgramName:         req.ProgramName,
		Url:                 string(urlJSON),
		ProgramIntroduction: req.ProgramIntroduction,
		ShowTimeHour:        string(hoursJSON),
		FloorId:             req.FloorId,
		ShowTimeStart:       req.ShowTimeStart,
		ShowTimeEnd:         req.ShowTimeEnd,
	}

	if err := s.db.Create(program).Error; err != nil {
		return fmt.Errorf("failed to create activity: %w", err)
	}

	return nil
}

func (s *ProgramService) UpdateProgram(id uint64, req *UpdateProgramRequest) error {
	var item models.Program
	if err := s.db.First(&item, id).Error; err != nil {
		if err == gorm.ErrRecordNotFound {
			return fmt.Errorf("program item not found")
		}
		return fmt.Errorf("failed to get program item: %w", err)
	}
	// 序列化数组字段
	urlJSON, err := json.Marshal(req.Url)
	if err != nil {
		return fmt.Errorf("failed to marshal URLs: %v", err)
	}

	hoursJSON, err := json.Marshal(req.ShowTimeHour)
	if err != nil {
		return fmt.Errorf("failed to marshal time ranges: %v", err)
	}
	// 更新字段
	updates := map[string]interface{}{
		"program_name":         req.ProgramName,
		"url":                  string(urlJSON),
		"program_introduction": req.ProgramIntroduction,
		"show_time_hour":       string(hoursJSON),
		"floor_id":             req.FloorId,
		"show_time_start":      req.ShowTimeStart,
		"show_time_end":        req.ShowTimeEnd,
	}

	if err := s.db.Model(&item).Updates(updates).Error; err != nil {
		return fmt.Errorf("failed to update program: %w", err)
	}

	return nil
}

// 删除节目单
func (s *ProgramService) DeleteProgram(ctx context.Context, id uint64) error {
	var item models.Program
	if err := s.db.First(&item, id).Error; err != nil {
		if err == gorm.ErrRecordNotFound {
			return fmt.Errorf("program item not found")
		}
		return fmt.Errorf("failed to get program item: %w", err)
	}

	return s.db.WithContext(ctx).Delete(&item).Error
}

// 获取节目单
func (s *ProgramService) GetPrograms(ctx context.Context, page int, size int, types string) ([]ProgramWithFloor, int64, error) {
	// var proframs []*models.Program
	var programs []ProgramWithFloor
	var total int64

	query := s.db.WithContext(ctx).Model(&models.Program{})
	currentDate := time.Now().UTC().Format("2006-01-02")
	// 根据状态筛选
	switch types {
	case "now":
		query = query.Where("show_time_start <= ? AND show_time_end >= ?", currentDate, currentDate)
	case "future":
		query = query.Where("show_time_start > ?", currentDate)
	case "expired":
		query = query.Where("show_time_end < ?", currentDate)
	case "all":
		// 所有节目：不需要添加时间条件
	default:
		// 可以添加默认处理或返回错误
	}

	// 分页参数校验
	if page < 1 {
		page = 1
	}
	if size < 1 {
		size = 10 // 默认分页大小
	}

	// 获取总数
	if err := query.Count(&total).Error; err != nil {
		return nil, 0, err
	}
	// 获取分页数据
	offset := (page - 1) * size
	if err := query.
		Select("programs.*, COALESCE(floors.name, '') as floor_name").
		Joins("LEFT JOIN floors ON floors.id = programs.floor_id").
		Order("programs.created_at DESC").
		Offset(offset).
		Limit(size).
		Find(&programs).Error; err != nil {
		return nil, 0, fmt.Errorf("failed to query programs: %w", err)
	}

	return programs, total, nil
}
