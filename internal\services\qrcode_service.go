package services

import (
	"bytes"
	"context"
	"fmt"
	"image/png"
	"time"
	"wangfujing_admin/internal/models"
	"wangfujing_admin/pkg/oss"
	"wangfujing_admin/pkg/security"

	"github.com/google/uuid"
	"github.com/skip2/go-qrcode"
	"gorm.io/gorm"
)

// QRCodeService 二维码服务
type QRCodeService struct {
	db         *gorm.DB
	ossManager *oss.OSSManager
	hmacSigner *security.HMACSigner
}

// NewQRCodeService 创建二维码服务
func NewQRCodeService(db *gorm.DB, ossManager *oss.OSSManager) *QRCodeService {
	// 创建HMAC签名器
	hmacConfig := security.GetDefaultConfig()
	hmacSigner := security.NewHMACSigner(hmacConfig)

	return &QRCodeService{
		db:         db,
		ossManager: ossManager,
		hmacSigner: hmacSigner,
	}
}

// GenerateOrderQRCode 生成订单二维码
func (s *QRCodeService) GenerateOrderQRCode(ctx context.Context, orderID uint64) (string, error) {
	fmt.Printf("QRCodeService: 开始生成订单二维码, orderID=%d\n", orderID)

	// 查询订单信息
	var order models.Order
	if err := s.db.WithContext(ctx).Where("id = ?", orderID).First(&order).Error; err != nil {
		fmt.Printf("QRCodeService: 查询订单失败, orderID=%d, error=%v\n", orderID, err)
		return "", fmt.Errorf("查询订单失败: %w", err)
	}

	fmt.Printf("QRCodeService: 找到订单, orderID=%d, orderNo=%s, qrCodeURL=%s\n",
		order.ID, order.OrderNo, order.QRCodeURL)

	// 如果订单已经有二维码URL，则直接返回
	if order.QRCodeURL != "" {
		fmt.Printf("QRCodeService: 订单已有二维码URL, orderID=%d, url=%s\n", orderID, order.QRCodeURL)
		return order.QRCodeURL, nil
	}

	// 生成二维码内容
	qrContent, err := s.generateQRContent(order)
	if err != nil {
		fmt.Printf("QRCodeService: 生成二维码内容失败, orderID=%d, error=%v\n", orderID, err)
		return "", fmt.Errorf("生成二维码内容失败: %w", err)
	}
	fmt.Printf("QRCodeService: 生成二维码内容, orderID=%d, content=%s\n", orderID, qrContent)

	// 生成二维码图片
	fmt.Printf("QRCodeService: 开始生成和上传二维码图片, orderID=%d\n", orderID)
	qrCodeURL, err := s.generateAndUploadQRCode(ctx, qrContent, orderID)
	if err != nil {
		fmt.Printf("QRCodeService: 生成二维码图片失败, orderID=%d, error=%v\n", orderID, err)
		return "", fmt.Errorf("生成二维码失败: %w", err)
	}

	fmt.Printf("QRCodeService: 二维码图片生成成功, orderID=%d, url=%s\n", orderID, qrCodeURL)

	// 更新订单二维码URL
	fmt.Printf("QRCodeService: 开始更新订单二维码URL, orderID=%d\n", orderID)
	if err := s.db.WithContext(ctx).Model(&order).Update("qr_code_url", qrCodeURL).Error; err != nil {
		fmt.Printf("QRCodeService: 更新订单二维码URL失败, orderID=%d, error=%v\n", orderID, err)
		return "", fmt.Errorf("更新订单二维码URL失败: %w", err)
	}

	fmt.Printf("QRCodeService: 订单二维码生成完成, orderID=%d, url=%s\n", orderID, qrCodeURL)
	return qrCodeURL, nil
}

// generateQRContent 生成二维码内容
func (s *QRCodeService) generateQRContent(order models.Order) (string, error) {
	// 生成唯一的核销码
	verifyCode := uuid.New().String()

	// 计算二维码过期时间（根据订单类型查询不同的商品表）
	var expiresAt int64

	if order.OrderType == models.ProductTypeMall {
		// 积分商城商品
		var item models.PointsMallItem
		if err := s.db.Where("id = ?", order.ProductID).First(&item).Error; err != nil {
			return "", fmt.Errorf("查询积分商城商品信息失败: %w", err)
		}

		// 根据商品类型设置不同的过期时间
		switch item.Type {
		case models.PointsMallItemTypeAvatar:
			// VIP形象：当天23:59:59过期
			now := time.Now()
			endOfDay := time.Date(now.Year(), now.Month(), now.Day(), 23, 59, 59, 0, now.Location())
			expiresAt = endOfDay.Unix()
			fmt.Printf("QRCodeService: VIP形象二维码过期时间设置为当天23:59:59: %s\n", endOfDay.Format("2006-01-02 15:04:05"))
		case models.PointsMallItemTypeProduct:
			// 普通商品：使用商品设置的有效期
			if item.EndTime != nil {
				expiresAt = item.EndTime.Unix()
				fmt.Printf("QRCodeService: 普通商品二维码过期时间使用商品有效期: %s\n", item.EndTime.Format("2006-01-02 15:04:05"))
			} else {
				// 如果商品没有设置有效期，默认7天
				defaultExpiry := time.Now().Add(7 * 24 * time.Hour)
				expiresAt = defaultExpiry.Unix()
				fmt.Printf("QRCodeService: 普通商品无有效期，使用默认7天: %s\n", defaultExpiry.Format("2006-01-02 15:04:05"))
			}
		default:
			// 未知类型，默认7天
			defaultExpiry := time.Now().Add(7 * 24 * time.Hour)
			expiresAt = defaultExpiry.Unix()
			fmt.Printf("QRCodeService: 未知商品类型，使用默认7天: %s\n", defaultExpiry.Format("2006-01-02 15:04:05"))
		}
	} else {
		// 商家商品
		var product models.Product
		if err := s.db.Where("id = ?", order.ProductID).First(&product).Error; err != nil {
			return "", fmt.Errorf("查询商品信息失败: %w", err)
		}

		if product.ValidTo != nil {
			expiresAt = product.ValidTo.Unix()
		} else {
			// 如果商品没有设置有效期，默认7天
			expiresAt = time.Now().Add(7 * 24 * time.Hour).Unix()
		}
	}

	// 创建二维码数据
	var merchantID uint64
	if order.MerchantID != nil {
		merchantID = *order.MerchantID
	}

	qrData, err := s.hmacSigner.CreateQRCodeDataWithExpiry(
		order.ID,
		order.OrderNo,
		order.UserID,
		order.ProductID,
		merchantID,
		verifyCode,
		expiresAt,
	)
	if err != nil {
		return "", fmt.Errorf("创建二维码数据失败: %w", err)
	}

	// 编码二维码数据
	qrContent := s.hmacSigner.EncodeQRCodeData(qrData)

	// 更新订单的核销码
	if err := s.db.Model(&order).Update("verify_code", verifyCode).Error; err != nil {
		return "", fmt.Errorf("更新订单核销码失败: %w", err)
	}

	return qrContent, nil
}

// generateAndUploadQRCode 生成二维码并上传到OSS
func (s *QRCodeService) generateAndUploadQRCode(ctx context.Context, content string, orderID uint64) (string, error) {
	// 生成二维码图片
	qrCode, err := qrcode.New(content, qrcode.Medium)
	if err != nil {
		return "", fmt.Errorf("创建二维码失败: %w", err)
	}

	// 设置二维码大小
	qrCode.DisableBorder = false

	// 将二维码转换为PNG图片
	img := qrCode.Image(256)

	// 将图片编码为字节数组
	var buf bytes.Buffer
	if err := png.Encode(&buf, img); err != nil {
		return "", fmt.Errorf("编码二维码图片失败: %w", err)
	}

	// 生成文件名
	filename := fmt.Sprintf("qrcode_order_%d_%d.png", orderID, time.Now().Unix())

	// 上传到OSS
	qrCodeURL, err := s.ossManager.UploadBytes(buf.Bytes(), filename, "qrcodes")
	if err != nil {
		return "", fmt.Errorf("上传二维码到OSS失败: %w", err)
	}

	return qrCodeURL, nil
}

// BatchGenerateQRCodes 批量生成二维码（用于补充历史订单）
func (s *QRCodeService) BatchGenerateQRCodes(ctx context.Context, limit int) error {
	// 查询没有二维码URL的订单
	var orders []models.Order
	if err := s.db.WithContext(ctx).
		Where("qr_code_url = '' OR qr_code_url IS NULL").
		Limit(limit).
		Find(&orders).Error; err != nil {
		return fmt.Errorf("查询订单失败: %w", err)
	}

	// 批量生成二维码
	for _, order := range orders {
		qrCodeURL, err := s.GenerateOrderQRCode(ctx, order.ID)
		if err != nil {
			// 记录错误但继续处理其他订单
			fmt.Printf("生成订单 %d 二维码失败: %v\n", order.ID, err)
			continue
		}
		fmt.Printf("成功生成订单 %d 二维码: %s\n", order.ID, qrCodeURL)
	}

	return nil
}

// RegenerateQRCode 重新生成二维码（用于更新已有的二维码）
func (s *QRCodeService) RegenerateQRCode(ctx context.Context, orderID uint64) (string, error) {
	// 查询订单信息
	var order models.Order
	if err := s.db.WithContext(ctx).Where("id = ?", orderID).First(&order).Error; err != nil {
		return "", fmt.Errorf("查询订单失败: %w", err)
	}

	// 生成二维码内容（会自动使用商品有效期）
	qrContent, err := s.generateQRContent(order)
	if err != nil {
		return "", fmt.Errorf("生成二维码内容失败: %w", err)
	}

	// 生成新的二维码图片
	qrCodeURL, err := s.generateAndUploadQRCode(ctx, qrContent, orderID)
	if err != nil {
		return "", fmt.Errorf("生成二维码失败: %w", err)
	}

	// 更新订单二维码URL
	if err := s.db.WithContext(ctx).Model(&order).Update("qr_code_url", qrCodeURL).Error; err != nil {
		return "", fmt.Errorf("更新订单二维码URL失败: %w", err)
	}

	return qrCodeURL, nil
}

// GetQRCodeStats 获取二维码生成统计
func (s *QRCodeService) GetQRCodeStats(ctx context.Context) (map[string]interface{}, error) {
	var totalOrders int64
	var ordersWithQR int64

	// 统计总订单数
	if err := s.db.WithContext(ctx).Model(&models.Order{}).Count(&totalOrders).Error; err != nil {
		return nil, fmt.Errorf("统计总订单数失败: %w", err)
	}

	// 统计有二维码的订单数
	if err := s.db.WithContext(ctx).Model(&models.Order{}).
		Where("qr_code_url != '' AND qr_code_url IS NOT NULL").
		Count(&ordersWithQR).Error; err != nil {
		return nil, fmt.Errorf("统计有二维码的订单数失败: %w", err)
	}

	stats := map[string]interface{}{
		"total_orders":      totalOrders,
		"orders_with_qr":    ordersWithQR,
		"orders_without_qr": totalOrders - ordersWithQR,
		"completion_rate":   float64(ordersWithQR) / float64(totalOrders) * 100,
	}

	return stats, nil
}
