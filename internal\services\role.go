package services

import (
	"context"
	"errors"
	"fmt"
	"strconv"

	"wangfujing_admin/internal/models"
	"wangfujing_admin/pkg/cache"

	"gorm.io/gorm"
)

// RoleService 角色服务
type RoleService struct {
	db *gorm.DB
}

// NewRoleService 创建角色服务
func NewRoleService(db *gorm.DB) *RoleService {
	return &RoleService{
		db: db,
	}
}

// CreateRole 创建角色
func (s *RoleService) CreateRole(ctx context.Context, role *models.Role) error {
	// 检查角色名是否已存在
	var existingRole models.Role
	if err := s.db.WithContext(ctx).Where("name = ?", role.Name).First(&existingRole).Error; err == nil {
		return errors.New("role name already exists")
	} else if !errors.Is(err, gorm.ErrRecordNotFound) {
		return fmt.Errorf("failed to check existing role: %w", err)
	}

	// 创建角色
	if err := s.db.WithContext(ctx).Create(role).Error; err != nil {
		return fmt.Errorf("failed to create role: %w", err)
	}

	return nil
}

// GetRoleByID 根据ID获取角色
func (s *RoleService) GetRoleByID(ctx context.Context, id string) (*models.Role, error) {
	var role models.Role
	if err := s.db.WithContext(ctx).First(&role, "id = ?", id).Error; err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return nil, errors.New("role not found")
		}
		return nil, fmt.Errorf("failed to get role: %w", err)
	}
	return &role, nil
}

// GetRoleByName 根据名称获取角色
func (s *RoleService) GetRoleByName(ctx context.Context, name string) (*models.Role, error) {
	var role models.Role
	if err := s.db.WithContext(ctx).First(&role, "name = ?", name).Error; err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return nil, errors.New("role not found")
		}
		return nil, fmt.Errorf("failed to get role: %w", err)
	}
	return &role, nil
}

// GetRoleWithPermissions 获取角色及其权限
func (s *RoleService) GetRoleWithPermissions(ctx context.Context, id string) (*models.Role, error) {
	// 先获取角色
	role, err := s.GetRoleByID(ctx, id)
	if err != nil {
		return nil, err
	}

	// 再获取角色的权限
	var permissions []models.Permission
	err = s.db.WithContext(ctx).
		Table("permissions").
		Joins("INNER JOIN role_permissions ON permissions.id = role_permissions.permission_id").
		Where("role_permissions.role_id = ?", id).
		Find(&permissions).Error

	if err != nil {
		return nil, fmt.Errorf("failed to get role permissions: %w", err)
	}

	role.Permissions = permissions
	return role, nil
}

// UpdateRole 更新角色
func (s *RoleService) UpdateRole(ctx context.Context, id string, updates map[string]interface{}) error {
	result := s.db.WithContext(ctx).Model(&models.Role{}).Where("id = ?", id).Updates(updates)
	if result.Error != nil {
		return fmt.Errorf("failed to update role: %w", result.Error)
	}
	if result.RowsAffected == 0 {
		return errors.New("role not found")
	}

	// 清除缓存
	cache.Delete(ctx, fmt.Sprintf("role:%s", id))

	return nil
}

// DeleteRole 删除角色
func (s *RoleService) DeleteRole(ctx context.Context, id string) error {
	// 检查是否有用户使用该角色
	var count int64
	if err := s.db.WithContext(ctx).Model(&models.UserRole{}).Where("role_id = ?", id).Count(&count).Error; err != nil {
		return fmt.Errorf("failed to check role usage: %w", err)
	}
	if count > 0 {
		return errors.New("role is in use, cannot delete")
	}

	// 开始事务
	tx := s.db.WithContext(ctx).Begin()
	defer func() {
		if r := recover(); r != nil {
			tx.Rollback()
		}
	}()

	// 删除角色权限关联
	if err := tx.Where("role_id = ?", id).Delete(&models.RolePermission{}).Error; err != nil {
		tx.Rollback()
		return fmt.Errorf("failed to delete role permissions: %w", err)
	}

	// 删除角色
	result := tx.Delete(&models.Role{}, "id = ?", id)
	if result.Error != nil {
		tx.Rollback()
		return fmt.Errorf("failed to delete role: %w", result.Error)
	}
	if result.RowsAffected == 0 {
		tx.Rollback()
		return errors.New("role not found")
	}

	if err := tx.Commit().Error; err != nil {
		return fmt.Errorf("failed to commit transaction: %w", err)
	}

	// 清除缓存
	cache.Delete(ctx, fmt.Sprintf("role:%s", id))

	return nil
}

// GetRoles 获取角色列表
func (s *RoleService) GetRoles(ctx context.Context, page, size int, status *models.Status) ([]*models.Role, int64, error) {
	var roles []*models.Role
	var total int64

	query := s.db.WithContext(ctx).Model(&models.Role{})

	// 添加过滤条件
	if status != nil {
		query = query.Where("status = ?", *status)
	}

	// 获取总数
	if err := query.Count(&total).Error; err != nil {
		return nil, 0, fmt.Errorf("failed to count roles: %w", err)
	}

	// 获取分页数据
	offset := (page - 1) * size
	if err := query.Preload("Permissions").Offset(offset).Limit(size).Order("created_at DESC").Find(&roles).Error; err != nil {
		return nil, 0, fmt.Errorf("failed to get roles: %w", err)
	}

	return roles, total, nil
}

// UpdateRoleStatus 更新角色状态
func (s *RoleService) UpdateRoleStatus(ctx context.Context, id string, status models.Status) error {
	return s.UpdateRole(ctx, id, map[string]interface{}{
		"status": status,
	})
}

// AssignPermissions 分配权限
func (s *RoleService) AssignPermissions(ctx context.Context, roleID string, permissionIDs []string) error {
	// 开始事务
	tx := s.db.WithContext(ctx).Begin()
	defer func() {
		if r := recover(); r != nil {
			tx.Rollback()
		}
	}()

	// 删除现有权限关联
	if err := tx.Where("role_id = ?", roleID).Delete(&models.RolePermission{}).Error; err != nil {
		tx.Rollback()
		return fmt.Errorf("failed to delete existing role permissions: %w", err)
	}

	// 添加新的权限关联
	for _, permissionID := range permissionIDs {
		// 转换字符串ID为uint64
		roleIDUint, err := strconv.ParseUint(roleID, 10, 64)
		if err != nil {
			return fmt.Errorf("invalid role ID: %w", err)
		}
		permissionIDUint, err := strconv.ParseUint(permissionID, 10, 64)
		if err != nil {
			return fmt.Errorf("invalid permission ID: %w", err)
		}

		rolePermission := &models.RolePermission{
			RoleID:       roleIDUint,
			PermissionID: permissionIDUint,
		}
		if err := tx.Create(rolePermission).Error; err != nil {
			tx.Rollback()
			return fmt.Errorf("failed to create role permission: %w", err)
		}
	}

	if err := tx.Commit().Error; err != nil {
		return fmt.Errorf("failed to commit transaction: %w", err)
	}

	// 清除缓存
	cache.Delete(ctx, fmt.Sprintf("role:%s", roleID))

	return nil
}

// GetRolePermissions 获取角色权限
func (s *RoleService) GetRolePermissions(ctx context.Context, roleID string) ([]*models.Permission, error) {
	var permissions []*models.Permission

	if err := s.db.WithContext(ctx).
		Table("permissions").
		Joins("JOIN role_permissions ON permissions.id = role_permissions.permission_id").
		Where("role_permissions.role_id = ?", roleID).
		Find(&permissions).Error; err != nil {
		return nil, fmt.Errorf("failed to get role permissions: %w", err)
	}

	return permissions, nil
}

// GetAllRoles 获取所有角色（不分页）
func (s *RoleService) GetAllRoles(ctx context.Context) ([]*models.Role, error) {
	var roles []*models.Role

	if err := s.db.WithContext(ctx).Order("created_at ASC").Find(&roles).Error; err != nil {
		return nil, fmt.Errorf("failed to get all roles: %w", err)
	}

	return roles, nil
}

// GetRoleUsers 获取角色下的用户
func (s *RoleService) GetRoleUsers(ctx context.Context, roleID string, page, size int) ([]*models.User, int64, error) {
	var users []*models.User
	var total int64

	query := s.db.WithContext(ctx).
		Table("users").
		Joins("JOIN user_roles ON users.id = user_roles.user_id").
		Where("user_roles.role_id = ?", roleID)

	// 获取总数
	if err := query.Count(&total).Error; err != nil {
		return nil, 0, fmt.Errorf("failed to count role users: %w", err)
	}

	// 获取分页数据
	offset := (page - 1) * size
	if err := query.Offset(offset).Limit(size).Order("users.created_at DESC").Find(&users).Error; err != nil {
		return nil, 0, fmt.Errorf("failed to get role users: %w", err)
	}

	return users, total, nil
}

// CheckRoleExists 检查角色是否存在
func (s *RoleService) CheckRoleExists(ctx context.Context, id string) (bool, error) {
	// 转换字符串ID为uint64
	idUint, err := strconv.ParseUint(id, 10, 64)
	if err != nil {
		return false, fmt.Errorf("invalid role ID: %w", err)
	}

	var count int64
	if err := s.db.WithContext(ctx).Model(&models.Role{}).Where("id = ?", idUint).Count(&count).Error; err != nil {
		return false, fmt.Errorf("failed to check role existence: %w", err)
	}
	return count > 0, nil
}

// GetRolesByIDs 根据ID列表获取角色
func (s *RoleService) GetRolesByIDs(ctx context.Context, ids []string) ([]*models.Role, error) {
	var roles []*models.Role

	// 转换字符串ID列表为uint64列表
	var idUints []uint64
	for _, id := range ids {
		idUint, err := strconv.ParseUint(id, 10, 64)
		if err != nil {
			return nil, fmt.Errorf("invalid role ID %s: %w", id, err)
		}
		idUints = append(idUints, idUint)
	}

	if err := s.db.WithContext(ctx).Where("id IN ?", idUints).Find(&roles).Error; err != nil {
		return nil, fmt.Errorf("failed to get roles by ids: %w", err)
	}

	return roles, nil
}
