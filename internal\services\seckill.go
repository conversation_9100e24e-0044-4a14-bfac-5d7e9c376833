package services

import (
	"context"
	"encoding/json"
	"fmt"
	"strconv"
	"time"

	"wangfujing_admin/internal/models"

	"github.com/redis/go-redis/v9"
	"gorm.io/gorm"
)

const (
	MaxQPS         = 1000  // 单机最大处理量
	RedisPoolSize  = 200   // Redis连接池大小
	TokenRate      = 800   // 令牌生成速率/秒
	BucketCapacity = 2000  // 令牌桶容量
	MaxQueueSize   = 10000 // 最大队列长度
)

// SeckillService 秒杀服务
type SeckillService struct {
	db    *gorm.DB
	redis *redis.Client
}

// NewSeckillService 创建秒杀服务
func NewSeckillService(db *gorm.DB, redis *redis.Client) *SeckillService {
	return &SeckillService{
		db:    db,
		redis: redis,
	}
}

// ProductStock 商品库存信息
type ProductStock struct {
	ProductID  uint64 `json:"product_id"`
	Stock      int    `json:"stock"`
	DailyLimit int    `json:"daily_limit"`
	UsedCount  int    `json:"used_count"` // 已使用数量
	// UserLimit 字段已移除 - 不限制用户购买次数，只限制每日总量
	IsActive    bool   `json:"is_active"`
	ValidFrom   string `json:"valid_from"`
	ValidTo     string `json:"valid_to"`
	RefreshTime string `json:"refresh_time"` // 刷新时间
}

// InitProductStock 初始化商品库存到Redis
func (s *SeckillService) InitProductStock(ctx context.Context, productID uint64) error {
	// 从数据库获取商品信息
	var product models.Product
	if err := s.db.WithContext(ctx).Where("id = ? AND status = ? AND approval_status = ?",
		productID, models.StatusActive, models.ApprovalStatusApproved).First(&product).Error; err != nil {
		return fmt.Errorf("failed to get product: %w", err)
	}

	// 构建库存信息
	stock := ProductStock{
		ProductID:  product.ID,
		Stock:      product.DailyLimit,
		DailyLimit: product.DailyLimit,
		IsActive:   true,
	}

	if product.ValidFrom != nil {
		stock.ValidFrom = product.ValidFrom.Format("2006-01-02 15:04:05")
	}
	if product.ValidTo != nil {
		stock.ValidTo = product.ValidTo.Format("2006-01-02 15:04:05")
	}

	// 序列化为JSON
	stockData, err := json.Marshal(stock)
	if err != nil {
		return fmt.Errorf("failed to marshal stock data: %w", err)
	}

	// 计算Redis键的过期时间
	expiration := s.CalculateRedisExpiration(&product)

	// 存储到Redis
	stockKey := fmt.Sprintf("seckill:product:%d:stock", productID)
	if err := s.redis.Set(ctx, stockKey, stockData, expiration).Err(); err != nil {
		return fmt.Errorf("failed to set stock to redis: %w", err)
	}

	// 初始化库存计数器
	stockCountKey := fmt.Sprintf("seckill:product:%d:count", productID)
	if err := s.redis.Set(ctx, stockCountKey, product.DailyLimit, expiration).Err(); err != nil {
		return fmt.Errorf("failed to set stock count to redis: %w", err)
	}

	return nil
}

// CalculateRedisExpiration 计算Redis键的过期时间（严格按照商品有效期）
func (s *SeckillService) CalculateRedisExpiration(product *models.Product) time.Duration {
	now := time.Now()

	// 如果商品有明确的结束时间，严格按照商品有效期设置
	if product.ValidTo != nil {
		// 计算到商品过期的时间
		timeToExpire := product.ValidTo.Sub(now)

		// 如果商品已经过期，立即过期（1分钟内清理）
		if timeToExpire <= 0 {
			return time.Minute
		}

		// 如果商品即将过期（1小时内），设置为实际剩余时间
		if timeToExpire <= time.Hour {
			return timeToExpire
		}

		return timeToExpire
	}

	// 如果没有结束时间，按照默认策略：
	// 商品肯定会有有效期，这里只是兜底策略，设置到今天23:59:59
	endOfToday := time.Date(now.Year(), now.Month(), now.Day(), 23, 59, 59, 0, now.Location())
	timeToEndOfDay := endOfToday.Sub(now)

	// 如果已经过了今天23:59:59，设置1分钟过期
	if timeToEndOfDay <= 0 {
		return time.Minute
	}

	return timeToEndOfDay
}

// CheckAndDecrStock 检查并扣减库存（仅限制每日总量，不限制用户购买次数）
func (s *SeckillService) CheckAndDecrStock(ctx context.Context, productID, userID uint64) (bool, error) {
	// 使用Lua脚本原子性扣减库存（移除用户限制）
	luaScript := `
		local stock_key = KEYS[1]

		-- 检查库存
		local stock = redis.call('GET', stock_key)
		if not stock or tonumber(stock) <= 0 then
			return 0
		end

		-- 扣减库存
		local new_stock = redis.call('DECR', stock_key)
		if new_stock < 0 then
			-- 库存不足，回滚
			redis.call('INCR', stock_key)
			return 0
		end

		return 1
	`

	stockCountKey := fmt.Sprintf("seckill:product:%d:count", productID)
	result, err := s.redis.Eval(ctx, luaScript, []string{stockCountKey}).Result()
	if err != nil {
		return false, fmt.Errorf("failed to execute lua script: %w", err)
	}

	success := result.(int64) == 1
	return success, nil
}

// GetProductStock 获取商品库存信息
func (s *SeckillService) GetProductStock(ctx context.Context, productID uint64) (*ProductStock, error) {
	stockKey := fmt.Sprintf("seckill:product:%d:stock", productID)
	stockData, err := s.redis.Get(ctx, stockKey).Result()
	if err != nil {
		if err == redis.Nil {
			// Redis中没有数据，从数据库初始化
			if err := s.InitProductStock(ctx, productID); err != nil {
				return nil, err
			}
			return s.GetProductStock(ctx, productID)
		}
		return nil, fmt.Errorf("failed to get stock from redis: %w", err)
	}

	var stock ProductStock
	if err := json.Unmarshal([]byte(stockData), &stock); err != nil {
		return nil, fmt.Errorf("failed to unmarshal stock data: %w", err)
	}

	// 获取当前库存数量
	stockCountKey := fmt.Sprintf("seckill:product:%d:count", productID)
	countStr, err := s.redis.Get(ctx, stockCountKey).Result()
	if err != nil && err != redis.Nil {
		return nil, fmt.Errorf("failed to get stock count: %w", err)
	}

	if err != redis.Nil {
		count, _ := strconv.Atoi(countStr)
		stock.Stock = count
	}

	return &stock, nil
}

// CheckUserPurchased 检查用户是否已购买（已移除限制，始终返回false）
func (s *SeckillService) CheckUserPurchased(ctx context.Context, productID, userID uint64) (bool, error) {
	// 不再限制用户重复购买，始终返回false
	return false, nil
}

// RefreshProductStock 刷新商品库存（当商品信息更新时调用）
func (s *SeckillService) RefreshProductStock(ctx context.Context, productID uint64) error {
	// 删除旧的缓存
	stockKey := fmt.Sprintf("seckill:product:%d:stock", productID)
	stockCountKey := fmt.Sprintf("seckill:product:%d:count", productID)

	s.redis.Del(ctx, stockKey)
	s.redis.Del(ctx, stockCountKey)

	// 重新初始化
	return s.InitProductStock(ctx, productID)
}

// BatchInitProductStock 批量初始化商品库存
func (s *SeckillService) BatchInitProductStock(ctx context.Context) error {
	// 获取所有已审核通过且上架的商家商品
	var products []models.Product
	if err := s.db.WithContext(ctx).Where("type = ? AND status = ? AND approval_status = ?",
		models.ProductTypeMerchant, models.StatusActive, models.ApprovalStatusApproved).Find(&products).Error; err != nil {
		return fmt.Errorf("failed to get products: %w", err)
	}

	// 批量初始化
	for _, product := range products {
		if err := s.InitProductStock(ctx, product.ID); err != nil {
			// 记录错误但继续处理其他商品
			fmt.Printf("Failed to init stock for product %d: %v\n", product.ID, err)
		}
	}

	return nil
}
