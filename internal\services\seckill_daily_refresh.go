package services

import (
	"context"
	"encoding/json"
	"fmt"
	"log"
	"time"

	"wangfujing_admin/internal/models"

	"github.com/redis/go-redis/v9"
	"gorm.io/gorm"
)

// SeckillDailyRefreshService 秒杀商品每日库存刷新服务
type SeckillDailyRefreshService struct {
	db             *gorm.DB
	redis          *redis.Client
	seckillService *SeckillService
}

// NewSeckillDailyRefreshService 创建每日刷新服务
func NewSeckillDailyRefreshService(db *gorm.DB, redis *redis.Client) *SeckillDailyRefreshService {
	return &SeckillDailyRefreshService{
		db:             db,
		redis:          redis,
		seckillService: NewSeckillService(db, redis),
	}
}

// DailyRefreshStock 每日刷新库存（定时任务调用）
func (s *SeckillDailyRefreshService) DailyRefreshStock(ctx context.Context) error {
	log.Printf("Starting daily stock refresh at %s", time.Now().Format("2006-01-02 15:04:05"))

	// 获取当前日期
	now := time.Now()
	today := time.Date(now.Year(), now.Month(), now.Day(), 0, 0, 0, 0, now.Location())

	// 查询今日有效的秒杀商品
	var products []models.Product
	if err := s.db.WithContext(ctx).Where(
		"type = ? AND status = ? AND approval_status = ? AND (valid_from IS NULL OR valid_from <= ?) AND (valid_to IS NULL OR valid_to >= ?)",
		models.ProductTypeMerchant,
		models.StatusActive,
		models.ApprovalStatusApproved,
		today,
		today,
	).Find(&products).Error; err != nil {
		return fmt.Errorf("failed to get products for daily refresh: %w", err)
	}

	log.Printf("Found %d products for daily refresh", len(products))

	successCount := 0
	errorCount := 0

	// 逐个刷新商品库存
	for _, product := range products {
		if err := s.refreshProductStock(ctx, product); err != nil {
			log.Printf("Failed to refresh stock for product %d: %v", product.ID, err)
			errorCount++
		} else {
			log.Printf("Successfully refreshed stock for product %d (%s), daily_limit: %d",
				product.ID, product.Name, product.DailyLimit)
			successCount++
		}
	}

	log.Printf("Daily stock refresh completed: %d success, %d errors", successCount, errorCount)
	return nil
}

// refreshProductStock 刷新单个商品库存
func (s *SeckillDailyRefreshService) refreshProductStock(ctx context.Context, product models.Product) error {
	// 1. 计算今日已兑换数量
	today := time.Now().Format("2006-01-02")
	var usedCount int64

	// 查询今日该商品的订单数量
	if err := s.db.WithContext(ctx).Table("orders").
		Where("product_id = ? AND DATE(created_at) = ? AND status != ?",
			product.ID, today, -1). // -1表示已取消的订单
		Count(&usedCount).Error; err != nil {
		log.Printf("Warning: failed to get used count for product %d: %v", product.ID, err)
		usedCount = 0 // 如果查询失败，假设没有使用
	}

	// 2. 计算剩余库存
	remainingStock := product.DailyLimit - int(usedCount)
	if remainingStock < 0 {
		remainingStock = 0 // 确保库存不为负数
	}

	// 3. 计算Redis键的过期时间（统一过期策略）
	expiration := s.seckillService.CalculateRedisExpiration(&product)

	// 4. 设置Redis库存计数为剩余库存
	stockCountKey := fmt.Sprintf("seckill:product:%d:count", product.ID)
	if err := s.redis.Set(ctx, stockCountKey, remainingStock, expiration).Err(); err != nil {
		return fmt.Errorf("failed to reset stock count: %w", err)
	}

	log.Printf("Product %d stock refreshed: daily_limit=%d, used=%d, remaining=%d, expiration=%v",
		product.ID, product.DailyLimit, usedCount, remainingStock, expiration)

	// 5. 更新库存详情信息
	stockInfo := ProductStock{
		ProductID:   product.ID,
		Stock:       remainingStock, // 使用剩余库存而不是每日限量
		DailyLimit:  product.DailyLimit,
		UsedCount:   int(usedCount), // 添加已使用数量
		IsActive:    true,
		ValidFrom:   formatTime(product.ValidFrom),
		ValidTo:     formatTime(product.ValidTo),
		RefreshTime: time.Now().Format("2006-01-02 15:04:05"), // 添加刷新时间
	}

	stockKey := fmt.Sprintf("seckill:product:%d:stock", product.ID)
	stockData, _ := json.Marshal(stockInfo)
	if err := s.redis.Set(ctx, stockKey, stockData, expiration).Err(); err != nil {
		return fmt.Errorf("failed to update stock info: %w", err)
	}

	// 3. 清理昨日的处理结果（可选）
	s.cleanupYesterdayResults(ctx, product.ID)

	return nil
}

// cleanupYesterdayResults 清理昨日的处理结果
func (s *SeckillDailyRefreshService) cleanupYesterdayResults(ctx context.Context, productID uint64) {
	// 获取昨日的结果键模式
	yesterday := time.Now().AddDate(0, 0, -1)
	yesterdayTimestamp := yesterday.UnixNano()

	// 查找昨日的结果键（基于时间戳模式）
	pattern := fmt.Sprintf("seckill:result:%d_*_%d*", productID, yesterdayTimestamp/1000000000)
	keys, err := s.redis.Keys(ctx, pattern).Result()
	if err != nil {
		log.Printf("Failed to get yesterday result keys for product %d: %v", productID, err)
		return
	}

	// 批量删除昨日结果
	if len(keys) > 0 {
		deleted, err := s.redis.Del(ctx, keys...).Result()
		if err != nil {
			log.Printf("Failed to delete yesterday results for product %d: %v", productID, err)
		} else {
			log.Printf("Cleaned up %d yesterday results for product %d", deleted, productID)
		}
	}
}

// formatTime 格式化时间
func formatTime(t *time.Time) string {
	if t == nil {
		return ""
	}
	return t.Format("2006-01-02 15:04:05")
}

// GetRefreshStatus 获取刷新状态
func (s *SeckillDailyRefreshService) GetRefreshStatus(ctx context.Context) (map[string]interface{}, error) {
	// 获取今日已刷新的商品数量
	today := time.Now().Format("2006-01-02")
	refreshKey := fmt.Sprintf("seckill:daily_refresh:%s", today)

	refreshInfo, err := s.redis.HGetAll(ctx, refreshKey).Result()
	if err != nil {
		refreshInfo = make(map[string]string)
	}

	// 获取当前有效商品总数
	var totalProducts int64
	now := time.Now()
	todayStart := time.Date(now.Year(), now.Month(), now.Day(), 0, 0, 0, 0, now.Location())

	s.db.Model(&models.Product{}).Where(
		"type = ? AND status = ? AND approval_status = ? AND (valid_from IS NULL OR valid_from <= ?) AND (valid_to IS NULL OR valid_to >= ?)",
		models.ProductTypeMerchant,
		models.StatusActive,
		models.ApprovalStatusApproved,
		todayStart,
		todayStart,
	).Count(&totalProducts)

	return map[string]interface{}{
		"date":           today,
		"total_products": totalProducts,
		"refresh_info":   refreshInfo,
		"last_refresh":   refreshInfo["last_refresh_time"],
		"refresh_count":  refreshInfo["refresh_count"],
	}, nil
}

// MarkRefreshCompleted 标记刷新完成
func (s *SeckillDailyRefreshService) MarkRefreshCompleted(ctx context.Context, productCount int) error {
	today := time.Now().Format("2006-01-02")
	refreshKey := fmt.Sprintf("seckill:daily_refresh:%s", today)

	refreshInfo := map[string]interface{}{
		"last_refresh_time": time.Now().Format("2006-01-02 15:04:05"),
		"refresh_count":     productCount,
		"status":            "completed",
	}

	return s.redis.HMSet(ctx, refreshKey, refreshInfo).Err()
}

// ManualRefreshProduct 手动刷新指定商品库存
func (s *SeckillDailyRefreshService) ManualRefreshProduct(ctx context.Context, productID uint64) error {
	var product models.Product
	if err := s.db.WithContext(ctx).First(&product, productID).Error; err != nil {
		return fmt.Errorf("product not found: %w", err)
	}

	// 检查商品是否有效
	now := time.Now()
	if product.Status != models.StatusActive || product.ApprovalStatus != models.ApprovalStatusApproved {
		return fmt.Errorf("product is not active or not approved")
	}

	if product.ValidFrom != nil && now.Before(*product.ValidFrom) {
		return fmt.Errorf("product is not yet valid")
	}

	if product.ValidTo != nil && now.After(*product.ValidTo) {
		return fmt.Errorf("product has expired")
	}

	return s.refreshProductStock(ctx, product)
}

// StartDailyRefreshScheduler 启动每日定时刷新任务
func (s *SeckillDailyRefreshService) StartDailyRefreshScheduler(ctx context.Context) {
	log.Printf("Starting daily refresh scheduler...")

	// 计算下次执行时间（每天凌晨0点）
	now := time.Now()
	nextRun := time.Date(now.Year(), now.Month(), now.Day()+1, 0, 0, 0, 0, now.Location())

	// 如果当前时间已经过了今天的0点，则今天执行一次
	todayRun := time.Date(now.Year(), now.Month(), now.Day(), 0, 0, 0, 0, now.Location())
	if now.After(todayRun) {
		log.Printf("Current time is after today's refresh time, running refresh now...")
		go func() {
			if err := s.DailyRefreshStock(ctx); err != nil {
				log.Printf("Failed to run daily refresh: %v", err)
			}
		}()
	}

	// 启动定时器
	timer := time.NewTimer(time.Until(nextRun))

	go func() {
		for {
			select {
			case <-ctx.Done():
				timer.Stop()
				log.Printf("Daily refresh scheduler stopped")
				return
			case <-timer.C:
				// 执行每日刷新
				if err := s.DailyRefreshStock(ctx); err != nil {
					log.Printf("Daily refresh failed: %v", err)
				}

				// 设置下次执行时间（24小时后）
				timer.Reset(24 * time.Hour)
			}
		}
	}()

	log.Printf("Daily refresh scheduler started, next run at: %s", nextRun.Format("2006-01-02 15:04:05"))
}
