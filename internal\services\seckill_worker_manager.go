package services

import (
	"context"
	"encoding/json"
	"fmt"
	"log"
	"sync"
	"time"
	"wangfujing_admin/internal/config"

	"github.com/redis/go-redis/v9"
	"gorm.io/gorm"
)

// SeckillWorkerManager 秒杀队列处理器管理器
type SeckillWorkerManager struct {
	db             *gorm.DB
	redis          *redis.Client
	seckillService *HighConcurrencySeckillService
	config         *config.SeckillConfig
	workers        int
	ctx            context.Context
	cancel         context.CancelFunc
	wg             sync.WaitGroup
	isRunning      bool
	mu             sync.RWMutex
}

// NewSeckillWorkerManager 创建秒杀队列处理器管理器
func NewSeckillWorkerManager(db *gorm.DB, redis *redis.Client, cfg *config.SeckillConfig) *SeckillWorkerManager {
	workers := cfg.WorkerCount
	if workers <= 0 {
		workers = 10 // 默认10个worker
	}

	return &SeckillWorkerManager{
		db:             db,
		redis:          redis,
		seckillService: NewHighConcurrencySeckillService(db, redis, cfg),
		config:         cfg,
		workers:        workers,
	}
}

// GetSeckillService 获取秒杀服务
func (m *SeckillWorkerManager) GetSeckillService() *HighConcurrencySeckillService {
	return m.seckillService
}

// Start 启动队列处理器
func (m *SeckillWorkerManager) Start() error {
	m.mu.Lock()
	defer m.mu.Unlock()

	if m.isRunning {
		return fmt.Errorf("worker manager is already running")
	}

	m.ctx, m.cancel = context.WithCancel(context.Background())
	m.isRunning = true

	log.Printf("Starting %d seckill workers...", m.workers)

	// 启动worker goroutines
	for i := 0; i < m.workers; i++ {
		m.wg.Add(1)
		go func(workerID int) {
			defer m.wg.Done()
			m.runWorker(workerID)
		}(i)
	}

	// 启动监控goroutine
	m.wg.Add(1)
	go func() {
		defer m.wg.Done()
		m.runMonitor()
	}()

	log.Printf("Seckill workers started successfully")
	return nil
}

// Stop 停止队列处理器
func (m *SeckillWorkerManager) Stop() error {
	m.mu.Lock()
	defer m.mu.Unlock()

	if !m.isRunning {
		return fmt.Errorf("worker manager is not running")
	}

	log.Printf("Stopping seckill workers...")

	// 取消context
	m.cancel()

	// 等待所有worker停止
	done := make(chan struct{})
	go func() {
		m.wg.Wait()
		close(done)
	}()

	// 等待最多30秒
	select {
	case <-done:
		log.Printf("All seckill workers stopped")
	case <-time.After(30 * time.Second):
		log.Printf("Timeout waiting for workers to stop")
	}

	m.isRunning = false
	return nil
}

// IsRunning 检查是否正在运行
func (m *SeckillWorkerManager) IsRunning() bool {
	m.mu.RLock()
	defer m.mu.RUnlock()
	return m.isRunning
}

// runWorker 运行单个worker
func (m *SeckillWorkerManager) runWorker(workerID int) {
	log.Printf("Worker %d started", workerID)
	defer log.Printf("Worker %d stopped", workerID)

	for {
		select {
		case <-m.ctx.Done():
			return
		default:
			if err := m.processOneRequest(workerID); err != nil {
				// 处理错误，但不退出worker
				log.Printf("Worker %d error: %v", workerID, err)
				time.Sleep(1 * time.Second)
			}
		}
	}
}

// processOneRequest 处理一个请求
func (m *SeckillWorkerManager) processOneRequest(workerID int) error {
	// 获取所有商品队列
	keys, err := m.redis.Keys(m.ctx, "seckill:queue:*").Result()
	if err != nil {
		return fmt.Errorf("failed to get queue keys: %w", err)
	}

	if len(keys) == 0 {
		time.Sleep(1 * time.Second)
		return nil
	}

	// 从队列获取请求（阻塞式，超时30秒）
	result, err := m.redis.BLPop(m.ctx, 30*time.Second, keys...).Result()
	if err != nil {
		if err == redis.Nil {
			// 超时，正常情况
			return nil
		}
		return fmt.Errorf("failed to pop from queue: %w", err)
	}

	// queueName := result[0] // 队列名称，暂时不需要使用
	data := result[1]

	// 解析请求
	var req PurchaseRequest
	if err := json.Unmarshal([]byte(data), &req); err != nil {
		log.Printf("Worker %d: failed to unmarshal request: %v", workerID, err)
		return nil
	}

	log.Printf("Worker %d processing request: %s for product %d", workerID, req.RequestID, req.ProductID)

	// 处理请求
	resultKey := fmt.Sprintf("seckill:result:%s", req.RequestID)
	success, err := m.seckillService.processPurchase(m.ctx, req.ProductID, req.UserID)

	// 存储结果
	if err != nil {
		m.redis.Set(m.ctx, resultKey, "error:"+err.Error(), 10*time.Minute)
		log.Printf("Worker %d: request %s failed: %v", workerID, req.RequestID, err)
	} else if success {
		m.redis.Set(m.ctx, resultKey, "success", 24*time.Hour)
		log.Printf("Worker %d: request %s succeeded", workerID, req.RequestID)
	} else {
		m.redis.Set(m.ctx, resultKey, "sold_out", 10*time.Minute)
		log.Printf("Worker %d: request %s sold out", workerID, req.RequestID)
	}

	return nil
}

// runMonitor 运行监控
func (m *SeckillWorkerManager) runMonitor() {
	log.Printf("Monitor started")
	defer log.Printf("Monitor stopped")

	ticker := time.NewTicker(30 * time.Second)
	defer ticker.Stop()

	for {
		select {
		case <-m.ctx.Done():
			return
		case <-ticker.C:
			m.printQueueStats()
		}
	}
}

// printQueueStats 打印队列统计信息
func (m *SeckillWorkerManager) printQueueStats() {
	keys, err := m.redis.Keys(m.ctx, "seckill:queue:*").Result()
	if err != nil {
		log.Printf("Failed to get queue keys for stats: %v", err)
		return
	}

	totalQueueLength := int64(0)
	for _, key := range keys {
		length, err := m.redis.LLen(m.ctx, key).Result()
		if err != nil {
			continue
		}
		totalQueueLength += length
		if length > 0 {
			log.Printf("Queue %s length: %d", key, length)
		}
	}

	if totalQueueLength > 0 {
		log.Printf("Total queue length: %d", totalQueueLength)
	}

	// 检查是否有队列积压
	if totalQueueLength > MaxQueueSize/2 {
		log.Printf("WARNING: Queue length is high (%d), consider adding more workers", totalQueueLength)
	}
}

// GetStats 获取统计信息
func (m *SeckillWorkerManager) GetStats() map[string]interface{} {
	m.mu.RLock()
	defer m.mu.RUnlock()

	stats := map[string]interface{}{
		"is_running":     m.isRunning,
		"worker_count":   m.workers,
		"max_queue_size": MaxQueueSize,
	}

	if m.isRunning {
		// 获取队列长度
		keys, err := m.redis.Keys(context.Background(), "seckill:queue:*").Result()
		if err == nil {
			totalLength := int64(0)
			queueStats := make(map[string]int64)

			for _, key := range keys {
				length, err := m.redis.LLen(context.Background(), key).Result()
				if err == nil {
					queueStats[key] = length
					totalLength += length
				}
			}

			stats["total_queue_length"] = totalLength
			stats["queue_details"] = queueStats
		}
	}

	return stats
}

// RestartWorkers 重启workers
func (m *SeckillWorkerManager) RestartWorkers() error {
	if err := m.Stop(); err != nil {
		return fmt.Errorf("failed to stop workers: %w", err)
	}

	time.Sleep(2 * time.Second) // 等待2秒确保完全停止

	if err := m.Start(); err != nil {
		return fmt.Errorf("failed to start workers: %w", err)
	}

	return nil
}

// SetWorkerCount 设置worker数量（需要重启生效）
func (m *SeckillWorkerManager) SetWorkerCount(count int) {
	m.mu.Lock()
	defer m.mu.Unlock()

	if count > 0 {
		m.workers = count
	}
}
