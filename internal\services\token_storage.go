package services

import (
	"context"
	"fmt"
	"time"

	"github.com/redis/go-redis/v9"
)

// TokenStorageService Token存储服务
type TokenStorageService struct {
	redis *redis.Client
}

// NewTokenStorageService 创建Token存储服务
func NewTokenStorageService(rdb *redis.Client) *TokenStorageService {
	return &TokenStorageService{
		redis: rdb,
	}
}

// StoreUserToken 存储用户端Token（包括access token和refresh token）
func (s *TokenStorageService) StoreUserToken(ctx context.Context, userID, accessToken, refreshToken string, expiration time.Duration) error {
	// 存储access token
	accessKey := s.getUserTokenKey(userID)
	if err := s.redis.Set(ctx, accessKey, accessToken, expiration).Err(); err != nil {
		return err
	}

	// 存储refresh token（有效期更长）
	refreshKey := s.getUserRefreshTokenKey(userID)
	return s.redis.Set(ctx, refreshKey, refreshToken, expiration*7).Err() // refresh token有效期7倍
}

// StoreAdminToken 存储管理端Token（包括access token和refresh token）
func (s *TokenStorageService) StoreAdminToken(ctx context.Context, userID, accessToken, refreshToken string, expiration time.Duration) error {
	// 存储access token
	accessKey := s.getAdminTokenKey(userID)
	if err := s.redis.Set(ctx, accessKey, accessToken, expiration).Err(); err != nil {
		return err
	}

	// 存储refresh token（有效期更长）
	refreshKey := s.getAdminRefreshTokenKey(userID)
	return s.redis.Set(ctx, refreshKey, refreshToken, expiration*7).Err() // refresh token有效期7倍
}

// ValidateUserToken 验证用户端Token
func (s *TokenStorageService) ValidateUserToken(ctx context.Context, userID, token string) (bool, error) {
	key := s.getUserTokenKey(userID)
	storedToken, err := s.redis.Get(ctx, key).Result()
	if err != nil {
		if err == redis.Nil {
			return false, nil // Token不存在
		}
		return false, err
	}
	return storedToken == token, nil
}

// ValidateAdminToken 验证管理端Token
func (s *TokenStorageService) ValidateAdminToken(ctx context.Context, userID, token string) (bool, error) {
	key := s.getAdminTokenKey(userID)
	storedToken, err := s.redis.Get(ctx, key).Result()
	if err != nil {
		if err == redis.Nil {
			return false, nil // Token不存在
		}
		return false, err
	}
	return storedToken == token, nil
}

// ValidateUserRefreshToken 验证用户端RefreshToken
func (s *TokenStorageService) ValidateUserRefreshToken(ctx context.Context, userID, token string) (bool, error) {
	key := s.getUserRefreshTokenKey(userID)
	storedToken, err := s.redis.Get(ctx, key).Result()
	if err != nil {
		if err == redis.Nil {
			return false, nil // Token不存在
		}
		return false, err
	}
	return storedToken == token, nil
}

// DeleteUserToken 删除用户端Token
func (s *TokenStorageService) DeleteUserToken(ctx context.Context, userID string) error {
	accessKey := s.getUserTokenKey(userID)
	refreshKey := s.getUserRefreshTokenKey(userID)

	// 删除access token和refresh token
	pipe := s.redis.Pipeline()
	pipe.Del(ctx, accessKey)
	pipe.Del(ctx, refreshKey)
	_, err := pipe.Exec(ctx)
	return err
}

// ValidateAdminRefreshToken 验证管理端RefreshToken
func (s *TokenStorageService) ValidateAdminRefreshToken(ctx context.Context, userID, token string) (bool, error) {
	key := s.getAdminRefreshTokenKey(userID)
	storedToken, err := s.redis.Get(ctx, key).Result()
	if err != nil {
		if err == redis.Nil {
			return false, nil // Token不存在
		}
		return false, err
	}
	return storedToken == token, nil
}

// InvalidateUserToken 使用户端Token失效（同时删除access token和refresh token）
func (s *TokenStorageService) InvalidateUserToken(ctx context.Context, userID string) error {
	accessKey := s.getUserTokenKey(userID)
	refreshKey := s.getUserRefreshTokenKey(userID)
	return s.redis.Del(ctx, accessKey, refreshKey).Err()
}

// InvalidateAdminToken 使管理端Token失效（同时删除access token和refresh token）
func (s *TokenStorageService) InvalidateAdminToken(ctx context.Context, userID string) error {
	accessKey := s.getAdminTokenKey(userID)
	refreshKey := s.getAdminRefreshTokenKey(userID)
	return s.redis.Del(ctx, accessKey, refreshKey).Err()
}

// ExtendUserToken 延长用户端Token有效期
func (s *TokenStorageService) ExtendUserToken(ctx context.Context, userID string, expiration time.Duration) error {
	key := s.getUserTokenKey(userID)
	return s.redis.Expire(ctx, key, expiration).Err()
}

// ExtendAdminToken 延长管理端Token有效期
func (s *TokenStorageService) ExtendAdminToken(ctx context.Context, userID string, expiration time.Duration) error {
	key := s.getAdminTokenKey(userID)
	return s.redis.Expire(ctx, key, expiration).Err()
}

// IsUserTokenExists 检查用户端Token是否存在
func (s *TokenStorageService) IsUserTokenExists(ctx context.Context, userID string) (bool, error) {
	key := s.getUserTokenKey(userID)
	exists, err := s.redis.Exists(ctx, key).Result()
	return exists > 0, err
}

// IsAdminTokenExists 检查管理端Token是否存在
func (s *TokenStorageService) IsAdminTokenExists(ctx context.Context, userID string) (bool, error) {
	key := s.getAdminTokenKey(userID)
	exists, err := s.redis.Exists(ctx, key).Result()
	return exists > 0, err
}

// GetUserTokenTTL 获取用户端Token剩余时间
func (s *TokenStorageService) GetUserTokenTTL(ctx context.Context, userID string) (time.Duration, error) {
	key := s.getUserTokenKey(userID)
	return s.redis.TTL(ctx, key).Result()
}

// GetAdminTokenTTL 获取管理端Token剩余时间
func (s *TokenStorageService) GetAdminTokenTTL(ctx context.Context, userID string) (time.Duration, error) {
	key := s.getAdminTokenKey(userID)
	return s.redis.TTL(ctx, key).Result()
}

// CleanExpiredTokens 清理过期Token（可选的维护方法）
func (s *TokenStorageService) CleanExpiredTokens(ctx context.Context) error {
	// Redis会自动清理过期键，这里可以添加额外的清理逻辑
	return nil
}

// getUserTokenKey 生成用户端Token的Redis键
func (s *TokenStorageService) getUserTokenKey(userID string) string {
	return fmt.Sprintf("user_token:%s", userID)
}

// getUserRefreshTokenKey 生成用户端RefreshToken的Redis键
func (s *TokenStorageService) getUserRefreshTokenKey(userID string) string {
	return fmt.Sprintf("user_refresh_token:%s", userID)
}

// getAdminTokenKey 生成管理端Token的Redis键
func (s *TokenStorageService) getAdminTokenKey(userID string) string {
	return fmt.Sprintf("admin_token:%s", userID)
}

// getAdminRefreshTokenKey 生成管理端RefreshToken的Redis键
func (s *TokenStorageService) getAdminRefreshTokenKey(userID string) string {
	return fmt.Sprintf("admin_refresh_token:%s", userID)
}

// BatchInvalidateUserTokens 批量使用户Token失效
func (s *TokenStorageService) BatchInvalidateUserTokens(ctx context.Context, userIDs []string) error {
	if len(userIDs) == 0 {
		return nil
	}

	keys := make([]string, len(userIDs))
	for i, userID := range userIDs {
		keys[i] = s.getUserTokenKey(userID)
	}

	return s.redis.Del(ctx, keys...).Err()
}

// BatchInvalidateAdminTokens 批量使管理端Token失效
func (s *TokenStorageService) BatchInvalidateAdminTokens(ctx context.Context, userIDs []string) error {
	if len(userIDs) == 0 {
		return nil
	}

	keys := make([]string, len(userIDs))
	for i, userID := range userIDs {
		keys[i] = s.getAdminTokenKey(userID)
	}

	return s.redis.Del(ctx, keys...).Err()
}

// GetActiveUserCount 获取活跃用户数量（有Token的用户）
func (s *TokenStorageService) GetActiveUserCount(ctx context.Context) (int64, error) {
	keys, err := s.redis.Keys(ctx, "user_token:*").Result()
	if err != nil {
		return 0, err
	}
	return int64(len(keys)), nil
}

// GetActiveAdminCount 获取活跃管理员数量（有Token的管理员）
func (s *TokenStorageService) GetActiveAdminCount(ctx context.Context) (int64, error) {
	keys, err := s.redis.Keys(ctx, "admin_token:*").Result()
	if err != nil {
		return 0, err
	}
	return int64(len(keys)), nil
}
