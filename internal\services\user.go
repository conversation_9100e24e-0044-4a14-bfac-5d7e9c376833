package services

import (
	"context"
	"errors"
	"fmt"
	"strconv"
	"time"

	"wangfujing_admin/internal/models"
	"wangfujing_admin/pkg/cache"

	"golang.org/x/crypto/bcrypt"
	"gorm.io/gorm"
)

// UserService 用户服务
type UserService struct {
	db *gorm.DB
}

// NewUserService 创建用户服务
func NewUserService(db *gorm.DB) *UserService {
	return &UserService{
		db: db,
	}
}

// CreateUser 创建用户
func (s *UserService) CreateUser(ctx context.Context, user *models.User) error {
	// 检查手机号是否已存在
	var existingUser models.User
	if err := s.db.WithContext(ctx).Where("phone = ?", user.Phone).First(&existingUser).Error; err == nil {
		return errors.New("phone number already exists")
	} else if !errors.Is(err, gorm.ErrRecordNotFound) {
		return fmt.Errorf("failed to check existing user: %w", err)
	}

	// 创建用户
	if err := s.db.WithContext(ctx).Create(user).Error; err != nil {
		return fmt.Errorf("failed to create user: %w", err)
	}

	return nil
}

// GetUserByID 根据ID获取用户
func (s *UserService) GetUserByID(ctx context.Context, id string) (*models.User, error) {
	var user models.User
	if err := s.db.WithContext(ctx).Preload("Roles").First(&user, "id = ?", id).Error; err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return nil, errors.New("user not found")
		}
		return nil, fmt.Errorf("failed to get user: %w", err)
	}
	return &user, nil
}

// GetUserByPhone 根据手机号获取用户
func (s *UserService) GetUserByPhone(ctx context.Context, phone string) (*models.User, error) {
	var user models.User
	if err := s.db.WithContext(ctx).Preload("Roles").First(&user, "phone = ?", phone).Error; err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return nil, errors.New("user not found")
		}
		return nil, fmt.Errorf("failed to get user: %w", err)
	}
	return &user, nil
}

// GetUserByOpenID 根据OpenID获取用户
func (s *UserService) GetUserByOpenID(ctx context.Context, openID string) (*models.User, error) {
	var user models.User
	if err := s.db.WithContext(ctx).Preload("Roles").First(&user, "open_id = ?", openID).Error; err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return nil, errors.New("user not found")
		}
		return nil, fmt.Errorf("failed to get user: %w", err)
	}
	return &user, nil
}

// UpdateUser 更新用户
func (s *UserService) UpdateUser(ctx context.Context, id string, updates map[string]interface{}) error {
	result := s.db.WithContext(ctx).Model(&models.User{}).Where("id = ?", id).Updates(updates)
	if result.Error != nil {
		return fmt.Errorf("failed to update user: %w", result.Error)
	}
	if result.RowsAffected == 0 {
		return errors.New("user not found")
	}

	// 清除缓存
	cache.Delete(ctx, fmt.Sprintf("user:%s", id))

	return nil
}

// DeleteUser 删除用户
func (s *UserService) DeleteUser(ctx context.Context, id string) error {
	result := s.db.WithContext(ctx).Delete(&models.User{}, "id = ?", id)
	if result.Error != nil {
		return fmt.Errorf("failed to delete user: %w", result.Error)
	}
	if result.RowsAffected == 0 {
		return errors.New("user not found")
	}

	// 清除缓存
	cache.Delete(ctx, fmt.Sprintf("user:%s", id))

	return nil
}

// GetUsers 获取用户列表
func (s *UserService) GetUsers(ctx context.Context, page, size int, userType *models.UserType, status *models.Status) ([]*models.User, int64, error) {
	var users []*models.User
	var total int64

	query := s.db.WithContext(ctx).Model(&models.User{})

	// 添加过滤条件
	if userType != nil {
		query = query.Where("user_type = ?", *userType)
	}
	if status != nil {
		query = query.Where("status = ?", *status)
	}

	// 获取总数
	if err := query.Count(&total).Error; err != nil {
		return nil, 0, fmt.Errorf("failed to count users: %w", err)
	}

	// 获取分页数据
	offset := (page - 1) * size
	if err := query.Preload("Roles").Offset(offset).Limit(size).Order("created_at DESC").Find(&users).Error; err != nil {
		return nil, 0, fmt.Errorf("failed to get users: %w", err)
	}

	return users, total, nil
}

// UpdateUserStatus 更新用户状态
func (s *UserService) UpdateUserStatus(ctx context.Context, id string, status models.Status) error {
	return s.UpdateUser(ctx, id, map[string]interface{}{
		"status": status,
	})
}

// UpdateLastLogin 更新最后登录时间
func (s *UserService) UpdateLastLogin(ctx context.Context, id string) error {
	now := time.Now()
	return s.UpdateUser(ctx, id, map[string]interface{}{
		"last_login_at": &now,
	})
}

// AssignRoles 分配角色
func (s *UserService) AssignRoles(ctx context.Context, userID string, roleIDs []string) error {
	// 开始事务
	tx := s.db.WithContext(ctx).Begin()
	defer func() {
		if r := recover(); r != nil {
			tx.Rollback()
		}
	}()

	// 删除现有角色关联
	if err := tx.Where("user_id = ?", userID).Delete(&models.UserRole{}).Error; err != nil {
		tx.Rollback()
		return fmt.Errorf("failed to delete existing user roles: %w", err)
	}

	// 添加新的角色关联
	for _, roleID := range roleIDs {
		// 转换字符串ID为uint64
		userIDUint, err := strconv.ParseUint(userID, 10, 64)
		if err != nil {
			return fmt.Errorf("invalid user ID: %w", err)
		}
		roleIDUint, err := strconv.ParseUint(roleID, 10, 64)
		if err != nil {
			return fmt.Errorf("invalid role ID: %w", err)
		}

		userRole := &models.UserRole{
			UserID: userIDUint,
			RoleID: roleIDUint,
		}
		if err := tx.Create(userRole).Error; err != nil {
			tx.Rollback()
			return fmt.Errorf("failed to create user role: %w", err)
		}
	}

	if err := tx.Commit().Error; err != nil {
		return fmt.Errorf("failed to commit transaction: %w", err)
	}

	// 清除缓存
	cache.Delete(ctx, fmt.Sprintf("user:%s", userID))

	return nil
}

// GetUserRoles 获取用户角色
func (s *UserService) GetUserRoles(ctx context.Context, userID string) ([]*models.Role, error) {
	var roles []*models.Role

	if err := s.db.WithContext(ctx).
		Table("roles").
		Joins("JOIN user_roles ON roles.id = user_roles.role_id").
		Where("user_roles.user_id = ? AND roles.status = ?", userID, models.StatusActive).
		Find(&roles).Error; err != nil {
		return nil, fmt.Errorf("failed to get user roles: %w", err)
	}

	return roles, nil
}

// AddPoints 增加积分
func (s *UserService) AddPoints(ctx context.Context, userID string, points int, source, description string, relatedID, relatedType string) error {
	// 开始事务
	tx := s.db.WithContext(ctx).Begin()
	defer func() {
		if r := recover(); r != nil {
			tx.Rollback()
		}
	}()

	// 获取用户当前积分
	var user models.User
	if err := tx.First(&user, "id = ?", userID).Error; err != nil {
		tx.Rollback()
		return fmt.Errorf("failed to get user: %w", err)
	}

	// 更新用户积分
	newBalance := user.Points + points
	if err := tx.Model(&user).Update("points", newBalance).Error; err != nil {
		tx.Rollback()
		return fmt.Errorf("failed to update user points: %w", err)
	}

	// 转换用户ID
	userIDUint, err := strconv.ParseUint(userID, 10, 64)
	if err != nil {
		tx.Rollback()
		return fmt.Errorf("invalid user ID: %w", err)
	}

	// 转换关联ID
	var relatedIDUint *uint64
	if relatedID != "" {
		relatedIDUintVal, err := strconv.ParseUint(relatedID, 10, 64)
		if err != nil {
			tx.Rollback()
			return fmt.Errorf("invalid related ID: %w", err)
		}
		relatedIDUint = &relatedIDUintVal
	}

	// 创建积分记录
	record := &models.PointsRecord{
		UserID:      userIDUint,
		Type:        models.PointsTypeEarn,
		Points:      points,
		Balance:     newBalance,
		Source:      source,
		Description: description,
		RelatedID:   relatedIDUint,
		RelatedType: relatedType,
	}
	if err := tx.Create(record).Error; err != nil {
		tx.Rollback()
		return fmt.Errorf("failed to create points record: %w", err)
	}

	if err := tx.Commit().Error; err != nil {
		return fmt.Errorf("failed to commit transaction: %w", err)
	}

	// 清除缓存
	cache.Delete(ctx, fmt.Sprintf("user:%s", userID))

	return nil
}

// SpendPoints 消费积分
func (s *UserService) SpendPoints(ctx context.Context, userID string, points int, source, description string, relatedID, relatedType string) error {
	// 开始事务
	tx := s.db.WithContext(ctx).Begin()
	defer func() {
		if r := recover(); r != nil {
			tx.Rollback()
		}
	}()

	// 获取用户当前积分
	var user models.User
	if err := tx.First(&user, "id = ?", userID).Error; err != nil {
		tx.Rollback()
		return fmt.Errorf("failed to get user: %w", err)
	}

	// 检查积分是否足够
	if user.Points < points {
		tx.Rollback()
		return errors.New("insufficient points")
	}

	// 更新用户积分
	newBalance := user.Points - points
	if err := tx.Model(&user).Update("points", newBalance).Error; err != nil {
		tx.Rollback()
		return fmt.Errorf("failed to update user points: %w", err)
	}

	// 转换用户ID
	userIDUint, err := strconv.ParseUint(userID, 10, 64)
	if err != nil {
		tx.Rollback()
		return fmt.Errorf("invalid user ID: %w", err)
	}

	// 转换关联ID
	var relatedIDUint *uint64
	if relatedID != "" {
		relatedIDUintVal, err := strconv.ParseUint(relatedID, 10, 64)
		if err != nil {
			tx.Rollback()
			return fmt.Errorf("invalid related ID: %w", err)
		}
		relatedIDUint = &relatedIDUintVal
	}

	// 创建积分记录
	record := &models.PointsRecord{
		UserID:      userIDUint,
		Type:        models.PointsTypeSpend,
		Points:      points,
		Balance:     newBalance,
		Source:      source,
		Description: description,
		RelatedID:   relatedIDUint,
		RelatedType: relatedType,
	}
	if err := tx.Create(record).Error; err != nil {
		tx.Rollback()
		return fmt.Errorf("failed to create points record: %w", err)
	}

	if err := tx.Commit().Error; err != nil {
		return fmt.Errorf("failed to commit transaction: %w", err)
	}

	// 清除缓存
	cache.Delete(ctx, fmt.Sprintf("user:%s", userID))

	return nil
}

// GetPointsRecords 获取积分记录
func (s *UserService) GetPointsRecords(ctx context.Context, userID string, page, size int) ([]*models.PointsRecord, int64, error) {
	var records []*models.PointsRecord
	var total int64

	query := s.db.WithContext(ctx).Model(&models.PointsRecord{}).Where("user_id = ?", userID)

	// 获取总数
	if err := query.Count(&total).Error; err != nil {
		return nil, 0, fmt.Errorf("failed to count points records: %w", err)
	}

	// 获取分页数据
	offset := (page - 1) * size
	if err := query.Offset(offset).Limit(size).Order("created_at DESC").Find(&records).Error; err != nil {
		return nil, 0, fmt.Errorf("failed to get points records: %w", err)
	}

	return records, total, nil
}

// HashPassword 哈希密码
func HashPassword(password string) (string, error) {
	bytes, err := bcrypt.GenerateFromPassword([]byte(password), bcrypt.DefaultCost)
	return string(bytes), err
}

// CheckPassword 检查密码
func CheckPassword(password, hash string) bool {
	err := bcrypt.CompareHashAndPassword([]byte(hash), []byte(password))
	return err == nil
}
