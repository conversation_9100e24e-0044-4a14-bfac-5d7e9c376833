package services

import (
	"context"
	"fmt"
	"time"
	"wangfujing_admin/internal/models"

	"gorm.io/gorm"
)

// UserManagementService 用户管理服务
type UserManagementService struct {
	db *gorm.DB
}

// NewUserManagementService 创建用户管理服务
func NewUserManagementService(db *gorm.DB) *UserManagementService {
	return &UserManagementService{
		db: db,
	}
}

// GetUsersByDateRange 根据注册时间范围获取用户列表
func (s *UserManagementService) GetUsersByDateRange(ctx context.Context, startDate, endDate time.Time, page, size int) ([]*models.User, int64, error) {
	var users []*models.User
	var total int64

	// 构建查询条件
	query := s.db.WithContext(ctx).Model(&models.User{})

	// 时间范围筛选
	query = query.Where("DATE(register_date) >= ? AND DATE(register_date) <= ?",
		startDate.Format("2006-01-02"),
		endDate.Format("2006-01-02"))

	// 统计总数
	if err := query.Count(&total).Error; err != nil {
		return nil, 0, fmt.Errorf("failed to count users: %w", err)
	}

	// 分页查询
	offset := (page - 1) * size
	if err := query.Offset(offset).Limit(size).
		Order("register_date DESC").
		Find(&users).Error; err != nil {
		return nil, 0, fmt.Errorf("failed to get users: %w", err)
	}

	return users, total, nil
}

// GetUserByID 根据ID获取用户详情
func (s *UserManagementService) GetUserByID(ctx context.Context, userID uint64) (*models.User, error) {
	var user models.User
	if err := s.db.WithContext(ctx).First(&user, userID).Error; err != nil {
		if err == gorm.ErrRecordNotFound {
			return nil, fmt.Errorf("user not found")
		}
		return nil, fmt.Errorf("failed to get user: %w", err)
	}
	return &user, nil
}

// GetUserPointsRecords 获取用户积分明细
func (s *UserManagementService) GetUserPointsRecords(ctx context.Context, userID uint64, year, month int, page, size int) ([]*models.PointsRecord, int64, error) {
	var records []*models.PointsRecord
	var total int64

	// 构建查询条件
	query := s.db.WithContext(ctx).Model(&models.PointsRecord{}).Where("user_id = ?", userID)

	// 如果指定了年月，则按月筛选
	if year > 0 && month > 0 {
		startDate := time.Date(year, time.Month(month), 1, 0, 0, 0, 0, time.Local)
		endDate := startDate.AddDate(0, 1, 0).Add(-time.Second)
		query = query.Where("created_at >= ? AND created_at <= ?", startDate, endDate)
	}

	// 统计总数
	if err := query.Count(&total).Error; err != nil {
		return nil, 0, fmt.Errorf("failed to count points records: %w", err)
	}

	// 分页查询，按时间倒序
	offset := (page - 1) * size
	if err := query.Offset(offset).Limit(size).
		Order("created_at DESC").
		Find(&records).Error; err != nil {
		return nil, 0, fmt.Errorf("failed to get points records: %w", err)
	}

	return records, total, nil
}

// GetUserPointsRecordsByMonth 获取用户指定月份的积分明细
func (s *UserManagementService) GetUserPointsRecordsByMonth(ctx context.Context, userID uint64, year, month int) ([]*models.PointsRecord, error) {
	var records []*models.PointsRecord

	// 构建时间范围
	startDate := time.Date(year, time.Month(month), 1, 0, 0, 0, 0, time.Local)
	endDate := startDate.AddDate(0, 1, 0).Add(-time.Second)

	// 查询指定月份的积分记录
	if err := s.db.WithContext(ctx).
		Where("user_id = ? AND created_at >= ? AND created_at <= ?", userID, startDate, endDate).
		Order("created_at DESC").
		Find(&records).Error; err != nil {
		return nil, fmt.Errorf("failed to get points records: %w", err)
	}

	return records, nil
}

// GetUserPointsSummary 获取用户积分汇总信息
func (s *UserManagementService) GetUserPointsSummary(ctx context.Context, userID uint64) (map[string]interface{}, error) {
	summary := make(map[string]interface{})

	// 获取用户基本信息
	user, err := s.GetUserByID(ctx, userID)
	if err != nil {
		return nil, err
	}

	// 总获得积分
	var totalEarned int64
	if err := s.db.WithContext(ctx).Model(&models.PointsRecord{}).
		Where("user_id = ? AND type = ?", userID, models.PointsTypeEarn).
		Select("COALESCE(SUM(points), 0)").
		Scan(&totalEarned).Error; err != nil {
		return nil, fmt.Errorf("failed to get total earned points: %w", err)
	}

	// 总消费积分
	var totalSpent int64
	if err := s.db.WithContext(ctx).Model(&models.PointsRecord{}).
		Where("user_id = ? AND type = ?", userID, models.PointsTypeSpend).
		Select("COALESCE(SUM(points), 0)").
		Scan(&totalSpent).Error; err != nil {
		return nil, fmt.Errorf("failed to get total spent points: %w", err)
	}

	// 本月获得积分
	now := time.Now()
	startOfMonth := time.Date(now.Year(), now.Month(), 1, 0, 0, 0, 0, time.Local)
	endOfMonth := startOfMonth.AddDate(0, 1, 0).Add(-time.Second)

	var monthlyEarned int64
	if err := s.db.WithContext(ctx).Model(&models.PointsRecord{}).
		Where("user_id = ? AND type = ? AND created_at >= ? AND created_at <= ?",
			userID, models.PointsTypeEarn, startOfMonth, endOfMonth).
		Select("COALESCE(SUM(points), 0)").
		Scan(&monthlyEarned).Error; err != nil {
		return nil, fmt.Errorf("failed to get monthly earned points: %w", err)
	}

	summary["user_id"] = user.ID
	summary["nickname"] = user.Nickname
	summary["phone"] = user.Phone
	summary["current_points"] = user.Points
	summary["total_earned"] = totalEarned
	summary["total_spent"] = totalSpent
	summary["monthly_earned"] = monthlyEarned
	summary["register_date"] = user.RegisterDate.Format("2006-01-02")

	return summary, nil
}

// SearchUsers 搜索用户（按手机号或昵称）
func (s *UserManagementService) SearchUsers(ctx context.Context, keyword string, page, size int) ([]*models.User, int64, error) {
	var users []*models.User
	var total int64

	// 构建搜索查询
	query := s.db.WithContext(ctx).Model(&models.User{})
	if keyword != "" {
		query = query.Where("phone LIKE ? OR nickname LIKE ?", "%"+keyword+"%", "%"+keyword+"%")
	}

	// 统计总数
	if err := query.Count(&total).Error; err != nil {
		return nil, 0, fmt.Errorf("failed to count users: %w", err)
	}

	// 分页查询
	offset := (page - 1) * size
	if err := query.Offset(offset).Limit(size).
		Order("register_date DESC").
		Find(&users).Error; err != nil {
		return nil, 0, fmt.Errorf("failed to search users: %w", err)
	}

	return users, total, nil
}
