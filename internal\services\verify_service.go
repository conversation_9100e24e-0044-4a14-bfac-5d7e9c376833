package services

import (
	"context"
	"fmt"
	"time"
	"wangfujing_admin/internal/models"
	"wangfujing_admin/pkg/security"

	"gorm.io/gorm"
)

// VerifyService 核销服务
type VerifyService struct {
	db         *gorm.DB
	hmacSigner *security.HMACSigner
}

// NewVerifyService 创建核销服务
func NewVerifyService(db *gorm.DB) *VerifyService {
	// 创建HMAC签名器
	hmacConfig := security.GetDefaultConfig()
	hmacSigner := security.NewHMACSigner(hmacConfig)

	return &VerifyService{
		db:         db,
		hmacSigner: hmacSigner,
	}
}

// VerifyQRCode 验证二维码
func (s *VerifyService) VerifyQRCode(ctx context.Context, qrContent string, verifierID uint64, remark string) (*models.Order, error) {
	// 解码二维码内容
	qrData, err := s.hmacSigner.DecodeQRCodeData(qrContent)
	if err != nil {
		return nil, fmt.Errorf("解码二维码失败: %w", err)
	}

	// 验证二维码数据
	if err := s.hmacSigner.ValidateQRCodeData(qrData); err != nil {
		return nil, fmt.Errorf("验证二维码失败: %w", err)
	}

	// 查询订单
	var order models.Order
	if err := s.db.WithContext(ctx).Where("id = ? AND verify_code = ?", qrData.OrderID, qrData.VerifyCode).First(&order).Error; err != nil {
		return nil, fmt.Errorf("查询订单失败: %w", err)
	}

	// 检查订单状态
	if order.Status != models.OrderStatusPending {
		return nil, fmt.Errorf("订单状态不正确，无法核销")
	}

	// 检查是否已核销
	if order.VerifiedAt != nil {
		return nil, fmt.Errorf("订单已核销")
	}

	// 开始事务
	var updatedOrder models.Order
	err = s.db.WithContext(ctx).Transaction(func(tx *gorm.DB) error {
		// 更新订单状态
		now := time.Now()
		updates := map[string]interface{}{
			"status":        models.OrderStatusCompleted,
			"verified_at":   now,
			"verifier_id":   verifierID,
			"verify_remark": remark,
			"completed_at":  now,
		}

		if err := tx.Model(&order).Updates(updates).Error; err != nil {
			return fmt.Errorf("更新订单状态失败: %w", err)
		}

		// 创建核销记录（使用扩展字段）
		verifyRecord := &models.VerifyRecord{
			OrderID:          order.ID,
			UserID:           order.UserID,
			ScannerID:        verifierID,
			Location:         "商家",
			Remark:           remark,
			VerifyType:       models.VerifyRecordTypeMerchant,
			Points:           order.Points,
			PointsChangeType: models.PointsTypeSpend, // 商家核销是消费积分
			Price:            order.TotalPrice,
			ProductID:        &order.ProductID,
			MerchantID:       order.MerchantID,
			VerifyCode:       order.VerifyCode,
			VerifyMethod:     "qr_code",
		}

		if err := tx.Create(verifyRecord).Error; err != nil {
			return fmt.Errorf("创建核销记录失败: %w", err)
		}

		// 更新对应的积分记录，关联核销记录ID
		if err := tx.Model(&models.PointsRecord{}).
			Where("related_id = ? AND related_type = ? AND source = ?", order.ID, "order", "商品兑换").
			Update("verification_id", verifyRecord.ID).Error; err != nil {
			return fmt.Errorf("更新积分记录失败: %w", err)
		}

		// 重新查询订单以获取最新状态
		if err := tx.First(&updatedOrder, order.ID).Error; err != nil {
			return fmt.Errorf("查询更新后的订单失败: %w", err)
		}

		return nil
	})

	if err != nil {
		return nil, err
	}

	return &updatedOrder, nil
}

// VerifyByOrderID 通过订单ID核销
func (s *VerifyService) VerifyByOrderID(ctx context.Context, orderID uint64, verifierID uint64, remark string) (*models.Order, error) {
	// 查询订单
	var order models.Order
	if err := s.db.WithContext(ctx).Where("id = ?", orderID).First(&order).Error; err != nil {
		return nil, fmt.Errorf("查询订单失败: %w", err)
	}

	// 检查订单状态
	if order.Status != models.OrderStatusPending {
		return nil, fmt.Errorf("订单状态不正确，无法核销")
	}

	// 检查是否已核销
	if order.VerifiedAt != nil {
		return nil, fmt.Errorf("订单已核销")
	}

	// 开始事务
	err := s.db.WithContext(ctx).Transaction(func(tx *gorm.DB) error {
		// 更新订单状态
		now := time.Now()
		updates := map[string]interface{}{
			"status":        models.OrderStatusCompleted,
			"verified_at":   now,
			"verifier_id":   verifierID,
			"verify_remark": remark,
			"completed_at":  now,
		}

		if err := tx.Model(&order).Updates(updates).Error; err != nil {
			return fmt.Errorf("更新订单状态失败: %w", err)
		}

		// 创建核销记录（使用扩展字段）
		verifyRecord := &models.VerifyRecord{
			OrderID:          order.ID,
			UserID:           order.UserID,
			ScannerID:        verifierID,
			Location:         "商家",
			Remark:           remark,
			VerifyType:       models.VerifyRecordTypeMerchant,
			Points:           order.Points,
			PointsChangeType: models.PointsTypeSpend, // 商家核销是消费积分
			Price:            order.TotalPrice,
			ProductID:        &order.ProductID,
			MerchantID:       order.MerchantID,
			VerifyCode:       order.VerifyCode,
			VerifyMethod:     "qr_code",
		}

		if err := tx.Create(verifyRecord).Error; err != nil {
			return fmt.Errorf("创建核销记录失败: %w", err)
		}

		// 更新对应的积分记录，关联核销记录ID
		if err := tx.Model(&models.PointsRecord{}).
			Where("related_id = ? AND related_type = ? AND source = ?", order.ID, "order", "商品兑换").
			Update("verification_id", verifyRecord.ID).Error; err != nil {
			return fmt.Errorf("更新积分记录失败: %w", err)
		}

		// 重新查询订单以获取最新状态
		if err := tx.First(&order, order.ID).Error; err != nil {
			return fmt.Errorf("查询更新后的订单失败: %w", err)
		}

		return nil
	})

	if err != nil {
		return nil, err
	}

	return &order, nil
}
