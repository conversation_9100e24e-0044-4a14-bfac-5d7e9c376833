package services

import (
	"context"
	"time"

	"github.com/robfig/cron/v3"
	"gorm.io/gorm"

	"wangfujing_admin/internal/models"
	"wangfujing_admin/pkg/logger"
)

// VipAvatarExpiryService VIP形象过期处理服务
type VipAvatarExpiryService struct {
	db   *gorm.DB
	cron *cron.Cron
}

// NewVipAvatarExpiryService 创建VIP形象过期处理服务
func NewVipAvatarExpiryService(db *gorm.DB) *VipAvatarExpiryService {
	// 创建cron实例，使用Asia/Shanghai时区
	location, err := time.LoadLocation("Asia/Shanghai")
	if err != nil {
		logger.Error("Failed to load timezone", logger.Err(err))
		location = time.UTC
	}

	c := cron.New(cron.WithLocation(location))

	return &VipAvatarExpiryService{
		db:   db,
		cron: c,
	}
}

// StartExpiryProcessor 启动VIP形象过期处理器
func (s *VipAvatarExpiryService) StartExpiryProcessor(ctx context.Context) {
	// 添加定时任务：每天0点执行
	_, err := s.cron.AddFunc("0 0 * * *", func() {
		s.processExpiredAvatars()
	})
	if err != nil {
		logger.Error("Failed to add cron job for VIP avatar expiry", logger.Err(err))
		return
	}

	// 启动cron调度器
	s.cron.Start()
	logger.Info("VIP avatar expiry processor started (runs daily at 00:00)")

	// 监听上下文取消信号
	<-ctx.Done()
	s.Stop()
}

// Stop 停止过期处理器
func (s *VipAvatarExpiryService) Stop() {
	if s.cron != nil {
		s.cron.Stop()
		logger.Info("VIP avatar expiry processor stopped")
	}
}

// processExpiredAvatars 处理过期的VIP形象
func (s *VipAvatarExpiryService) processExpiredAvatars() {
	logger.Info("Starting VIP avatar expiry processing...")

	// 获取当前时间（Asia/Shanghai时区）
	now := time.Now()
	today := time.Date(now.Year(), now.Month(), now.Day(), 0, 0, 0, 0, now.Location())

	// 开始事务
	tx := s.db.Begin()
	defer func() {
		if r := recover(); r != nil {
			tx.Rollback()
			logger.Error("VIP avatar expiry processing failed with panic", logger.Any("panic", r))
		}
	}()

	// 统计处理前的状态
	var beforeStats struct {
		Redeemed    int64 // 已兑换
		InUse       int64 // 正在使用
		Expired     int64 // 已过期
		Deactivated int64 // 已停用
	}

	tx.Model(&models.PointsMallExchange{}).
		Where("lottery_win = ?", true).
		Where("status = ?", models.ExchangeStatusRedeemed).
		Count(&beforeStats.Redeemed)

	tx.Model(&models.PointsMallExchange{}).
		Where("lottery_win = ?", true).
		Where("status = ?", models.ExchangeStatusInUse).
		Count(&beforeStats.InUse)

	tx.Model(&models.PointsMallExchange{}).
		Where("lottery_win = ?", true).
		Where("status = ?", models.ExchangeStatusExpired).
		Count(&beforeStats.Expired)

	tx.Model(&models.PointsMallExchange{}).
		Where("lottery_win = ?", true).
		Where("status = ?", models.ExchangeStatusDeactivated).
		Count(&beforeStats.Deactivated)

	logger.Info("VIP avatar status before processing",
		logger.Int64("redeemed", beforeStats.Redeemed),
		logger.Int64("in_use", beforeStats.InUse),
		logger.Int64("expired", beforeStats.Expired),
		logger.Int64("deactivated", beforeStats.Deactivated))

	// 1. 将0点前兑换的"已兑换"状态记录设为"已过期"
	result1 := tx.Model(&models.PointsMallExchange{}).
		Where("status = ?", models.ExchangeStatusRedeemed).
		Where("DATE(exchange_time) < ?", today.Format("2006-01-02")).
		Where("lottery_win = ?", true).
		Updates(map[string]interface{}{
			"status":     models.ExchangeStatusExpired,
			"updated_at": now,
		})

	if result1.Error != nil {
		tx.Rollback()
		logger.Error("Failed to expire redeemed avatars", logger.Err(result1.Error))
		return
	}

	// 2. 将0点前兑换或应用的"正在使用"状态记录设为"已停用"（因为已经应用过了）
	result2 := tx.Model(&models.PointsMallExchange{}).
		Where("status = ?", models.ExchangeStatusInUse).
		Where("(DATE(exchange_time) < ? OR (applied_at IS NOT NULL AND DATE(applied_at) < ?))",
			today.Format("2006-01-02"), today.Format("2006-01-02")).
		Where("lottery_win = ?", true).
		Updates(map[string]interface{}{
			"status":     models.ExchangeStatusDeactivated, // 已停用，因为已经应用过
			"updated_at": now,
		})

	if result2.Error != nil {
		tx.Rollback()
		logger.Error("Failed to expire in-use avatars", logger.Err(result2.Error))
		return
	}

	// 提交事务
	if err := tx.Commit().Error; err != nil {
		logger.Error("Failed to commit VIP avatar expiry transaction", logger.Err(err))
		return
	}

	// 统计处理后的状态
	var afterStats struct {
		Redeemed    int64
		InUse       int64
		Expired     int64
		Deactivated int64
	}

	s.db.Model(&models.PointsMallExchange{}).
		Where("lottery_win = ?", true).
		Where("status = ?", models.ExchangeStatusRedeemed).
		Count(&afterStats.Redeemed)

	s.db.Model(&models.PointsMallExchange{}).
		Where("lottery_win = ?", true).
		Where("status = ?", models.ExchangeStatusInUse).
		Count(&afterStats.InUse)

	s.db.Model(&models.PointsMallExchange{}).
		Where("lottery_win = ?", true).
		Where("status = ?", models.ExchangeStatusExpired).
		Count(&afterStats.Expired)

	s.db.Model(&models.PointsMallExchange{}).
		Where("lottery_win = ?", true).
		Where("status = ?", models.ExchangeStatusDeactivated).
		Count(&afterStats.Deactivated)

	logger.Info("VIP avatar expiry processing completed",
		logger.Int64("expired_redeemed_count", result1.RowsAffected),
		logger.Int64("deactivated_in_use_count", result2.RowsAffected),
		logger.Int64("total_processed", result1.RowsAffected+result2.RowsAffected))

	logger.Info("VIP avatar status after processing",
		logger.Int64("redeemed", afterStats.Redeemed),
		logger.Int64("in_use", afterStats.InUse),
		logger.Int64("expired", afterStats.Expired),
		logger.Int64("deactivated", afterStats.Deactivated))

	// 记录状态变化
	logger.Info("VIP avatar status changes",
		logger.Int64("redeemed_change", afterStats.Redeemed-beforeStats.Redeemed),
		logger.Int64("in_use_change", afterStats.InUse-beforeStats.InUse),
		logger.Int64("expired_change", afterStats.Expired-beforeStats.Expired),
		logger.Int64("deactivated_change", afterStats.Deactivated-beforeStats.Deactivated))
}

// ProcessExpiredAvatarsManually 手动触发过期处理（用于测试）
func (s *VipAvatarExpiryService) ProcessExpiredAvatarsManually() {
	logger.Info("Manual VIP avatar expiry processing triggered")
	s.processExpiredAvatars()
}

// GetNextRunTime 获取下次运行时间
func (s *VipAvatarExpiryService) GetNextRunTime() time.Time {
	if s.cron != nil && len(s.cron.Entries()) > 0 {
		return s.cron.Entries()[0].Next
	}
	return time.Time{}
}
