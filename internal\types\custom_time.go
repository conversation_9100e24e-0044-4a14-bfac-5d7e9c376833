package types

import (
	"database/sql/driver"
	"fmt"
	"strings"
	"time"
)

// CustomDateTime 自定义时间类型，支持 "2024-01-15 12:00" 格式
type CustomDateTime struct {
	time.Time
}

const CustomDateTimeFormat = "2006-01-02 15:04"

// UnmarshalJSON 实现 JSON 反序列化
func (ct *CustomDateTime) UnmarshalJSON(data []byte) error {
	// 去掉引号
	str := strings.Trim(string(data), `"`)
	if str == "null" || str == "" {
		return nil
	}

	// 解析时间
	t, err := time.Parse(CustomDateTimeFormat, str)
	if err != nil {
		return fmt.Errorf("时间格式错误，请使用格式：2024-01-15 12:00")
	}

	ct.Time = t
	return nil
}

// MarshalJSON 实现 JSON 序列化
func (ct CustomDateTime) MarshalJSON() ([]byte, error) {
	if ct.Time.IsZero() {
		return []byte("null"), nil
	}
	return []byte(`"` + ct.Time.Format(CustomDateTimeFormat) + `"`), nil
}

// Value 实现 driver.Valuer 接口，用于数据库存储
func (ct CustomDateTime) Value() (driver.Value, error) {
	if ct.Time.IsZero() {
		return nil, nil
	}
	return ct.Time, nil
}

// Scan 实现 sql.Scanner 接口，用于数据库读取
func (ct *CustomDateTime) Scan(value interface{}) error {
	if value == nil {
		ct.Time = time.Time{}
		return nil
	}

	switch v := value.(type) {
	case time.Time:
		ct.Time = v
		return nil
	case string:
		t, err := time.Parse(time.RFC3339, v)
		if err != nil {
			t, err = time.Parse("2006-01-02 15:04", v)
			if err != nil {
				// 尝试带秒的格式（兼容旧数据）
				t, err = time.Parse("2006-01-02 15:04:05", v)
				if err != nil {
					return err
				}
			}
		}
		ct.Time = t
		return nil
	default:
		return fmt.Errorf("cannot scan %T into CustomDateTime", value)
	}
}

// String 返回格式化的时间字符串
func (ct CustomDateTime) String() string {
	if ct.Time.IsZero() {
		return ""
	}
	return ct.Time.Format(CustomDateTimeFormat)
}

// IsZero 检查时间是否为零值
func (ct CustomDateTime) IsZero() bool {
	return ct.Time.IsZero()
}

// Before 检查时间是否早于另一个时间
func (ct CustomDateTime) Before(t time.Time) bool {
	return ct.Time.Before(t)
}

// After 检查时间是否晚于另一个时间
func (ct CustomDateTime) After(t time.Time) bool {
	return ct.Time.After(t)
}

// ToTime 转换为标准 time.Time
func (ct CustomDateTime) ToTime() time.Time {
	return ct.Time
}

// StandardDateTime 标准时间类型，支持 "2025-07-10 12:00:00" 格式
type StandardDateTime struct {
	time.Time
}

const StandardDateTimeFormat = "2006-01-02 15:04:05"

// UnmarshalJSON 实现 JSON 反序列化
func (st *StandardDateTime) UnmarshalJSON(data []byte) error {
	// 去掉引号
	str := strings.Trim(string(data), `"`)
	if str == "null" || str == "" {
		return nil
	}

	// 解析时间
	t, err := time.Parse(StandardDateTimeFormat, str)
	if err != nil {
		return fmt.Errorf("时间格式错误，请使用格式：2025-07-10 12:00:00")
	}

	st.Time = t
	return nil
}

// MarshalJSON 实现 JSON 序列化
func (st StandardDateTime) MarshalJSON() ([]byte, error) {
	if st.Time.IsZero() {
		return []byte("null"), nil
	}
	return []byte(`"` + st.Time.Format(StandardDateTimeFormat) + `"`), nil
}

// Value 实现 driver.Valuer 接口，用于数据库存储
func (st StandardDateTime) Value() (driver.Value, error) {
	if st.Time.IsZero() {
		return nil, nil
	}
	return st.Time, nil
}

// Scan 实现 sql.Scanner 接口，用于数据库读取
func (st *StandardDateTime) Scan(value interface{}) error {
	if value == nil {
		st.Time = time.Time{}
		return nil
	}

	switch v := value.(type) {
	case time.Time:
		st.Time = v
		return nil
	case string:
		t, err := time.Parse(time.RFC3339, v)
		if err != nil {
			t, err = time.Parse(StandardDateTimeFormat, v)
			if err != nil {
				// 尝试不带秒的格式（兼容）
				t, err = time.Parse("2006-01-02 15:04", v)
				if err != nil {
					return err
				}
			}
		}
		st.Time = t
		return nil
	default:
		return fmt.Errorf("cannot scan %T into StandardDateTime", value)
	}
}

// String 返回格式化的时间字符串
func (st StandardDateTime) String() string {
	if st.Time.IsZero() {
		return ""
	}
	return st.Time.Format(StandardDateTimeFormat)
}
