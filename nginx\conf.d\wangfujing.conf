# 小程序API服务
upstream wangfujing_api {
    server app:8888;
}

# 管理端服务
upstream wangfujing_admin {
    server app:8889;
}

# 小程序API域名配置
server {
    listen 80;
    server_name api.wangfujing.com;
    
    # 重定向到HTTPS
    return 301 https://$server_name$request_uri;
}

server {
    listen 443 ssl http2;
    server_name api.wangfujing.com;
    
    # SSL证书配置
    ssl_certificate /etc/nginx/ssl/api.wangfujing.com.crt;
    ssl_certificate_key /etc/nginx/ssl/api.wangfujing.com.key;
    ssl_session_timeout 1d;
    ssl_session_cache shared:SSL:50m;
    ssl_session_tickets off;
    
    # SSL安全配置
    ssl_protocols TLSv1.2 TLSv1.3;
    ssl_ciphers ECDHE-RSA-AES128-GCM-SHA256:ECDHE-RSA-AES256-GCM-SHA384;
    ssl_prefer_server_ciphers off;
    
    # 安全头
    add_header Strict-Transport-Security "max-age=63072000" always;
    add_header X-Frame-Options DENY;
    add_header X-Content-Type-Options nosniff;
    add_header X-XSS-Protection "1; mode=block";
    
    # 日志配置
    access_log /var/log/nginx/api.access.log;
    error_log /var/log/nginx/api.error.log;
    
    # 客户端配置
    client_max_body_size 10M;
    client_body_timeout 60s;
    client_header_timeout 60s;
    
    # 代理配置
    location / {
        proxy_pass http://wangfujing_api;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
        
        # 超时配置
        proxy_connect_timeout 30s;
        proxy_send_timeout 60s;
        proxy_read_timeout 60s;
        
        # 缓冲配置
        proxy_buffering on;
        proxy_buffer_size 4k;
        proxy_buffers 8 4k;
    }
    
    # 健康检查
    location /health {
        proxy_pass http://wangfujing_api/health;
        access_log off;
    }
}

# 管理端域名配置
server {
    listen 80;
    server_name admin.wangfujing.com;
    
    # 重定向到HTTPS
    return 301 https://$server_name$request_uri;
}

server {
    listen 443 ssl http2;
    server_name admin.wangfujing.com;
    
    # SSL证书配置
    ssl_certificate /etc/nginx/ssl/admin.wangfujing.com.crt;
    ssl_certificate_key /etc/nginx/ssl/admin.wangfujing.com.key;
    ssl_session_timeout 1d;
    ssl_session_cache shared:SSL:50m;
    ssl_session_tickets off;
    
    # SSL安全配置
    ssl_protocols TLSv1.2 TLSv1.3;
    ssl_ciphers ECDHE-RSA-AES128-GCM-SHA256:ECDHE-RSA-AES256-GCM-SHA384;
    ssl_prefer_server_ciphers off;
    
    # 安全头
    add_header Strict-Transport-Security "max-age=63072000" always;
    add_header X-Frame-Options DENY;
    add_header X-Content-Type-Options nosniff;
    add_header X-XSS-Protection "1; mode=block";
    
    # 日志配置
    access_log /var/log/nginx/admin.access.log;
    error_log /var/log/nginx/admin.error.log;
    
    # 客户端配置
    client_max_body_size 50M;
    client_body_timeout 120s;
    client_header_timeout 60s;
    
    # 代理配置
    location / {
        proxy_pass http://wangfujing_admin;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
        
        # 超时配置
        proxy_connect_timeout 30s;
        proxy_send_timeout 120s;
        proxy_read_timeout 120s;
        
        # 缓冲配置
        proxy_buffering on;
        proxy_buffer_size 4k;
        proxy_buffers 8 4k;
    }
    
    # 健康检查
    location /health {
        proxy_pass http://wangfujing_admin/health;
        access_log off;
    }
}

# 默认服务器配置
server {
    listen 80 default_server;
    listen 443 ssl default_server;
    server_name _;
    
    # 默认SSL证书
    ssl_certificate /etc/nginx/ssl/default.crt;
    ssl_certificate_key /etc/nginx/ssl/default.key;
    
    return 444;
}
