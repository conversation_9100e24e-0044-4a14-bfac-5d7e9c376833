package auth

import (
	"context"
	"fmt"
	"strings"

	"gorm.io/gorm"
)

// PermissionManager 权限管理器
type PermissionManager struct {
	db *gorm.DB
}

// NewPermissionManager 创建权限管理器
func NewPermissionManager(db *gorm.DB) *PermissionManager {
	return &PermissionManager{
		db: db,
	}
}

// UserPermission 用户权限信息
type UserPermission struct {
	UserID      string   `json:"user_id"`
	Roles       []string `json:"roles"`
	Permissions []string `json:"permissions"`
}

// GetUserPermissions 获取用户权限
func (p *PermissionManager) GetUserPermissions(ctx context.Context, userID string) (*UserPermission, error) {
	var result struct {
		UserID      string
		Roles       string
		Permissions string
	}

	query := `
		SELECT 
			u.id as user_id,
			GROUP_CONCAT(DISTINCT r.name) as roles,
			GROUP_CONCAT(DISTINCT perm.name) as permissions
		FROM users u
		LEFT JOIN user_roles ur ON u.id = ur.user_id
		LEFT JOIN roles r ON ur.role_id = r.id AND r.status = 1
		LEFT JOIN role_permissions rp ON r.id = rp.role_id
		LEFT JOIN permissions perm ON rp.permission_id = perm.id AND perm.status = 1
		WHERE u.id = ? AND u.status = 1
		GROUP BY u.id
	`

	if err := p.db.WithContext(ctx).Raw(query, userID).Scan(&result).Error; err != nil {
		return nil, fmt.Errorf("failed to get user permissions: %w", err)
	}

	userPerm := &UserPermission{
		UserID: result.UserID,
	}

	if result.Roles != "" {
		userPerm.Roles = strings.Split(result.Roles, ",")
	}

	if result.Permissions != "" {
		userPerm.Permissions = strings.Split(result.Permissions, ",")
	}

	return userPerm, nil
}

// HasPermission 检查用户是否有指定权限
func (p *PermissionManager) HasPermission(ctx context.Context, userID, permission string) (bool, error) {
	userPerm, err := p.GetUserPermissions(ctx, userID)
	if err != nil {
		return false, err
	}

	for _, perm := range userPerm.Permissions {
		if perm == permission {
			return true, nil
		}
	}

	return false, nil
}

// HasRole 检查用户是否有指定角色
func (p *PermissionManager) HasRole(ctx context.Context, userID, role string) (bool, error) {
	userPerm, err := p.GetUserPermissions(ctx, userID)
	if err != nil {
		return false, err
	}

	for _, r := range userPerm.Roles {
		if r == role {
			return true, nil
		}
	}

	return false, nil
}

// HasAnyPermission 检查用户是否有任意一个权限
func (p *PermissionManager) HasAnyPermission(ctx context.Context, userID string, permissions []string) (bool, error) {
	userPerm, err := p.GetUserPermissions(ctx, userID)
	if err != nil {
		return false, err
	}

	userPermMap := make(map[string]bool)
	for _, perm := range userPerm.Permissions {
		userPermMap[perm] = true
	}

	for _, permission := range permissions {
		if userPermMap[permission] {
			return true, nil
		}
	}

	return false, nil
}

// HasAllPermissions 检查用户是否有所有权限
func (p *PermissionManager) HasAllPermissions(ctx context.Context, userID string, permissions []string) (bool, error) {
	userPerm, err := p.GetUserPermissions(ctx, userID)
	if err != nil {
		return false, err
	}

	userPermMap := make(map[string]bool)
	for _, perm := range userPerm.Permissions {
		userPermMap[perm] = true
	}

	for _, permission := range permissions {
		if !userPermMap[permission] {
			return false, nil
		}
	}

	return true, nil
}

// PermissionRule 权限规则
type PermissionRule struct {
	Resource string   `json:"resource"`
	Actions  []string `json:"actions"`
}

// CheckResourcePermission 检查资源权限
func (p *PermissionManager) CheckResourcePermission(ctx context.Context, userID, resource, action string) (bool, error) {
	permission := fmt.Sprintf("%s:%s", resource, action)
	return p.HasPermission(ctx, userID, permission)
}

// GetUserRolePermissions 获取用户角色权限映射
func (p *PermissionManager) GetUserRolePermissions(ctx context.Context, userID string) (map[string][]string, error) {
	var results []struct {
		RoleName       string
		PermissionName string
	}

	query := `
		SELECT 
			r.name as role_name,
			perm.name as permission_name
		FROM users u
		JOIN user_roles ur ON u.id = ur.user_id
		JOIN roles r ON ur.role_id = r.id AND r.status = 1
		JOIN role_permissions rp ON r.id = rp.role_id
		JOIN permissions perm ON rp.permission_id = perm.id AND perm.status = 1
		WHERE u.id = ? AND u.status = 1
		ORDER BY r.name, perm.name
	`

	if err := p.db.WithContext(ctx).Raw(query, userID).Scan(&results).Error; err != nil {
		return nil, fmt.Errorf("failed to get user role permissions: %w", err)
	}

	rolePermissions := make(map[string][]string)
	for _, result := range results {
		rolePermissions[result.RoleName] = append(rolePermissions[result.RoleName], result.PermissionName)
	}

	return rolePermissions, nil
}

// IsSuperAdmin 检查是否为超级管理员
func (p *PermissionManager) IsSuperAdmin(ctx context.Context, userID string) (bool, error) {
	return p.HasRole(ctx, userID, "super_admin")
}

// CanAccessModule 检查是否可以访问模块
func (p *PermissionManager) CanAccessModule(ctx context.Context, userID, module string) (bool, error) {
	// 超级管理员可以访问所有模块
	if isSuper, err := p.IsSuperAdmin(ctx, userID); err != nil {
		return false, err
	} else if isSuper {
		return true, nil
	}

	// 检查模块权限
	permission := fmt.Sprintf("%s:read", module)
	return p.HasPermission(ctx, userID, permission)
}

// GetAccessibleModules 获取用户可访问的模块列表
func (p *PermissionManager) GetAccessibleModules(ctx context.Context, userID string) ([]string, error) {
	userPerm, err := p.GetUserPermissions(ctx, userID)
	if err != nil {
		return nil, err
	}

	moduleMap := make(map[string]bool)
	for _, permission := range userPerm.Permissions {
		parts := strings.Split(permission, ":")
		if len(parts) >= 2 {
			module := parts[0]
			moduleMap[module] = true
		}
	}

	modules := make([]string, 0, len(moduleMap))
	for module := range moduleMap {
		modules = append(modules, module)
	}

	return modules, nil
}

// PermissionCheck 权限检查结果
type PermissionCheck struct {
	Allowed bool   `json:"allowed"`
	Reason  string `json:"reason,omitempty"`
}

// CheckPermissions 批量检查权限
func (p *PermissionManager) CheckPermissions(ctx context.Context, userID string, permissions []string) (map[string]*PermissionCheck, error) {
	userPerm, err := p.GetUserPermissions(ctx, userID)
	if err != nil {
		return nil, err
	}

	userPermMap := make(map[string]bool)
	for _, perm := range userPerm.Permissions {
		userPermMap[perm] = true
	}

	results := make(map[string]*PermissionCheck)
	for _, permission := range permissions {
		if userPermMap[permission] {
			results[permission] = &PermissionCheck{Allowed: true}
		} else {
			results[permission] = &PermissionCheck{
				Allowed: false,
				Reason:  "permission denied",
			}
		}
	}

	return results, nil
}
