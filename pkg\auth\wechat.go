package auth

import (
	"fmt"
	"io"
	"net/http"
	"net/url"
	"strings"
	"wangfujing_admin/pkg/json"
)

// WeChatConfig 微信配置
type WeChatConfig struct {
	UserAppID         string
	UserAppSecret     string
	MerchantAppID     string
	MerchantAppSecret string
}

// WeChatManager 微信管理器
type WeChatManager struct {
	config *WeChatConfig
}

// NewWeChatManager 创建微信管理器
func NewWeChatManager(config *WeChatConfig) *WeChatManager {
	return &WeChatManager{
		config: config,
	}
}

// Code2SessionResponse 微信code2session响应
type Code2SessionResponse struct {
	OpenID     string `json:"openid"`
	SessionKey string `json:"session_key"`
	UnionID    string `json:"unionid"`
	ErrCode    int    `json:"errcode"`
	ErrMsg     string `json:"errmsg"`
}

// GetUserOpenID 获取用户端OpenID
func (w *WeChatManager) GetUserOpenID(code string) (*Code2SessionResponse, error) {
	return w.code2Session(code, w.config.UserAppID, w.config.UserAppSecret)
}

// GetMerchantOpenID 获取商家端OpenID
func (w *WeChatManager) GetMerchantOpenID(code string) (*Code2SessionResponse, error) {
	return w.code2Session(code, w.config.MerchantAppID, w.config.MerchantAppSecret)
}

// code2Session 微信code2session接口
func (w *WeChatManager) code2Session(code, appID, appSecret string) (*Code2SessionResponse, error) {
	apiURL := "https://api.weixin.qq.com/sns/jscode2session"

	params := url.Values{}
	params.Set("appid", appID)
	params.Set("secret", appSecret)
	params.Set("js_code", code)
	params.Set("grant_type", "authorization_code")

	fullURL := fmt.Sprintf("%s?%s", apiURL, params.Encode())

	resp, err := http.Get(fullURL)
	if err != nil {
		return nil, fmt.Errorf("failed to request wechat api: %w", err)
	}
	defer resp.Body.Close()

	body, err := io.ReadAll(resp.Body)
	if err != nil {
		return nil, fmt.Errorf("failed to read response body: %w", err)
	}

	var result Code2SessionResponse
	if err := json.Unmarshal(body, &result); err != nil {
		return nil, fmt.Errorf("failed to unmarshal response: %w", err)
	}

	if result.ErrCode != 0 {
		return nil, fmt.Errorf("wechat api error: %d %s", result.ErrCode, result.ErrMsg)
	}

	return &result, nil
}

// AccessTokenResponse 微信访问令牌响应
type AccessTokenResponse struct {
	AccessToken string `json:"access_token"`
	ExpiresIn   int    `json:"expires_in"`
	ErrCode     int    `json:"errcode"`
	ErrMsg      string `json:"errmsg"`
}

// GetUserAccessToken 获取用户端访问令牌
func (w *WeChatManager) GetUserAccessToken() (*AccessTokenResponse, error) {
	return w.getAccessToken(w.config.UserAppID, w.config.UserAppSecret)
}

// GetMerchantAccessToken 获取商家端访问令牌
func (w *WeChatManager) GetMerchantAccessToken() (*AccessTokenResponse, error) {
	return w.getAccessToken(w.config.MerchantAppID, w.config.MerchantAppSecret)
}

// getAccessToken 获取访问令牌
func (w *WeChatManager) getAccessToken(appID, appSecret string) (*AccessTokenResponse, error) {
	apiURL := "https://api.weixin.qq.com/cgi-bin/token"

	params := url.Values{}
	params.Set("grant_type", "client_credential")
	params.Set("appid", appID)
	params.Set("secret", appSecret)

	fullURL := fmt.Sprintf("%s?%s", apiURL, params.Encode())

	resp, err := http.Get(fullURL)
	if err != nil {
		return nil, fmt.Errorf("failed to request wechat api: %w", err)
	}
	defer resp.Body.Close()

	body, err := io.ReadAll(resp.Body)
	if err != nil {
		return nil, fmt.Errorf("failed to read response body: %w", err)
	}

	var result AccessTokenResponse
	if err := json.Unmarshal(body, &result); err != nil {
		return nil, fmt.Errorf("failed to unmarshal response: %w", err)
	}

	if result.ErrCode != 0 {
		return nil, fmt.Errorf("wechat api error: %d %s", result.ErrCode, result.ErrMsg)
	}

	return &result, nil
}

// UserInfo 微信用户信息
type UserInfo struct {
	OpenID     string `json:"openid"`
	Nickname   string `json:"nickname"`
	Sex        int    `json:"sex"`
	Province   string `json:"province"`
	City       string `json:"city"`
	Country    string `json:"country"`
	HeadImgURL string `json:"headimgurl"`
	UnionID    string `json:"unionid"`
	ErrCode    int    `json:"errcode"`
	ErrMsg     string `json:"errmsg"`
}

// GetUserInfo 获取用户信息
func (w *WeChatManager) GetUserInfo(accessToken, openID string) (*UserInfo, error) {
	apiURL := "https://api.weixin.qq.com/cgi-bin/user/info"

	params := url.Values{}
	params.Set("access_token", accessToken)
	params.Set("openid", openID)
	params.Set("lang", "zh_CN")

	fullURL := fmt.Sprintf("%s?%s", apiURL, params.Encode())

	resp, err := http.Get(fullURL)
	if err != nil {
		return nil, fmt.Errorf("failed to request wechat api: %w", err)
	}
	defer resp.Body.Close()

	body, err := io.ReadAll(resp.Body)
	if err != nil {
		return nil, fmt.Errorf("failed to read response body: %w", err)
	}

	var result UserInfo
	if err := json.Unmarshal(body, &result); err != nil {
		return nil, fmt.Errorf("failed to unmarshal response: %w", err)
	}

	if result.ErrCode != 0 {
		return nil, fmt.Errorf("wechat api error: %d %s", result.ErrCode, result.ErrMsg)
	}

	return &result, nil
}

// PhoneInfo 微信手机号信息
type PhoneInfo struct {
	PhoneNumber     string `json:"phoneNumber"`
	PurePhoneNumber string `json:"purePhoneNumber"`
	CountryCode     string `json:"countryCode"` // 微信API返回字符串类型，如"86"
	Watermark       struct {
		Timestamp int64  `json:"timestamp"`
		AppID     string `json:"appid"`
	} `json:"watermark"`
}

// GetPhoneNumber 通过code获取手机号
func (w *WeChatManager) GetPhoneNumber(accessToken, code string) (*PhoneInfo, error) {
	apiURL := "https://api.weixin.qq.com/wxa/business/getuserphonenumber"

	requestBody := map[string]string{
		"code": code,
	}

	jsonData, err := json.Marshal(requestBody)
	if err != nil {
		return nil, fmt.Errorf("failed to marshal request: %w", err)
	}

	fullURL := fmt.Sprintf("%s?access_token=%s", apiURL, accessToken)
	resp, err := http.Post(fullURL, "application/json", strings.NewReader(string(jsonData)))
	if err != nil {
		return nil, fmt.Errorf("failed to request wechat api: %w", err)
	}
	defer resp.Body.Close()

	body, err := io.ReadAll(resp.Body)
	if err != nil {
		return nil, fmt.Errorf("failed to read response body: %w", err)
	}

	var result struct {
		ErrCode   int       `json:"errcode"`
		ErrMsg    string    `json:"errmsg"`
		PhoneInfo PhoneInfo `json:"phone_info"`
	}

	if err := json.Unmarshal(body, &result); err != nil {
		return nil, fmt.Errorf("failed to unmarshal response: %w", err)
	}

	if result.ErrCode != 0 {
		return nil, fmt.Errorf("wechat api error: %d %s", result.ErrCode, result.ErrMsg)
	}

	return &result.PhoneInfo, nil
}

// DecryptPhoneNumber 解密手机号
func (w *WeChatManager) DecryptPhoneNumber(sessionKey, encryptedData, iv string) (*PhoneInfo, error) {
	// 这里需要实现AES解密逻辑
	// 由于涉及到复杂的加密解密，这里先返回一个占位符
	// 实际项目中需要使用微信提供的解密库或自己实现AES-128-CBC解密
	return nil, fmt.Errorf("phone number decryption not implemented yet")
}

// LoginRequest 登录请求
type LoginRequest struct {
	Code     string `json:"code" binding:"required"`
	UserType int    `json:"user_type" binding:"required"` // 1:用户端 2:商家端
	Phone    string `json:"phone"`
	Nickname string `json:"nickname"`
	Avatar   string `json:"avatar"`
}

// LoginResponse 登录响应
type LoginResponse struct {
	User         interface{} `json:"user"`
	AccessToken  string      `json:"access_token"`
	RefreshToken string      `json:"refresh_token"`
	ExpiresIn    int64       `json:"expires_in"`
}
