package cache

import (
	"context"
	"encoding/json"
	"time"
	"wangfujing_admin/internal/config"

	"github.com/redis/go-redis/v9"
)

var rdb *redis.Client

// InitRedis 初始化Redis连接（简化版本，保持向后兼容）
func InitRedis(addr, password string, db int) *redis.Client {
	rdb = redis.NewClient(&redis.Options{
		Addr:     addr,
		Password: password,
		DB:       db,
	})

	// 测试连接
	ctx, cancel := context.WithTimeout(context.Background(), 5*time.Second)
	defer cancel()

	_, err := rdb.Ping(ctx).Result()
	if err != nil {
		panic("Failed to connect to Redis: " + err.Error())
	}

	return rdb
}

// InitRedisWithConfig 使用配置初始化Redis连接
func InitRedisWithConfig(cfg config.RedisConfig) *redis.Client {
	// 设置默认值
	poolSize := cfg.PoolSize
	if poolSize <= 0 {
		poolSize = 200 // 默认连接池大小
	}

	minIdleConns := cfg.MinIdleConns
	if minIdleConns <= 0 {
		minIdleConns = 10 // 默认最小空闲连接
	}

	maxRetries := cfg.MaxRetries
	if maxRetries <= 0 {
		maxRetries = 3 // 默认重试次数
	}

	dialTimeout := cfg.DialTimeout
	if dialTimeout <= 0 {
		dialTimeout = 5 // 默认连接超时5秒
	}

	readTimeout := cfg.ReadTimeout
	if readTimeout <= 0 {
		readTimeout = 3 // 默认读取超时3秒
	}

	writeTimeout := cfg.WriteTimeout
	if writeTimeout <= 0 {
		writeTimeout = 3 // 默认写入超时3秒
	}

	rdb = redis.NewClient(&redis.Options{
		Addr:         cfg.Addr,
		Password:     cfg.Password,
		DB:           cfg.DB,
		PoolSize:     poolSize,
		MinIdleConns: minIdleConns,
		MaxRetries:   maxRetries,
		DialTimeout:  time.Duration(dialTimeout) * time.Second,
		ReadTimeout:  time.Duration(readTimeout) * time.Second,
		WriteTimeout: time.Duration(writeTimeout) * time.Second,
	})

	// 测试连接
	ctx, cancel := context.WithTimeout(context.Background(), 5*time.Second)
	defer cancel()

	_, err := rdb.Ping(ctx).Result()
	if err != nil {
		panic("Failed to connect to Redis: " + err.Error())
	}

	return rdb
}

// GetRedis 获取Redis客户端
func GetRedis() *redis.Client {
	return rdb
}

// Set 设置缓存
func Set(ctx context.Context, key string, value interface{}, expiration time.Duration) error {
	data, err := json.Marshal(value)
	if err != nil {
		return err
	}
	return rdb.Set(ctx, key, data, expiration).Err()
}

// Get 获取缓存
func Get(ctx context.Context, key string, dest interface{}) error {
	data, err := rdb.Get(ctx, key).Result()
	if err != nil {
		return err
	}
	return json.Unmarshal([]byte(data), dest)
}

// GetString 获取字符串缓存
func GetString(ctx context.Context, key string) (string, error) {
	return rdb.Get(ctx, key).Result()
}

// SetString 设置字符串缓存
func SetString(ctx context.Context, key, value string, expiration time.Duration) error {
	return rdb.Set(ctx, key, value, expiration).Err()
}

// Delete 删除缓存
func Delete(ctx context.Context, keys ...string) error {
	return rdb.Del(ctx, keys...).Err()
}

// Exists 检查键是否存在
func Exists(ctx context.Context, keys ...string) (int64, error) {
	return rdb.Exists(ctx, keys...).Result()
}

// Expire 设置过期时间
func Expire(ctx context.Context, key string, expiration time.Duration) error {
	return rdb.Expire(ctx, key, expiration).Err()
}

// TTL 获取剩余过期时间
func TTL(ctx context.Context, key string) (time.Duration, error) {
	return rdb.TTL(ctx, key).Result()
}

// Incr 递增
func Incr(ctx context.Context, key string) (int64, error) {
	return rdb.Incr(ctx, key).Result()
}

// Decr 递减
func Decr(ctx context.Context, key string) (int64, error) {
	return rdb.Decr(ctx, key).Result()
}

// IncrBy 按指定值递增
func IncrBy(ctx context.Context, key string, value int64) (int64, error) {
	return rdb.IncrBy(ctx, key, value).Result()
}

// DecrBy 按指定值递减
func DecrBy(ctx context.Context, key string, value int64) (int64, error) {
	return rdb.DecrBy(ctx, key, value).Result()
}

// HSet 设置哈希字段
func HSet(ctx context.Context, key string, values ...interface{}) error {
	return rdb.HSet(ctx, key, values...).Err()
}

// HGet 获取哈希字段
func HGet(ctx context.Context, key, field string) (string, error) {
	return rdb.HGet(ctx, key, field).Result()
}

// HGetAll 获取所有哈希字段
func HGetAll(ctx context.Context, key string) (map[string]string, error) {
	return rdb.HGetAll(ctx, key).Result()
}

// HDel 删除哈希字段
func HDel(ctx context.Context, key string, fields ...string) error {
	return rdb.HDel(ctx, key, fields...).Err()
}

// HExists 检查哈希字段是否存在
func HExists(ctx context.Context, key, field string) (bool, error) {
	return rdb.HExists(ctx, key, field).Result()
}

// LPush 从左侧推入列表
func LPush(ctx context.Context, key string, values ...interface{}) error {
	return rdb.LPush(ctx, key, values...).Err()
}

// RPush 从右侧推入列表
func RPush(ctx context.Context, key string, values ...interface{}) error {
	return rdb.RPush(ctx, key, values...).Err()
}

// LPop 从左侧弹出列表元素
func LPop(ctx context.Context, key string) (string, error) {
	return rdb.LPop(ctx, key).Result()
}

// RPop 从右侧弹出列表元素
func RPop(ctx context.Context, key string) (string, error) {
	return rdb.RPop(ctx, key).Result()
}

// LLen 获取列表长度
func LLen(ctx context.Context, key string) (int64, error) {
	return rdb.LLen(ctx, key).Result()
}

// LRange 获取列表范围元素
func LRange(ctx context.Context, key string, start, stop int64) ([]string, error) {
	return rdb.LRange(ctx, key, start, stop).Result()
}

// SAdd 添加集合成员
func SAdd(ctx context.Context, key string, members ...interface{}) error {
	return rdb.SAdd(ctx, key, members...).Err()
}

// SRem 移除集合成员
func SRem(ctx context.Context, key string, members ...interface{}) error {
	return rdb.SRem(ctx, key, members...).Err()
}

// SMembers 获取集合所有成员
func SMembers(ctx context.Context, key string) ([]string, error) {
	return rdb.SMembers(ctx, key).Result()
}

// SIsMember 检查是否为集合成员
func SIsMember(ctx context.Context, key string, member interface{}) (bool, error) {
	return rdb.SIsMember(ctx, key, member).Result()
}

// SCard 获取集合成员数量
func SCard(ctx context.Context, key string) (int64, error) {
	return rdb.SCard(ctx, key).Result()
}

// ZAdd 添加有序集合成员
func ZAdd(ctx context.Context, key string, members ...redis.Z) error {
	return rdb.ZAdd(ctx, key, members...).Err()
}

// ZRem 移除有序集合成员
func ZRem(ctx context.Context, key string, members ...interface{}) error {
	return rdb.ZRem(ctx, key, members...).Err()
}

// ZRange 获取有序集合范围成员
func ZRange(ctx context.Context, key string, start, stop int64) ([]string, error) {
	return rdb.ZRange(ctx, key, start, stop).Result()
}

// ZRangeWithScores 获取有序集合范围成员及分数
func ZRangeWithScores(ctx context.Context, key string, start, stop int64) ([]redis.Z, error) {
	return rdb.ZRangeWithScores(ctx, key, start, stop).Result()
}

// ZCard 获取有序集合成员数量
func ZCard(ctx context.Context, key string) (int64, error) {
	return rdb.ZCard(ctx, key).Result()
}

// Lock 分布式锁
func Lock(ctx context.Context, key string, expiration time.Duration) (bool, error) {
	return rdb.SetNX(ctx, key, "locked", expiration).Result()
}

// Unlock 释放分布式锁
func Unlock(ctx context.Context, key string) error {
	return rdb.Del(ctx, key).Err()
}

// Pipeline 管道操作
func Pipeline() redis.Pipeliner {
	return rdb.Pipeline()
}

// TxPipeline 事务管道操作
func TxPipeline() redis.Pipeliner {
	return rdb.TxPipeline()
}
