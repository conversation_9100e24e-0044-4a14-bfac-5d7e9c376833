package json

import jsoniter "github.com/json-iterator/go"

// JSON 全局JSON实例，兼容标准库
var JSON = jsoniter.ConfigCompatibleWithStandardLibrary

// Marshal 序列化
func Marshal(v interface{}) ([]byte, error) {
	return JSON.Marshal(v)
}

// Unmarshal 反序列化
func Unmarshal(data []byte, v interface{}) error {
	return JSON.Unmarshal(data, v)
}

// MarshalToString 序列化为字符串
func MarshalToString(v interface{}) (string, error) {
	return JSON.MarshalToString(v)
}

// UnmarshalFromString 从字符串反序列化
func UnmarshalFromString(str string, v interface{}) error {
	return JSON.UnmarshalFromString(str, v)
}
