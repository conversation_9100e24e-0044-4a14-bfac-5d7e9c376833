package logger

import (
	"compress/gzip"
	"fmt"
	"io"
	"os"
	"path/filepath"
	"strings"
	"time"

	"go.uber.org/zap"
	"go.uber.org/zap/zapcore"
	"gopkg.in/natefinch/lumberjack.v2"
)

var (
	logger *zap.Logger
	sugar  *zap.SugaredLogger
)

// Field 类型别名
type Field = zap.Field

// DailyRotateLogger 按天轮转的日志器
type DailyRotateLogger struct {
	*lumberjack.Logger
	lastRotateDate string
	baseFilename   string
	maxAge         int
	compress       bool
	localTime      bool
}

// NewDailyRotateLogger 创建按天轮转的日志器
func NewDailyRotateLogger(filename string, maxSize, maxAge int, compress, localTime bool) *DailyRotateLogger {
	// 如果maxSize为0，设置为一个很大的值以禁用大小限制
	if maxSize == 0 {
		maxSize = 1000000 // 1TB，实际上不会达到
	}

	// 创建自定义的lumberjack.Logger，禁用MaxBackups以避免冲突
	logger := &lumberjack.Logger{
		Filename:   filename,
		MaxSize:    maxSize,
		MaxAge:     0,     // 禁用lumberjack的自动清理，我们自己管理
		MaxBackups: 0,     // 禁用MaxBackups，我们自己管理按天轮转
		Compress:   false, // 禁用lumberjack的压缩，我们自己管理
		LocalTime:  localTime,
	}

	return &DailyRotateLogger{
		Logger:         logger,
		lastRotateDate: getCurrentDate(localTime),
		baseFilename:   filename,
		maxAge:         maxAge,
		compress:       compress,
		localTime:      localTime,
	}
}

// Write 实现io.Writer接口，支持按天轮转
func (l *DailyRotateLogger) Write(p []byte) (n int, err error) {
	currentDate := getCurrentDate(l.Logger.LocalTime)

	// 检查是否需要按天轮转
	if currentDate != l.lastRotateDate {
		// 执行轮转
		if err := l.rotateByDate(); err != nil {
			// 轮转失败，记录错误但继续写入
			// 这里不能使用logger，因为可能导致循环调用
		}
		l.lastRotateDate = currentDate
	}

	return l.Logger.Write(p)
}

// rotateByDate 按日期轮转日志文件
func (l *DailyRotateLogger) rotateByDate() error {
	// 关闭当前文件
	if err := l.Logger.Close(); err != nil {
		return err
	}

	// 生成昨天的日期（因为轮转时已经是新的一天）
	var yesterday time.Time
	if l.localTime {
		loc, _ := time.LoadLocation("Asia/Shanghai")
		yesterday = time.Now().In(loc).AddDate(0, 0, -1)
	} else {
		yesterday = time.Now().UTC().AddDate(0, 0, -1)
	}

	// 生成新的文件名：原文件名-日期.扩展名
	oldFilename := l.baseFilename
	ext := filepath.Ext(oldFilename)
	nameWithoutExt := strings.TrimSuffix(oldFilename, ext)
	newFilename := fmt.Sprintf("%s-%s%s", nameWithoutExt, yesterday.Format("2006-01-02"), ext)

	// 重命名当前文件
	if err := os.Rename(oldFilename, newFilename); err != nil {
		// 如果重命名失败，可能是文件不存在，这是正常的
		if !os.IsNotExist(err) {
			return err
		}
	}

	// 压缩文件（如果需要）
	if l.compress {
		if err := l.compressFile(newFilename); err != nil {
			// 压缩失败不影响日志轮转
		}
	}

	// 清理过期文件
	l.cleanupOldFiles()

	return nil
}

// compressFile 压缩文件
func (l *DailyRotateLogger) compressFile(filename string) error {
	// 打开原文件
	file, err := os.Open(filename)
	if err != nil {
		return err
	}
	defer file.Close()

	// 创建压缩文件
	gzFilename := filename + ".gz"
	gzFile, err := os.Create(gzFilename)
	if err != nil {
		return err
	}
	defer gzFile.Close()

	// 创建gzip写入器
	gzWriter := gzip.NewWriter(gzFile)
	defer gzWriter.Close()

	// 复制文件内容到压缩文件
	_, err = io.Copy(gzWriter, file)
	if err != nil {
		return err
	}

	// 删除原文件
	return os.Remove(filename)
}

// cleanupOldFiles 清理过期的日志文件
func (l *DailyRotateLogger) cleanupOldFiles() {
	if l.maxAge <= 0 {
		return
	}

	// 计算过期时间
	var cutoff time.Time
	if l.localTime {
		loc, _ := time.LoadLocation("Asia/Shanghai")
		cutoff = time.Now().In(loc).AddDate(0, 0, -l.maxAge)
	} else {
		cutoff = time.Now().UTC().AddDate(0, 0, -l.maxAge)
	}

	// 获取日志文件目录
	dir := filepath.Dir(l.baseFilename)
	baseName := filepath.Base(l.baseFilename)
	ext := filepath.Ext(baseName)
	nameWithoutExt := strings.TrimSuffix(baseName, ext)

	// 遍历目录查找过期文件
	filepath.Walk(dir, func(path string, info os.FileInfo, err error) error {
		if err != nil {
			return nil
		}

		// 检查是否是我们的日志文件
		fileName := filepath.Base(path)
		if !strings.HasPrefix(fileName, nameWithoutExt+"-") {
			return nil
		}

		// 检查文件修改时间
		if info.ModTime().Before(cutoff) {
			os.Remove(path)
		}

		return nil
	})
}

// getCurrentDate 获取当前日期字符串
func getCurrentDate(localTime bool) string {
	var now time.Time
	if localTime {
		// 使用北京时间
		loc, _ := time.LoadLocation("Asia/Shanghai")
		now = time.Now().In(loc)
	} else {
		now = time.Now().UTC()
	}
	return now.Format("2006-01-02")
}

// LogConfig 日志配置
type LogConfig struct {
	Level       string
	Filename    string
	ErrorFile   string // 错误日志文件
	MaxSize     int
	MaxAge      int
	Compress    bool
	LocalTime   bool
	RotateDaily bool
	Console     bool // 是否输出到控制台
}

// Init 初始化日志
func Init(level, filename string) {
	InitWithConfig(LogConfig{
		Level:       level,
		Filename:    filename,
		MaxSize:     100,
		MaxAge:      7,
		Compress:    true,
		LocalTime:   true,
		RotateDaily: false,
	})
}

// InitWithConfig 使用配置初始化日志
func InitWithConfig(config LogConfig) {
	// 确保日志目录存在
	if config.Filename != "" {
		dir := filepath.Dir(config.Filename)
		if err := os.MkdirAll(dir, 0755); err != nil {
			panic(err)
		}
	}

	// 设置日志级别
	var logLevel zapcore.Level
	switch config.Level {
	case "debug":
		logLevel = zapcore.DebugLevel
	case "info":
		logLevel = zapcore.InfoLevel
	case "warn":
		logLevel = zapcore.WarnLevel
	case "error":
		logLevel = zapcore.ErrorLevel
	default:
		logLevel = zapcore.InfoLevel
	}

	// 编码器配置
	encoderConfig := zapcore.EncoderConfig{
		TimeKey:        "time",
		LevelKey:       "level",
		NameKey:        "logger",
		CallerKey:      "caller",
		MessageKey:     "msg",
		StacktraceKey:  "stacktrace",
		LineEnding:     zapcore.DefaultLineEnding,
		EncodeLevel:    zapcore.LowercaseLevelEncoder,
		EncodeTime:     zapcore.ISO8601TimeEncoder,
		EncodeDuration: zapcore.SecondsDurationEncoder,
		EncodeCaller:   zapcore.ShortCallerEncoder,
	}

	// 创建核心
	var cores []zapcore.Core

	// 控制台输出（可选）
	if config.Console {
		consoleEncoder := zapcore.NewConsoleEncoder(encoderConfig)
		consoleCore := zapcore.NewCore(consoleEncoder, zapcore.AddSync(os.Stdout), logLevel)
		cores = append(cores, consoleCore)
	}

	// 文件输出
	if config.Filename != "" {
		fileEncoder := zapcore.NewJSONEncoder(encoderConfig)

		var fileWriter zapcore.WriteSyncer
		if config.RotateDaily {
			// 使用按天轮转的日志器
			dailyLogger := NewDailyRotateLogger(
				config.Filename,
				config.MaxSize,
				config.MaxAge,
				config.Compress,
				config.LocalTime,
			)
			fileWriter = zapcore.AddSync(dailyLogger)
		} else {
			// 使用标准的lumberjack日志器
			maxSize := config.MaxSize
			if maxSize == 0 {
				maxSize = 1000000 // 1TB，实际上不会达到
			}
			fileWriter = zapcore.AddSync(&lumberjack.Logger{
				Filename:   config.Filename,
				MaxSize:    maxSize,
				MaxBackups: 3,
				MaxAge:     config.MaxAge,
				Compress:   config.Compress,
				LocalTime:  config.LocalTime,
			})
		}

		fileCore := zapcore.NewCore(fileEncoder, fileWriter, logLevel)
		cores = append(cores, fileCore)
	}

	// 错误日志文件输出（可选）
	if config.ErrorFile != "" {
		// 确保错误日志目录存在
		dir := filepath.Dir(config.ErrorFile)
		if err := os.MkdirAll(dir, 0755); err != nil {
			panic(err)
		}

		errorEncoder := zapcore.NewJSONEncoder(encoderConfig)

		var errorWriter zapcore.WriteSyncer
		if config.RotateDaily {
			// 使用按天轮转的日志器
			dailyLogger := NewDailyRotateLogger(
				config.ErrorFile,
				config.MaxSize,
				config.MaxAge,
				config.Compress,
				config.LocalTime,
			)
			errorWriter = zapcore.AddSync(dailyLogger)
		} else {
			// 使用标准的lumberjack日志器
			maxSize := config.MaxSize
			if maxSize == 0 {
				maxSize = 1000000 // 1TB，实际上不会达到
			}
			errorWriter = zapcore.AddSync(&lumberjack.Logger{
				Filename:   config.ErrorFile,
				MaxSize:    maxSize,
				MaxBackups: 3,
				MaxAge:     config.MaxAge,
				Compress:   config.Compress,
				LocalTime:  config.LocalTime,
			})
		}

		// 错误日志只记录ERROR级别及以上
		errorCore := zapcore.NewCore(errorEncoder, errorWriter, zapcore.ErrorLevel)
		cores = append(cores, errorCore)
	}

	// 创建logger
	core := zapcore.NewTee(cores...)
	logger = zap.New(core, zap.AddCaller(), zap.AddStacktrace(zapcore.ErrorLevel))
	sugar = logger.Sugar()
}

// GetLogger 获取zap logger
func GetLogger() *zap.Logger {
	return logger
}

// GetSugar 获取sugar logger
func GetSugar() *zap.SugaredLogger {
	return sugar
}

// Sync 同步日志
func Sync() {
	if logger != nil {
		logger.Sync()
	}
}

// Debug 调试日志
func Debug(msg string, fields ...zap.Field) {
	logger.Debug(msg, fields...)
}

// Info 信息日志
func Info(msg string, fields ...zap.Field) {
	logger.Info(msg, fields...)
}

// Warn 警告日志
func Warn(msg string, fields ...zap.Field) {
	logger.Warn(msg, fields...)
}

// Error 错误日志
func Error(msg string, fields ...zap.Field) {
	logger.Error(msg, fields...)
}

// Fatal 致命错误日志
func Fatal(msg string, fields ...zap.Field) {
	logger.Fatal(msg, fields...)
}

// Panic panic日志
func Panic(msg string, fields ...zap.Field) {
	logger.Panic(msg, fields...)
}

// 便捷的字段构造函数
func String(key, val string) zap.Field {
	return zap.String(key, val)
}

func Int(key string, val int) zap.Field {
	return zap.Int(key, val)
}

func Int64(key string, val int64) zap.Field {
	return zap.Int64(key, val)
}

func Float64(key string, val float64) zap.Field {
	return zap.Float64(key, val)
}

func Bool(key string, val bool) zap.Field {
	return zap.Bool(key, val)
}

func Any(key string, val interface{}) zap.Field {
	return zap.Any(key, val)
}

func Err(err error) zap.Field {
	return zap.Error(err)
}

func Duration(key string, val time.Duration) zap.Field {
	return zap.Duration(key, val)
}

// Sugar logger 便捷方法
func Debugf(template string, args ...interface{}) {
	sugar.Debugf(template, args...)
}

func Infof(template string, args ...interface{}) {
	sugar.Infof(template, args...)
}

func Warnf(template string, args ...interface{}) {
	sugar.Warnf(template, args...)
}

func Errorf(template string, args ...interface{}) {
	sugar.Errorf(template, args...)
}

func Fatalf(template string, args ...interface{}) {
	sugar.Fatalf(template, args...)
}

func Panicf(template string, args ...interface{}) {
	sugar.Panicf(template, args...)
}
