package oss

import (
	"fmt"
	"io"
	"mime/multipart"
	"path/filepath"
	"strings"
	"time"

	"github.com/aliyun/aliyun-oss-go-sdk/oss"
	"github.com/google/uuid"
)

// OSSConfig OSS配置
type OSSConfig struct {
	Endpoint        string
	AccessKeyID     string
	AccessKeySecret string
	BucketName      string
	Domain          string
}

// OSSManager OSS管理器
type OSSManager struct {
	client *oss.Client
	bucket *oss.Bucket
	config *OSSConfig
}

// NewOSSManager 创建OSS管理器
func NewOSSManager(config *OSSConfig) (*OSSManager, error) {
	client, err := oss.New(config.Endpoint, config.AccessKeyID, config.AccessKeySecret)
	if err != nil {
		return nil, fmt.Errorf("failed to create oss client: %w", err)
	}

	bucket, err := client.Bucket(config.BucketName)
	if err != nil {
		return nil, fmt.Errorf("failed to get bucket: %w", err)
	}

	return &OSSManager{
		client: client,
		bucket: bucket,
		config: config,
	}, nil
}

// UploadFile 上传文件
func (o *OSSManager) UploadFile(file multipart.File, header *multipart.FileHeader, folder string) (string, error) {
	// 生成文件名
	ext := filepath.Ext(header.Filename)
	filename := fmt.Sprintf("%s%s", uuid.New().String(), ext)

	// 构建对象键
	objectKey := filepath.Join(folder, time.Now().Format("2006/01/02"), filename)
	objectKey = strings.ReplaceAll(objectKey, "\\", "/") // 确保使用正斜杠

	// 上传文件
	err := o.bucket.PutObject(objectKey, file)
	if err != nil {
		return "", fmt.Errorf("failed to upload file: %w", err)
	}

	// 返回完整URL
	return fmt.Sprintf("%s/%s", strings.TrimRight(o.config.Domain, "/"), objectKey), nil
}

// UploadBytes 上传字节数据
func (o *OSSManager) UploadBytes(data []byte, filename, folder string) (string, error) {
	// 生成文件名
	ext := filepath.Ext(filename)
	newFilename := fmt.Sprintf("%s%s", uuid.New().String(), ext)

	// 构建对象键
	objectKey := filepath.Join(folder, time.Now().Format("2006/01/02"), newFilename)
	objectKey = strings.ReplaceAll(objectKey, "\\", "/")

	// 上传数据
	err := o.bucket.PutObject(objectKey, strings.NewReader(string(data)))
	if err != nil {
		return "", fmt.Errorf("failed to upload bytes: %w", err)
	}

	// 返回完整URL
	return fmt.Sprintf("%s/%s", strings.TrimRight(o.config.Domain, "/"), objectKey), nil
}

// UploadReader 上传Reader数据
func (o *OSSManager) UploadReader(reader io.Reader, filename, folder string) (string, error) {
	// 生成文件名
	ext := filepath.Ext(filename)
	newFilename := fmt.Sprintf("%s%s", uuid.New().String(), ext)

	// 构建对象键
	objectKey := filepath.Join(folder, time.Now().Format("2006/01/02"), newFilename)
	objectKey = strings.ReplaceAll(objectKey, "\\", "/")

	// 上传数据
	err := o.bucket.PutObject(objectKey, reader)
	if err != nil {
		return "", fmt.Errorf("failed to upload reader: %w", err)
	}

	// 返回完整URL
	return fmt.Sprintf("%s/%s", strings.TrimRight(o.config.Domain, "/"), objectKey), nil
}

// DeleteFile 删除文件
func (o *OSSManager) DeleteFile(url string) error {
	// 从URL中提取对象键
	objectKey := strings.TrimPrefix(url, o.config.Domain+"/")
	objectKey = strings.TrimPrefix(objectKey, o.config.Domain)
	objectKey = strings.TrimPrefix(objectKey, "/")

	err := o.bucket.DeleteObject(objectKey)
	if err != nil {
		return fmt.Errorf("failed to delete file: %w", err)
	}

	return nil
}

// DeleteFiles 批量删除文件
func (o *OSSManager) DeleteFiles(urls []string) error {
	if len(urls) == 0 {
		return nil
	}

	objectKeys := make([]string, len(urls))
	for i, url := range urls {
		objectKey := strings.TrimPrefix(url, o.config.Domain+"/")
		objectKey = strings.TrimPrefix(objectKey, o.config.Domain)
		objectKey = strings.TrimPrefix(objectKey, "/")
		objectKeys[i] = objectKey
	}

	_, err := o.bucket.DeleteObjects(objectKeys)
	if err != nil {
		return fmt.Errorf("failed to delete files: %w", err)
	}

	return nil
}

// FileExists 检查文件是否存在
func (o *OSSManager) FileExists(url string) (bool, error) {
	// 从URL中提取对象键
	objectKey := strings.TrimPrefix(url, o.config.Domain+"/")
	objectKey = strings.TrimPrefix(objectKey, o.config.Domain)
	objectKey = strings.TrimPrefix(objectKey, "/")

	exists, err := o.bucket.IsObjectExist(objectKey)
	if err != nil {
		return false, fmt.Errorf("failed to check file existence: %w", err)
	}

	return exists, nil
}

// GetFileInfo 获取文件信息
func (o *OSSManager) GetFileInfo(url string) (map[string]string, error) {
	// 从URL中提取对象键
	objectKey := strings.TrimPrefix(url, o.config.Domain+"/")
	objectKey = strings.TrimPrefix(objectKey, o.config.Domain)
	objectKey = strings.TrimPrefix(objectKey, "/")

	meta, err := o.bucket.GetObjectMeta(objectKey)
	if err != nil {
		return nil, fmt.Errorf("failed to get file info: %w", err)
	}

	// 转换http.Header到map[string]string
	result := make(map[string]string)
	for key, values := range meta {
		if len(values) > 0 {
			result[key] = values[0]
		}
	}

	return result, nil
}

// GeneratePresignedURL 生成预签名URL
func (o *OSSManager) GeneratePresignedURL(url string, expiration time.Duration) (string, error) {
	// 从URL中提取对象键
	objectKey := strings.TrimPrefix(url, o.config.Domain+"/")
	objectKey = strings.TrimPrefix(objectKey, o.config.Domain)
	objectKey = strings.TrimPrefix(objectKey, "/")

	signedURL, err := o.bucket.SignURL(objectKey, oss.HTTPGet, int64(expiration.Seconds()))
	if err != nil {
		return "", fmt.Errorf("failed to generate presigned url: %w", err)
	}

	return signedURL, nil
}

// CopyFile 复制文件
func (o *OSSManager) CopyFile(sourceURL, destFolder string) (string, error) {
	// 从URL中提取源对象键
	sourceKey := strings.TrimPrefix(sourceURL, o.config.Domain+"/")
	sourceKey = strings.TrimPrefix(sourceKey, o.config.Domain)
	sourceKey = strings.TrimPrefix(sourceKey, "/")

	// 生成目标对象键
	ext := filepath.Ext(sourceKey)
	filename := fmt.Sprintf("%s%s", uuid.New().String(), ext)
	destKey := filepath.Join(destFolder, time.Now().Format("2006/01/02"), filename)
	destKey = strings.ReplaceAll(destKey, "\\", "/")

	// 复制文件
	_, err := o.bucket.CopyObject(sourceKey, destKey)
	if err != nil {
		return "", fmt.Errorf("failed to copy file: %w", err)
	}

	// 返回新文件URL
	return fmt.Sprintf("%s/%s", strings.TrimRight(o.config.Domain, "/"), destKey), nil
}

// ListFiles 列出文件
func (o *OSSManager) ListFiles(prefix string, maxKeys int) ([]oss.ObjectProperties, error) {
	marker := ""
	var allObjects []oss.ObjectProperties

	for {
		lsRes, err := o.bucket.ListObjects(oss.Prefix(prefix), oss.Marker(marker), oss.MaxKeys(maxKeys))
		if err != nil {
			return nil, fmt.Errorf("failed to list files: %w", err)
		}

		allObjects = append(allObjects, lsRes.Objects...)

		if !lsRes.IsTruncated {
			break
		}
		marker = lsRes.NextMarker
	}

	return allObjects, nil
}

// GetFileSize 获取文件大小
func (o *OSSManager) GetFileSize(url string) (int64, error) {
	info, err := o.GetFileInfo(url)
	if err != nil {
		return 0, err
	}

	if contentLength, exists := info["Content-Length"]; exists {
		// 这里需要解析字符串为int64，暂时返回0
		_ = contentLength
		return 0, nil
	}
	return 0, nil
}

// IsValidImageType 验证是否为有效的图片类型
func (o *OSSManager) IsValidImageType(filename string) bool {
	ext := strings.ToLower(filepath.Ext(filename))
	validExts := []string{".jpg", ".jpeg", ".png", ".gif", ".bmp", ".webp"}

	for _, validExt := range validExts {
		if ext == validExt {
			return true
		}
	}
	return false
}

// IsValidVideoType 验证是否为有效的视频类型
func (o *OSSManager) IsValidVideoType(filename string) bool {
	ext := strings.ToLower(filepath.Ext(filename))
	validExts := []string{".mp4", ".avi", ".mov", ".wmv", ".flv", ".mkv", ".m4v", ".3gp", ".webm"}

	for _, validExt := range validExts {
		if ext == validExt {
			return true
		}
	}
	return false
}

// IsValidDocumentType 验证是否为有效的文档类型
func (o *OSSManager) IsValidDocumentType(filename string) bool {
	ext := strings.ToLower(filepath.Ext(filename))
	validExts := []string{".pdf", ".doc", ".docx", ".xls", ".xlsx", ".ppt", ".pptx", ".txt", ".rtf", ".csv"}

	for _, validExt := range validExts {
		if ext == validExt {
			return true
		}
	}
	return false
}

// IsValidAudioType 验证是否为有效的音频类型
func (o *OSSManager) IsValidAudioType(filename string) bool {
	ext := strings.ToLower(filepath.Ext(filename))
	validExts := []string{".mp3", ".wav", ".flac", ".aac", ".ogg", ".wma", ".m4a"}

	for _, validExt := range validExts {
		if ext == validExt {
			return true
		}
	}
	return false
}

// GetFileType 根据文件扩展名获取文件类型
func (o *OSSManager) GetFileType(filename string) string {
	if o.IsValidImageType(filename) {
		return "image"
	}
	if o.IsValidVideoType(filename) {
		return "video"
	}
	if o.IsValidDocumentType(filename) {
		return "document"
	}
	if o.IsValidAudioType(filename) {
		return "audio"
	}
	return "other"
}

// IsValidImageType 检查是否为有效的图片类型
func IsValidImageType(filename string) bool {
	ext := strings.ToLower(filepath.Ext(filename))
	validExts := []string{".jpg", ".jpeg", ".png", ".gif", ".bmp", ".webp"}

	for _, validExt := range validExts {
		if ext == validExt {
			return true
		}
	}
	return false
}

// IsValidVideoType 检查是否为有效的视频类型
func IsValidVideoType(filename string) bool {
	ext := strings.ToLower(filepath.Ext(filename))
	validExts := []string{".mp4", ".avi", ".mov", ".wmv", ".flv", ".webm", ".mkv"}

	for _, validExt := range validExts {
		if ext == validExt {
			return true
		}
	}
	return false
}

// GetFileExtension 获取文件扩展名
func GetFileExtension(filename string) string {
	return strings.ToLower(filepath.Ext(filename))
}
