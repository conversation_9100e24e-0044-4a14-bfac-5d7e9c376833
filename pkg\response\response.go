package response

import (
	"net/http"

	"github.com/gin-gonic/gin"
)

// Response 统一响应结构
type Response struct {
	Code    int         `json:"code"`
	Message string      `json:"message"`
	Data    interface{} `json:"data,omitempty"`
}

// PageResponse 分页响应结构
type PageResponse struct {
	Code    int         `json:"code"`
	Message string      `json:"message"`
	Data    interface{} `json:"data,omitempty"`
	Total   int64       `json:"total"`
	Page    int         `json:"page"`
	Size    int         `json:"size"`
}

// Success 成功响应
func Success(c *gin.Context, data interface{}) {
	c.JSON(http.StatusOK, Response{
		Code:    0,
		Message: "success",
		Data:    data,
	})
}

// SuccessWithMessage 带消息的成功响应
func SuccessWithMessage(c *gin.Context, message string, data interface{}) {
	c.JSON(http.StatusOK, Response{
		Code:    0,
		Message: message,
		Data:    data,
	})
}

// Error 错误响应
func Error(c *gin.Context, httpCode int, message string) {
	c.JSO<PERSON>(httpCode, Response{
		Code:    httpCode,
		Message: message,
	})
}

// ErrorWithCode 带错误码的错误响应
func ErrorWithCode(c *gin.Context, httpCode, errCode int, message string) {
	c.JSON(httpCode, Response{
		Code:    errCode,
		Message: message,
	})
}

// BadRequest 400错误响应
func BadRequest(c *gin.Context, message string) {
	Error(c, http.StatusBadRequest, message)
}

// Unauthorized 401错误响应
func Unauthorized(c *gin.Context, message string) {
	Error(c, http.StatusUnauthorized, message)
}

// Forbidden 403错误响应
func Forbidden(c *gin.Context, message string) {
	Error(c, http.StatusForbidden, message)
}

// NotFound 404错误响应
func NotFound(c *gin.Context, message string) {
	Error(c, http.StatusNotFound, message)
}

// InternalServerError 500错误响应
func InternalServerError(c *gin.Context, message string) {
	Error(c, http.StatusInternalServerError, message)
}

// Page 分页成功响应
func Page(c *gin.Context, data interface{}, total int64, page, size int) {
	c.JSON(http.StatusOK, PageResponse{
		Code:    0,
		Message: "success",
		Data:    data,
		Total:   total,
		Page:    page,
		Size:    size,
	})
}

// PageWithMessage 带消息的分页成功响应
func PageWithMessage(c *gin.Context, message string, data interface{}, total int64, page, size int) {
	c.JSON(http.StatusOK, PageResponse{
		Code:    0,
		Message: message,
		Data:    data,
		Total:   total,
		Page:    page,
		Size:    size,
	})
}

// 常用错误码定义
const (
	// 通用错误码
	CodeSuccess       = 0
	CodeInvalidParams = 400001
	CodeUnauthorized  = 401001
	CodeForbidden     = 403001
	CodeNotFound      = 404001
	CodeInternalError = 500001
	CodeDatabaseError = 500002
	CodeCacheError    = 500003

	// 用户相关错误码
	CodeUserNotFound    = 100001
	CodeUserExists      = 100002
	CodeUserDisabled    = 100003
	CodeInvalidPassword = 100004
	CodeInvalidPhone    = 100005

	// 权限相关错误码
	CodeInsufficientPermission = 200001
	CodeInvalidRole            = 200002
	CodePermissionDenied       = 200003

	// 商家相关错误码
	CodeMerchantNotFound = 300001
	CodeMerchantDisabled = 300002
	CodeFloorNotFound    = 300003

	// 商品相关错误码
	CodeProductNotFound    = 400101
	CodeProductOutOfStock  = 400102
	CodeProductExpired     = 400103
	CodeInvalidProductType = 400104

	// 订单相关错误码
	CodeOrderNotFound      = 500101
	CodeOrderStatusError   = 500102
	CodeInsufficientPoints = 500103

	// 微信相关错误码
	CodeWeChatError       = 600001
	CodeInvalidWeChatCode = 600002
)

// GetErrorMessage 获取错误消息
func GetErrorMessage(code int) string {
	messages := map[int]string{
		CodeSuccess:                "操作成功",
		CodeInvalidParams:          "参数错误",
		CodeUnauthorized:           "未授权",
		CodeForbidden:              "禁止访问",
		CodeNotFound:               "资源不存在",
		CodeInternalError:          "内部服务器错误",
		CodeDatabaseError:          "数据库错误",
		CodeCacheError:             "缓存错误",
		CodeUserNotFound:           "用户不存在",
		CodeUserExists:             "用户已存在",
		CodeUserDisabled:           "用户已被禁用",
		CodeInvalidPassword:        "密码错误",
		CodeInvalidPhone:           "手机号格式错误",
		CodeInsufficientPermission: "权限不足",
		CodeInvalidRole:            "角色无效",
		CodePermissionDenied:       "权限被拒绝",
		CodeMerchantNotFound:       "商家不存在",
		CodeMerchantDisabled:       "商家已被禁用",
		CodeFloorNotFound:          "楼层不存在",
		CodeProductNotFound:        "商品不存在",
		CodeProductOutOfStock:      "商品库存不足",
		CodeProductExpired:         "商品已过期",
		CodeInvalidProductType:     "商品类型无效",
		CodeOrderNotFound:          "订单不存在",
		CodeOrderStatusError:       "订单状态错误",
		CodeInsufficientPoints:     "积分不足",
		CodeWeChatError:            "微信接口错误",
		CodeInvalidWeChatCode:      "微信授权码无效",
	}

	if message, exists := messages[code]; exists {
		return message
	}
	return "未知错误"
}

// ErrorWithCustomCode 自定义错误码响应
func ErrorWithCustomCode(c *gin.Context, code int) {
	message := GetErrorMessage(code)
	c.JSON(http.StatusOK, Response{
		Code:    code,
		Message: message,
	})
}

// ValidateError 参数验证错误响应
func ValidateError(c *gin.Context, err error) {
	ErrorWithCode(c, http.StatusBadRequest, CodeInvalidParams, err.Error())
}

// DatabaseError 数据库错误响应
func DatabaseError(c *gin.Context, err error) {
	ErrorWithCode(c, http.StatusInternalServerError, CodeDatabaseError, "数据库操作失败")
}

// CacheError 缓存错误响应
func CacheError(c *gin.Context, err error) {
	ErrorWithCode(c, http.StatusInternalServerError, CodeCacheError, "缓存操作失败")
}
