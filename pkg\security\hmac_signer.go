package security

import (
	"crypto/hmac"
	"crypto/sha256"
	"encoding/hex"
	"fmt"
	"strings"
	"time"
)

// HMACConfig HMAC配置
type HMACConfig struct {
	SecretKey string // 密钥
	TTL       int64  // 有效期（秒）
}

// HMACSigner HMAC签名器
type HMACSigner struct {
	config *HMACConfig
}

// NewHMACSigner 创建HMAC签名器
func NewHMACSigner(config *HMACConfig) *HMACSigner {
	return &HMACSigner{
		config: config,
	}
}

// QRCodeData 二维码数据结构
type QRCodeData struct {
	OrderID    uint64 `json:"order_id"`    // 订单ID
	OrderNo    string `json:"order_no"`    // 订单号
	UserID     uint64 `json:"user_id"`     // 用户ID
	ProductID  uint64 `json:"product_id"`  // 商品ID
	MerchantID uint64 `json:"merchant_id"` // 商家ID
	VerifyCode string `json:"verify_code"` // 核销码UUID
	Timestamp  int64  `json:"timestamp"`   // 时间戳
	ExpiresAt  int64  `json:"expires_at"`  // 过期时间
	Signature  string `json:"signature"`   // 签名
}

// GenerateSignature 生成签名
func (s *HMACSigner) GenerateSignature(data *QRCodeData) (string, error) {
	// 构建待签名字符串
	signString := fmt.Sprintf("%d|%s|%d|%d|%d|%s|%d|%d",
		data.OrderID,
		data.OrderNo,
		data.UserID,
		data.ProductID,
		data.MerchantID,
		data.VerifyCode,
		data.Timestamp,
		data.ExpiresAt,
	)

	fmt.Printf("HMAC签名生成: signString=%s, secretKey=%s\n", signString, s.config.SecretKey)

	// 生成HMAC签名
	h := hmac.New(sha256.New, []byte(s.config.SecretKey))
	h.Write([]byte(signString))
	signature := hex.EncodeToString(h.Sum(nil))

	fmt.Printf("HMAC签名结果: %s\n", signature)

	return signature, nil
}

// VerifySignature 验证签名
func (s *HMACSigner) VerifySignature(data *QRCodeData) bool {
	// 生成期望的签名
	expectedSignature, err := s.GenerateSignature(data)
	if err != nil {
		return false
	}

	// 比较签名
	return hmac.Equal([]byte(expectedSignature), []byte(data.Signature))
}

// CreateQRCodeData 创建二维码数据
func (s *HMACSigner) CreateQRCodeData(orderID uint64, orderNo string, userID, productID, merchantID uint64, verifyCode string) (*QRCodeData, error) {
	now := time.Now().Unix()
	expiresAt := now + s.config.TTL

	data := &QRCodeData{
		OrderID:    orderID,
		OrderNo:    orderNo,
		UserID:     userID,
		ProductID:  productID,
		MerchantID: merchantID,
		VerifyCode: verifyCode,
		Timestamp:  now,
		ExpiresAt:  expiresAt,
	}

	// 生成签名
	signature, err := s.GenerateSignature(data)
	if err != nil {
		return nil, fmt.Errorf("生成签名失败: %w", err)
	}

	data.Signature = signature
	return data, nil
}

// CreateQRCodeDataWithExpiry 创建二维码数据（指定过期时间）
func (s *HMACSigner) CreateQRCodeDataWithExpiry(orderID uint64, orderNo string, userID, productID, merchantID uint64, verifyCode string, expiresAt int64) (*QRCodeData, error) {
	now := time.Now().Unix()

	data := &QRCodeData{
		OrderID:    orderID,
		OrderNo:    orderNo,
		UserID:     userID,
		ProductID:  productID,
		MerchantID: merchantID,
		VerifyCode: verifyCode,
		Timestamp:  now,
		ExpiresAt:  expiresAt,
	}

	// 生成签名
	signature, err := s.GenerateSignature(data)
	if err != nil {
		return nil, fmt.Errorf("生成签名失败: %w", err)
	}

	data.Signature = signature
	return data, nil
}

// ValidateQRCodeData 验证二维码数据
func (s *HMACSigner) ValidateQRCodeData(data *QRCodeData) error {
	// 检查时间戳
	now := time.Now().Unix()
	if data.ExpiresAt < now {
		return fmt.Errorf("二维码已过期")
	}

	// 验证签名
	if !s.VerifySignature(data) {
		return fmt.Errorf("签名验证失败")
	}

	return nil
}

// EncodeQRCodeData 编码二维码数据为字符串
func (s *HMACSigner) EncodeQRCodeData(data *QRCodeData) string {
	return fmt.Sprintf("%d|%s|%d|%d|%d|%s|%d|%d|%s",
		data.OrderID,
		data.OrderNo,
		data.UserID,
		data.ProductID,
		data.MerchantID,
		data.VerifyCode,
		data.Timestamp,
		data.ExpiresAt,
		data.Signature,
	)
}

// DecodeQRCodeData 解码二维码数据字符串
func (s *HMACSigner) DecodeQRCodeData(qrString string) (*QRCodeData, error) {
	parts := strings.Split(qrString, "|")
	if len(parts) != 9 {
		return nil, fmt.Errorf("二维码格式错误")
	}

	data := &QRCodeData{}

	// 解析各个字段
	if _, err := fmt.Sscanf(parts[0], "%d", &data.OrderID); err != nil {
		return nil, fmt.Errorf("解析订单ID失败: %w", err)
	}

	data.OrderNo = parts[1]

	if _, err := fmt.Sscanf(parts[2], "%d", &data.UserID); err != nil {
		return nil, fmt.Errorf("解析用户ID失败: %w", err)
	}

	if _, err := fmt.Sscanf(parts[3], "%d", &data.ProductID); err != nil {
		return nil, fmt.Errorf("解析商品ID失败: %w", err)
	}

	if _, err := fmt.Sscanf(parts[4], "%d", &data.MerchantID); err != nil {
		return nil, fmt.Errorf("解析商家ID失败: %w", err)
	}

	data.VerifyCode = parts[5]

	if _, err := fmt.Sscanf(parts[6], "%d", &data.Timestamp); err != nil {
		return nil, fmt.Errorf("解析时间戳失败: %w", err)
	}

	if _, err := fmt.Sscanf(parts[7], "%d", &data.ExpiresAt); err != nil {
		return nil, fmt.Errorf("解析过期时间失败: %w", err)
	}

	data.Signature = parts[8]

	return data, nil
}

// GetDefaultConfig 获取默认配置
func GetDefaultConfig() *HMACConfig {
	return &HMACConfig{
		SecretKey: "wangfujing_qrcode_secret_key_2024", // 生产环境应该从配置文件读取
		TTL:       7 * 24 * 3600,                       // 7天有效期
	}
}
