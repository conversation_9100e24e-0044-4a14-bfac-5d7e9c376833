package timeutil

import (
	"time"
)

// 时间格式常量
const (
	// StandardFormat 标准时间格式 - 用于一般时间字段
	StandardFormat = "2006-01-02 15:04:05"
	
	// ActivityFormat 活动时间格式 - 用于营销活动、商家活动、积分商城
	ActivityFormat = "2006-01-02 15:04"
	
	// DateFormat 日期格式
	DateFormat = "2006-01-02"
)

// FormatTime 格式化时间为标准格式 (2025-07-10 12:00:00)
func FormatTime(t *time.Time) string {
	if t == nil || t.<PERSON>() {
		return ""
	}
	return t.Format(StandardFormat)
}

// FormatTimeValue 格式化时间值为标准格式 (2025-07-10 12:00:00)
func FormatTimeValue(t time.Time) string {
	if t.IsZero() {
		return ""
	}
	return t.Format(StandardFormat)
}

// FormatActivityTime 格式化活动时间为活动格式 (2025-07-10 12:00)
func FormatActivityTime(t *time.Time) string {
	if t == nil || t.<PERSON><PERSON><PERSON>() {
		return ""
	}
	return t.Format(ActivityFormat)
}

// FormatActivityTimeValue 格式化活动时间值为活动格式 (2025-07-10 12:00)
func FormatActivityTimeValue(t time.Time) string {
	if t.IsZero() {
		return ""
	}
	return t.Format(ActivityFormat)
}

// FormatDate 格式化日期 (2025-07-10)
func FormatDate(t *time.Time) string {
	if t == nil || t.IsZero() {
		return ""
	}
	return t.Format(DateFormat)
}

// FormatDateValue 格式化日期值 (2025-07-10)
func FormatDateValue(t time.Time) string {
	if t.IsZero() {
		return ""
	}
	return t.Format(DateFormat)
}

// TimeResponse 统一的时间响应结构
type TimeResponse struct {
	CreatedAt    string `json:"created_at,omitempty"`
	UpdatedAt    string `json:"updated_at,omitempty"`
	DeletedAt    string `json:"deleted_at,omitempty"`
	StartTime    string `json:"start_time,omitempty"`
	EndTime      string `json:"end_time,omitempty"`
	ValidFrom    string `json:"valid_from,omitempty"`
	ValidTo      string `json:"valid_to,omitempty"`
	RegisterTime string `json:"register_time,omitempty"`
	ApprovedAt   string `json:"approved_at,omitempty"`
	SubmittedAt  string `json:"submitted_at,omitempty"`
	LastLoginAt  string `json:"last_login_at,omitempty"`
}

// FormatStandardTimes 格式化标准时间字段（用于一般业务）
func FormatStandardTimes(createdAt, updatedAt, deletedAt *time.Time) TimeResponse {
	return TimeResponse{
		CreatedAt: FormatTime(createdAt),
		UpdatedAt: FormatTime(updatedAt),
		DeletedAt: FormatTime(deletedAt),
	}
}

// FormatActivityTimes 格式化活动时间字段（用于营销活动、商家活动、积分商城）
func FormatActivityTimes(startTime, endTime *time.Time) TimeResponse {
	return TimeResponse{
		StartTime: FormatActivityTime(startTime),
		EndTime:   FormatActivityTime(endTime),
	}
}

// FormatProductTimes 格式化商品时间字段（积分商城商品）
func FormatProductTimes(startTime, endTime, validFrom, validTo *time.Time) TimeResponse {
	return TimeResponse{
		StartTime: FormatActivityTime(startTime),
		EndTime:   FormatActivityTime(endTime),
		ValidFrom: FormatActivityTime(validFrom),
		ValidTo:   FormatActivityTime(validTo),
	}
}

// FormatAllTimes 格式化所有时间字段（混合使用）
func FormatAllTimes(createdAt, updatedAt, deletedAt, startTime, endTime *time.Time, isActivity bool) TimeResponse {
	response := TimeResponse{
		CreatedAt: FormatTime(createdAt),
		UpdatedAt: FormatTime(updatedAt),
		DeletedAt: FormatTime(deletedAt),
	}
	
	if isActivity {
		response.StartTime = FormatActivityTime(startTime)
		response.EndTime = FormatActivityTime(endTime)
	} else {
		response.StartTime = FormatTime(startTime)
		response.EndTime = FormatTime(endTime)
	}
	
	return response
}

// BuildTimeFields 构建时间字段映射（用于gin.H）
func BuildTimeFields(createdAt, updatedAt *time.Time) map[string]interface{} {
	fields := make(map[string]interface{})
	
	if createdAt != nil && !createdAt.IsZero() {
		fields["created_at"] = FormatTime(createdAt)
	}
	
	if updatedAt != nil && !updatedAt.IsZero() {
		fields["updated_at"] = FormatTime(updatedAt)
	}
	
	return fields
}

// BuildActivityTimeFields 构建活动时间字段映射（用于gin.H）
func BuildActivityTimeFields(startTime, endTime *time.Time) map[string]interface{} {
	fields := make(map[string]interface{})
	
	if startTime != nil && !startTime.IsZero() {
		fields["start_time"] = FormatActivityTime(startTime)
	}
	
	if endTime != nil && !endTime.IsZero() {
		fields["end_time"] = FormatActivityTime(endTime)
	}
	
	return fields
}

// BuildAllTimeFields 构建所有时间字段映射（用于gin.H）
func BuildAllTimeFields(createdAt, updatedAt, startTime, endTime *time.Time, isActivity bool) map[string]interface{} {
	fields := BuildTimeFields(createdAt, updatedAt)
	
	if isActivity {
		activityFields := BuildActivityTimeFields(startTime, endTime)
		for k, v := range activityFields {
			fields[k] = v
		}
	} else {
		if startTime != nil && !startTime.IsZero() {
			fields["start_time"] = FormatTime(startTime)
		}
		if endTime != nil && !endTime.IsZero() {
			fields["end_time"] = FormatTime(endTime)
		}
	}
	
	return fields
}
