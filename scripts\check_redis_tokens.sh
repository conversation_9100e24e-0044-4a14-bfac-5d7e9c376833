#!/bin/bash

# Redis Token检查脚本
# 用于调试token失效问题

echo "=== Redis Token 检查脚本 ==="
echo "时间: $(date)"
echo ""

# 检查Redis连接
echo "1. 检查Redis连接..."
redis-cli ping
if [ $? -eq 0 ]; then
    echo "✅ Redis连接正常"
else
    echo "❌ Redis连接失败"
    exit 1
fi
echo ""

# 检查用户token相关的keys
echo "2. 检查用户token keys..."
USER_TOKEN_KEYS=$(redis-cli keys "user_token:*")
USER_REFRESH_TOKEN_KEYS=$(redis-cli keys "user_refresh_token:*")

echo "用户token keys:"
if [ -z "$USER_TOKEN_KEYS" ]; then
    echo "❌ 没有找到用户token"
else
    echo "$USER_TOKEN_KEYS"
fi
echo ""

echo "用户refresh token keys:"
if [ -z "$USER_REFRESH_TOKEN_KEYS" ]; then
    echo "❌ 没有找到用户refresh token"
else
    echo "$USER_REFRESH_TOKEN_KEYS"
fi
echo ""

# 检查特定用户的token（用户ID=4）
USER_ID=4
echo "3. 检查用户ID=$USER_ID 的token..."

# 检查access token
ACCESS_TOKEN=$(redis-cli get "user_token:$USER_ID")
if [ -z "$ACCESS_TOKEN" ]; then
    echo "❌ 用户$USER_ID 的access token不存在"
else
    echo "✅ 用户$USER_ID 的access token存在"
    echo "Token长度: ${#ACCESS_TOKEN}"
    echo "Token前50字符: ${ACCESS_TOKEN:0:50}..."
    
    # 检查TTL
    TTL=$(redis-cli ttl "user_token:$USER_ID")
    echo "Token TTL: $TTL 秒"
    if [ "$TTL" -eq -1 ]; then
        echo "⚠️  Token没有设置过期时间"
    elif [ "$TTL" -eq -2 ]; then
        echo "❌ Token已过期"
    else
        echo "✅ Token还有 $TTL 秒过期"
    fi
fi
echo ""

# 检查refresh token
REFRESH_TOKEN=$(redis-cli get "user_refresh_token:$USER_ID")
if [ -z "$REFRESH_TOKEN" ]; then
    echo "❌ 用户$USER_ID 的refresh token不存在"
else
    echo "✅ 用户$USER_ID 的refresh token存在"
    echo "Refresh Token长度: ${#REFRESH_TOKEN}"
    echo "Refresh Token前50字符: ${REFRESH_TOKEN:0:50}..."
    
    # 检查TTL
    REFRESH_TTL=$(redis-cli ttl "user_refresh_token:$USER_ID")
    echo "Refresh Token TTL: $REFRESH_TTL 秒"
    if [ "$REFRESH_TTL" -eq -1 ]; then
        echo "⚠️  Refresh Token没有设置过期时间"
    elif [ "$REFRESH_TTL" -eq -2 ]; then
        echo "❌ Refresh Token已过期"
    else
        echo "✅ Refresh Token还有 $REFRESH_TTL 秒过期"
    fi
fi
echo ""

# 检查管理员token（如果存在）
echo "4. 检查管理员token keys..."
ADMIN_TOKEN_KEYS=$(redis-cli keys "admin_token:*")
if [ -z "$ADMIN_TOKEN_KEYS" ]; then
    echo "没有找到管理员token"
else
    echo "管理员token keys:"
    echo "$ADMIN_TOKEN_KEYS"
fi
echo ""

# 检查Redis内存使用情况
echo "5. Redis内存使用情况..."
redis-cli info memory | grep used_memory_human
echo ""

# 检查Redis配置
echo "6. Redis配置检查..."
echo "最大内存: $(redis-cli config get maxmemory | tail -1)"
echo "过期策略: $(redis-cli config get maxmemory-policy | tail -1)"
echo ""

# 提供清理命令
echo "=== 故障排除命令 ==="
echo ""
echo "如果需要清理用户$USER_ID 的token:"
echo "redis-cli del user_token:$USER_ID"
echo "redis-cli del user_refresh_token:$USER_ID"
echo ""
echo "如果需要清理所有用户token:"
echo "redis-cli keys 'user_token:*' | xargs redis-cli del"
echo "redis-cli keys 'user_refresh_token:*' | xargs redis-cli del"
echo ""
echo "检查应用日志:"
echo "tail -f logs/admin.log | grep -i token"
echo ""
echo "测试token接口:"
echo "curl -X GET 'http://localhost:8888/api/v1/avatars/carousel' -H 'Authorization: Bearer YOUR_TOKEN' -v"
echo ""

echo "=== 检查完成 ==="
