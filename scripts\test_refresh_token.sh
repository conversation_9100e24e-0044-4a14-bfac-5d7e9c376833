#!/bin/bash

# 刷新令牌测试脚本
# 用于验证刷新令牌修复是否生效

echo "=== 刷新令牌测试脚本 ==="
echo "时间: $(date)"
echo ""

# 配置
BASE_URL="http://localhost:8888"
USER_ID=4

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
NC='\033[0m' # No Color

# 函数：打印带颜色的消息
print_success() {
    echo -e "${GREEN}✅ $1${NC}"
}

print_error() {
    echo -e "${RED}❌ $1${NC}"
}

print_warning() {
    echo -e "${YELLOW}⚠️  $1${NC}"
}

print_info() {
    echo -e "ℹ️  $1"
}

# 函数：解析JWT token（简单版本，只提取payload）
decode_jwt() {
    local token=$1
    if [ -z "$token" ]; then
        echo "Token为空"
        return 1
    fi
    
    # 提取payload部分（第二部分）
    local payload=$(echo $token | cut -d'.' -f2)
    
    # 添加padding（如果需要）
    local padding=$((4 - ${#payload} % 4))
    if [ $padding -ne 4 ]; then
        payload="${payload}$(printf '%*s' $padding | tr ' ' '=')"
    fi
    
    # Base64解码
    echo $payload | base64 -d 2>/dev/null | jq . 2>/dev/null || echo "无法解析token"
}

# 步骤1: 检查Redis中的当前token
echo "1. 检查Redis中的当前token..."
CURRENT_ACCESS_TOKEN=$(redis-cli get "user_token:$USER_ID" 2>/dev/null)
CURRENT_REFRESH_TOKEN=$(redis-cli get "user_refresh_token:$USER_ID" 2>/dev/null)

if [ -z "$CURRENT_ACCESS_TOKEN" ] || [ -z "$CURRENT_REFRESH_TOKEN" ]; then
    print_error "Redis中没有找到用户$USER_ID 的token，请先登录"
    echo ""
    echo "登录命令示例:"
    echo "curl -X POST \"$BASE_URL/api/v1/auth/wechat/login\" \\"
    echo "  -H \"Content-Type: application/json\" \\"
    echo "  -d '{\"login_code\": \"your_code\", \"phone_code\": \"your_phone_code\"}'"
    exit 1
fi

print_success "找到当前token"
echo "Access Token前50字符: ${CURRENT_ACCESS_TOKEN:0:50}..."
echo "Refresh Token前50字符: ${CURRENT_REFRESH_TOKEN:0:50}..."
echo ""

# 步骤2: 解析当前的refresh token
echo "2. 解析当前的refresh token..."
echo "Refresh Token Claims:"
decode_jwt "$CURRENT_REFRESH_TOKEN"
echo ""

# 步骤3: 测试当前access token是否有效
echo "3. 测试当前access token..."
RESPONSE=$(curl -s -w "\n%{http_code}" -X GET "$BASE_URL/api/v1/avatars/carousel" \
  -H "Authorization: Bearer $CURRENT_ACCESS_TOKEN")

HTTP_CODE=$(echo "$RESPONSE" | tail -n1)
BODY=$(echo "$RESPONSE" | head -n -1)

if [ "$HTTP_CODE" = "200" ]; then
    print_success "当前access token有效"
else
    print_warning "当前access token无效 (HTTP $HTTP_CODE)"
    echo "响应: $BODY"
fi
echo ""

# 步骤4: 刷新token
echo "4. 刷新token..."
REFRESH_RESPONSE=$(curl -s -w "\n%{http_code}" -X POST "$BASE_URL/api/v1/auth/refresh" \
  -H "Content-Type: application/json" \
  -d "{\"refresh_token\": \"$CURRENT_REFRESH_TOKEN\"}")

REFRESH_HTTP_CODE=$(echo "$REFRESH_RESPONSE" | tail -n1)
REFRESH_BODY=$(echo "$REFRESH_RESPONSE" | head -n -1)

if [ "$REFRESH_HTTP_CODE" = "200" ]; then
    print_success "刷新token成功"
    echo "刷新响应: $REFRESH_BODY"
    
    # 提取新的access token
    NEW_ACCESS_TOKEN=$(echo "$REFRESH_BODY" | jq -r '.data.access_token' 2>/dev/null)
    NEW_REFRESH_TOKEN=$(echo "$REFRESH_BODY" | jq -r '.data.refresh_token' 2>/dev/null)
    
    if [ "$NEW_ACCESS_TOKEN" = "null" ] || [ -z "$NEW_ACCESS_TOKEN" ]; then
        print_error "无法从响应中提取新的access token"
        exit 1
    fi
    
    echo ""
    echo "新Access Token前50字符: ${NEW_ACCESS_TOKEN:0:50}..."
    echo "新Refresh Token前50字符: ${NEW_REFRESH_TOKEN:0:50}..."
else
    print_error "刷新token失败 (HTTP $REFRESH_HTTP_CODE)"
    echo "响应: $REFRESH_BODY"
    exit 1
fi
echo ""

# 步骤5: 解析新的access token
echo "5. 解析新的access token..."
echo "新Access Token Claims:"
decode_jwt "$NEW_ACCESS_TOKEN"
echo ""

# 步骤6: 测试新的access token
echo "6. 测试新的access token..."
NEW_RESPONSE=$(curl -s -w "\n%{http_code}" -X GET "$BASE_URL/api/v1/avatars/carousel" \
  -H "Authorization: Bearer $NEW_ACCESS_TOKEN")

NEW_HTTP_CODE=$(echo "$NEW_RESPONSE" | tail -n1)
NEW_BODY=$(echo "$NEW_RESPONSE" | head -n -1)

if [ "$NEW_HTTP_CODE" = "200" ]; then
    print_success "新access token有效！刷新token修复成功！"
    echo "接口调用成功，返回数据长度: ${#NEW_BODY} 字符"
else
    print_error "新access token无效 (HTTP $NEW_HTTP_CODE)"
    echo "响应: $NEW_BODY"
    
    # 检查调试信息
    DEBUG_INFO=$(curl -s -I -X GET "$BASE_URL/api/v1/avatars/carousel" \
      -H "Authorization: Bearer $NEW_ACCESS_TOKEN" | grep -i "x-debug-info" || echo "")
    
    if [ ! -z "$DEBUG_INFO" ]; then
        echo "调试信息: $DEBUG_INFO"
    fi
fi
echo ""

# 步骤7: 检查Redis中的token是否已更新
echo "7. 检查Redis中的token是否已更新..."
UPDATED_ACCESS_TOKEN=$(redis-cli get "user_token:$USER_ID" 2>/dev/null)
UPDATED_REFRESH_TOKEN=$(redis-cli get "user_refresh_token:$USER_ID" 2>/dev/null)

if [ "$UPDATED_ACCESS_TOKEN" = "$NEW_ACCESS_TOKEN" ]; then
    print_success "Redis中的access token已正确更新"
else
    print_error "Redis中的access token未更新或不匹配"
    echo "Redis中的token: ${UPDATED_ACCESS_TOKEN:0:50}..."
    echo "新生成的token: ${NEW_ACCESS_TOKEN:0:50}..."
fi

if [ "$UPDATED_REFRESH_TOKEN" = "$NEW_REFRESH_TOKEN" ]; then
    print_success "Redis中的refresh token已正确更新"
else
    print_error "Redis中的refresh token未更新或不匹配"
fi
echo ""

# 步骤8: 测试其他接口
echo "8. 测试其他接口..."
OTHER_APIS=(
    "/api/v1/avatars/current"
    "/api/v1/avatars/my-exchanges"
)

for api in "${OTHER_APIS[@]}"; do
    echo "测试接口: $api"
    API_RESPONSE=$(curl -s -w "\n%{http_code}" -X GET "$BASE_URL$api" \
      -H "Authorization: Bearer $NEW_ACCESS_TOKEN")
    
    API_HTTP_CODE=$(echo "$API_RESPONSE" | tail -n1)
    
    if [ "$API_HTTP_CODE" = "200" ]; then
        print_success "接口 $api 调用成功"
    else
        print_error "接口 $api 调用失败 (HTTP $API_HTTP_CODE)"
    fi
done

echo ""
echo "=== 测试完成 ==="

# 总结
if [ "$NEW_HTTP_CODE" = "200" ] && [ "$UPDATED_ACCESS_TOKEN" = "$NEW_ACCESS_TOKEN" ]; then
    print_success "🎉 刷新token功能正常工作！"
else
    print_error "❌ 刷新token功能仍有问题，请检查日志"
fi
