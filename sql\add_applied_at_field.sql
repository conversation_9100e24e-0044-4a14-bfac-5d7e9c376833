-- 为 points_mall_exchanges 表添加 applied_at 字段
-- 用于记录VIP形象的应用时间
--
-- VIP形象有效期规则：
-- 1. 兑换后的形象有效期为当天（从兑换时间到当天23:59:59）
-- 2. 即使已经应用的形象，到期也会自动停用（状态变为已过期）
-- 3. 用户只能有1个正在使用的形象
-- 4. 应用新形象时，之前正在使用的形象自动变为已停用

USE wangfushiji;

-- 添加 applied_at 字段
ALTER TABLE points_mall_exchanges 
ADD COLUMN applied_at DATETIME NULL COMMENT '应用时间' AFTER exchange_time;

-- 验证字段是否添加成功
DESCRIBE points_mall_exchanges;

-- 查看表结构
SHOW CREATE TABLE points_mall_exchanges;

-- 可选：为现有的已使用记录设置应用时间（如果有的话）
-- UPDATE points_mall_exchanges 
-- SET applied_at = updated_at 
-- WHERE status = 2 AND applied_at IS NULL;

-- 验证数据
SELECT 
    id,
    item_id,
    user_id,
    status,
    exchange_time,
    applied_at,
    created_at,
    updated_at
FROM points_mall_exchanges 
ORDER BY created_at DESC 
LIMIT 10;
