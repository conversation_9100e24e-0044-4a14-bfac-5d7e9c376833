-- 添加新的VIP形象数据
-- 使用真实的OSS图片URL

USE wangfushiji;

-- 添加两个新的VIP形象
INSERT INTO `points_mall_items` (
    `type`, 
    `name`, 
    `images`, 
    `description`, 
    `start_time`, 
    `end_time`, 
    `stock`, 
    `points`, 
    `status`, 
    `creator_id`, 
    `sort`, 
    `background_image`, 
    `album_image`, 
    `discount_rate`, 
    `enable_lottery`, 
    `lottery_rate`, 
    `is_enabled`, 
    `created_at`, 
    `updated_at`
) VALUES
-- 梦幻星空头像
(
    2, -- type: VIP形象
    '梦幻星空头像', 
    '["https://wangfushiji.oss-cn-beijing.aliyuncs.com/products/2025/07/25/53e1f412-2e67-430b-8f5e-30e7a48bde92.png"]', 
    '梦幻星空主题VIP头像，璀璨星河伴你闪耀，展现神秘浪漫的个人魅力', 
    NOW(), 
    DATE_ADD(NOW(), INTERVAL 365 DAY), 
    100, -- stock: 库存
    1,   -- points: 兑换积分
    1,   -- status: 启用
    1,   -- creator_id
    11,  -- sort: 排序
    'https://wangfushiji.oss-cn-beijing.aliyuncs.com/products/2025/07/25/a164e46a-c882-4319-b8bb-5c7864e10cdc.jpg', -- background_image: 背景图1
    'https://wangfushiji.oss-cn-beijing.aliyuncs.com/products/2025/07/25/53e1f412-2e67-430b-8f5e-30e7a48bde92.png', -- album_image: 头像图3
    25,  -- discount_rate: 折扣率
    1,   -- enable_lottery: 启用抽卡
    45,  -- lottery_rate: 抽卡成功率45%
    1,   -- is_enabled: 启用
    NOW(), 
    NOW()
),
-- 炫彩光影头像
(
    2, -- type: VIP形象
    '炫彩光影头像', 
    '["https://wangfushiji.oss-cn-beijing.aliyuncs.com/products/2025/07/25/3749dc3f-030f-4a89-8a40-18ff57ee2050.png"]', 
    '炫彩光影主题VIP头像，绚烂色彩展现个性，彰显时尚前卫的独特品味', 
    NOW(), 
    DATE_ADD(NOW(), INTERVAL 365 DAY), 
    100, -- stock: 库存
    1,   -- points: 兑换积分
    1,   -- status: 启用
    1,   -- creator_id
    12,  -- sort: 排序
    'https://wangfushiji.oss-cn-beijing.aliyuncs.com/products/2025/07/25/1e37e33d-e9d0-4f45-9013-05618b967ded.jpg', -- background_image: 背景图2
    'https://wangfushiji.oss-cn-beijing.aliyuncs.com/products/2025/07/25/3749dc3f-030f-4a89-8a40-18ff57ee2050.png', -- album_image: 头像图4
    30,  -- discount_rate: 折扣率
    1,   -- enable_lottery: 启用抽卡
    40,  -- lottery_rate: 抽卡成功率40%
    1,   -- is_enabled: 启用
    NOW(), 
    NOW()
);

-- 查看添加结果
SELECT 
    id,
    type,
    name,
    description,
    points,
    background_image,
    album_image,
    enable_lottery,
    lottery_rate,
    is_enabled,
    created_at
FROM points_mall_items 
WHERE type = 2 
ORDER BY sort DESC 
LIMIT 5;

-- 验证图片URL是否正确
SELECT 
    name,
    '背景图' as image_type,
    background_image as image_url
FROM points_mall_items 
WHERE id IN (
    SELECT id FROM points_mall_items WHERE name IN ('梦幻星空头像', '炫彩光影头像')
)
UNION ALL
SELECT 
    name,
    '相册图' as image_type,
    album_image as image_url
FROM points_mall_items 
WHERE id IN (
    SELECT id FROM points_mall_items WHERE name IN ('梦幻星空头像', '炫彩光影头像')
)
ORDER BY name, image_type;
