-- 王府井商城业务数据初始化脚本
-- 不包含用户数据，仅包含业务基础数据

-- 设置字符集
SET NAMES utf8mb4 COLLATE utf8mb4_general_ci;

-- 1. 楼层数据
INSERT INTO `floors` (`name`, `sort`, `map_image`, `description`, `status`, `created_at`, `updated_at`) VALUES
('B2层', 1, 'https://example.com/floor_b2.jpg', '地下二层，停车场及仓储区域', 1, NOW(), NOW()),
('B1层', 2, 'https://example.com/floor_b1.jpg', '地下一层，美食广场及超市', 1, NOW(), NOW()),
('1F', 3, 'https://example.com/floor_1f.jpg', '一层，奢侈品牌及化妆品', 1, NOW(), NOW()),
('2F', 4, 'https://example.com/floor_2f.jpg', '二层，女装及配饰', 1, NOW(), NOW()),
('3F', 5, 'https://example.com/floor_3f.jpg', '三层，男装及运动品牌', 1, NOW(), NOW()),
('4F', 6, 'https://example.com/floor_4f.jpg', '四层，儿童用品及玩具', 1, NOW(), NOW()),
('5F', 7, 'https://example.com/floor_5f.jpg', '五层，数码电器及家居', 1, NOW(), NOW()),
('6F', 8, 'https://example.com/floor_6f.jpg', '六层，餐饮及娱乐', 1, NOW(), NOW());

-- 2. 商家数据
INSERT INTO `merchants` (`name`, `phone`, `login_phone`, `contact`, `floor_id`, `position`, `description`, `area`, `score`, `level`, `status`, `created_at`, `updated_at`) VALUES
('星巴克咖啡', '010-12345678', '18800001001', '张经理', 2, 'B1-001', '全球知名咖啡连锁品牌', 50.00, 95, 1, 1, NOW(), NOW()),
('优衣库', '010-87654321', '18800001002', '李经理', 4, '2F-001', '日本休闲服装品牌', 120.50, 88, 1, 1, NOW(), NOW()),
('苹果专卖店', '010-11111111', '18800001003', '王经理', 7, '5F-001', '苹果产品官方零售店', 80.00, 92, 1, 1, NOW(), NOW()),
('海底捞火锅', '010-22222222', '18800001004', '刘经理', 8, '6F-001', '知名火锅连锁品牌', 200.00, 90, 1, 1, NOW(), NOW()),
('ZARA', '010-33333333', '18800001005', '陈经理', 4, '2F-002', '西班牙快时尚品牌', 150.00, 85, 1, 1, NOW(), NOW()),
('华为体验店', '010-44444444', '18800001006', '赵经理', 7, '5F-002', '华为产品体验店', 90.00, 89, 1, 1, NOW(), NOW()),
('麦当劳', '010-55555555', '18800001007', '孙经理', 2, 'B1-002', '全球快餐连锁品牌', 60.00, 87, 1, 1, NOW(), NOW()),
('周大福珠宝', '010-66666666', '18800001008', '钱经理', 3, '1F-001', '知名珠宝品牌', 40.00, 93, 1, 1, NOW(), NOW()),
('肯德基', '010-77777777', '18800001009', '吴经理', 2, 'B1-003', '全球快餐连锁品牌', 55.00, 86, 1, 1, NOW(), NOW()),
('H&M', '010-88888888', '18800001010', '周经理', 4, '2F-003', '瑞典快时尚品牌', 130.00, 84, 1, 1, NOW(), NOW()),
('小米之家', '010-99999999', '18800001011', '郑经理', 7, '5F-003', '小米产品体验店', 75.00, 88, 1, 1, NOW(), NOW()),
('必胜客', '010-10101010', '18800001012', '何经理', 8, '6F-002', '知名披萨连锁品牌', 180.00, 89, 1, 1, NOW(), NOW());

-- 3. 商品数据
INSERT INTO `products` (`name`, `description`, `images`, `points`, `daily_limit`, `type`, `merchant_id`, `category`, `tags`, `approval_status`, `approver_id`, `approved_at`, `submitted_at`, `valid_from`, `valid_to`, `sort`, `status`, `created_at`, `updated_at`) VALUES
('星巴克拿铁咖啡', '经典拿铁咖啡，香醇浓郁', '["https://example.com/latte.jpg"]', 100, 10, 1, 1, '饮品', '咖啡,热饮', 2, 1, NOW(), NOW(), NOW(), DATE_ADD(NOW(), INTERVAL 30 DAY), 1, 1, NOW(), NOW()),
('优衣库基础T恤', '100%纯棉基础款T恤', '["https://example.com/tshirt.jpg"]', 300, 5, 1, 2, '服装', '服装,T恤', 2, 1, NOW(), NOW(), NOW(), DATE_ADD(NOW(), INTERVAL 90 DAY), 2, 1, NOW(), NOW()),
('iPhone 15 Pro', '苹果最新旗舰手机', '["https://example.com/iphone15.jpg"]', 2000, 1, 1, 3, '数码', '手机,苹果', 2, 1, NOW(), NOW(), NOW(), DATE_ADD(NOW(), INTERVAL 180 DAY), 3, 1, NOW(), NOW()),
('海底捞火锅套餐', '2-3人火锅套餐', '["https://example.com/hotpot.jpg"]', 500, 20, 1, 4, '餐饮', '火锅,套餐', 2, 1, NOW(), NOW(), NOW(), DATE_ADD(NOW(), INTERVAL 30 DAY), 4, 1, NOW(), NOW()),
('ZARA连衣裙', '春季新款连衣裙', '["https://example.com/dress.jpg"]', 800, 3, 1, 5, '服装', '服装,连衣裙', 2, 1, NOW(), NOW(), NOW(), DATE_ADD(NOW(), INTERVAL 90 DAY), 5, 1, NOW(), NOW()),
('华为Mate60 Pro', '华为旗舰手机', '["https://example.com/mate60.jpg"]', 1800, 1, 1, 6, '数码', '手机,华为', 2, 1, NOW(), NOW(), NOW(), DATE_ADD(NOW(), INTERVAL 180 DAY), 6, 1, NOW(), NOW()),
('麦当劳巨无霸套餐', '经典汉堡套餐', '["https://example.com/bigmac.jpg"]', 120, 50, 1, 7, '餐饮', '汉堡,套餐', 2, 1, NOW(), NOW(), NOW(), DATE_ADD(NOW(), INTERVAL 30 DAY), 7, 1, NOW(), NOW()),
('周大福黄金项链', '999足金项链', '["https://example.com/necklace.jpg"]', 5000, 1, 1, 8, '珠宝', '黄金,项链', 2, 1, NOW(), NOW(), NOW(), DATE_ADD(NOW(), INTERVAL 365 DAY), 8, 1, NOW(), NOW()),
('肯德基全家桶', '经典全家桶套餐', '["https://example.com/kfc_bucket.jpg"]', 150, 30, 1, 9, '餐饮', '炸鸡,套餐', 2, 1, NOW(), NOW(), NOW(), DATE_ADD(NOW(), INTERVAL 30 DAY), 9, 1, NOW(), NOW()),
('H&M牛仔裤', '时尚修身牛仔裤', '["https://example.com/jeans.jpg"]', 600, 5, 1, 10, '服装', '服装,牛仔裤', 2, 1, NOW(), NOW(), NOW(), DATE_ADD(NOW(), INTERVAL 90 DAY), 10, 1, NOW(), NOW()),
('小米14 Ultra', '小米旗舰拍照手机', '["https://example.com/mi14.jpg"]', 1500, 2, 1, 11, '数码', '手机,小米', 2, 1, NOW(), NOW(), NOW(), DATE_ADD(NOW(), INTERVAL 180 DAY), 11, 1, NOW(), NOW()),
('必胜客至尊披萨', '经典至尊披萨', '["https://example.com/pizza.jpg"]', 200, 20, 1, 12, '餐饮', '披萨,意式', 2, 1, NOW(), NOW(), NOW(), DATE_ADD(NOW(), INTERVAL 30 DAY), 12, 1, NOW(), NOW());

-- 4. 营销活动数据
INSERT INTO `marketing_activities` (`name`, `description`, `video_name`, `video_url`, `image`, `category`, `type`, `required_points`, `reward_points`, `daily_limit`, `start_time`, `end_time`, `status`, `creator_id`, `sort`, `created_at`, `updated_at`) VALUES
('每日签到活动', '每日签到获得积分奖励', '', '', 'https://example.com/checkin.jpg', 1, 1, 0, 10, 1, '2025-01-01 00:00:00', '2025-12-31 23:59:59', 1, 1, 1, NOW(), NOW()),
('分享有礼', '分享商城获得积分', '分享视频', 'https://example.com/share_video.mp4', 'https://example.com/share.jpg', 3, 1, 0, 20, 3, '2025-01-01 00:00:00', '2025-12-31 23:59:59', 1, 1, 2, NOW(), NOW()),
('购物达人挑战', '完成购物任务获得奖励', '', '', 'https://example.com/shopping.jpg', 2, 2, 50, 100, 1, '2025-07-01 00:00:00', '2025-07-31 23:59:59', 1, 1, 3, NOW(), NOW()),
('新用户注册礼', '新用户注册即送积分', '', '', 'https://example.com/register.jpg', 1, 1, 0, 50, 1, '2025-01-01 00:00:00', '2025-12-31 23:59:59', 1, 1, 4, NOW(), NOW()),
('消费返积分', '消费满额返还积分', '', '', 'https://example.com/cashback.jpg', 1, 1, 0, 0, 10, '2025-01-01 00:00:00', '2025-12-31 23:59:59', 1, 1, 5, NOW(), NOW()),
('周末狂欢', '周末特别活动', '', '', 'https://example.com/weekend.jpg', 2, 2, 100, 200, 2, '2025-07-01 00:00:00', '2025-12-31 23:59:59', 1, 1, 6, NOW(), NOW()),
('生日特惠', '生日月专享活动', '', '', 'https://example.com/birthday.jpg', 1, 1, 0, 88, 1, '2025-01-01 00:00:00', '2025-12-31 23:59:59', 1, 1, 7, NOW(), NOW());

-- 5. 积分商城商品数据（VIP形象等）
INSERT INTO `points_mall_items` (`type`, `name`, `images`, `description`, `start_time`, `end_time`, `stock`, `points`, `status`, `creator_id`, `sort`, `background_image`, `album_image`, `discount_rate`, `enable_lottery`, `lottery_rate`, `is_enabled`, `created_at`, `updated_at`) VALUES
(1, '星巴克咖啡券', '["https://example.com/starbucks_coupon.jpg"]', '星巴克任意饮品券一张', NOW(), DATE_ADD(NOW(), INTERVAL 30 DAY), 50, 100, 1, 1, 1, '', '', 0, 0, 0, 1, NOW(), NOW()),
(1, '优衣库购物券', '["https://example.com/uniqlo_coupon.jpg"]', '优衣库100元购物券', NOW(), DATE_ADD(NOW(), INTERVAL 90 DAY), 20, 800, 1, 1, 2, '', '', 0, 0, 0, 1, NOW(), NOW()),
(2, '超级英雄头像', '["https://example.com/hero_avatar.jpg"]', '限量版超级英雄VIP头像', NOW(), DATE_ADD(NOW(), INTERVAL 365 DAY), 100, 1, 1, 1, 3, 'https://example.com/hero_bg.jpg', 'https://example.com/hero_album.jpg', 10, 1, 80, 1, NOW(), NOW()),
(2, '可爱萌宠头像', '["https://example.com/pet_avatar.jpg"]', '萌宠系列VIP头像', NOW(), DATE_ADD(NOW(), INTERVAL 365 DAY), 100, 1, 1, 1, 4, 'https://example.com/pet_bg.jpg', 'https://example.com/pet_album.jpg', 15, 1, 70, 1, NOW(), NOW()),
(2, '时尚达人头像', '["https://example.com/fashion_avatar.jpg"]', '时尚系列VIP头像', NOW(), DATE_ADD(NOW(), INTERVAL 365 DAY), 100, 1, 1, 1, 5, 'https://example.com/fashion_bg.jpg', 'https://example.com/fashion_album.jpg', 12, 1, 60, 1, NOW(), NOW()),
(1, '麦当劳套餐券', '["https://example.com/mcd_coupon.jpg"]', '麦当劳经典套餐券', NOW(), DATE_ADD(NOW(), INTERVAL 30 DAY), 30, 120, 1, 1, 6, '', '', 0, 0, 0, 1, NOW(), NOW()),
(2, '神秘大奖头像', '["https://example.com/mystery_avatar.jpg"]', '稀有神秘VIP头像', NOW(), DATE_ADD(NOW(), INTERVAL 365 DAY), 10, 1, 1, 1, 7, 'https://example.com/mystery_bg.jpg', 'https://example.com/mystery_album.jpg', 50, 1, 10, 1, NOW(), NOW()),
(1, '华为耳机券', '["https://example.com/huawei_earphone.jpg"]', '华为无线耳机兑换券', NOW(), DATE_ADD(NOW(), INTERVAL 180 DAY), 5, 1500, 1, 1, 8, '', '', 0, 0, 0, 1, NOW(), NOW()),
(2, '运动达人头像', '["https://example.com/sport_avatar.jpg"]', '运动系列VIP头像', NOW(), DATE_ADD(NOW(), INTERVAL 365 DAY), 100, 1, 1, 1, 9, 'https://example.com/sport_bg.jpg', 'https://example.com/sport_album.jpg', 8, 1, 65, 1, NOW(), NOW()),
(2, '商务精英头像', '["https://example.com/business_avatar.jpg"]', '商务系列VIP头像', NOW(), DATE_ADD(NOW(), INTERVAL 365 DAY), 100, 1, 1, 1, 10, 'https://example.com/business_bg.jpg', 'https://example.com/business_album.jpg', 20, 1, 50, 1, NOW(), NOW()),
(2, '梦幻星空头像', '["https://wangfushiji.oss-cn-beijing.aliyuncs.com/products/2025/07/25/53e1f412-2e67-430b-8f5e-30e7a48bde92.png"]', '梦幻星空主题VIP头像，璀璨星河伴你闪耀', NOW(), DATE_ADD(NOW(), INTERVAL 365 DAY), 100, 1, 1, 1, 11, 'https://wangfushiji.oss-cn-beijing.aliyuncs.com/products/2025/07/25/a164e46a-c882-4319-b8bb-5c7864e10cdc.jpg', 'https://wangfushiji.oss-cn-beijing.aliyuncs.com/products/2025/07/25/53e1f412-2e67-430b-8f5e-30e7a48bde92.png', 25, 1, 45, 1, NOW(), NOW()),
(2, '炫彩光影头像', '["https://wangfushiji.oss-cn-beijing.aliyuncs.com/products/2025/07/25/3749dc3f-030f-4a89-8a40-18ff57ee2050.png"]', '炫彩光影主题VIP头像，绚烂色彩展现个性', NOW(), DATE_ADD(NOW(), INTERVAL 365 DAY), 100, 1, 1, 1, 12, 'https://wangfushiji.oss-cn-beijing.aliyuncs.com/products/2025/07/25/1e37e33d-e9d0-4f45-9013-05618b967ded.jpg', 'https://wangfushiji.oss-cn-beijing.aliyuncs.com/products/2025/07/25/3749dc3f-030f-4a89-8a40-18ff57ee2050.png', 30, 1, 40, 1, NOW(), NOW());

-- 6. 分值项目数据

# 用户端VIP形象API文档

## 概述

用户端VIP形象功能包括：
1. 获取VIP形象轮播列表
2. 兑换VIP形象（支持抽卡机制）
3. 查询用户已兑换的形象记录
4. **应用VIP形象（新增）**

## 接口列表

### 1. 获取VIP形象轮播列表

**接口地址：** `GET /api/v1/avatars/carousel`

**请求头：**
```
Authorization: Bearer {token}
```

**响应示例：**
```json
{
  "code": 0,
  "message": "success",
  "data": [
    {
      "id": 1,
      "type": 2,
      "name": "VIP形象1",
      "images": "",
      "description": "形象描述",
      "start_time": null,
      "end_time": null,
      "stock": 1,
      "points": 1,
      "status": 1,
      "creator_id": 1,
      "sort": 0,
      "background_image": "https://example.com/bg.jpg",
      "album_image": "https://example.com/album.jpg",
      "discount_rate": 10,
      "enable_lottery": true,
      "lottery_rate": 50,
      "is_enabled": true,
      "created_at": "2025-01-11 10:00:00",
      "updated_at": "2025-01-11 10:00:00"
    }
  ]
}
```

### 2. 兑换VIP形象

**接口地址：** `POST /api/v1/avatars/{id}/exchange`

**请求头：**
```
Authorization: Bearer {token}
Content-Type: application/json
```

**路径参数：**
- `id`: 形象ID

**请求体：**
```json
{}
```

**响应示例（兑换成功）：**
```json
{
  "code": 0,
  "message": "success",
  "data": {
    "result": "success",
    "message": "兑换成功",
    "is_lottery": true,
    "lottery_win": true,
    "exchange_id": 1,
    "exchange": {
      "id": 1,
      "item_id": 1,
      "user_id": 1,
      "points": 1,
      "exchange_time": "2025-01-11 10:00",
      "status": 1,
      "is_lottery": true,
      "lottery_win": true,
      "created_at": "2025-01-11 10:00:00",
      "updated_at": "2025-01-11 10:00:00"
    },
    "avatar": {
      "id": 1,
      "type": 2,
      "name": "VIP形象1",
      "images": "",
      "description": "形象描述",
      "start_time": null,
      "end_time": null,
      "stock": 1,
      "points": 1,
      "status": 1,
      "creator_id": 1,
      "sort": 0,
      "background_image": "https://example.com/bg.jpg",
      "album_image": "https://example.com/album.jpg",
      "discount_rate": 10,
      "enable_lottery": true,
      "lottery_rate": 50,
      "is_enabled": true,
      "created_at": "2025-01-11 10:00:00",
      "updated_at": "2025-01-11 10:00:00"
    },
    "user_points": 99
  }
}
```

**响应示例（抽卡失败）：**
```json
{
  "code": 0,
  "message": "success",
  "data": {
    "result": "failed",
    "message": "很遗憾~没逮到，再试试吧",
    "is_lottery": true,
    "lottery_win": false
  }
}
```

**错误响应示例：**
```json
{
  "code": 500103,
  "message": "积分余额不足"
}
```

### 3. 获取我的形象兑换记录

**接口地址：** `GET /api/v1/avatars/my-exchanges`

**请求头：**
```
Authorization: Bearer {token}
```

**查询参数：**
- `page`: 页码，默认1
- `size`: 每页数量，默认10，最大100
- `expired_filter`: 过期筛选，可选值：
  - `active`: 只返回未过期的记录（默认）
  - `expired`: 只返回已过期的记录
  - `all`: 返回全部记录

**响应示例：**
```json
{
  "code": 0,
  "message": "success",
  "data": [
    {
      "id": 1,
      "item_id": 1,
      "user_id": 1,
      "points": 1,
      "exchange_time": "2025-01-11 10:00",
      "status": 1,
      "status_text": "已兑换",
      "is_lottery": true,
      "lottery_win": true,
      "is_expired": false,
      "created_at": "2025-01-11 10:00:00",
      "updated_at": "2025-01-11 10:00:00",
      "avatar": {
        "id": 1,
        "type": 2,
        "name": "VIP形象1",
        "images": "",
        "description": "形象描述",
        "start_time": null,
        "end_time": null,
        "stock": 1,
        "points": 1,
        "status": 1,
        "creator_id": 1,
        "sort": 0,
        "background_image": "https://example.com/bg.jpg",
        "album_image": "https://example.com/album.jpg",
        "discount_rate": 10,
        "enable_lottery": true,
        "lottery_rate": 50,
        "is_enabled": true,
        "created_at": "2025-01-11 10:00:00",
        "updated_at": "2025-01-11 10:00:00"
      }
    }
  ],
  "total": 1,
  "page": 1,
  "size": 10
}
```

### 4. 应用VIP形象 ⭐ **新增接口**

**接口地址：** `POST /api/v1/avatars/exchanges/{exchange_id}/apply`

**请求头：**
```
Authorization: Bearer {token}
Content-Type: application/json
```

**路径参数：**
- `exchange_id`: 兑换记录ID

**请求体：**
```json
{}
```

**响应示例（应用成功）：**
```json
{
  "code": 0,
  "message": "success",
  "data": {
    "message": "形象应用成功",
    "exchange": {
      "id": 1,
      "item_id": 1,
      "user_id": 1,
      "points": 1,
      "exchange_time": "2025-01-11 10:00",
      "status": 2,
      "status_text": "已使用",
      "is_lottery": true,
      "lottery_win": true,
      "is_expired": false,
      "applied_at": "2025-01-11 11:30:00",
      "created_at": "2025-01-11 10:00:00",
      "updated_at": "2025-01-11 11:30:00",
      "avatar": {
        "id": 1,
        "type": 2,
        "name": "VIP形象1",
        "images": "",
        "description": "形象描述",
        "background_image": "https://example.com/bg.jpg",
        "album_image": "https://example.com/album.jpg",
        "discount_rate": 10,
        "enable_lottery": true,
        "lottery_rate": 50,
        "is_enabled": true,
        "created_at": "2025-01-11 10:00:00",
        "updated_at": "2025-01-11 10:00:00"
      }
    }
  }
}
```

**错误响应示例：**
```json
{
  "code": 404001,
  "message": "兑换记录不存在"
}
```

```json
{
  "code": 400001,
  "message": "该形象已过期，无法应用"
}
```

```json
{
  "code": 400002,
  "message": "该形象已经应用过了"
}
```

```json
{
  "code": 403001,
  "message": "无权限操作此兑换记录"
}
```

## 字段说明

### VIP形象字段
- `id`: 形象ID
- `type`: 商品类型，2表示VIP形象
- `name`: 形象名称
- `background_image`: 背景图片URL
- `album_image`: 相册图片URL
- `points`: 兑换所需积分
- `enable_lottery`: 是否启用抽卡
- `lottery_rate`: 中奖概率（1-100）
- `discount_rate`: 折扣比例
- `is_enabled`: 是否启用
- `status`: 商品状态（1:已上架）

### 兑换记录字段
- `id`: 兑换记录ID
- `item_id`: 形象ID
- `user_id`: 用户ID
- `points`: 消耗积分
- `exchange_time`: 兑换时间
- `status`: 兑换状态（1:已兑换 2:已使用 3:已过期）
- `status_text`: 兑换状态文本描述
- `is_lottery`: 是否通过抽卡获得
- `lottery_win`: 是否中奖
- `is_expired`: 是否过期（基于status字段判断）
- `applied_at`: 应用时间（仅当status=2时有值）

## 业务规则

1. **兑换限制**：
   - 每个形象每个用户在24小时内只能兑换一次（过期后可重新兑换）
   - 兑换VIP形象固定消耗1积分
   - 用户积分必须足够（至少1积分）
   - 形象必须处于已上架且启用状态

2. **抽卡机制**：
   - 如果形象启用了抽卡（enable_lottery=true），则需要按概率抽卡
   - 抽卡失败会扣除1积分但不创建兑换记录
   - 抽卡成功会扣除1积分并创建兑换记录

3. **有效期和状态管理**：
   - 每个兑换的形象有效期为24小时
   - 系统会自动更新过期记录的status字段为3（已过期）
   - 过期判断基于exchange_time字段和当前时间的差值
   - 过期后形象在小程序中不可查看（用户保存到相册的除外）
   - 过期后可以重新兑换同一形象

4. **应用形象规则** ⭐ **新增**：
   - 只有状态为"已兑换"（status=1）且未过期的形象才能应用
   - 应用后状态变更为"已使用"（status=2）
   - 已使用的形象不能重复应用
   - 已过期的形象不能应用
   - 只能应用自己的兑换记录

5. **积分记录**：
   - 所有兑换操作都会创建积分消费记录，固定扣除1积分
   - 抽卡失败也会记录积分消费

## 过期状态更新机制

系统采用以下方式更新过期状态：

1. **实时更新**：每次调用相关接口时会自动更新过期状态
2. **定时任务**：建议配置定时任务每小时执行一次状态更新
3. **SQL更新语句**：
   ```sql
   UPDATE points_mall_exchanges
   SET status = 3
   WHERE status = 1
   AND exchange_time <= DATE_SUB(NOW(), INTERVAL 24 HOUR);
   ```

## 错误码

- `400001`: 参数错误
- `400002`: 业务逻辑错误（如重复应用）
- `401001`: 未授权
- `403001`: 无权限
- `404001`: 资源不存在
- `500103`: 积分余额不足
- `500001`: 服务器内部错误

## 应用形象的前端交互流程

1. **用户查看已兑换的形象**：
   - 调用 `GET /api/v1/avatars/my-exchanges?expired_filter=active` 获取未过期的形象列表
   - 显示状态为"已兑换"的形象，提供"使用形象"按钮

2. **用户点击"使用形象"**：
   - 调用 `POST /api/v1/avatars/exchanges/{exchange_id}/apply` 应用形象
   - 成功后状态变更为"已使用"，按钮变为"已使用"状态

3. **状态显示**：
   - 已兑换：显示"使用形象"按钮
   - 已使用：显示"已使用"标识，不可再次点击
   - 已过期：显示"已过期"标识，不可使用
INSERT INTO `score_items` (`type`, `name`, `score`, `description`, `created_at`, `updated_at`) VALUES
(1, '服务质量', 5, '商家服务质量评分', NOW(), NOW()),
(2, '商品质量', -3, '商品质量问题扣分', NOW(), NOW()),
(1, '环境卫生', 3, '店铺环境卫生评分', NOW(), NOW()),
(1, '销售业绩', 10, '月度销售业绩奖励', NOW(), NOW()),
(2, '客户投诉', -5, '客户投诉处理扣分', NOW(), NOW()),
(1, '创新营销', 8, '创新营销活动加分', NOW(), NOW()),
(1, '安全管理', 4, '安全管理评分', NOW(), NOW());

-- 7. 等级规则数据
INSERT INTO `level_rules` (`level`, `name`, `min_score`, `max_score`, `benefits`, `color`, `icon`, `description`, `status`, `created_at`, `updated_at`) VALUES
(1, '铜牌商家', 0, 59, '[]', '#CD7F32', 'bronze-icon.png', '新入驻或表现一般的商家', 1, NOW(), NOW()),
(2, '银牌商家', 60, 79, '["延迟闭店30分钟"]', '#C0C0C0', 'silver-icon.png', '表现良好的商家', 1, NOW(), NOW()),
(3, '金牌商家', 80, 89, '["延迟闭店60分钟","优先推广","营销支持"]', '#FFD700', 'gold-icon.png', '表现优秀的商家', 1, NOW(), NOW()),
(4, '钻石商家', 90, 100, '["延迟闭店90分钟","最优推广","专属客服","全方位营销支持"]', '#B9F2FF', 'diamond-icon.png', '表现卓越的商家', 1, NOW(), NOW());

-- 8. 等级权益数据（全局配置，只有一条记录）
INSERT INTO `level_benefits` (`required_score`, `delayed_closing`, `created_at`, `updated_at`) VALUES
(80, 1, NOW(), NOW());

-- 9. 会员规则数据
INSERT INTO `member_rules` (`type`, `name`, `enable_points`, `points`, `enable_discount`, `discount`, `creator_id`, `status`, `created_at`, `updated_at`) VALUES
(1, '注册成功奖励', 1, 50, 0, 10.0, 1, 1, NOW(), NOW()),
(2, '消费成功奖励', 1, 10, 1, 9.5, 1, 1, NOW(), NOW());

-- 提交事务
COMMIT;
