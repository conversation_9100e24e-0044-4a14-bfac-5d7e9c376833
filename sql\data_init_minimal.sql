-- 最小化数据初始化脚本
-- 权限和角色通过代码自动初始化，此处只包含必要的基础数据


-- 王府井小程序数据库初始化数据脚本
-- 注意：表结构由GORM自动迁移创建，此脚本仅包含初始化数据

-- 设置字符集
SET NAMES utf8mb4 COLLATE utf8mb4_general_ci;

-- 插入测试楼层数据
INSERT INTO `floors` (`id`, `name`, `sort`, `map_image`, `description`, `status`) VALUES
(1, 'B1层', 1, 'floor_plan_b1.jpg', '地下一层，主要为餐饮区', 1),
(2, '1F', 2, 'floor_plan_1f.jpg', '一层，主要为服装零售', 1),
(3, '2F', 3, 'floor_plan_2f.jpg', '二层，主要为数码电器', 1);

-- 插入测试商家数据
INSERT INTO `merchants` (`id`, `name`, `phone`, `login_phone`, `contact`, `floor_id`, `position`, `description`, `area`, `score`, `level`, `status`) VALUES
(1, '星巴克咖啡', '010-12345678', '18800001001', '张经理', 1, 'B1-001', '全球知名咖啡连锁品牌', 50.00, 95, 1, 1),
(2, '优衣库', '010-87654321', '18800001002', '李经理', 2, '1F-001', '日本休闲服装品牌', 120.50, 88, 1, 1),
(3, '苹果专卖店', '010-11111111', '18800001003', '王经理', 3, '2F-001', '苹果产品官方零售店', 80.00, 92, 1, 1);

-- 插入超级管理员用户（其他用户通过页面创建）
INSERT INTO `admin_users` (`id`, `phone`, `nickname`, `open_id`, `user_type`, `status`, `register_date`) VALUES
(1, '18701685085', '系统管理员', 'oWfEA7slrOB83XybzXMgPFq3yHk8', 3, 1, NOW());

-- 注意：
-- 1. 权限(permissions)和角色(roles)通过代码自动初始化
-- 2. 角色权限分配(role_permissions)通过代码自动初始化  
-- 3. 超级管理员会自动获得所有权限，无需手动分配角色
-- 4. 其他管理员用户通过管理界面创建和分配角色
-- 5. 商家用户通过微信登录自动创建（基于merchants表的login_phone）
