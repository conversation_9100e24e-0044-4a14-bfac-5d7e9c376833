-- 调试用户积分问题的SQL脚本

USE wangfushiji;

-- 1. 查看用户积分信息
SELECT 
    id,
    nickname,
    points,
    status,
    user_type,
    created_at,
    updated_at
FROM users 
WHERE id = 4  -- 替换为实际的用户ID
ORDER BY id;

-- 2. 查看所有用户的积分情况
SELECT 
    id,
    nickname,
    points,
    status,
    CASE 
        WHEN points >= 1000000 THEN '超级富豪'
        WHEN points >= 100000 THEN '富豪'
        WHEN points >= 10000 THEN '小康'
        WHEN points >= 1000 THEN '普通'
        ELSE '贫困'
    END as wealth_level
FROM users 
WHERE status = 1
ORDER BY points DESC
LIMIT 10;

-- 3. 查看商品积分要求
SELECT 
    id,
    name,
    points,
    status,
    type,
    daily_limit,
    created_at
FROM products 
WHERE status = 1 
AND type = 1  -- 商家商品
ORDER BY points ASC
LIMIT 10;

-- 4. 检查数据类型
DESCRIBE users;
DESCRIBE products;

-- 5. 查看积分相关的订单记录
SELECT 
    o.id,
    o.user_id,
    o.product_id,
    o.points,
    o.status,
    o.created_at,
    u.nickname,
    p.name as product_name
FROM orders o
LEFT JOIN users u ON o.user_id = u.id
LEFT JOIN products p ON o.product_id = p.id
WHERE o.user_id = 4  -- 替换为实际的用户ID
ORDER BY o.created_at DESC
LIMIT 5;

-- 6. 查看积分变动记录（如果有积分记录表）
-- SELECT * FROM points_records WHERE user_id = 4 ORDER BY created_at DESC LIMIT 10;

-- 7. 模拟积分检查逻辑
SELECT 
    u.id as user_id,
    u.points as user_points,
    p.id as product_id,
    p.points as product_points,
    CASE 
        WHEN u.points >= p.points THEN '积分充足'
        ELSE CONCAT('积分不足，差额：', (p.points - u.points))
    END as check_result
FROM users u
CROSS JOIN products p
WHERE u.id = 4  -- 替换为实际的用户ID
AND p.id = 1    -- 替换为实际的商品ID
AND u.status = 1
AND p.status = 1;

-- 8. 检查是否有积分字段的数据类型问题
SELECT 
    COLUMN_NAME,
    DATA_TYPE,
    IS_NULLABLE,
    COLUMN_DEFAULT,
    COLUMN_COMMENT
FROM INFORMATION_SCHEMA.COLUMNS 
WHERE TABLE_SCHEMA = 'wangfushiji' 
AND TABLE_NAME IN ('users', 'products')
AND COLUMN_NAME = 'points';

-- 9. 查看最近的积分变动（如果有相关表）
-- 这里假设有积分记录表，如果没有可以注释掉
/*
SELECT 
    pr.id,
    pr.user_id,
    pr.points_change,
    pr.points_after,
    pr.reason,
    pr.created_at,
    u.nickname
FROM points_records pr
LEFT JOIN users u ON pr.user_id = u.id
WHERE pr.user_id = 4
ORDER BY pr.created_at DESC
LIMIT 10;
*/

-- 10. 检查用户状态和商品状态
SELECT 
    '用户状态检查' as check_type,
    COUNT(*) as count,
    status
FROM users 
WHERE id = 4
GROUP BY status
UNION ALL
SELECT 
    '商品状态检查' as check_type,
    COUNT(*) as count,
    status
FROM products 
WHERE id = 1  -- 替换为实际的商品ID
GROUP BY status;
