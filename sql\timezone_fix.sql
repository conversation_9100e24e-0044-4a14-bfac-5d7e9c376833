-- 时区修复脚本

-- 1. 检查当前时区设置
SELECT @@global.time_zone, @@session.time_zone;

-- 2. 检查当前时间
SELECT NOW() as current_time, UTC_TIMESTAMP() as utc_time;

-- 3. 设置全局时区为中国时区
SET GLOBAL time_zone = '+08:00';

-- 4. 设置当前会话时区为中国时区
SET time_zone = '+08:00';

-- 5. 再次检查时区设置
SELECT @@global.time_zone, @@session.time_zone;

-- 6. 再次检查当前时间
SELECT NOW() as current_time_after_fix, UTC_TIMESTAMP() as utc_time;

-- 7. 如果需要，可以更新已有的错误时间数据
-- 注意：这会将所有时间加8小时，请根据实际情况决定是否执行

-- 更新admin_users表的时间字段（如果需要）
-- UPDATE admin_users SET 
--   created_at = DATE_ADD(created_at, INTERVAL 8 HOUR),
--   updated_at = DATE_ADD(updated_at, INTERVAL 8 HOUR),
--   register_date = DATE_ADD(register_date, INTERVAL 8 HOUR),
--   last_login_at = CASE WHEN last_login_at IS NOT NULL THEN DATE_ADD(last_login_at, INTERVAL 8 HOUR) ELSE NULL END
-- WHERE created_at < '2025-07-10 12:00:00';

-- 更新营销活动表的时间字段（如果需要）
-- UPDATE marketing_activities SET 
--   created_at = DATE_ADD(created_at, INTERVAL 8 HOUR),
--   updated_at = DATE_ADD(updated_at, INTERVAL 8 HOUR),
--   start_time = CASE WHEN start_time IS NOT NULL THEN DATE_ADD(start_time, INTERVAL 8 HOUR) ELSE NULL END,
--   end_time = CASE WHEN end_time IS NOT NULL THEN DATE_ADD(end_time, INTERVAL 8 HOUR) ELSE NULL END
-- WHERE created_at < '2025-07-10 12:00:00';

-- 更新商家活动表的时间字段（如果需要）
-- UPDATE merchant_activities SET 
--   created_at = DATE_ADD(created_at, INTERVAL 8 HOUR),
--   updated_at = DATE_ADD(updated_at, INTERVAL 8 HOUR),
--   start_time = CASE WHEN start_time IS NOT NULL THEN DATE_ADD(start_time, INTERVAL 8 HOUR) ELSE NULL END,
--   end_time = CASE WHEN end_time IS NOT NULL THEN DATE_ADD(end_time, INTERVAL 8 HOUR) ELSE NULL END
-- WHERE created_at < '2025-07-10 12:00:00';

-- 更新积分商城表的时间字段（如果需要）
-- UPDATE points_mall_items SET 
--   created_at = DATE_ADD(created_at, INTERVAL 8 HOUR),
--   updated_at = DATE_ADD(updated_at, INTERVAL 8 HOUR),
--   start_time = CASE WHEN start_time IS NOT NULL THEN DATE_ADD(start_time, INTERVAL 8 HOUR) ELSE NULL END,
--   end_time = CASE WHEN end_time IS NOT NULL THEN DATE_ADD(end_time, INTERVAL 8 HOUR) ELSE NULL END
-- WHERE created_at < '2025-07-10 12:00:00';
