-- VIP形象每日过期处理SQL脚本
-- 每天0点执行，处理过期的VIP形象

USE wangfushiji;

-- ========================================
-- VIP形象过期处理逻辑
-- ========================================

-- 1. 将0点前兑换的"已兑换"状态记录设为"已过期"
UPDATE points_mall_exchanges 
SET status = 3, updated_at = NOW()
WHERE status = 1 
AND DATE(exchange_time) < CURDATE()
AND lottery_win = 1;

-- 2. 将0点前兑换或应用的"正在使用"状态记录设为"已停用"
UPDATE points_mall_exchanges 
SET status = 4, updated_at = NOW()
WHERE status = 2 
AND (DATE(exchange_time) < CURDATE() 
     OR (applied_at IS NOT NULL AND DATE(applied_at) < CURDATE()))
AND lottery_win = 1;

-- ========================================
-- 查看处理结果
-- ========================================

-- 查看今天的处理统计
SELECT 
    '处理结果统计' as description,
    SUM(CASE WHEN status = 1 THEN 1 ELSE 0 END) as current_redeemed,
    SUM(CASE WHEN status = 2 THEN 1 ELSE 0 END) as current_in_use,
    SUM(CASE WHEN status = 3 THEN 1 ELSE 0 END) as current_expired,
    SUM(CASE WHEN status = 4 THEN 1 ELSE 0 END) as current_deactivated,
    COUNT(*) as total_records
FROM points_mall_exchanges 
WHERE lottery_win = 1;

-- 查看今天更新的记录
SELECT 
    '今天处理的记录' as description,
    status,
    CASE status 
        WHEN 1 THEN '已兑换'
        WHEN 2 THEN '正在使用'
        WHEN 3 THEN '已过期'
        WHEN 4 THEN '已停用'
    END as status_text,
    COUNT(*) as count
FROM points_mall_exchanges 
WHERE lottery_win = 1
AND DATE(updated_at) = CURDATE()
GROUP BY status
ORDER BY status;

-- ========================================
-- 数据一致性检查
-- ========================================

-- 检查是否有用户有多个正在使用的形象
SELECT 
    '数据一致性检查' as description,
    user_id,
    COUNT(*) as in_use_count,
    GROUP_CONCAT(id) as exchange_ids
FROM points_mall_exchanges 
WHERE status = 2  -- 正在使用
AND lottery_win = 1
GROUP BY user_id
HAVING COUNT(*) > 1;

-- 如果上面的查询有结果，说明存在数据不一致

-- ========================================
-- Linux Crontab 配置示例
-- ========================================
/*
在服务器上设置crontab任务，每天0点执行：

1. 编辑crontab：
   crontab -e

2. 添加以下行：
   0 0 * * * mysql -h localhost -u root -p'your_password' wangfushiji < /path/to/vip_avatar_daily_expiry_simple.sql >> /var/log/vip_avatar_expiry.log 2>&1

3. 或者使用更安全的方式（使用配置文件）：
   0 0 * * * mysql --defaults-file=/etc/mysql/cron.cnf wangfushiji < /path/to/vip_avatar_daily_expiry_simple.sql >> /var/log/vip_avatar_expiry.log 2>&1

配置文件 /etc/mysql/cron.cnf 内容：
[client]
host=localhost
user=root
password=your_password

4. 查看crontab任务：
   crontab -l

5. 查看执行日志：
   tail -f /var/log/vip_avatar_expiry.log
*/

-- ========================================
-- 手动执行测试
-- ========================================
/*
手动测试过期处理：

1. 查看当前状态：
   SELECT status, COUNT(*) FROM points_mall_exchanges WHERE lottery_win = 1 GROUP BY status;

2. 创建测试数据（可选）：
   INSERT INTO points_mall_exchanges (item_id, user_id, points, exchange_time, status, lottery_win, created_at, updated_at)
   VALUES (1, 1, 1, DATE_SUB(NOW(), INTERVAL 1 DAY), 1, 1, NOW(), NOW());

3. 执行过期处理：
   运行上面的UPDATE语句

4. 验证结果：
   SELECT status, COUNT(*) FROM points_mall_exchanges WHERE lottery_win = 1 GROUP BY status;
*/

-- ========================================
-- 监控查询
-- ========================================

-- 查看最近7天的过期处理情况
SELECT 
    DATE(updated_at) as process_date,
    status,
    CASE status 
        WHEN 3 THEN '已过期'
        WHEN 4 THEN '已停用'
    END as status_text,
    COUNT(*) as count
FROM points_mall_exchanges 
WHERE lottery_win = 1
AND status IN (3, 4)
AND DATE(updated_at) >= DATE_SUB(CURDATE(), INTERVAL 7 DAY)
GROUP BY DATE(updated_at), status
ORDER BY process_date DESC, status;

-- 查看需要过期但还没处理的记录（异常情况）
SELECT 
    '异常检查：需要过期但未处理' as description,
    status,
    COUNT(*) as count
FROM points_mall_exchanges 
WHERE lottery_win = 1
AND (
    (status = 1 AND DATE(exchange_time) < CURDATE()) OR
    (status = 2 AND (DATE(exchange_time) < CURDATE() OR (applied_at IS NOT NULL AND DATE(applied_at) < CURDATE())))
)
GROUP BY status;
