-- VIP形象每日过期处理定时任务SQL脚本
-- 每天0点自动执行，处理过期的VIP形象

USE wangfushiji;

-- ========================================
-- 1. 创建存储过程：VIP形象过期处理
-- ========================================
DELIMITER $$

DROP PROCEDURE IF EXISTS ProcessVipAvatarExpiry$$

CREATE PROCEDURE ProcessVipAvatarExpiry()
BEGIN
    DECLARE done INT DEFAULT FALSE;
    DECLARE v_expired_redeemed_count INT DEFAULT 0;
    DECLARE v_expired_in_use_count INT DEFAULT 0;
    DECLARE v_current_time DATETIME DEFAULT NOW();
    DECLARE v_today_date DATE DEFAULT CURDATE();
    
    -- 声明异常处理
    DECLARE EXIT HANDLER FOR SQLEXCEPTION
    BEGIN
        ROLLBACK;
        RESIGNAL;
    END;
    
    -- 开始事务
    START TRANSACTION;
    
    -- 记录处理开始
    INSERT INTO vip_avatar_expiry_logs (process_date, start_time, status, message)
    VALUES (v_today_date, v_current_time, 'STARTED', 'VIP形象过期处理开始');
    
    -- 1. 将0点前兑换的"已兑换"状态记录设为"已过期"
    UPDATE points_mall_exchanges 
    SET status = 3, updated_at = v_current_time
    WHERE status = 1 
    AND DATE(exchange_time) < v_today_date
    AND lottery_win = 1;
    
    SET v_expired_redeemed_count = ROW_COUNT();
    
    -- 2. 将0点前兑换或应用的"正在使用"状态记录设为"已过期"
    UPDATE points_mall_exchanges 
    SET status = 3, updated_at = v_current_time
    WHERE status = 2 
    AND (DATE(exchange_time) < v_today_date 
         OR (applied_at IS NOT NULL AND DATE(applied_at) < v_today_date))
    AND lottery_win = 1;
    
    SET v_expired_in_use_count = ROW_COUNT();
    
    -- 记录处理完成
    INSERT INTO vip_avatar_expiry_logs (process_date, start_time, end_time, status, message, expired_redeemed_count, expired_in_use_count)
    VALUES (v_today_date, v_current_time, NOW(), 'COMPLETED', 
            CONCAT('VIP形象过期处理完成，已兑换过期:', v_expired_redeemed_count, '，正在使用过期:', v_expired_in_use_count),
            v_expired_redeemed_count, v_expired_in_use_count);
    
    -- 提交事务
    COMMIT;
    
    -- 输出处理结果
    SELECT 
        v_today_date as process_date,
        v_expired_redeemed_count as expired_redeemed_count,
        v_expired_in_use_count as expired_in_use_count,
        (v_expired_redeemed_count + v_expired_in_use_count) as total_expired_count,
        'SUCCESS' as status;
        
END$$

DELIMITER ;

-- ========================================
-- 2. 创建日志表（如果不存在）
-- ========================================
CREATE TABLE IF NOT EXISTS vip_avatar_expiry_logs (
    id BIGINT AUTO_INCREMENT PRIMARY KEY COMMENT '日志ID',
    process_date DATE NOT NULL COMMENT '处理日期',
    start_time DATETIME NOT NULL COMMENT '开始时间',
    end_time DATETIME NULL COMMENT '结束时间',
    status ENUM('STARTED', 'COMPLETED', 'FAILED') NOT NULL COMMENT '处理状态',
    message TEXT COMMENT '处理消息',
    expired_redeemed_count INT DEFAULT 0 COMMENT '过期的已兑换数量',
    expired_in_use_count INT DEFAULT 0 COMMENT '过期的正在使用数量',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    INDEX idx_process_date (process_date),
    INDEX idx_status (status),
    INDEX idx_created_at (created_at)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci COMMENT='VIP形象过期处理日志表';

-- ========================================
-- 3. 手动执行过期处理（测试用）
-- ========================================
-- 调用存储过程处理过期形象
-- CALL ProcessVipAvatarExpiry();

-- ========================================
-- 4. 查看处理日志
-- ========================================
-- 查看最近的处理日志
SELECT 
    id,
    process_date,
    start_time,
    end_time,
    status,
    message,
    expired_redeemed_count,
    expired_in_use_count,
    (expired_redeemed_count + expired_in_use_count) as total_expired,
    TIMESTAMPDIFF(SECOND, start_time, end_time) as duration_seconds
FROM vip_avatar_expiry_logs 
ORDER BY created_at DESC 
LIMIT 10;

-- ========================================
-- 5. 统计信息查询
-- ========================================
-- 查看当前VIP形象状态分布
SELECT 
    status,
    CASE status 
        WHEN 1 THEN '已兑换'
        WHEN 2 THEN '正在使用'
        WHEN 3 THEN '已过期'
        WHEN 4 THEN '已停用'
        ELSE '未知'
    END as status_text,
    COUNT(*) as count,
    ROUND(COUNT(*) * 100.0 / (SELECT COUNT(*) FROM points_mall_exchanges WHERE lottery_win = 1), 2) as percentage
FROM points_mall_exchanges 
WHERE lottery_win = 1
GROUP BY status
ORDER BY status;

-- 查看今天需要过期的记录
SELECT 
    '需要过期的已兑换' as type,
    COUNT(*) as count
FROM points_mall_exchanges 
WHERE status = 1 
AND DATE(exchange_time) < CURDATE()
AND lottery_win = 1

UNION ALL

SELECT 
    '需要过期的正在使用' as type,
    COUNT(*) as count
FROM points_mall_exchanges 
WHERE status = 2 
AND (DATE(exchange_time) < CURDATE() 
     OR (applied_at IS NOT NULL AND DATE(applied_at) < CURDATE()))
AND lottery_win = 1;

-- ========================================
-- 6. 定时任务设置说明
-- ========================================
/*
由于MySQL的事件调度器在某些环境下可能不稳定，建议使用以下方式之一：

方式1：Linux Crontab（推荐）
在服务器上设置crontab任务：
0 0 * * * mysql -h localhost -u root -p'password' wangfushiji -e "CALL ProcessVipAvatarExpiry();" >> /var/log/vip_avatar_expiry.log 2>&1

方式2：应用程序内置定时任务（已实现）
在Go应用程序中使用cron库，每天0点执行过期处理

方式3：MySQL事件调度器（可选）
-- 启用事件调度器
SET GLOBAL event_scheduler = ON;

-- 创建定时事件
CREATE EVENT IF NOT EXISTS vip_avatar_daily_expiry
ON SCHEDULE EVERY 1 DAY
STARTS CONCAT(CURDATE() + INTERVAL 1 DAY, ' 00:00:00')
DO
  CALL ProcessVipAvatarExpiry();

-- 查看事件状态
SHOW EVENTS LIKE 'vip_avatar_daily_expiry';
*/

-- ========================================
-- 7. 监控和报警查询
-- ========================================
-- 检查是否有处理失败的记录
SELECT 
    process_date,
    start_time,
    status,
    message
FROM vip_avatar_expiry_logs 
WHERE status = 'FAILED'
ORDER BY created_at DESC;

-- 检查是否有超过1天没有处理的情况
SELECT 
    CURDATE() as today,
    MAX(process_date) as last_process_date,
    DATEDIFF(CURDATE(), MAX(process_date)) as days_since_last_process
FROM vip_avatar_expiry_logs 
WHERE status = 'COMPLETED';

-- 检查数据一致性（每个用户只能有1个正在使用的形象）
SELECT 
    user_id,
    COUNT(*) as in_use_count,
    GROUP_CONCAT(id) as exchange_ids
FROM points_mall_exchanges 
WHERE status = 2  -- 正在使用
AND lottery_win = 1
GROUP BY user_id
HAVING COUNT(*) > 1;
