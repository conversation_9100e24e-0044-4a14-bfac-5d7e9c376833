-- VIP形象有效期管理SQL脚本
-- 用于手动检查和更新过期的VIP形象

USE wangfushiji;

-- ========================================
-- 1. 查看当前VIP形象状态分布
-- ========================================
SELECT 
    status,
    CASE status 
        WHEN 1 THEN '已兑换'
        WHEN 2 THEN '正在使用'
        WHEN 3 THEN '已过期'
        WHEN 4 THEN '已停用'
        ELSE '未知'
    END as status_text,
    COUNT(*) as count
FROM points_mall_exchanges 
WHERE lottery_win = 1  -- 只统计中奖的记录
GROUP BY status
ORDER BY status;

-- ========================================
-- 2. 查看今天兑换的VIP形象
-- ========================================
SELECT 
    id,
    user_id,
    item_id,
    status,
    CASE status 
        WHEN 1 THEN '已兑换'
        WHEN 2 THEN '正在使用'
        WHEN 3 THEN '已过期'
        WHEN 4 THEN '已停用'
    END as status_text,
    exchange_time,
    applied_at,
    CASE 
        WHEN DATE(exchange_time) = CURDATE() THEN '今天'
        ELSE '非今天'
    END as is_today
FROM points_mall_exchanges 
WHERE lottery_win = 1
AND DATE(exchange_time) = CURDATE()
ORDER BY exchange_time DESC;

-- ========================================
-- 3. 查看需要过期的VIP形象（非今天兑换的）
-- ========================================
SELECT 
    id,
    user_id,
    item_id,
    status,
    CASE status 
        WHEN 1 THEN '已兑换'
        WHEN 2 THEN '正在使用'
        WHEN 3 THEN '已过期'
        WHEN 4 THEN '已停用'
    END as status_text,
    exchange_time,
    applied_at,
    DATEDIFF(CURDATE(), DATE(exchange_time)) as days_ago
FROM points_mall_exchanges 
WHERE lottery_win = 1
AND DATE(exchange_time) < CURDATE()  -- 非今天兑换的
AND status IN (1, 2)  -- 已兑换或正在使用状态
ORDER BY exchange_time DESC;

-- ========================================
-- 4. 手动更新过期的VIP形象
-- ========================================
-- 更新非今天兑换的已兑换状态记录为已过期
UPDATE points_mall_exchanges 
SET status = 3, updated_at = NOW()
WHERE status = 1 
AND DATE(exchange_time) < CURDATE()
AND lottery_win = 1;

-- 更新非今天兑换的正在使用状态记录为已过期
UPDATE points_mall_exchanges 
SET status = 3, updated_at = NOW()
WHERE status = 2 
AND DATE(exchange_time) < CURDATE()
AND lottery_win = 1;

-- ========================================
-- 5. 验证更新结果
-- ========================================
SELECT 
    '更新后状态分布' as description,
    status,
    CASE status 
        WHEN 1 THEN '已兑换'
        WHEN 2 THEN '正在使用'
        WHEN 3 THEN '已过期'
        WHEN 4 THEN '已停用'
    END as status_text,
    COUNT(*) as count
FROM points_mall_exchanges 
WHERE lottery_win = 1
GROUP BY status
ORDER BY status;

-- ========================================
-- 6. 查看每个用户当前正在使用的形象
-- ========================================
SELECT 
    u.id as user_id,
    u.nickname,
    e.id as exchange_id,
    e.item_id,
    p.name as avatar_name,
    e.exchange_time,
    e.applied_at,
    CASE 
        WHEN DATE(e.exchange_time) = CURDATE() THEN '今天有效'
        ELSE '应该已过期'
    END as validity_status
FROM points_mall_exchanges e
JOIN users u ON e.user_id = u.id
LEFT JOIN points_mall_items p ON e.item_id = p.id
WHERE e.status = 2  -- 正在使用
AND e.lottery_win = 1
ORDER BY e.exchange_time DESC;

-- ========================================
-- 7. 检查数据一致性（每个用户只能有1个正在使用的形象）
-- ========================================
SELECT 
    user_id,
    COUNT(*) as in_use_count,
    GROUP_CONCAT(id) as exchange_ids
FROM points_mall_exchanges 
WHERE status = 2  -- 正在使用
AND lottery_win = 1
GROUP BY user_id
HAVING COUNT(*) > 1;

-- 如果上面的查询有结果，说明存在数据不一致，需要修复

-- ========================================
-- 8. 定时任务SQL（建议每小时执行一次）
-- ========================================
-- 这个SQL可以放在定时任务中，自动清理过期的VIP形象
/*
UPDATE points_mall_exchanges 
SET status = 3, updated_at = NOW()
WHERE status IN (1, 2)  -- 已兑换或正在使用
AND DATE(exchange_time) < CURDATE()  -- 非今天兑换的
AND lottery_win = 1;
*/

-- ========================================
-- 9. 查看最近的形象使用记录
-- ========================================
SELECT 
    e.id,
    u.nickname as user_name,
    p.name as avatar_name,
    e.status,
    CASE e.status 
        WHEN 1 THEN '已兑换'
        WHEN 2 THEN '正在使用'
        WHEN 3 THEN '已过期'
        WHEN 4 THEN '已停用'
    END as status_text,
    e.exchange_time,
    e.applied_at,
    e.created_at,
    e.updated_at
FROM points_mall_exchanges e
JOIN users u ON e.user_id = u.id
LEFT JOIN points_mall_items p ON e.item_id = p.id
WHERE e.lottery_win = 1
ORDER BY e.updated_at DESC
LIMIT 20;
