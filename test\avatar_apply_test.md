# VIP形象应用功能测试文档

## 测试概述

本文档用于测试新增的VIP形象应用功能，包括：
1. 应用VIP形象接口
2. 查看兑换记录中的应用状态
3. 各种边界情况和错误处理

## 测试环境

- 基础URL: `http://localhost:8888`
- 需要用户端JWT Token

## 测试步骤

### 1. 准备测试数据

首先需要确保有可用的VIP形象和兑换记录：

#### 1.1 获取VIP形象列表
```http
GET /api/v1/avatars/carousel
Authorization: Bearer {user_token}
```

#### 1.2 兑换一个VIP形象
```http
POST /api/v1/avatars/{avatar_id}/exchange
Authorization: Bearer {user_token}
Content-Type: application/json

{}
```

#### 1.3 查看兑换记录
```http
GET /api/v1/avatars/my-exchanges?expired_filter=active
Authorization: Bearer {user_token}
```

预期响应：
```json
{
  "code": 0,
  "message": "success",
  "data": [
    {
      "id": 1,
      "status": 1,
      "status_text": "已兑换",
      "lottery_win": true,
      // ... 其他字段
    }
  ]
}
```

### 2. 测试应用形象功能

#### 2.1 正常应用形象
```http
POST /api/v1/avatars/exchanges/{exchange_id}/apply
Authorization: Bearer {user_token}
Content-Type: application/json

{}
```

预期响应：
```json
{
  "code": 0,
  "message": "success",
  "data": {
    "message": "形象应用成功",
    "exchange": {
      "id": 1,
      "status": 2,
      "status_text": "已使用",
      "applied_at": "2025-01-11 11:30:00",
      // ... 其他字段
    }
  }
}
```

#### 2.2 验证应用后的状态
```http
GET /api/v1/avatars/my-exchanges?expired_filter=active
Authorization: Bearer {user_token}
```

预期响应：
```json
{
  "code": 0,
  "message": "success",
  "data": [
    {
      "id": 1,
      "status": 2,
      "status_text": "已使用",
      "applied_at": "2025-01-11 11:30:00",
      // ... 其他字段
    }
  ]
}
```

### 3. 错误情况测试

#### 3.1 重复应用已使用的形象
```http
POST /api/v1/avatars/exchanges/{exchange_id}/apply
Authorization: Bearer {user_token}
Content-Type: application/json

{}
```

预期响应：
```json
{
  "code": 400002,
  "message": "该形象已经应用过了"
}
```

#### 3.2 应用不存在的兑换记录
```http
POST /api/v1/avatars/exchanges/99999/apply
Authorization: Bearer {user_token}
Content-Type: application/json

{}
```

预期响应：
```json
{
  "code": 404001,
  "message": "兑换记录不存在"
}
```

#### 3.3 应用其他用户的兑换记录
使用另一个用户的token尝试应用：
```http
POST /api/v1/avatars/exchanges/{other_user_exchange_id}/apply
Authorization: Bearer {another_user_token}
Content-Type: application/json

{}
```

预期响应：
```json
{
  "code": 404001,
  "message": "兑换记录不存在"
}
```

#### 3.4 应用已过期的形象
等待形象过期（24小时后）或手动修改数据库状态：
```sql
UPDATE points_mall_exchanges 
SET status = 3, exchange_time = DATE_SUB(NOW(), INTERVAL 25 HOUR)
WHERE id = {exchange_id};
```

然后尝试应用：
```http
POST /api/v1/avatars/exchanges/{expired_exchange_id}/apply
Authorization: Bearer {user_token}
Content-Type: application/json

{}
```

预期响应：
```json
{
  "code": 400001,
  "message": "该形象已过期，无法应用"
}
```

#### 3.5 应用抽卡失败的记录
对于抽卡失败的记录（lottery_win=false），尝试应用：
```http
POST /api/v1/avatars/exchanges/{failed_lottery_exchange_id}/apply
Authorization: Bearer {user_token}
Content-Type: application/json

{}
```

预期响应：
```json
{
  "code": 400001,
  "message": "该兑换记录未中奖，无法应用"
}
```

### 4. 数据库验证

#### 4.1 检查applied_at字段
```sql
SELECT id, status, applied_at, exchange_time 
FROM points_mall_exchanges 
WHERE user_id = {user_id} 
ORDER BY created_at DESC;
```

#### 4.2 检查状态更新
```sql
SELECT 
  id,
  status,
  CASE status 
    WHEN 1 THEN '已兑换'
    WHEN 2 THEN '已使用'
    WHEN 3 THEN '已过期'
    ELSE '未知'
  END as status_text,
  applied_at,
  exchange_time
FROM points_mall_exchanges 
WHERE user_id = {user_id};
```

## 测试检查点

### ✅ 功能测试
- [ ] 正常应用形象成功
- [ ] 状态正确更新为"已使用"
- [ ] applied_at字段正确记录应用时间
- [ ] 兑换记录列表正确显示应用状态

### ✅ 错误处理测试
- [ ] 重复应用返回正确错误
- [ ] 不存在的记录返回404
- [ ] 无权限访问返回404
- [ ] 过期记录无法应用
- [ ] 抽卡失败记录无法应用

### ✅ 数据一致性测试
- [ ] 数据库状态正确更新
- [ ] applied_at字段正确设置
- [ ] 查询接口返回一致数据

## 注意事项

1. **测试顺序**：必须先兑换形象，再测试应用功能
2. **权限验证**：确保只能应用自己的兑换记录
3. **状态管理**：验证状态转换的正确性
4. **时间字段**：检查applied_at字段的正确性
5. **过期处理**：测试过期状态的自动更新

## 预期结果

所有测试用例都应该按预期返回正确的响应码和数据格式，确保VIP形象应用功能的完整性和安全性。
