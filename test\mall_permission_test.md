# 商场端权限测试

## 测试目标

验证商场端管理员用户只能访问商场端权限范围内的API接口。

## 测试准备

### 1. 初始化数据
```bash
# 运行数据初始化脚本
mysql -u root -p wangfujing_admin < sql/data_init.sql
```

### 2. 启动服务
```bash
go run cmd/admin/main.go -f config/config.yaml
```

### 3. 商场管理员登录
使用商场管理员手机号登录获取token：
```bash
curl -X POST http://localhost:8889/admin/v1/auth/wechat/login \
  -H "Content-Type: application/json" \
  -d '{
    "login_code": "微信登录code",
    "phone_code": "微信手机号code"
  }'
```

测试手机号：`18900000001` (商场管理员)

## 商场端权限测试用例

### ✅ 应该成功的接口（商场管理员可访问）

#### 1. 商家管理
```bash
# 商家入驻新增
curl -X POST http://localhost:8889/admin/v1/merchants \
  -H "Authorization: Bearer YOUR_TOKEN" \
  -H "Content-Type: application/json" \
  -d '{
    "name": "测试商家",
    "phone": "010-12345678",
    "contact": "测试联系人",
    "floor_id": 1,
    "position": "B1-002"
  }'

# 商家入驻编辑
curl -X PUT http://localhost:8889/admin/v1/merchants/1 \
  -H "Authorization: Bearer YOUR_TOKEN" \
  -H "Content-Type: application/json" \
  -d '{
    "name": "更新商家名称"
  }'

# 商家入驻删除
curl -X DELETE http://localhost:8889/admin/v1/merchants/1 \
  -H "Authorization: Bearer YOUR_TOKEN"
```

#### 2. 等级管理
```bash
# 等级规则查看
curl -X GET http://localhost:8889/admin/v1/levels/score-rules \
  -H "Authorization: Bearer YOUR_TOKEN"

# 等级规则新增
curl -X POST http://localhost:8889/admin/v1/levels/score-items \
  -H "Authorization: Bearer YOUR_TOKEN" \
  -H "Content-Type: application/json" \
  -d '{
    "name": "测试分值项目",
    "score": 10,
    "type": 1
  }'

# 等级权益编辑
curl -X PUT http://localhost:8889/admin/v1/levels/benefits \
  -H "Authorization: Bearer YOUR_TOKEN" \
  -H "Content-Type: application/json" \
  -d '{
    "benefits": "测试权益内容"
  }'
```

#### 3. 权益申请审核
```bash
# 权益申请审核
curl -X PUT http://localhost:8889/admin/v1/benefit-applications/1/approve \
  -H "Authorization: Bearer YOUR_TOKEN" \
  -H "Content-Type: application/json" \
  -d '{
    "audit_note": "审核通过"
  }'
```

#### 4. 分值申请管理
```bash
# 分值申请记录
curl -X GET http://localhost:8889/admin/v1/merchants/score-applications \
  -H "Authorization: Bearer YOUR_TOKEN"

# 分值申请审核
curl -X POST http://localhost:8889/admin/v1/merchants/score-applications/1/approve \
  -H "Authorization: Bearer YOUR_TOKEN" \
  -H "Content-Type: application/json" \
  -d '{
    "audit_note": "审核通过"
  }'

# 分值申请新增
curl -X POST http://localhost:8889/admin/v1/merchants/score-applications \
  -H "Authorization: Bearer YOUR_TOKEN" \
  -H "Content-Type: application/json" \
  -d '{
    "merchant_id": 1,
    "score_item_id": 1,
    "reason": "测试申请"
  }'
```

#### 5. 商品管理
```bash
# 商品审核
curl -X POST http://localhost:8889/admin/v1/products/1/approve \
  -H "Authorization: Bearer YOUR_TOKEN" \
  -H "Content-Type: application/json" \
  -d '{
    "audit_note": "商品审核通过"
  }'

# 商品审核记录
curl -X GET http://localhost:8889/admin/v1/products/1/audit-records \
  -H "Authorization: Bearer YOUR_TOKEN"
```

#### 6. 客诉管理
```bash
# 新增客诉
curl -X POST http://localhost:8889/admin/v1/complaints \
  -H "Authorization: Bearer YOUR_TOKEN" \
  -H "Content-Type: application/json" \
  -d '{
    "title": "测试客诉",
    "content": "客诉内容",
    "merchant_id": 1
  }'

# 客诉历史
curl -X GET http://localhost:8889/admin/v1/complaints \
  -H "Authorization: Bearer YOUR_TOKEN"
```

#### 7. 核销扫码
```bash
# 核销扫码
curl -X POST http://localhost:8889/admin/v1/scan/task \
  -H "Authorization: Bearer YOUR_TOKEN" \
  -H "Content-Type: application/json" \
  -d '{
    "qr_code": "test_qr_code"
  }'

# 核销记录
curl -X GET http://localhost:8889/admin/v1/scan/records \
  -H "Authorization: Bearer YOUR_TOKEN"
```

#### 8. 用户管理
```bash
# 用户列表
curl -X GET http://localhost:8889/admin/v1/customers \
  -H "Authorization: Bearer YOUR_TOKEN"

# 积分明细
curl -X GET http://localhost:8889/admin/v1/customers/1/points \
  -H "Authorization: Bearer YOUR_TOKEN"
```

#### 9. 营销管理
```bash
# 会员积分活动查看
curl -X GET http://localhost:8889/admin/v1/marketing/activities \
  -H "Authorization: Bearer YOUR_TOKEN"

# 会员积分活动新增
curl -X POST http://localhost:8889/admin/v1/marketing/activities \
  -H "Authorization: Bearer YOUR_TOKEN" \
  -H "Content-Type: application/json" \
  -d '{
    "name": "测试活动",
    "description": "活动描述"
  }'
```

#### 10. 订单管理
```bash
# 订单列表
curl -X GET http://localhost:8889/admin/v1/orders \
  -H "Authorization: Bearer YOUR_TOKEN"
```

### ❌ 应该失败的接口（商场管理员无权访问）

#### 1. 超级管理员专用功能
```bash
# 权限管理 - 应该返回403
curl -X GET http://localhost:8889/admin/v1/permissions \
  -H "Authorization: Bearer YOUR_TOKEN"

# 角色管理 - 应该返回403
curl -X GET http://localhost:8889/admin/v1/roles \
  -H "Authorization: Bearer YOUR_TOKEN"

# 用户管理 - 应该返回403
curl -X GET http://localhost:8889/admin/v1/users \
  -H "Authorization: Bearer YOUR_TOKEN"
```

#### 2. 商家端专用功能
```bash
# 商家端权益申请 - 应该返回403（商场端只能审核，不能申请）
curl -X POST http://localhost:8889/admin/v1/benefit-applications \
  -H "Authorization: Bearer YOUR_TOKEN" \
  -H "Content-Type: application/json" \
  -d '{
    "benefit_type": "延迟闭店",
    "reason": "测试申请"
  }'
```

## 预期结果

### 成功响应 (200)
```json
{
    "code": 0,
    "message": "success",
    "data": {...}
}
```

### 权限不足响应 (403)
```json
{
    "code": 403,
    "message": "权限不足"
}
```

## 权限验证

### 1. 检查用户权限
```bash
# 获取用户权限列表
curl -X GET http://localhost:8889/admin/v1/permissions/user \
  -H "Authorization: Bearer YOUR_TOKEN"
```

### 2. 检查特定权限
```bash
# 检查商场端权限
curl -X GET "http://localhost:8889/admin/v1/permissions/check?permission=mall:merchant:create" \
  -H "Authorization: Bearer YOUR_TOKEN"

# 检查商家端权限（应该失败）
curl -X GET "http://localhost:8889/admin/v1/permissions/check?permission=merchant:benefit:apply" \
  -H "Authorization: Bearer YOUR_TOKEN"
```

## 数据库验证

### 1. 检查用户角色
```sql
SELECT au.phone, au.nickname, r.name as role_name, r.display_name
FROM admin_users au
JOIN admin_user_roles aur ON au.id = aur.admin_user_id
JOIN roles r ON aur.role_id = r.id
WHERE au.phone = '18900000001';
```

### 2. 检查角色权限
```sql
SELECT r.name as role_name, p.name as permission_name, p.display_name, p.module
FROM roles r
JOIN role_permissions rp ON r.id = rp.role_id
JOIN permissions p ON rp.permission_id = p.id
WHERE r.name = 'mall_admin'
ORDER BY p.module, p.name;
```

## 自动化测试脚本

```bash
#!/bin/bash
MALL_TOKEN="YOUR_MALL_ADMIN_TOKEN"

echo "测试商场端权限控制..."

# 测试应该成功的接口
echo "测试商家管理（应该成功）..."
curl -s -X GET http://localhost:8889/admin/v1/merchants \
  -H "Authorization: Bearer $MALL_TOKEN" | jq '.code'

# 测试应该失败的接口
echo "测试权限管理（应该失败）..."
curl -s -X GET http://localhost:8889/admin/v1/permissions \
  -H "Authorization: Bearer $MALL_TOKEN" | jq '.code'

echo "商场端权限测试完成"
```
