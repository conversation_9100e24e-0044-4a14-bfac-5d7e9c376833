# 商家登录测试说明

## 测试场景

### 场景1：预置管理员用户登录
- **手机号**: `18701685085` (admin_users表中已存在)
- **预期结果**: 直接登录成功，用户类型为管理员

### 场景2：商家用户自动创建登录
- **手机号**: `18800001001` (merchants表login_phone字段)
- **预期结果**: 
  1. 自动创建admin_users记录
  2. user_type = 2 (商家用户)
  3. merchant_id = 1 (星巴克咖啡)
  4. 自动分配商家角色和权限
  5. 登录成功

### 场景3：商家用户自动创建登录
- **手机号**: `18800001002` (merchants表login_phone字段)
- **预期结果**: 
  1. 自动创建admin_users记录
  2. user_type = 2 (商家用户)
  3. merchant_id = 2 (优衣库)
  4. 自动分配商家角色和权限
  5. 登录成功

### 场景4：未授权手机号登录
- **手机号**: `13900000000` (两个表都不存在)
- **预期结果**: 登录失败，提示"非管理员手机号，请联系系统管理员"

## 测试数据

### merchants表测试数据
```sql
INSERT INTO `merchants` (`id`, `name`, `phone`, `login_phone`, `contact`, `floor_id`, `position`, `description`, `score`, `level`, `status`) VALUES
(1, '星巴克咖啡', '010-12345678', '18800001001', '张经理', 1, 'B1-001', '全球知名咖啡连锁品牌', 95, 1, 1),
(2, '优衣库', '010-87654321', '18800001002', '李经理', 2, '1F-001', '日本休闲服装品牌', 88, 1, 1),
(3, '苹果专卖店', '010-11111111', '18800001003', '王经理', 3, '2F-001', '苹果产品官方零售店', 92, 1, 1);
```

### admin_users表预置数据
```sql
INSERT INTO `admin_users` (`id`, `phone`, `nickname`, `open_id`, `user_type`, `status`, `register_date`) VALUES
(1, '18701685085', '系统管理员', 'oWfEA7slrOB83XybzXMgPFq3yHk8', 3, 1, NOW()),
(2, '18613851200', '测试管理员', 'test-openid-002', 3, 1, NOW());
```

## 验证步骤

### 1. 检查自动创建的商家用户
登录成功后，检查admin_users表：
```sql
SELECT id, phone, nickname, user_type, merchant_id, register_date 
FROM admin_users 
WHERE phone IN ('18800001001', '18800001002', '18800001003');
```

### 2. 检查角色分配
检查商家用户是否分配了商家角色：
```sql
SELECT aur.admin_user_id, au.phone, r.name as role_name, r.display_name
FROM admin_user_roles aur
JOIN admin_users au ON aur.admin_user_id = au.id
JOIN roles r ON aur.role_id = r.id
WHERE au.user_type = 2;
```

### 3. 检查权限分配
检查商家用户是否拥有商家权限：
```sql
SELECT au.phone, p.name as permission_name, p.display_name, p.module
FROM admin_users au
JOIN admin_user_roles aur ON au.id = aur.admin_user_id
JOIN role_permissions rp ON aur.role_id = rp.role_id
JOIN permissions p ON rp.permission_id = p.id
WHERE au.user_type = 2 AND p.name LIKE 'merchant:%'
ORDER BY au.phone, p.module, p.name;
```

## 日志检查

### 成功创建商家用户的日志
```
MerchantUserAutoCreated: {
    "user_id": 新用户ID,
    "phone": "18800001001",
    "merchant_id": 1,
    "merchant_name": "星巴克咖啡"
}
```

### 角色分配成功的日志
```
为商家用户自动分配商家角色成功: {
    "user_id": 新用户ID,
    "phone": "18800001001"
}
```

### 登录失败的日志
```
AdminLogin: {
    "phone": "13900000000",
    "open_id": "xxx",
    "message": "管理端用户和商家登录手机号都不存在"
}
```

## API测试

### 登录接口
```
POST /admin/v1/auth/wechat/login
Content-Type: application/json

{
    "login_code": "微信登录code",
    "phone_code": "微信手机号code"
}
```

### 成功响应
```json
{
    "code": 0,
    "message": "登录成功",
    "data": {
        "token": {
            "access_token": "jwt_token",
            "refresh_token": "refresh_token",
            "expires_at": "2024-01-01T12:00:00Z"
        },
        "user": {
            "user_id": 123,
            "nickname": "张经理",
            "phone": "18800001001",
            "user_type": 2,
            "merchant_id": 1
        }
    }
}
```

### 失败响应
```json
{
    "code": 403,
    "message": "非管理员手机号，请联系系统管理员"
}
```
