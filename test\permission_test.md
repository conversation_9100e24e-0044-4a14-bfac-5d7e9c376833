# 权限控制测试

## 测试目标

验证商家用户只能访问指定的API接口，其他接口返回403权限不足错误。

## 测试准备

### 1. 启动服务
```bash
go run cmd/admin/main.go -f config/config.yaml
```

### 2. 商家用户登录
使用商家登录手机号登录获取token：
```bash
curl -X POST http://localhost:8889/admin/v1/auth/wechat/login \
  -H "Content-Type: application/json" \
  -d '{
    "login_code": "微信登录code",
    "phone_code": "微信手机号code"
  }'
```

测试手机号：
- `18800001001` (星巴克咖啡)
- `18800001002` (优衣库)
- `18800001003` (苹果专卖店)

## 权限测试用例

### ✅ 应该成功的接口（商家用户可访问）

#### 1. 权益申请管理
```bash
# 获取权益申请列表
curl -X GET http://localhost:8889/admin/v1/benefit-applications \
  -H "Authorization: Bearer YOUR_TOKEN"

# 创建权益申请
curl -X POST http://localhost:8889/admin/v1/benefit-applications \
  -H "Authorization: Bearer YOUR_TOKEN" \
  -H "Content-Type: application/json" \
  -d '{...}'

# 重新申请权益
curl -X PUT http://localhost:8889/admin/v1/benefit-applications/1/reapply \
  -H "Authorization: Bearer YOUR_TOKEN" \
  -H "Content-Type: application/json" \
  -d '{...}'
```

#### 2. 商家排名查看
```bash
# 查看商家排名
curl -X GET http://localhost:8889/admin/v1/merchants/ranking \
  -H "Authorization: Bearer YOUR_TOKEN"

# 查看商家列表
curl -X GET http://localhost:8889/admin/v1/merchants \
  -H "Authorization: Bearer YOUR_TOKEN"
```

#### 3. 商品管理
```bash
# 查看商品列表
curl -X GET http://localhost:8889/admin/v1/products \
  -H "Authorization: Bearer YOUR_TOKEN"

# 查看商品详情
curl -X GET http://localhost:8889/admin/v1/products/1 \
  -H "Authorization: Bearer YOUR_TOKEN"
```

#### 4. 活动管理
```bash
# 查看活动列表
curl -X GET http://localhost:8889/admin/v1/marketing/activities \
  -H "Authorization: Bearer YOUR_TOKEN"

# 查看活动详情
curl -X GET http://localhost:8889/admin/v1/marketing/activities/1 \
  -H "Authorization: Bearer YOUR_TOKEN"
```

#### 5. 扫码管理
```bash
# 核销扫码
curl -X POST http://localhost:8889/admin/v1/scan/task \
  -H "Authorization: Bearer YOUR_TOKEN" \
  -H "Content-Type: application/json" \
  -d '{...}'

# 查看核销记录
curl -X GET http://localhost:8889/admin/v1/scan/records \
  -H "Authorization: Bearer YOUR_TOKEN"
```

#### 6. 文件上传
```bash
# 文件上传
curl -X POST http://localhost:8889/admin/v1/upload/file \
  -H "Authorization: Bearer YOUR_TOKEN" \
  -F "file=@test.jpg"

# 获取上传配置
curl -X GET http://localhost:8889/admin/v1/upload/config \
  -H "Authorization: Bearer YOUR_TOKEN"
```

#### 7. 用户信息管理
```bash
# 获取用户信息
curl -X GET http://localhost:8889/admin/v1/auth/user/info \
  -H "Authorization: Bearer YOUR_TOKEN"

# 更新用户信息
curl -X PUT http://localhost:8889/admin/v1/auth/user/info \
  -H "Authorization: Bearer YOUR_TOKEN" \
  -H "Content-Type: application/json" \
  -d '{...}'
```

#### 8. 权限查询
```bash
# 检查权限
curl -X GET "http://localhost:8889/admin/v1/permissions/check?permission=merchant:benefit:apply" \
  -H "Authorization: Bearer YOUR_TOKEN"

# 获取用户权限
curl -X GET http://localhost:8889/admin/v1/permissions/user \
  -H "Authorization: Bearer YOUR_TOKEN"
```

### ❌ 应该失败的接口（商家用户无权访问）

#### 1. 权限管理
```bash
# 获取权限列表 - 应该返回403
curl -X GET http://localhost:8889/admin/v1/permissions \
  -H "Authorization: Bearer YOUR_TOKEN"

# 创建权限 - 应该返回403
curl -X POST http://localhost:8889/admin/v1/permissions \
  -H "Authorization: Bearer YOUR_TOKEN" \
  -H "Content-Type: application/json" \
  -d '{...}'
```

#### 2. 角色管理
```bash
# 获取角色列表 - 应该返回403
curl -X GET http://localhost:8889/admin/v1/roles \
  -H "Authorization: Bearer YOUR_TOKEN"

# 创建角色 - 应该返回403
curl -X POST http://localhost:8889/admin/v1/roles \
  -H "Authorization: Bearer YOUR_TOKEN" \
  -H "Content-Type: application/json" \
  -d '{...}'
```

#### 3. 用户管理
```bash
# 获取管理员用户列表 - 应该返回403
curl -X GET http://localhost:8889/admin/v1/users \
  -H "Authorization: Bearer YOUR_TOKEN"

# 创建管理员用户 - 应该返回403
curl -X POST http://localhost:8889/admin/v1/users \
  -H "Authorization: Bearer YOUR_TOKEN" \
  -H "Content-Type: application/json" \
  -d '{...}'
```

#### 4. 楼层管理
```bash
# 获取楼层列表 - 应该返回403
curl -X GET http://localhost:8889/admin/v1/floors \
  -H "Authorization: Bearer YOUR_TOKEN"

# 创建楼层 - 应该返回403
curl -X POST http://localhost:8889/admin/v1/floors \
  -H "Authorization: Bearer YOUR_TOKEN" \
  -H "Content-Type: application/json" \
  -d '{...}'
```

#### 5. 等级管理
```bash
# 获取分值规则 - 应该返回403
curl -X GET http://localhost:8889/admin/v1/levels/score-rules \
  -H "Authorization: Bearer YOUR_TOKEN"

# 创建增减分项目 - 应该返回403
curl -X POST http://localhost:8889/admin/v1/levels/score-items \
  -H "Authorization: Bearer YOUR_TOKEN" \
  -H "Content-Type: application/json" \
  -d '{...}'
```

#### 6. 客诉管理
```bash
# 获取投诉列表 - 应该返回403
curl -X GET http://localhost:8889/admin/v1/complaints \
  -H "Authorization: Bearer YOUR_TOKEN"
```

#### 7. 操作日志
```bash
# 获取操作日志 - 应该返回403
curl -X GET http://localhost:8889/admin/v1/operation-logs \
  -H "Authorization: Bearer YOUR_TOKEN"
```

## 预期结果

### 成功响应 (200)
```json
{
    "code": 0,
    "message": "success",
    "data": {...}
}
```

### 权限不足响应 (403)
```json
{
    "code": 403,
    "message": "权限不足"
}
```

### 未认证响应 (401)
```json
{
    "code": 401,
    "message": "用户未登录"
}
```

## 验证步骤

1. **登录商家用户**：使用商家手机号登录获取token
2. **测试可访问接口**：验证商家用户可以正常访问指定接口
3. **测试受限接口**：验证商家用户访问受限接口时返回403
4. **检查日志**：查看服务器日志中的权限检查记录
5. **验证权限列表**：调用权限查询接口确认用户权限

## 自动化测试脚本

可以编写自动化测试脚本来批量验证权限控制：

```bash
#!/bin/bash
TOKEN="YOUR_MERCHANT_TOKEN"

echo "测试商家用户权限控制..."

# 测试应该成功的接口
echo "测试权益申请列表..."
curl -s -X GET http://localhost:8889/admin/v1/benefit-applications \
  -H "Authorization: Bearer $TOKEN" | jq '.code'

# 测试应该失败的接口
echo "测试权限管理（应该失败）..."
curl -s -X GET http://localhost:8889/admin/v1/permissions \
  -H "Authorization: Bearer $TOKEN" | jq '.code'

echo "权限测试完成"
```
