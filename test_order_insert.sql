-- 直接插入测试订单数据
USE wangfujing_admin;

-- 确保有测试用户
INSERT IGNORE INTO `users` (`id`, `phone`, `nickname`, `open_id`, `points`, `status`, `register_date`, `created_at`, `updated_at`) VALUES
(2, '18800000001', '测试用户1', 'oWfEA7slrOB83XybzXMgPFq3yHk8', 1000, 1, NOW(), NOW(), NOW());

-- 确保有测试商品
INSERT IGNORE INTO `products` (`id`, `name`, `description`, `images`, `points`, `daily_limit`, `type`, `merchant_id`, `approval_status`, `valid_from`, `valid_to`, `status`, `created_at`, `updated_at`) VALUES
(1, '风景传奇演唱会门票', '门票1张说明文字说明文字说明文字说明文字说明文字说明文字', '["https://example.com/image1.jpg"]', 30, 10, 1, 1, 1, '2024-12-23 12:00:00', '2024-12-30 13:00:00', 1, NOW(), NOW()),
(2, '德云社相声门票', '门票1张说明文字说明文字说明文字说明文字说明文字说明文字', '["https://example.com/image2.jpg"]', 30, 5, 1, 2, 1, '2024-12-23 12:00:00', '2024-12-30 17:30:00', 1, NOW(), NOW());

-- 插入测试订单数据
INSERT IGNORE INTO `orders` (`id`, `order_no`, `request_id`, `user_id`, `product_id`, `merchant_id`, `quantity`, `price`, `total_price`, `points`, `status`, `qr_code`, `verified_at`, `verifier_id`, `verify_remark`, `created_at`, `updated_at`) VALUES
-- 待核销订单（用户2）
(1, 'ORD20241214120001', 'REQ_001', 2, 1, 1, 1, 0.00, 0.00, 30, 0, 'ORDER:ORD20241214120001', NULL, NULL, '', NOW(), NOW()),
(2, 'ORD20241214120002', 'REQ_002', 2, 2, 2, 1, 0.00, 0.00, 30, 0, 'ORDER:ORD20241214120002', NULL, NULL, '', DATE_SUB(NOW(), INTERVAL 1 HOUR), DATE_SUB(NOW(), INTERVAL 1 HOUR)),

-- 已核销订单（用户2）
(3, 'ORD20241214120003', 'REQ_003', 2, 1, 1, 1, 0.00, 0.00, 30, 0, 'ORDER:ORD20241214120003', NOW(), 2, '核销成功', DATE_SUB(NOW(), INTERVAL 2 HOUR), NOW());

SELECT '测试订单数据插入完成' as result;
